#![allow(unused)]
use crate::anki::mind_card::common::{
    mind_node_to_anki_notes, normalize_color, ClozeContext, MindNode, StyleAttribute,
};
use crate::anki::models::{gen_apkg, get_mindmap_card_assets, AnkiNote};
use crate::anki::utils::save_to_temp_file;
use indexmap::indexmap;
use serde_json::Value;
use std::fs;
use std::io;
use std::io::BufWriter;
use std::path::Path;
use std::path::PathBuf;
use tempfile::TempDir;
use ulid::Ulid;

#[derive(Debug)]
pub struct MarkdownCardConfig {
    pub file_path: String,
    pub deck_name: String,
    pub model_name: String,
    pub output_path: String,
    pub tags: Vec<String>,
    pub map_id: String,
    pub img_paths: Vec<String>,
}

/// 将JSON文件转换为MindNode结构
///
/// # Arguments
/// * `json_path` - JSON文件的路径
///
/// # Returns
/// * `Result<MindNode, Box<dyn std::error::Error>>` - 转换后的MindNode或错误
pub fn convert_json_to_node(content: &str) -> Result<MindNode, Box<dyn std::error::Error>> {
    let json: Value = serde_json::from_str(&content)?;
    // 从JSON中获取root节点
    let root = json.get("root").ok_or("Root node not found in JSON")?;
    // 递归转换节点
    fn convert_node(node: &Value) -> Result<MindNode, Box<dyn std::error::Error>> {
        let content = node
            .get("content")
            .and_then(Value::as_str)
            .unwrap_or("")
            .to_string();

        let children = node
            .get("children")
            .and_then(Value::as_array)
            .map(|children| {
                children
                    .iter()
                    .filter_map(|child| convert_node(child).ok())
                    .collect()
            })
            .unwrap_or_default();

        Ok(MindNode {
            id: Ulid::new().to_string(),
            content,
            children,
        })
    }

    convert_node(root)
}

// 主处理函数
pub async fn make_markdown_card(
    config: MarkdownCardConfig,
    progress_callback: impl Fn(f64, f64, String),
) -> Result<(), io::Error> {
    // 解析文件内容
    let json = fs::read_to_string(&config.file_path)?;
    // 生成思维节点树
    let extension = Path::new(&config.file_path)
        .extension()
        .and_then(|s| s.to_str())
        .unwrap_or("");
    dbg!(&extension);
    let temp_dir = crate::anki::utils::get_temp_dir()
        .await
        .map_err(|e| io::Error::new(io::ErrorKind::Other, e.to_string()))?;
    let mut root = convert_json_to_node(&json).map_err(|e| {
        io::Error::new(
            io::ErrorKind::InvalidData,
            "Failed to convert JSON to MindNode",
        )
    })?;
    // 生成Anki笔记
    let mut map_id = "".to_string();
    if config.map_id.is_empty() {
        map_id = Ulid::new().to_string();
    } else {
        map_id = config.map_id.clone();
    }
    let notes: Vec<AnkiNote> = mind_node_to_anki_notes(
        map_id.to_string(),
        &root,
        config.deck_name.clone(),
        config.tags.clone(),
    );
    // 修改媒体处理逻辑
    let mut media_files: Vec<String> = config
        .img_paths
        .into_iter()
        .filter_map(|path| {
            if Path::new(&path).exists() {
                Some(path)
            } else {
                eprintln!("Media file not found: {}", path);
                None
            }
        })
        .collect();
    let tree_js_path = PathBuf::from(temp_dir.clone()).join(format!("__{}.js", map_id.to_string()));
    fs::write(&tree_js_path, &root.to_js())?;
    media_files.push(tree_js_path.to_string_lossy().to_string());

    dbg!(&media_files);
    // 修改APKG生成调用
    crate::anki::models::gen_apkg(
        notes,
        Some(media_files),
        &config.output_path,
        None,
        false,
        Some(vec!["mindmap_card".to_string()]),
    )
    .await?;

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    const TEST_PATH: &str = "/Users/<USER>/Downloads/01jtf9dmtb9jzdrnj5ayze132r.json";

    #[tokio::test]
    async fn test_make_markdown_card_apkg() {
        let xmind_path = TEST_PATH;
        let parent_dir = Path::new(xmind_path).parent().unwrap();
        let output_path = parent_dir.join("output-markdown.apkg");

        let config = MarkdownCardConfig {
            file_path: xmind_path.to_string(),
            deck_name: "Test Deck".to_string(),
            model_name: "Kevin Mindmap Card v3".to_string(),
            tags: vec!["test".to_string()],
            output_path: output_path.to_string_lossy().to_string(),
            map_id: "".to_string(),
            img_paths: vec![],
        };

        let result = make_markdown_card(config, |progress, total, msg| {
            println!("Progress: {}/{} - {}", progress, total, msg);
        })
        .await;
        dbg!(&result);
        assert!(result.is_ok());
        assert!(Path::new(&output_path).exists());
    }
}
