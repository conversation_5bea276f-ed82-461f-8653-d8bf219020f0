import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';

class MarkdownCardPageController extends GetxController {
  // 已有数据
  final cardModel = "Kevin Text Cloze v3".obs;
  final tabController = ShadTabsController(value: 'cloze');
  final separatorList = [
    {"label": "# xx", "value": "^\\s*#\\s"},
    {"label": "## xx", "value": "^\\s*##\\s"},
    {"label": "### xx", "value": "^\\s*###\\s"},
    {"label": "#### xx", "value": "^\\s*####\\s"},
    {"label": "##### xx", "value": "^\\s*#####\\s"},
    {"label": "###### xx", "value": "^\\s*######\\s"},
  ];
  List<Map<String, String>> get qaAnswerRegexList => [
    {"label": 'anki.markdown_card.none'.tr, "value": ""},
    {"label": "---", "value": "---"},
    {"label": "答: xx", "value": "^\\s*答\\s*[:：]"},
    {"label": "A: xx", "value": "^\\s*A\\s*[:：]"},
    {"label": "答案: xx", "value": "^\\s*答案"},
    {"label": "正确答案: xx", "value": "^\\s*正确答案"},
    {"label": "【答案】 xx", "value": "^\\s*【答案】"},
    {"label": "【正确答案】 xx", "value": "^\\s*【正确答案"},
    {"label": "【答案解析】 xx", "value": "^\\s*【答案解析"},
    {"label": 'anki.markdown_card.chinese_character_set'.tr, "value": "^\\s*[\u4e00-\u9fff]+"},
  ];
  List<Map<String, String>> get clozeModeList => [
    {"label": 'anki.markdown_card.mask_one_guess_one'.tr, "value": "mask_one_guess_one"},
    {"label": 'anki.markdown_card.mask_all_guess_one'.tr, "value": "mask_all_guess_one"},
    {"label": 'anki.markdown_card.mask_all_guess_all'.tr, "value": "mask_all_guess_all"},
    // {"label": "遮半猜一", "value": "mask_half_guess_one"},
    {"label": 'anki.markdown_card.free_guess'.tr, "value": "free_guess"},
  ];
  final clozeGrammarList = [
    {"label": "[[c1::xx]]", "value": "[[c1::xx]]"},
    {"label": "{{c1::xx}}", "value": "{{c1::xx}}"},
    {"label": "[[xx]]", "value": "[[xx]]"},
    {"label": "{{xx}}", "value": "{{xx}}"},
    {"label": "==xx==", "value": "==xx=="},
    {"label": "**xx**", "value": "**xx**"},
    {"label": "***xx***", "value": "***xx***"},
    {"label": "*xx*", "value": "*xx*"},
  ].obs;
  // 表单参数
  final parentDeck = 'anki.markdown_card.guru_import'.tr.obs;
  final isCreateSubDeck = false.obs;
  final separator = '^\\s*##\\s'.obs;
  final clozeMode = "free_guess".obs;
  final isPerClozePerCard = false.obs;
  final clozeGrammar = <String>["[[c1::xx]]"].obs;
  final isIgnoreGroup = true.obs;
  final isObsidian = false.obs;
  final primaryMaskColor = "ffff5656".obs;
  final secondaryMaskColor = "ffffeba2".obs;
  final isAnswerCloze = false.obs;
  final isShowSource = true.obs;
  final mediaFolder = "".obs;
  final selectedFilePaths = <String>[].obs;

  final tags = <String>[].obs;
  final fieldMappings = <String, PatternMatch>{}.obs;

  // 控制器
  final ankiConnectController = Get.find<AnkiConnectController>();
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();

  @override
  void onInit() async {
    super.onInit();
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }
    updateFieldList(cardModel.value);
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> updateFieldList(String modelName) async {
    try {
      // 调用updateFieldList并等待它完成，但不使用返回值
      await ankiConnectController.updateFieldList(modelName);
      fieldMappings.clear();

      // 为新字段创建映射，保留已有的映射
      for (var field in ankiConnectController.fieldList) {
        if (tabController.selected == "cloze") {
          if (field == "Front") {
            fieldMappings[field] =
                PatternMatch(field, "^\\s*##\\s", null, true);
          } else {
            fieldMappings[field] = PatternMatch(field, "", null, true);
          }
        } else if (tabController.selected == "qa") {
          if (field == "Front") {
            fieldMappings[field] =
                PatternMatch(field, "^\\s*##\\s", null, true);
          } else {
            fieldMappings[field] = PatternMatch(field, "", null, true);
          }
        }

        // 更新字段映射相关的UI
        final updateId = 'field_mappings_${field}_$modelName';
        update([updateId]);
      }

      // 更新主控制器状态
      update();
    } catch (e, stack) {
      logger.e("updateFieldList error", error: e, stackTrace: stack);
    }
  }

  // 获取字段映射值
  PatternMatch? getFieldMappingValue(String field) {
    if (fieldMappings.containsKey(field)) {
      return fieldMappings[field];
    }
    return null;
  }

  // 更新字段映射
  void updateFieldMapping(String field, PatternMatch value) {
    fieldMappings[field] = value;
    fieldMappings.refresh();
    update();
  }

  /// 验证字段匹配模式
  String _validateFieldPattern(String field, String errorMessage) {
    final pattern = fieldMappings[field]?.regex;
    if (pattern == null || pattern.isEmpty) {
      throw Exception(errorMessage);
    }
    return pattern;
  }

  Future<String> getFirstField(String path, {List<String>? roiFields}) async {
    final text = await AnkiConnectController().readFileWithFallback(path);
    final matches = <PatternMatch>[];
    for (final mapping in fieldMappings.entries) {
      final field = mapping.key;
      final pattern = mapping.value;
      if (pattern.regex.isEmpty) continue;
      if (roiFields != null && !roiFields.contains(field)) continue;
      final regExp = RegExp(pattern.regex, multiLine: true);
      final match = regExp.firstMatch(text);
      if (match != null) {
        matches
            .add(PatternMatch(field, pattern.regex, match, pattern.keepPrefix));
      }
    }
    if (matches.isEmpty) {
      return "";
    }
    matches
        .sort((a, b) => (a.match?.start ?? 0).compareTo(b.match?.start ?? 0));
    return matches.first.field;
  }

  /// 创建笔记
  AnkiNote _createNote(
      String deckName, Map<String, String> content, String modelName) {
    final fields =
        List.generate(ankiConnectController.fieldList.length, (index) {
      final field = ankiConnectController.fieldList[index];
      return content[field] ?? '';
    });

    return AnkiNote(
      deckName: deckName,
      modelName: modelName,
      fields: fields,
      tags: tags.toList(),
    );
  }

  /// 处理文本提取
  Map<String, String> _extractContent(String text, List<PatternMatch> matches) {
    final extractedContent = <String, String>{};

    // 按位置排序
    matches
        .sort((a, b) => (a.match?.start ?? 0).compareTo(b.match?.start ?? 0));

    for (var i = 0; i < matches.length; i++) {
      final current = matches[i];
      final nextMatch = i < matches.length - 1 ? matches[i + 1] : null;
      final nextStart = nextMatch?.match?.start ?? text.length;

      final content = text
          .substring(
              current.keepPrefix ? current.match!.start : current.match!.end,
              nextStart)
          .trim();
      extractedContent[current.field] = content;
    }

    return extractedContent;
  }



  Future<void> submit(BuildContext context) async {
    final exportCardMode = settingController.getExportCardMode();
    final address = settingController.ankiConnectUrl;
    progressController.reset(
      showOutputHint: exportCardMode == "apkg",
      numberButtons: exportCardMode == "apkg" ? 2 : 0,
    );
    progressController.showProgressDialog(context);
    // Markdown 按标题级别分割
    final headingRegexLevels = {
      "^\\s*#\\s": 1,
      "^\\s*##\\s": 2,
      "^\\s*###\\s": 3,
      "^\\s*####\\s": 4,
      "^\\s*#####\\s": 5,
      "^\\s*######\\s": 6,
    };
    if (tabController.selected == "qa") {
      if (selectedFilePaths.isEmpty) {
        throw Exception('anki.markdown_card.please_select_files'.tr);
      }
      final notes = <AnkiNote>[];

      for (final filePath in selectedFilePaths) {
        String finalFilePath = filePath;
        final level = headingRegexLevels[separator.value] ?? 1;
        final objs = await AnkiConnectController().splitMarkdownByLevel(
          finalFilePath,
          level,
        );
        final totalQuestions = objs.length;
        var currentQuestion = 0;
        for (var obj in objs) {
          final title = obj['title'] ?? "";
          final body = obj['body'] ?? "";
          Map<String, String> extractedContent = {
            "Front": title,
            "Back": body,
          };
          // 转换挖空标记
          for (var field in ['Front', 'Back']) {
            if (extractedContent.containsKey(field)) {
              extractedContent[field] = AnkiConnectController().convertCloze(
                extractedContent[field]!,
                clozeGrammar,
                ignoreGroup: isIgnoreGroup.value,
              );
            }
          }
          logger.w("extractedContent: $extractedContent");
          if (isAnswerCloze.value) {
            for (var field in ["Front", "Back"]) {
              if (extractedContent.containsKey(field)) {
                extractedContent[field] = AnkiConnectController().convertCloze(
                  extractedContent[field]!,
                  clozeGrammar,
                  ignoreGroup: true,
                );
              }
            }
          }
          // 创建笔记
          final note =
              _createNote(parentDeck.value, extractedContent, cardModel.value);
          List<int> fieldIds =
              List.generate(ankiConnectController.fieldList.length, (i) => i);
          String mediaDir = PathUtils(filePath).parent;
          if (mediaFolder.value.isNotEmpty) {
            mediaDir = mediaFolder.value;
          }
          final newNote = await AnkiConnectController().convertMD2HTML(
              note, fieldIds, filePath,
              mediaDir: mediaDir, convertObsidianLinks: isObsidian.value);
          // 把每个字段的最外层p标签去除
          for (var idx = 0; idx < newNote.fields.length; idx++) {
            final fieldContent = newNote.fields[idx];
            newNote.fields[idx] =
                await AnkiConnectController().removeRootPTag(fieldContent);
          }
          notes.add(newNote);
          currentQuestion++;
          progressController.updateProgress(
            status: "running",
            message: 'anki.markdown_card.processing_question'.trParams({'current': currentQuestion.toString()}),
            current: currentQuestion.toDouble(),
            total: totalQuestions.toDouble(),
          );
        }
      }
      // 生成并导入卡片
      await AnkiConnectController().generateAndImportCards(notes);
    } else if (tabController.selected == "cloze") {
      if (cardModel.value != "Kevin Text Cloze v3") {
        progressController.updateProgress(
          status: "error",
          message: 'anki.markdown_card.please_select_kevin_text_cloze_template'.tr,
        );
        return;
      }
      if (selectedFilePaths.isEmpty) {
        throw Exception('anki.markdown_card.please_select_files'.tr);
      }
      _validateFieldPattern('Front', 'anki.markdown_card.please_set_front_field_pattern'.tr);

      final notes = <AnkiNote>[];

      for (final filePath in selectedFilePaths) {
        String finalFilePath = filePath;
        final level = headingRegexLevels[separator.value] ?? 1;
        logger.w("level: $level");
        final objs = await AnkiConnectController().splitMarkdownByLevel(
          finalFilePath,
          level,
        );
        final totalQuestions = objs.length;
        var currentQuestion = 0;
        for (var obj in objs) {
          final title = obj['title'] ?? "";
          final body = obj['body'] ?? "";
          Map<String, String> extractedContent = {
            "Front": "${title}${body.isEmpty ? "" : "\n${body}"}",
            "Back": "",
          };
          // 转换挖空标记
          for (var field in ['Front', 'Back']) {
            if (extractedContent.containsKey(field)) {
              extractedContent[field] = AnkiConnectController().convertCloze(
                extractedContent[field]!,
                clozeGrammar,
                ignoreGroup: isIgnoreGroup.value,
              );
            }
          }
          if (!extractedContent.containsKey("Mode")) {
            extractedContent["Mode"] = clozeMode.value;
          }
          logger.w("extractedContent: $extractedContent");

          // 创建笔记
          // 判断是否一空一卡
          if ((clozeMode.value == "mask_all_guess_one" ||
                  clozeMode.value == "mask_one_guess_one") &&
              isPerClozePerCard.value) {
            final content = extractedContent['Front'];
            extractedContent['Mode'] = '${clozeMode.value}_multi';
            // 提取所有cloze编号
            var clozeNumbers = <String>[];
            if (content != null) {
              final regex = RegExp(r'\[\[(c\d+)::.*?\]\]');
              final matches = regex.allMatches(content);
              for (final match in matches) {
                final clozeNum = match.group(1);
                if (clozeNum != null && !clozeNumbers.contains(clozeNum)) {
                  clozeNumbers.add(clozeNum);
                }
              }
            }
            logger.w('clozeNumbers: $clozeNumbers');
            // 去重clozeNumbers
            clozeNumbers = clozeNumbers.toSet().toList();
            if (clozeNumbers.isNotEmpty) {
              for (final clozeNum in clozeNumbers) {
                extractedContent['Index'] = clozeNum;
                // notes.add(
                //     _createNote(deckName, extractedContent, cardModel.value));
                final note = _createNote(
                    parentDeck.value, extractedContent, cardModel.value);
                List<int> fieldIds = List.generate(
                    ankiConnectController.fieldList.length, (i) => i);
                String mediaDir = PathUtils(filePath).parent;
                if (mediaFolder.value.isNotEmpty) {
                  mediaDir = mediaFolder.value;
                }
                final newNote = await AnkiConnectController().convertMD2HTML(
                    note, fieldIds, filePath,
                    mediaDir: mediaDir, convertObsidianLinks: isObsidian.value);
                // 把每个字段的最外层p标签去除
                for (var idx = 0; idx < newNote.fields.length; idx++) {
                  final fieldContent = newNote.fields[idx];
                  newNote.fields[idx] = await AnkiConnectController()
                      .removeRootPTag(fieldContent);
                }
                notes.add(newNote);
              }
            } else {
              // notes.add(
              //     _createNote(deckName, extractedContent, cardModel.value));
              final note = _createNote(
                  parentDeck.value, extractedContent, cardModel.value);
              List<int> fieldIds = List.generate(
                  ankiConnectController.fieldList.length, (i) => i);
              String mediaDir = PathUtils(filePath).parent;
              if (mediaFolder.value.isNotEmpty) {
                mediaDir = mediaFolder.value;
              }
              final newNote = await AnkiConnectController().convertMD2HTML(
                  note, fieldIds, filePath,
                  mediaDir: mediaDir, convertObsidianLinks: isObsidian.value);
              // 把每个字段的最外层p标签去除
              for (var idx = 0; idx < newNote.fields.length; idx++) {
                final fieldContent = newNote.fields[idx];
                newNote.fields[idx] =
                    await AnkiConnectController().removeRootPTag(fieldContent);
              }
              notes.add(newNote);
            }
          } else {
            // notes.add(_createNote(deckName, extractedContent, cardModel.value));
            final note = _createNote(
                parentDeck.value, extractedContent, cardModel.value);
            List<int> fieldIds =
                List.generate(ankiConnectController.fieldList.length, (i) => i);
            String mediaDir = PathUtils(filePath).parent;
            if (mediaFolder.value.isNotEmpty) {
              mediaDir = mediaFolder.value;
            }
            final newNote = await AnkiConnectController().convertMD2HTML(
                note, fieldIds, filePath,
                mediaDir: mediaDir, convertObsidianLinks: isObsidian.value);
            // 把每个字段的最外层p标签去除
            for (var idx = 0; idx < newNote.fields.length; idx++) {
              final fieldContent = newNote.fields[idx];
              newNote.fields[idx] =
                  await AnkiConnectController().removeRootPTag(fieldContent);
            }
            notes.add(newNote);
          }

          currentQuestion++;
          progressController.updateProgress(
            status: "running",
            message: 'anki.markdown_card.processing_question'.trParams({'current': currentQuestion.toString()}),
            current: currentQuestion.toDouble(),
            total: totalQuestions.toDouble(),
          );
        }
      }
      // 生成并导入卡片
      await AnkiConnectController().generateAndImportCards(notes);
    }
  }
}
