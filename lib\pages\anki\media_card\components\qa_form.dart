import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/media_card.dart';
import 'dart:io';

class QAForm extends GetView<MediaCardPageController> {
  const QAForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
                onAddNew: (newDeckName) {
                  // Add to the deck list if not already present
                  if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                    ankiConnectController.parentDeckList.add(newDeckName);
                  }

                  // Set as selected deck
                  controller.parentDeck.value = newDeckName;
                },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              ShadSwitchCustom(
                key: ValueKey("subdeck-${controller.isCreateSubDeck.hashCode}"),
                label: 'anki.common.create_subdeck'.tr,
                initialValue: controller.isCreateSubDeck.value,
                onChanged: (v) {
                  controller.isCreateSubDeck.value = v;
                },
              ),
              ShadSelectCustom(
                key: ValueKey(controller.cardModel.value),
                label: "anki.media_card.card_template".tr,
                placeholder: "anki.media_card.select_card_template".tr,
                initialValue: [controller.cardModel.value],
                options: ankiConnectController.modelList
                    .map((e) => {"value": e, "label": e})
                    .toList(),
                onChanged: (value) {
                  controller.cardModel.value = value.single;
                  controller.updateFieldList(controller.cardModel.value);
                },
              ),
              if (ankiConnectController.fieldList.isNotEmpty) ...[
                ListTile(
                  contentPadding: EdgeInsets.zero,
                  minVerticalPadding: 0,
                  title: Text('anki.media_card.field_mapping'.tr, style: defaultTitleStyle),
                  subtitle: Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxWidth: double.infinity,
                        maxHeight:
                            (1 + ankiConnectController.fieldList.length) * 48,
                      ),
                      child: ShadTable(
                        columnCount: 3,
                        rowCount: ankiConnectController.fieldList.length,
                        pinnedRowCount: 1,
                        header: (context, column) {
                          if (column == 0) {
                            return ShadTableCell.header(
                              child: Text('anki.media_card.template_field'.tr),
                            );
                          } else {
                            return ShadTableCell.header(
                              child: Text('anki.media_card.match_type'.tr),
                            );
                          }
                        },
                        columnSpanExtent: (index) {
                          if (Platform.isWindows ||
                              Platform.isLinux ||
                              Platform.isMacOS) {
                            final splitMap = {
                              0: 0.3,
                              1: 0.7,
                            };
                            return FractionalTableSpanExtent(
                                splitMap[index] ?? 0.2);
                          } else {
                            if (index == 1) {
                              return const FractionalTableSpanExtent(0.5);
                            }
                          }
                        },
                        rowSpanExtent: (index) =>
                            const FixedTableSpanExtent(48),
                        builder: (context, index) {
                          final field =
                              ankiConnectController.fieldList[index.row];
                          if (index.column == 0) {
                            return ShadTableCell(child: Text(field));
                          } else {
                            return ShadTableCell(
                              child: GetBuilder<MediaCardPageController>(
                                  id: 'field_mappings_$field',
                                  builder: (controller) {
                                    return SizedBox(
                                      width: double.infinity,
                                      child: ShadSelectCustom(
                                        key: ValueKey(
                                            'field_mapping_${field}_${controller.cardModel.value}'),
                                        label: 'anki.media_card.match_type'.tr,
                                        placeholder: 'anki.media_card.select_match_type'.tr,
                                        isMultiple: false,
                                        showLabel: false,
                                        options: controller.matchTypeList,
                                        onChanged: (value) {
                                          logger.i(value);
                                          if (value.isNotEmpty) {
                                            controller.fieldMappings[field] =
                                                value.single;
                                          } else {
                                            controller.fieldMappings
                                                .remove(field);
                                          }
                                        },
                                      ),
                                    );
                                  }),
                            );
                          }
                        },
                      ),
                    ),
                  ),
                )
              ],
              ShadSelectWithInput(
                label: "anki.media_card.file_suffix".tr,
                placeholder: "anki.media_card.select_file_suffix".tr,
                searchPlaceholder: "anki.media_card.input_file_suffix".tr,
                isMultiple: true,
                initialValue: controller.suffixList,
                options: controller.suffixListData,
                onChanged: (value) {
                  controller.suffixList.value = value;
                },
                onAddNew: (newSuffix) {
                  // Add new file suffix to the list if not already present
                  final newOption = {'value': newSuffix, 'label': newSuffix};
                  if (!controller.suffixListData.any((option) => option['value'] == newSuffix)) {
                    controller.suffixListData.add(newOption);
                  }

                  // Add to selected suffixes if not already present
                  if (!controller.suffixList.contains(newSuffix)) {
                    controller.suffixList.add(newSuffix);
                  }
                },
              ),
              ShadSelectWithInput(
                key:
                    ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.media_card.select_tags'.tr,
                searchPlaceholder: 'anki.media_card.input_tags'.tr,
                isMultiple: true,
                initialValue: controller.tags.toList(),
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tags.contains(newTag)) {
                    controller.tags.add(newTag);
                  }
                },
              ),
              ShadInputWithFileSelect(
                title: 'anki.media_card.media_file_directory'.tr,
                placeholder: Text('anki.media_card.media_file_directory_placeholder'.tr),
                initialValue: [controller.mediaFolder.value],
                isFolder: true,
                onFilesSelected: (value) {
                  controller.mediaFolder.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
          )),
    );
  }
}
