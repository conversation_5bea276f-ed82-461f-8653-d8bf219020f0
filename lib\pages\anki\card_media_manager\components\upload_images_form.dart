import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/card_media_controller.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';

class UploadImagesForm extends GetView<CardMediaManagerController> {
  const UploadImagesForm({super.key});

  @override
  Widget build(BuildContext context) {
    final ankiConnectController = Get.find<AnkiConnectController>();

    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
                onAddNew: (newDeckName) {
                  // Add to the deck list if not already present
                  if (!ankiConnectController.parentDeckList
                      .contains(newDeckName)) {
                    ankiConnectController.parentDeckList.add(newDeckName);
                  }

                  // Set as selected deck
                  controller.parentDeck.value = newDeckName;
                },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              ShadInputWithValidate(
                label: 'anki.card_media_manager.picgo_address'.tr,
                placeholder:
                    'anki.card_media_manager.picgo_address_placeholder'.tr,
                initialValue:
                    controller.uploadParams.picgoAddress.value.toString(),
                onChanged: (value) {
                  final regex = RegExp(r'^\d+$');
                  if (value.isNotEmpty && regex.hasMatch(value)) {
                    controller.uploadParams.picgoAddress.value = value;
                  }
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'anki.card_media_manager.picgo_address_cannot_empty'
                        .tr;
                  }
                  return null;
                },
              ),
              ShadCheckboxGroup(
                key: ValueKey(
                    'prompt-card-types-${controller.mediaTypeList.value}'),
                label: 'anki.card_media_manager.media_types'.tr,
                initialValues: controller.mediaTypeList.toList(),
                items: controller.mediaTypeOptions,
                onChanged: (value) {
                  logger.i(value);
                  controller.mediaTypeList.clear();
                  controller.mediaTypeList.addAll(value);
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return "anki.card_media_manager.at_least_one_card_type".tr;
                  }
                  return "";
                },
                onValidateError: (error) {},
              ),
              const SizedBox(height: 4),
            ],
          )),
    );
  }
}
