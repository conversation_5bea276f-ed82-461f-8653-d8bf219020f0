import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/video_notes/subtitle_controller.dart';
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';

class MySubtitleView extends StatelessWidget {
  final subtitleController = Get.find<SubtitleController>();
  final videoNoteController = Get.find<VideoNoteController>();

  MySubtitleView({super.key});

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String hours = twoDigits(duration.inHours);
    String minutes = twoDigits(duration.inMinutes.remainder(60));
    String seconds = twoDigits(duration.inSeconds.remainder(60));
    return "$hours:$minutes:$seconds";
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              IconButton.outlined(
                onPressed: () {
                  final currentIndex = videoNoteController.playlist.indexWhere(
                      (item) =>
                          item['url'] ==
                          videoNoteController.getCurrentVideoUrl());
                  subtitleController.loadSubtitleFile(
                    context,
                    playlistIndex: currentIndex >= 0 ? currentIndex : null,
                  );
                },
                icon: const Icon(Icons.folder_open),
                iconSize: 24,
                tooltip: '导入字幕文件',
              ),
              const SizedBox(width: 8),
              IconButton.outlined(
                onPressed: () => subtitleController.clearSubtitles(),
                icon: const Icon(Icons.delete_forever),
                iconSize: 24,
                tooltip: '清除字幕',
              ),
            ],
          ),
        ),
        Expanded(
          child: Obx(() {
            if (subtitleController.subtitles.isEmpty) {
              return const Center(
                child: Text('未加载字幕文件'),
              );
            }
            return ListView.builder(
              itemCount: subtitleController.subtitles.length,
              itemBuilder: (context, index) {
                final subtitle = subtitleController.subtitles[index];
                return Obx(() {
                  final isCurrentSubtitle =
                      subtitleController.currentSubtitleIndex.value == index;

                  return Container(
                    margin: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 4.0),
                    decoration: BoxDecoration(
                      color: isCurrentSubtitle
                          ? Theme.of(context).colorScheme.primary.withAlpha(25)
                          : Theme.of(context).colorScheme.surface,
                      border: Border.all(
                        color: isCurrentSubtitle
                            ? Theme.of(context)
                                .colorScheme
                                .primary
                                .withAlpha(75)
                            : Theme.of(context)
                                .colorScheme
                                .outline
                                .withAlpha(75),
                      ),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: ListTile(
                      leading: Text(
                        _formatDuration(subtitle.start),
                        style: TextStyle(
                          color: isCurrentSubtitle
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      title: Text(
                        subtitle.text,
                        style: TextStyle(
                          color: isCurrentSubtitle
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      onTap: () {
                        videoNoteController.player.value.seek(subtitle.start);
                      },
                    ),
                  );
                });
              },
            );
          }),
        ),
      ],
    );
  }
}
