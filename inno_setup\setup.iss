; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define MyAppName "PDF Guru Anki"
#define MyAppVersion "v25.06.0"
#define MyAppPublisher "Kevin2li"
#define MyAppURL "https://github.com/kevin2li/PDF-Guru"
#define MyAppExeName "anki_guru.exe"
#define MyUid "0001-0001-0001-0001-0002"
#define MyAppInstallPath "D:\PDF Guru Anki2"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
AppId={{9923F820-E111-4A4C-B7B0-************}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
;AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={#MyAppInstallPath}
DisableProgramGroupPage=yes
DisableDirPage=no
DirExistsWarning=yes
; Uncomment the following line to run in non administrative install mode (install for current user only.)
; PrivilegesRequired=lowest
OutputDir=..\build
OutputBaseFilename=PDF-Guru-Anki-Setup-Windows-x86_64_{#MyAppVersion}
WizardStyle=modern
SolidCompression=yes
Compression=lzma2/max

[Languages]
Name: "chinesesimplified"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "..\build\windows\x64\runner\Release\*"; DestDir: "{app}\"; Flags: recursesubdirs
Source: "..\native\hub\assets\windows\*"; DestDir: "{app}\"; Flags: ignoreversion
Source: "..\native\pylib\dist\*"; DestDir: "{app}\"; Flags: recursesubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[Registry]
; Root: HKCR; Subkey: "guru"; ValueType: string; ValueName: "URL Protocol"; ValueData: ""; Flags: uninsdeletevalue
; Root: HKCR; Subkey: "guru\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\core\seek.exe,1"; Flags: uninsdeletekey
; Root: HKCR; Subkey: "guru\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\core\seek.exe"" ""-i"" ""%1"""; Flags: uninsdeletekey
; Root: HKLM ;SubKey:"Software\Guru";ValueType:dword;ValueName:config;ValueData:10 ;Flags:uninsdeletevalue


