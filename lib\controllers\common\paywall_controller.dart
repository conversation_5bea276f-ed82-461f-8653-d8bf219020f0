import 'package:anki_guru/controllers/common.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:dio/dio.dart';
import 'dart:convert';
import 'dart:async';
import 'dart:io';

class PaywallController extends GetxController {
  final LicenseController licenseController = Get.find<LicenseController>();
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;

  // 环境标志和产品ID
  final isSandboxMode = true.obs;
  final String monthlyProductId = 'monthly';
  final String yearlyProductId = 'annually';
  final String lifetimeProductId = 'lifetime';

  // API URLs
  final String apiBaseUrl = 'https://appstore.kevin2li.top';
  final String sandboxApiBaseUrl = 'https://sandbox.kevin2li.top';

  // 购买状态和产品信息
  final isPurchasing = false.obs;
  final selectedProductId = ''.obs;
  final products = <ProductDetails>[].obs;
  final monthlyProduct = Rx<ProductDetails?>(null);
  final yearlyProduct = Rx<ProductDetails?>(null);
  final lifetimeProduct = Rx<ProductDetails?>(null);

  // 免费试用期信息
  final isTrialEligible = true.obs;
  final trialDurationDays = 3.obs;
  final showTrialForMonthly = true.obs;
  final showTrialForYearly = true.obs;

  // 用户标识
  final appleUserId = "".obs;
  final _storage = StorageManager();

  // 存储的键名
  static const String _storageBox = StorageBox.default_;
  static const String kPurchaseInfoKey = 'ios_purchase_info';
  static const String kUserIdKey = 'ios_user_id';
  static const String kProductIdKey = 'ios_product_id';
  static const String kExpiryDateKey = 'ios_expiry_date';
  static const String kTransactionIdKey = 'ios_transaction_id';
  static const String kPurchaseDateKey = 'ios_purchase_date';
  static const String kReceiptDataKey = 'ios_receipt_data';
  static const String kIsActiveKey = 'ios_is_active';
  static const String kTrialEligibilityKey = 'ios_trial_eligibility';

  // 控制状态标志
  final _isVerifying = false.obs;
  final _isInitialized = false.obs;
  final _verifyDebouncer = Debouncer(delay: const Duration(seconds: 3));
  late final Dio _dio;
  StreamSubscription<List<PurchaseDetails>>? _purchaseSubscription;

  @override
  void onInit() {
    super.onInit();
    _initDio();
    _initialize();
  }

  Dio get _apiClient => _dio;

  void _initDio() {
    _dio = Dio();
    _dio.options.baseUrl = isSandboxMode.value ? sandboxApiBaseUrl : apiBaseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    _dio.interceptors.clear();
    _dio.interceptors.add(LogInterceptor(
      request: true,
      responseBody: true,
      error: true,
      requestBody: true,
      requestHeader: true,
      responseHeader: true,
    ));

    // 添加重试拦截器
    _dio.interceptors.add(
      RetryInterceptor(
        dio: _dio,
        logPrint: (message) => logger.i(message),
        retries: 1,
        retryDelays: const [Duration(seconds: 2)],
      ),
    );
  }

  Future<void> _initialize() async {
    if (_isInitialized.value) {
      logger.i('PaywallController已经初始化，跳过重复初始化');
      return;
    }

    _isInitialized.value = true;
    await _loadPurchaseInfoFromStorage();

    final bool available = await _inAppPurchase.isAvailable();
    if (!available) {
      showToastNotification(null, '错误', '应用内购买不可用', type: "error");
      return;
    }

    _purchaseSubscription = _inAppPurchase.purchaseStream.listen(
      _handlePurchaseUpdates,
      onDone: () {},
      onError: (error) {
        showToastNotification(null, '错误', '购买监听错误: $error', type: "error");
      },
    );

    await _loadProducts();
    await _fetchAppleUserId();
    await _checkTrialEligibility();
  }

  // 检查用户是否有资格获得免费试用期
  Future<void> _checkTrialEligibility() async {
    final eligibility =
        _storage.read<bool>(_storageBox, kTrialEligibilityKey, true);
    isTrialEligible.value = eligibility;

    // 如果用户之前已经购买过任何产品，就不再有资格获得免费试用期
    final previousPurchase =
        _storage.read<String>(_storageBox, kProductIdKey, '');
    if (previousPurchase.isNotEmpty) {
      isTrialEligible.value = false;
      _storage.write(_storageBox, kTrialEligibilityKey, false);
    }

    logger.i('用户免费试用期资格: ${isTrialEligible.value}');
  }

  Future<void> _loadPurchaseInfoFromStorage() async {
    try {
      final isActive = _storage.read<bool>(_storageBox, kIsActiveKey, false);
      final productId = _storage.read<String>(_storageBox, kProductIdKey, '');
      final expiryDate = _storage.read<String>(_storageBox, kExpiryDateKey, '');
      final userId = _storage.read<String>(_storageBox, kUserIdKey, '');
      final receiptData =
          _storage.read<String>(_storageBox, kReceiptDataKey, '');

      logger.i(
          '从存储中读取购买信息: isActive=$isActive, productId=$productId, expiryDate=$expiryDate');

      if (userId.isNotEmpty) {
        appleUserId.value = userId;
      }

      // 验证收据数据
      if (receiptData.isNotEmpty && userId.isNotEmpty) {
        _verifyDebouncer.call(() async {
          try {
            final responseData = await _verifyPurchaseWithServer(
              receiptData: receiptData,
              userId: userId,
              timeout: const Duration(seconds: 10),
              retries: 1,
            );

            if (responseData != null) {
              _updateSubscriptionStatusFromResponse(
                  responseData, productId, expiryDate);
            }
          } catch (e) {
            logger.e('验证存储的收据数据失败: $e');
          }
        });
      }

      // 验证订阅状态有效性
      if (productId.isNotEmpty) {
        bool shouldBeActive = isActive;
        String updatedExpiryDate = expiryDate;

        if (expiryDate.isNotEmpty) {
          try {
            final expiry = DateTime.parse(expiryDate);
            final now = DateTime.now();

            // 检查过期日期是否有效
            if (_isExpiryDateValid(productId, expiry, now, updatedExpiryDate)) {
              updatedExpiryDate = _getUpdatedExpiryDate(productId, now);
              _storage.write(_storageBox, kExpiryDateKey, updatedExpiryDate);
            }

            // 检查是否过期（非终身会员）
            if (productId != lifetimeProductId && expiry.isBefore(now)) {
              shouldBeActive = false;
              logger.i('会员已过期，更新状态');

              // 尝试查询自动续订状态
              if (receiptData.isNotEmpty && userId.isNotEmpty) {
                logger.i('检测到会员已过期，向服务器查询最新订阅状态');
                _checkSubscriptionStatusWithServer(receiptData, userId);
              }
            }
          } catch (e) {
            logger.e('解析过期日期出错: $e');
            shouldBeActive = false;
          }
        } else {
          shouldBeActive = false;
        }

        // 更新激活状态
        if (shouldBeActive != isActive) {
          _storage.write(_storageBox, kIsActiveKey, shouldBeActive);
        }

        // 更新许可证状态
        Future.microtask(() {
          licenseController.isActivated.value = shouldBeActive;
          if (shouldBeActive) {
            licenseController.saveLicenseInfo(productId, updatedExpiryDate);
          }
        });
      } else {
        Future.microtask(() {
          licenseController.isActivated.value = false;
        });
      }
    } catch (e) {
      logger.e('加载购买信息出错: $e');
      Future.microtask(() {
        licenseController.isActivated.value = false;
      });
    }
  }

  // 检查过期日期是否有效
  bool _isExpiryDateValid(
      String productId, DateTime expiry, DateTime now, String expiryDate) {
    if (productId == monthlyProductId && expiry.difference(now).inDays > 60) {
      logger.w('月度会员过期时间异常: $expiryDate，重置为30天');
      return true;
    } else if (productId == yearlyProductId &&
        expiry.difference(now).inDays > 400) {
      logger.w('年度会员过期时间异常: $expiryDate，重置为365天');
      return true;
    } else if (productId == lifetimeProductId &&
        expiry.difference(now).inDays > 37000) {
      logger.w('终身会员过期时间异常: $expiryDate，重置为100年');
      return true;
    }
    return false;
  }

  // 获取更新后的过期日期
  String _getUpdatedExpiryDate(String productId, DateTime now) {
    if (productId == monthlyProductId) {
      return now.add(const Duration(days: 30)).toIso8601String();
    } else if (productId == yearlyProductId) {
      return now.add(const Duration(days: 365)).toIso8601String();
    } else if (productId == lifetimeProductId) {
      return now.add(const Duration(days: 36500)).toIso8601String();
    }
    return now.add(const Duration(days: 30)).toIso8601String();
  }

  // 从响应中更新订阅状态
  void _updateSubscriptionStatusFromResponse(Map<String, dynamic> responseData,
      String currentProductId, String currentExpiryDate) {
    logger.i('服务器验证成功，解析激活的订阅');
    final entitlements =
        responseData['user_current_entitlements'] as Map<String, dynamic>? ??
            {};
    final Map<String, dynamic> activeEntitlements = {};

    // 找出所有活跃的订阅
    for (var entry in entitlements.entries) {
      final product = entry.value as Map<String, dynamic>;
      if (product['is_active'] == true) {
        activeEntitlements[product['product_id'] as String] = product;
      }
    }

    // 按优先级选择订阅（终身>年度>月度）
    String? highestPriorityProductId;
    Map<String, dynamic>? highestPriorityProduct;

    if (activeEntitlements.containsKey(lifetimeProductId)) {
      highestPriorityProductId = lifetimeProductId;
      highestPriorityProduct = activeEntitlements[lifetimeProductId];
    } else if (activeEntitlements.containsKey(yearlyProductId)) {
      highestPriorityProductId = yearlyProductId;
      highestPriorityProduct = activeEntitlements[yearlyProductId];
    } else if (activeEntitlements.containsKey(monthlyProductId)) {
      highestPriorityProductId = monthlyProductId;
      highestPriorityProduct = activeEntitlements[monthlyProductId];
    }

    // 更新本地存储
    if (highestPriorityProductId != null && highestPriorityProduct != null) {
      String updatedExpiryDate = _getExpiryDateFromProduct(
          highestPriorityProduct, highestPriorityProductId);
      logger
          .i('发现更高优先级的产品: $highestPriorityProductId, 过期时间: $updatedExpiryDate');

      _storage.write(_storageBox, kIsActiveKey, true);
      _storage.write(_storageBox, kProductIdKey, highestPriorityProductId);
      _storage.write(_storageBox, kExpiryDateKey, updatedExpiryDate);

      licenseController.isActivated.value = true;
      licenseController.saveLicenseInfo(
          highestPriorityProductId, updatedExpiryDate);
    }
  }

  // 从产品信息中获取过期日期
  String _getExpiryDateFromProduct(
      Map<String, dynamic> product, String productId) {
    int? expiryMs = product['expires_date_ms'] as int?;
    if (expiryMs != null) {
      return DateTime.fromMillisecondsSinceEpoch(expiryMs).toIso8601String();
    } else if (productId == lifetimeProductId) {
      return DateTime.now().add(const Duration(days: 36500)).toIso8601String();
    } else {
      return productId == yearlyProductId
          ? DateTime.now().add(const Duration(days: 365)).toIso8601String()
          : DateTime.now().add(const Duration(days: 30)).toIso8601String();
    }
  }

  Future<void> _fetchAppleUserId() async {
    try {
      final storedUserId = _storage.read<String>(_storageBox, kUserIdKey, '');
      if (storedUserId.isNotEmpty) {
        appleUserId.value = storedUserId;
        logger.i('从存储中读取用户ID: ${appleUserId.value}');
        return;
      }

      appleUserId.value = await licenseController.getDeviceSerial();
      _storage.write(_storageBox, kUserIdKey, appleUserId.value);
      logger.i('使用设备ID作为用户标识并保存: ${appleUserId.value}');
    } catch (e) {
      logger.e('获取Apple用户ID失败: $e');
    }
  }

  Future<void> _loadProducts() async {
    final Set<String> ids = {
      monthlyProductId,
      yearlyProductId,
      lifetimeProductId,
    };

    try {
      final ProductDetailsResponse response =
          await _inAppPurchase.queryProductDetails(ids);
      if (response.notFoundIDs.isNotEmpty) {
        logger.e('未找到以下产品: ${response.notFoundIDs.join(", ")}');
      }
      products.value = response.productDetails;

      // 更新各个产品的详细信息并检查试用期
      for (var product in response.productDetails) {
        if (product.id == monthlyProductId) {
          monthlyProduct.value = product;
          _checkTrialForProduct(product, showTrialForMonthly);
        } else if (product.id == yearlyProductId) {
          yearlyProduct.value = product;
          _checkTrialForProduct(product, showTrialForYearly);
        } else if (product.id == lifetimeProductId) {
          lifetimeProduct.value = product;
        }
      }

      logger.i(
          '加载产品信息成功，月度试用期: ${showTrialForMonthly.value}, 年度试用期: ${showTrialForYearly.value}');
    } catch (e) {
      showToastNotification(null, '错误', '加载产品信息失败: $e', type: "error");
    }
  }

  // 检查产品是否有免费试用期
  void _checkTrialForProduct(ProductDetails product, RxBool trialFlag) {
    if (product is AppStoreProductDetails) {
      final storeKitProduct = product.skProduct;
      trialFlag.value =
          storeKitProduct.introductoryPrice != null && isTrialEligible.value;
    }
  }

  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    logger.i('收到购买更新：${purchaseDetailsList.length} 个记录');
    _storage.write(_storageBox, 'received_purchase_updates', true);

    final isRestoring = _storage.read<bool>(_storageBox, 'is_restoring', false);

    if (purchaseDetailsList.isEmpty) {
      _handleEmptyPurchaseList(isRestoring);
      return;
    }

    bool anyRestored = false;
    bool anyPurchased = false;
    bool anyPending = false;
    bool anyError = false;

    for (var purchase in purchaseDetailsList) {
      logger.i('处理购买状态: ${purchase.status}, 产品: ${purchase.productID}');

      switch (purchase.status) {
        case PurchaseStatus.pending:
          isPurchasing.value = true;
          anyPending = true;
          break;

        case PurchaseStatus.error:
          logger.e('购买错误: ${purchase.error?.message ?? "未知错误"}');
          anyError = true;
          if (!isRestoring) _handleError(purchase.error!);
          break;

        case PurchaseStatus.purchased:
        case PurchaseStatus.restored:
          if (purchase.status == PurchaseStatus.restored) {
            anyRestored = true;
            logger.i('成功恢复购买: ${purchase.productID}');
          } else {
            anyPurchased = true;
            logger.i('成功购买: ${purchase.productID}');
          }
          _verifyAndDeliverProduct(purchase);
          break;

        case PurchaseStatus.canceled:
          logger.i('购买已取消');
          if (!isRestoring) {
            isPurchasing.value = false;
            _closeProcessingDialog();
          }
          break;
      }

      // 完成购买处理
      if (purchase.pendingCompletePurchase) {
        logger.i('完成购买处理: ${purchase.productID}');
        _inAppPurchase.completePurchase(purchase);
      }
    }

    // 处理恢复购买流程的状态
    if (isRestoring) {
      _handleRestorationStatus(anyRestored, anyError, anyPurchased, anyPending);
    }
  }

  // 处理空的购买列表
  void _handleEmptyPurchaseList(bool isRestoring) {
    if (isRestoring && isPurchasing.value) {
      logger.i('恢复购买流程收到空购买列表，可能没有可恢复的购买');
      Future.delayed(const Duration(seconds: 2), () {
        if (isPurchasing.value) {
          _resetPurchasingState();
          showToastNotification(null, '无可恢复购买', '未找到可恢复的购买记录', type: "info");
        }
      });
    }
  }

  // 处理恢复购买流程的状态
  void _handleRestorationStatus(
      bool anyRestored, bool anyError, bool anyPurchased, bool anyPending) {
    if (anyRestored) {
      logger.i('成功恢复购买，重置恢复状态');
      _storage.write(_storageBox, 'is_restoring', false);
    } else if (anyError && !anyPurchased && !anyRestored && !anyPending) {
      logger.e('恢复购买过程中出现错误，没有成功恢复任何购买');
      _resetPurchasingState();
      showToastNotification(null, '恢复失败', '恢复购买过程中出现错误，请稍后重试', type: "error");
    } else if (!anyPurchased && !anyRestored && !anyPending) {
      Future.delayed(const Duration(seconds: 2), () {
        if (isPurchasing.value) {
          logger.i('没有恢复任何购买，重置状态');
          _resetPurchasingState();
          showToastNotification(null, '恢复结果', '没有找到可恢复的购买', type: "info");
        }
      });
    }
  }

  // 重置购买状态
  void _resetPurchasingState() {
    isPurchasing.value = false;
    _isVerifying.value = false;
    _storage.write(_storageBox, 'is_restoring', false);
    _closeProcessingDialog();
  }

  Future<void> _verifyAndDeliverProduct(PurchaseDetails purchase) async {
    try {
      // 避免重复验证
      if (_isVerifying.value) {
        logger.i('已有验证正在进行中，跳过此次验证请求');
        return;
      }

      // 检查交易ID是否已处理过
      final transactionId = purchase.purchaseID ?? '';
      if (transactionId.isNotEmpty) {
        final lastVerifiedTransaction =
            _storage.read<String>(_storageBox, 'last_verified_transaction', '');
        if (lastVerifiedTransaction == transactionId &&
            selectedProductId.value != purchase.productID) {
          logger.i('交易 $transactionId 已经被验证过，跳过重复处理');
          return;
        }
        _storage.write(_storageBox, 'last_verified_transaction', transactionId);
      }

      _isVerifying.value = true;

      // 获取必要信息
      final productId = purchase.productID;
      final purchaseDate = DateTime.now().toIso8601String();
      final userId = appleUserId.value.isNotEmpty
          ? appleUserId.value
          : await licenseController.getDeviceSerial();

      // 处理收据数据
      final receiptData = purchase.verificationData.serverVerificationData;
      final cleanReceipt = receiptData.replaceAll(RegExp(r'\s+'), '');
      _saveReceiptData(cleanReceipt, purchase.purchaseID, userId);

      // 设置过期日期并在本地激活
      final expiryDate = _calculateExpiryDate(productId);
      final isRestoring =
          _storage.read<bool>(_storageBox, 'is_restoring', false);
      final isAutoRenewal = _isAutoRenewal(isRestoring, productId);

      // 立即在本地激活购买
      await _savePurchaseInfo(
          productId, expiryDate, purchaseDate, purchase.purchaseID, userId);
      _closeProcessingDialog();

      // 显示成功消息
      _showSuccessMessage(isRestoring, isAutoRenewal);

      // 如果在支付页面，关闭它
      if (Get.currentRoute == '/paywall') {
        Get.back();
      }

      // 后台进行API验证
      _verifyDebouncer.call(
          () => _performBackgroundVerification(cleanReceipt, userId, purchase));
    } catch (e) {
      logger.e('启动验证流程时出错: $e');
      _handleVerificationError(e);
    }
  }

  // 保存收据数据
  void _saveReceiptData(
      String receiptData, String? transactionId, String userId) {
    _storage.write(_storageBox, kReceiptDataKey, receiptData);
    _storage.write(_storageBox, kTransactionIdKey, transactionId ?? '');
    _storage.write(_storageBox, kUserIdKey, userId);
  }

  // 计算过期日期
  String _calculateExpiryDate(String productId) {
    if (productId == lifetimeProductId) {
      return DateTime.now().add(const Duration(days: 36500)).toIso8601String();
    } else if (productId == yearlyProductId) {
      return DateTime.now().add(const Duration(days: 365)).toIso8601String();
    } else {
      return DateTime.now().add(const Duration(days: 30)).toIso8601String();
    }
  }

  // 判断是否是自动续期
  bool _isAutoRenewal(bool isRestoring, String productId) {
    final currentIsActive =
        _storage.read<bool>(_storageBox, kIsActiveKey, false);
    final currentProductId =
        _storage.read<String>(_storageBox, kProductIdKey, '');

    return !isRestoring &&
        currentIsActive &&
        currentProductId == productId &&
        productId != lifetimeProductId;
  }

  // 显示成功消息
  void _showSuccessMessage(bool isRestoring, bool isAutoRenewal) {
    if (!isAutoRenewal) {
      if (isRestoring) {
        showToastNotification(null, '恢复成功', '您的购买已成功恢复并激活。', type: "success");
      } else {
        showToastNotification(null, '购买成功', '感谢您的购买！您已成功激活高级版功能。',
            type: "success");
      }
    } else {
      logger.i('自动续期订阅，不显示提示');
    }
  }

  // 处理验证错误
  void _handleVerificationError(dynamic error) {
    _isVerifying.value = false;
    isPurchasing.value = false;
    _storage.write(_storageBox, 'is_restoring', false);
    _closeProcessingDialog();

    final isRestoring = _storage.read<bool>(_storageBox, 'is_restoring', false);
    if (isRestoring) {
      showToastNotification(null, '恢复失败', '恢复购买过程中出现错误，请稍后重试', type: "error");
    } else {
      showToastNotification(null, '购买失败', '处理购买时出错，请稍后重试或联系客服', type: "error");
    }
  }

  // 执行后台验证
  Future<void> _performBackgroundVerification(
      String receiptData, String userId, PurchaseDetails purchase) async {
    try {
      logger.i('在后台进行API验证...');
      final responseData = await _verifyPurchaseWithServer(
        receiptData: receiptData,
        userId: userId,
        timeout: const Duration(seconds: 20),
        retries: 2,
      );

      if (responseData != null) {
        _processVerificationResponse(responseData, purchase);
      }
    } catch (e) {
      logger.e('后台API验证失败: $e');
    } finally {
      isPurchasing.value = false;
      _isVerifying.value = false;
      _storage.write(_storageBox, 'is_restoring', false);
    }
  }

  // 处理验证响应
  Future<void> _processVerificationResponse(
      Map<String, dynamic> responseData, PurchaseDetails purchase) async {
    // 获取当前购买的时间戳
    final currentPurchaseTimeMs = _getPurchaseTimeMsForProduct(
        purchase.productID, purchase.purchaseID, responseData);
    logger.i('当前购买的时间戳: $currentPurchaseTimeMs, 产品ID: ${purchase.productID}');

    // 解析响应，获取订阅状态
    final entitlements =
        responseData['user_current_entitlements'] as Map<String, dynamic>? ??
            {};
    logger.i('从API响应中获取entitlements，数据: ${jsonEncode(entitlements)}');

    // 查找当前购买的产品状态
    if (await _updateFromCurrentProduct(entitlements, purchase.productID)) {
      return;
    }

    // 查找其他活跃产品
    if (await _updateFromBestActiveProduct(
        entitlements, currentPurchaseTimeMs)) {
      return;
    }

    // 如果没有找到任何活跃产品，使用当前购买的信息
    logger.w('API验证未找到任何活跃产品，使用当前购买的产品ID: ${purchase.productID}');
    final expiryDateFromServer = _getExpiryDateFromResponse(responseData);
    logger.i('从服务器响应获取默认过期日期: $expiryDateFromServer');

    _storage.write(_storageBox, kExpiryDateKey, expiryDateFromServer);
    await licenseController.saveLicenseInfo(
        purchase.productID, expiryDateFromServer);
    logger.i('已使用当前产品ID和默认过期日期更新本地数据');
  }

  // 尝试从当前产品更新订阅信息
  Future<bool> _updateFromCurrentProduct(
      Map<String, dynamic> entitlements, String productId) async {
    for (var entry in entitlements.entries) {
      final product = entry.value as Map<String, dynamic>;
      if (product['product_id'] == productId && product['is_active'] == true) {
        final expiryMs = product['expires_date_ms'] as int?;
        String updatedExpiryDate =
            _getExpiryDateFromExpiryMs(expiryMs, productId);

        // 更新存储
        _storage.write(_storageBox, kIsActiveKey, true);
        _storage.write(_storageBox, kProductIdKey, productId);
        _storage.write(_storageBox, kExpiryDateKey, updatedExpiryDate);

        // 更新许可证控制器
        await licenseController.saveLicenseInfo(productId, updatedExpiryDate);
        logger.i('已使用当前活跃产品信息更新本地数据');
        return true;
      }
    }
    return false;
  }

  // 从最佳活跃产品更新订阅信息
  Future<bool> _updateFromBestActiveProduct(
      Map<String, dynamic> entitlements, int currentPurchaseTimeMs) async {
    Map<String, dynamic>? bestActiveProduct;
    String bestActiveProductId = '';
    int bestPurchaseTime = 0;

    // 查找最新的活跃产品
    for (var entry in entitlements.entries) {
      final product = entry.value as Map<String, dynamic>;
      if (product['is_active'] == true) {
        final productId = product['product_id'] as String;
        final purchaseTimeMs = product['purchase_date_ms'] as int? ?? 0;

        if (purchaseTimeMs > bestPurchaseTime) {
          bestPurchaseTime = purchaseTimeMs;
          bestActiveProduct = product;
          bestActiveProductId = productId;
        }
      }
    }

    // 如果最佳活跃产品的购买时间比当前购买时间更新，则使用它
    if (bestActiveProductId.isNotEmpty &&
        bestActiveProduct != null &&
        bestPurchaseTime > currentPurchaseTimeMs) {
      final expiryMs = bestActiveProduct['expires_date_ms'] as int?;
      String updatedExpiryDate =
          _getExpiryDateFromExpiryMs(expiryMs, bestActiveProductId);

      // 更新存储
      _storage.write(_storageBox, kIsActiveKey, true);
      _storage.write(_storageBox, kProductIdKey, bestActiveProductId);
      _storage.write(_storageBox, kExpiryDateKey, updatedExpiryDate);

      // 更新许可证控制器
      await licenseController.saveLicenseInfo(
          bestActiveProductId, updatedExpiryDate);
      logger.i('已使用最佳活跃产品信息更新本地数据');
      return true;
    }

    return false;
  }

  // 从过期毫秒数获取过期日期
  String _getExpiryDateFromExpiryMs(int? expiryMs, String productId) {
    if (expiryMs != null && expiryMs > 0) {
      return DateTime.fromMillisecondsSinceEpoch(expiryMs).toIso8601String();
    } else if (productId == lifetimeProductId) {
      return DateTime.now().add(const Duration(days: 36500)).toIso8601String();
    } else {
      return productId == yearlyProductId
          ? DateTime.now().add(const Duration(days: 365)).toIso8601String()
          : DateTime.now().add(const Duration(days: 30)).toIso8601String();
    }
  }

  // 从API响应中获取指定产品的购买时间戳
  int _getPurchaseTimeMsForProduct(String productId, String? transactionId,
      Map<String, dynamic> responseData) {
    try {
      logger.i('获取产品购买时间戳，产品ID: $productId, 交易ID: $transactionId');
      final currentTimeMs = DateTime.now().millisecondsSinceEpoch;
      final entitlements =
          responseData['user_current_entitlements'] as Map<String, dynamic>? ??
              {};

      // 1. 直接根据产品ID匹配
      if (entitlements.containsKey(productId)) {
        final product = entitlements[productId] as Map<String, dynamic>;
        final purchaseTimeMs = product['purchase_date_ms'] as int? ?? 0;
        if (purchaseTimeMs > 0) {
          logger.i('直接找到产品ID匹配，返回购买时间: $purchaseTimeMs');
          return purchaseTimeMs;
        }

        // 如果没有购买时间但有过期时间，尝试估算
        final expiryTimeMs = product['expires_date_ms'] as int? ?? 0;
        if (expiryTimeMs > 0) {
          int estimatedPurchaseTime =
              _estimatePurchaseTimeFromExpiry(productId, expiryTimeMs);
          if (estimatedPurchaseTime > 0) {
            logger.i('基于过期时间估算购买时间: $estimatedPurchaseTime');
            return estimatedPurchaseTime;
          }
        }
      }

      // 2. 根据交易ID匹配
      if (transactionId != null && transactionId.isNotEmpty) {
        for (var entry in entitlements.entries) {
          final product = entry.value as Map<String, dynamic>;
          final productTransactionId =
              product['transaction_id'] as String? ?? '';
          if (productTransactionId == transactionId) {
            final purchaseTimeMs = product['purchase_date_ms'] as int? ?? 0;
            if (purchaseTimeMs > 0) {
              logger.i('匹配到交易ID，返回购买时间: $purchaseTimeMs');
              return purchaseTimeMs;
            }
          }
        }
      }

      // 3. 再次尝试按产品ID匹配（遍历所有entitlements）
      for (var entry in entitlements.entries) {
        final product = entry.value as Map<String, dynamic>;
        final entryProductId = product['product_id'] as String? ?? '';
        if (entryProductId == productId) {
          final purchaseTimeMs = product['purchase_date_ms'] as int? ?? 0;
          if (purchaseTimeMs > 0) {
            logger.i('匹配到产品ID，返回购买时间: $purchaseTimeMs');
            return purchaseTimeMs;
          }
        }
      }

      // 未找到匹配信息，返回当前时间
      logger.i('未找到匹配的产品，返回当前时间戳: $currentTimeMs');
      return currentTimeMs;
    } catch (e) {
      logger.e('获取产品购买时间戳失败: $e');
      return DateTime.now().millisecondsSinceEpoch;
    }
  }

  // 从过期时间估算购买时间
  int _estimatePurchaseTimeFromExpiry(String productId, int expiryTimeMs) {
    if (productId == monthlyProductId) {
      return expiryTimeMs - (30 * 24 * 60 * 60 * 1000);
    } else if (productId == yearlyProductId) {
      return expiryTimeMs - (365 * 24 * 60 * 60 * 1000);
    } else if (productId == lifetimeProductId) {
      return DateTime.now().millisecondsSinceEpoch;
    }
    return 0;
  }

  // 保存购买信息到本地存储和许可证控制器
  Future<void> _savePurchaseInfo(String productId, String expiryDate,
      String purchaseDate, String? transactionId, String userId) async {
    // 保存到持久存储
    _storage.write(_storageBox, kProductIdKey, productId);
    _storage.write(_storageBox, kExpiryDateKey, expiryDate);
    _storage.write(_storageBox, kPurchaseDateKey, purchaseDate);
    _storage.write(_storageBox, kIsActiveKey, true);

    // 保存完整的购买信息JSON
    final purchaseInfo = {
      'productId': productId,
      'expiryDate': expiryDate,
      'purchaseDate': purchaseDate,
      'transactionId': transactionId ?? '',
      'userId': userId,
      'isActive': true,
    };
    _storage.write(_storageBox, kPurchaseInfoKey, jsonEncode(purchaseInfo));

    // 更新许可证状态
    licenseController.isActivated.value = true;
    await licenseController.saveLicenseInfo(productId, expiryDate);
  }

  // 从响应中获取过期日期
  String _getExpiryDateFromResponse(Map<String, dynamic> response) {
    try {
      logger.i('开始解析过期日期，完整响应数据: ${jsonEncode(response)}');
      final entitlements =
          response['user_current_entitlements'] as Map<String, dynamic>? ?? {};
      final now = DateTime.now();

      // 按优先级查找活跃产品：monthly, annually, lifetime
      String bestMatchId = '';
      Map<String, dynamic>? bestMatch;

      for (String productType in [
        monthlyProductId,
        yearlyProductId,
        lifetimeProductId
      ]) {
        if (entitlements.containsKey(productType)) {
          final product = entitlements[productType] as Map<String, dynamic>;
          if (product['is_active'] == true) {
            bestMatch = product;
            bestMatchId = productType;
            logger.i('找到活跃的产品: $bestMatchId');
            break;
          }
        }
      }

      // 如果找到了活跃产品
      if (bestMatch != null) {
        // 获取过期时间
        final expiryMs = bestMatch['expires_date_ms'] as int?;

        if (expiryMs != null) {
          final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryMs);

          // 检查过期日期是否合理
          if (bestMatchId == monthlyProductId &&
              expiryDate.difference(now).inDays > 60) {
            logger.w('月度会员过期时间异常，重置为30天');
            return now.add(const Duration(days: 30)).toIso8601String();
          } else if (bestMatchId == yearlyProductId &&
              expiryDate.difference(now).inDays > 400) {
            logger.w('年度会员过期时间异常，重置为365天');
            return now.add(const Duration(days: 365)).toIso8601String();
          } else if (bestMatchId == lifetimeProductId &&
              expiryDate.difference(now).inDays > 36600) {
            logger.w('终身会员过期时间异常，重置为100年');
            return now.add(const Duration(days: 36500)).toIso8601String();
          }

          // 过期日期合理，返回
          return expiryDate.toIso8601String();
        }
      }

      // 未找到活跃产品或产品无过期时间，使用当前购买的产品类型设置默认过期时间
      final currentProductId =
          _storage.read<String>(_storageBox, kProductIdKey, '');

      if (currentProductId == monthlyProductId) {
        return now.add(const Duration(days: 30)).toIso8601String();
      } else if (currentProductId == yearlyProductId) {
        return now.add(const Duration(days: 365)).toIso8601String();
      } else if (currentProductId == lifetimeProductId) {
        return now.add(const Duration(days: 36500)).toIso8601String();
      } else {
        logger.w('无法确定产品类型，使用保守的默认过期时间(30天)');
        return now.add(const Duration(days: 30)).toIso8601String();
      }
    } catch (e) {
      logger.e('解析过期日期出错: $e');
      // 默认返回30天的过期时间
      return DateTime.now().add(const Duration(days: 30)).toIso8601String();
    }
  }

  void _handleError(IAPError error) {
    isPurchasing.value = false;
    _closeProcessingDialog();

    // 使用 debounce 来避免重复显示弹窗
    Future.delayed(Duration.zero, () {
      if (isPurchasing.value == false) {
        showToastNotification(null, '错误', error.message, type: "error");
      }
    });
  }

  // 显示处理状态弹窗
  void _showProcessingDialog(
      BuildContext context, String title, String message) {
    if (Get.isDialogOpen != true) {
      Get.dialog(
        AlertDialog(
          title: Text(title),
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Expanded(child: Text(message)),
            ],
          ),
          actions: const [],
        ),
        barrierDismissible: false,
      );
    }
  }

  // 关闭处理状态弹窗
  void _closeProcessingDialog() {
    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }

  // 购买产品
  Future<void> purchaseProduct(String productId, BuildContext context) async {
    if (isPurchasing.value) return;

    // 查找选择的产品
    final ProductDetails? productDetails = products.firstWhereOrNull(
      (product) => product.id == productId,
    );

    if (productDetails == null) {
      showToastNotification(null, '错误', '找不到选择的产品', type: "error");
      return;
    }

    try {
      isPurchasing.value = true;
      _showProcessingDialog(context, '处理中', '正在处理您的购买请求，请稍候...');

      // 创建购买参数
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
        applicationUserName: appleUserId.value.isNotEmpty
            ? appleUserId.value
            : await licenseController.getDeviceSerial(),
      );

      // 根据产品类型执行购买
      if (productId == lifetimeProductId) {
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      } else {
        await _inAppPurchase.buyConsumable(
            purchaseParam: purchaseParam, autoConsume: false);
      }

      // 购买后，用户不再有资格获得免费试用期
      isTrialEligible.value = false;
      _storage.write(_storageBox, kTrialEligibilityKey, false);
    } catch (e) {
      logger.e(e);
      isPurchasing.value = false;
      _closeProcessingDialog();
      showToastNotification(null, '购买失败', '启动购买流程时出错: $e', type: "error");
    }
  }

  // 恢复购买
  Future<void> restorePurchases() async {
    if (isPurchasing.value) {
      logger.i('已有购买流程正在进行中，忽略此次恢复请求');
      return;
    }

    final startTime = DateTime.now();
    _storage.write(_storageBox, 'is_restoring', true);
    _storage.write(_storageBox, 'received_purchase_updates', false);
    isPurchasing.value = true;
    _showProcessingDialog(Get.context!, '恢复中', '正在恢复您的购买，请稍候...');

    Timer? timeoutTimer;

    try {
      logger.i('开始恢复购买流程');
      final receiptData =
          _storage.read<String>(_storageBox, kReceiptDataKey, '');
      final userId = appleUserId.value.isNotEmpty
          ? appleUserId.value
          : await licenseController.getDeviceSerial();

      // 如果有收据数据，先尝试通过API验证
      if (receiptData.isNotEmpty && userId.isNotEmpty) {
        await _tryVerifyWithExistingReceipt(receiptData, userId);
      }

      // 设置超时
      timeoutTimer = Timer(const Duration(seconds: 30), () {
        if (isPurchasing.value) {
          logger.w('恢复购买超时（30秒），强制重置状态');
          _resetPurchasingState();
          showToastNotification(null, '恢复超时', '恢复购买未在预期时间内完成，请稍后再试',
              type: "warning");
        }
      });

      // 调用恢复购买API
      await _inAppPurchase.restorePurchases().timeout(
        const Duration(seconds: 20),
        onTimeout: () {
          logger.w('恢复购买API调用超时（20秒）');
          throw TimeoutException('恢复购买API调用超时');
        },
      );

      // 设置安全检查
      Future.delayed(const Duration(seconds: 5), () {
        final elapsedTime = DateTime.now().difference(startTime);
        if (isPurchasing.value && elapsedTime.inSeconds > 7) {
          // 检查是否收到了任何购买更新
          if (_storage.read<bool>(
                  _storageBox, 'received_purchase_updates', false) !=
              true) {
            logger.i('未收到任何购买更新，认为没有可恢复的购买');
            _resetPurchasingState();
            showToastNotification(null, '无可恢复购买', '未找到可恢复的购买记录', type: "info");
          }
        }
      });
    } catch (e) {
      logger.e('恢复购买失败: $e');

      // 提供更详细的错误消息
      String errorMessage = '恢复购买时发生错误，请稍后再试';
      if (e is TimeoutException) {
        errorMessage = '恢复购买超时，请检查网络连接后重试';
      } else if (e.toString().contains('network')) {
        errorMessage = '网络连接错误，请检查您的网络设置后重试';
      }

      _closeProcessingDialog();
      showToastNotification(null, '恢复购买失败', errorMessage, type: "error");
      _resetPurchasingState();
    } finally {
      timeoutTimer?.cancel();
    }
  }

  // 尝试使用现有收据验证
  Future<bool> _tryVerifyWithExistingReceipt(
      String receiptData, String userId) async {
    try {
      logger.i('发现已存储的收据数据，尝试通过API验证');
      final responseData = await _verifyPurchaseWithServer(
        receiptData: receiptData,
        userId: userId,
      );

      if (responseData != null) {
        logger.i('API验证成功');
        final entitlements = responseData['user_current_entitlements']
                as Map<String, dynamic>? ??
            {};
        bool foundActive = false;
        String productId = '';
        String expiryDate = '';

        // 检查是否有活跃的订阅
        for (var entry in entitlements.entries) {
          final product = entry.value as Map<String, dynamic>;
          if (product['is_active'] == true) {
            foundActive = true;
            productId = product['product_id'] as String;
            expiryDate = _getExpiryDateFromExpiryMs(
                product['expires_date_ms'] as int?, productId);
            break;
          }
        }

        // 更新存储和状态
        _storage.write(_storageBox, kIsActiveKey, foundActive);
        if (productId.isNotEmpty) {
          _storage.write(_storageBox, kProductIdKey, productId);
          _storage.write(_storageBox, kExpiryDateKey, expiryDate);
          licenseController.isActivated.value = foundActive;
          licenseController.saveLicenseInfo(productId, expiryDate);
        }

        // 如果验证成功并且用户是活跃状态，显示成功消息并退出
        if (foundActive) {
          _resetPurchasingState();
          showToastNotification(null, '恢复成功', '您的购买已成功恢复并激活。', type: "success");
          return true;
        }
      }
    } catch (e) {
      logger.e('通过API验证收据失败: $e');
    }
    return false;
  }

  // 显示苹果的兑换码弹窗
  Future<bool> presentCodeRedemptionSheet() async {
    try {
      if (!Platform.isIOS) {
        logger.w('当前平台不支持兑换码功能');
        showToastNotification(null, '不支持', '当前平台不支持兑换码功能', type: "warning");
        return false;
      }

      logger.i('开始调用兑换码弹窗...');
      final iosPlatformAddition = _inAppPurchase
          .getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      await iosPlatformAddition.presentCodeRedemptionSheet();
      logger.i('兑换码弹窗显示成功');
      return true;
    } catch (e) {
      logger.e('显示兑换码弹窗失败: $e');
      showToastNotification(
          null, '操作失败', '无法打开兑换码弹窗，请确保您使用的是iOS设备并已安装App Store',
          type: "error");
      return false;
    }
  }

  // 向服务器验证购买收据
  Future<Map<String, dynamic>?> _verifyPurchaseWithServer({
    required String receiptData,
    required String userId,
    Duration timeout = const Duration(seconds: 15),
    int retries = 1,
  }) async {
    try {
      logger.i('向服务器验证购买收据...');
      final cleanReceipt = receiptData.replaceAll(RegExp(r'\s+'), '');

      final Map<String, dynamic> requestData = {
        'user_id': userId,
        'receipt_data': cleanReceipt,
      };

      final response = await _apiClient.post(
        '/verify_purchase',
        data: requestData,
        options: Options(
          receiveTimeout: timeout,
          sendTimeout: timeout,
          extra: {'retries': retries},
        ),
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        logger.i('服务器验证成功，完整响应：${jsonEncode(responseData)}');

        final entitlements = responseData['user_current_entitlements']
                as Map<String, dynamic>? ??
            {};

        // 检查是否有活跃产品
        bool hasActive = false;
        entitlements.forEach((key, value) {
          if (value['is_active'] == true) {
            hasActive = true;
            logger.i('找到活跃产品：$key，详情：${jsonEncode(value)}');
          }
        });

        if (!hasActive) {
          logger.w('未找到任何活跃产品！');
        }

        if (responseData['status'] == 'success') {
          logger.i('服务器验证成功');
          return responseData;
        } else {
          logger.w('服务器验证未返回成功状态: ${responseData['status']}');
          return null;
        }
      } else {
        logger.w('服务器返回非200状态码: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      logger.e('向服务器验证购买收据失败: $e');
      return null;
    }
  }

  // 向服务器查询最新的订阅状态
  Future<bool> _checkSubscriptionStatusWithServer(
      String receiptData, String userId) async {
    try {
      logger.i('开始向服务器查询订阅状态...');
      final responseData = await _verifyPurchaseWithServer(
        receiptData: receiptData,
        userId: userId,
        retries: 1,
      );

      if (responseData != null) {
        final entitlements = responseData['user_current_entitlements']
                as Map<String, dynamic>? ??
            {};

        // 查找所有激活的产品，选择最新的
        Map<String, dynamic>? latestProduct;
        int latestPurchaseTime = 0;
        String latestProductId = '';

        for (var entry in entitlements.entries) {
          final product = entry.value as Map<String, dynamic>;
          if (product['is_active'] == true) {
            final productId = product['product_id'] as String;
            final purchaseTimeMs = product['purchase_date_ms'] as int? ?? 0;

            if (purchaseTimeMs > latestPurchaseTime) {
              latestPurchaseTime = purchaseTimeMs;
              latestProduct = product;
              latestProductId = productId;
            }
          }
        }

        // 如果找到了有效的最新产品
        if (latestProductId.isNotEmpty && latestProduct != null) {
          String expiryDate = _getExpiryDateFromExpiryMs(
              latestProduct['expires_date_ms'] as int?, latestProductId);

          logger.i(
              '发现最新购买的产品: $latestProductId, 购买时间: ${DateTime.fromMillisecondsSinceEpoch(latestPurchaseTime)}, 过期时间: $expiryDate');

          _storage.write(_storageBox, kIsActiveKey, true);
          _storage.write(_storageBox, kProductIdKey, latestProductId);
          _storage.write(_storageBox, kExpiryDateKey, expiryDate);

          Future.microtask(() {
            licenseController.isActivated.value = true;
            licenseController.saveLicenseInfo(latestProductId, expiryDate);
          });

          return true;
        }
      }
      return false;
    } catch (e) {
      logger.e('查询服务器订阅状态失败: $e');
      return false;
    }
  }

  // 为恢复购买功能添加一个重置状态的方法
  void resetPurchasingState() {
    logger.w('手动重置购买状态');
    isPurchasing.value = false;
    _isVerifying.value = false;
    _storage.write(_storageBox, 'is_restoring', false);
    _storage.write(_storageBox, 'received_purchase_updates', false);
  }

  @override
  void onClose() {
    _purchaseSubscription?.cancel();
    _dio.close();
    _verifyDebouncer.dispose();
    super.onClose();
  }
}

// 防抖类
class Debouncer {
  final Duration delay;
  Timer? _timer;

  Debouncer({required this.delay});

  void call(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(delay, action);
  }

  void dispose() {
    _timer?.cancel();
    _timer = null;
  }
}

// 重试拦截器
class RetryInterceptor extends Interceptor {
  final Dio dio;
  final Function(String message) logPrint;
  final int retries;
  final List<Duration> retryDelays;
  final Map<String, int> _retryCount = {};

  RetryInterceptor({
    required this.dio,
    required this.logPrint,
    this.retries = 3,
    this.retryDelays = const [
      Duration(seconds: 1),
      Duration(seconds: 2),
      Duration(seconds: 3),
    ],
  });

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final String requestKey =
        '${err.requestOptions.uri}${err.requestOptions.method}';
    final maxRetries = err.requestOptions.extra['retries'] as int? ?? retries;
    final retryCount = _retryCount[requestKey] ?? 0;

    if (retryCount < maxRetries &&
        _shouldRetry(err) &&
        err.requestOptions.extra['no_retry'] != true) {
      _retryCount[requestKey] = retryCount + 1;
      final delay = retryCount < retryDelays.length
          ? retryDelays[retryCount]
          : retryDelays.last;

      logPrint('⚠️ 重试请求 $requestKey (尝试 ${retryCount + 1}/$maxRetries)');
      await Future.delayed(delay);

      try {
        final options = Options(
          method: err.requestOptions.method,
          headers: err.requestOptions.headers,
          extra: {
            ...err.requestOptions.extra,
            'retries': maxRetries,
          },
          validateStatus: err.requestOptions.validateStatus,
          receiveTimeout: err.requestOptions.receiveTimeout,
          sendTimeout: err.requestOptions.sendTimeout,
        );

        final response = await dio.request(
          err.requestOptions.path,
          data: err.requestOptions.data,
          queryParameters: err.requestOptions.queryParameters,
          options: options,
        );

        _retryCount.remove(requestKey);
        return handler.resolve(response);
      } catch (e) {
        return handler.next(err);
      }
    }

    _retryCount.remove(requestKey);
    return handler.next(err);
  }

  bool _shouldRetry(DioException err) {
    return err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.sendTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.connectionError ||
        (err.response != null && err.response!.statusCode! >= 500);
  }
}
