// import 'dart:io';

// import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:flutter_speed_dial/flutter_speed_dial.dart';
// import '../../controllers/pdf_viewer/pdf_viewer.dart';

// class PDFViewer extends StatelessWidget {
//   const PDFViewer({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.put(GetxPDFViewerController());
//     return Scaffold(
//         backgroundColor: Theme.of(context).scaffoldBackgroundColor,
//         appBar: AppBar(
//           title: const Text("PDF Viewer"),
//           backgroundColor: Theme.of(context).colorScheme.surface,
//           leading: IconButton(
//             icon: const Icon(Icons.arrow_back),
//             onPressed: () => Get.back(),
//           ),
//           actions: <Widget>[
//             IconButton(
//               icon: const Icon(
//                 Icons.folder_open_outlined,
//                 color: Colors.white,
//               ),
//               onPressed: () {
//                 // controller.pdfViewerController.jumpToPage(3);
//                 // print(controller.pdfViewerController.scrollOffset.dy);
//                 // print(controller.pdfViewerController.scrollOffset.dx);
//                 controller.openFile();
//               },
//             ),
//           ],
//         ),
//         floatingActionButton: SpeedDial(
//           icon: Icons.menu,
//           activeIcon: Icons.close,
//           backgroundColor: Colors.blue,
//           foregroundColor: Colors.white,
//           activeBackgroundColor: Colors.cyan,
//           activeForegroundColor: Colors.white,
//           children: [
//             SpeedDialChild(
//               child: const Icon(Icons.rotate_right),
//               label: '合并页面',
//               onTap: () {
//                 // controller.pdfViewerController.rotatePages(90);
//               },
//             ),
//             SpeedDialChild(
//               child: const Icon(Icons.rotate_right),
//               label: '旋转页面',
//               onTap: () {
//                 // controller.pdfViewerController.rotatePages(90);
//               },
//             ),
//             SpeedDialChild(
//               child: const Icon(Icons.delete),
//               // backgroundColor: Colors.blue,
//               label: '删除页面',
//               onTap: () {
//                 // 实现删除页面的逻辑
//               },
//             ),
//             SpeedDialChild(
//               child: const Icon(Icons.delete),
//               // backgroundColor: Colors.blue,
//               label: '拆分页面',
//               onTap: () {
//                 // 实现删除页面的逻辑
//               },
//             ),
//             SpeedDialChild(
//               child: const Icon(Icons.delete),
//               // backgroundColor: Colors.blue,
//               label: '提取页面',
//               onTap: () {
//                 // 实现删除页面的逻辑
//               },
//             ),
//             SpeedDialChild(
//               child: const Icon(Icons.delete),
//               // backgroundColor: Colors.blue,
//               label: 'PDF转换',
//               onTap: () {
//                 // 实现删除页面的逻辑
//               },
//             ),
//             SpeedDialChild(
//               child: const Icon(Icons.delete),
//               // backgroundColor: Colors.blue,
//               label: 'PDF书签',
//               onTap: () {
//                 // 实现删除页面的逻辑
//               },
//             ),
//             SpeedDialChild(
//               child: const Icon(Icons.delete),
//               // backgroundColor: Colors.blue,
//               label: 'PDF OCR',
//               onTap: () {
//                 // 实现删除页面的逻辑
//               },
//             ),
//             // 可以添加更多菜单项
//           ],
//         ),
//         body: Obx(() => controller.path.value.isNotEmpty
//             ? SfPdfViewer.file(
//                 File(controller.path.value),
//                 controller: controller.pdfViewerController,
//               )
//             : const SizedBox()));
//   }
// }
