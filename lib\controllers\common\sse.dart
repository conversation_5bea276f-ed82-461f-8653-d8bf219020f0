import 'dart:async';
import 'dart:convert';
import 'package:flutter_client_sse/flutter_client_sse.dart';
import 'package:flutter_client_sse/constants/sse_request_type_enum.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';

/// 任务状态枚举
enum TaskStatus {
  pending('pending', '等待中'),
  inProgress('in_progress', '进行中'),
  completed('completed', '已完成'),
  error('error', '出错');

  final String value;
  final String label;
  const TaskStatus(this.value, this.label);

  factory TaskStatus.fromString(String value) {
    return TaskStatus.values.firstWhere(
      (e) => e.value == value,
      orElse: () => TaskStatus.pending,
    );
  }
}

/// 任务信息模型
class TaskInfo {
  final String id;
  final String name;
  final int progress;
  final TaskStatus status;
  final String message;
  final double createdAt;
  final String? funcId;
  final Map<String, dynamic>? funcParams;
  final dynamic result;
  final String? error;

  TaskInfo({
    required this.id,
    required this.name,
    required this.progress,
    required this.status,
    required this.message,
    required this.createdAt,
    this.funcId,
    this.funcParams,
    this.result,
    this.error,
  });

  factory TaskInfo.fromJson(Map<String, dynamic> json) {
    return TaskInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      progress: json['progress'] ?? 0,
      status: TaskStatus.fromString(json['status'] ?? 'pending'),
      message: json['message'] ?? '',
      createdAt: json['created_at']?.toDouble() ?? 0,
      funcId: json['func_id'],
      funcParams: json['func_params'],
      result: json['result'],
      error: json['error'],
    );
  }

  bool get isCompleted => status == TaskStatus.completed;
  bool get isError => status == TaskStatus.error;
  bool get isInProgress => status == TaskStatus.inProgress;
  bool get isPending => status == TaskStatus.pending;

  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch((createdAt * 1000).toInt());
}

/// 函数参数信息
class FunctionParamInfo {
  final String name;
  final String? defaultValue;
  final String? annotation;
  final String kind;

  FunctionParamInfo({
    required this.name,
    this.defaultValue,
    this.annotation,
    required this.kind,
  });

  factory FunctionParamInfo.fromJson(Map<String, dynamic> json) {
    return FunctionParamInfo(
      name: json['name'] ?? '',
      defaultValue: json['default'],
      annotation: json['annotation'],
      kind: json['kind'] ?? '',
    );
  }
}

/// 函数信息模型
class FunctionInfo {
  final String id;
  final String name;
  final String? doc;
  final Map<String, FunctionParamInfo> params;

  FunctionInfo({
    required this.id,
    required this.name,
    this.doc,
    required this.params,
  });

  factory FunctionInfo.fromJson(Map<String, dynamic> json) {
    final paramsMap = json['params'] as Map<String, dynamic>? ?? {};

    return FunctionInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      doc: json['doc'],
      params: paramsMap.map((key, value) => MapEntry(
            key,
            FunctionParamInfo.fromJson(value as Map<String, dynamic>),
          )),
    );
  }
}

/// SSE事件类型
enum SseEventType {
  progress('progress'),
  completed('completed'),
  error('error'),
  progressAll('progress_all');

  final String value;
  const SseEventType(this.value);

  factory SseEventType.fromString(String value) {
    return SseEventType.values.firstWhere(
      (e) => e.value == value,
      orElse: () => SseEventType.progress,
    );
  }
}

/// SSE服务管理器
class SseManager {
  static SseManager get to => Get.find<SseManager>();

  // 远程服务器基础URL
  final String baseUrl;

  // 存储所有活跃的任务信息
  final _tasks = <String, Rx<TaskInfo>>{}.obs;

  // 存储所有可用函数信息
  final _functions = <String, FunctionInfo>{}.obs;

  // SSE事件源订阅
  final _subscriptions = <String, StreamSubscription<SSEModel>>{};

  SseManager({required this.baseUrl}) {
    _init();
  }

  /// 初始化服务
  Future<void> _init() async {
    // 获取可用函数列表
    await refreshFunctions();
  }

  /// 获取所有任务信息
  Map<String, Rx<TaskInfo>> get tasks => _tasks;

  /// 获取所有函数信息
  Map<String, FunctionInfo> get functions => _functions;

  /// 刷新可用函数列表
  Future<void> refreshFunctions() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/functions'));

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body) as Map<String, dynamic>;

        _functions.clear();
        jsonData.forEach((key, value) {
          _functions[key] =
              FunctionInfo.fromJson(value as Map<String, dynamic>);
        });
      } else {
        throw Exception('获取函数列表失败: ${response.statusCode}');
      }
    } catch (e) {
      print('刷新函数列表出错: $e');
      rethrow;
    }
  }

  /// 创建任务但不自动订阅进度
  Future<String> createTask({
    required String funcId,
    Map<String, dynamic>? funcParams,
    bool autoStart = false,
  }) async {
    // 构建URL
    final url = "$baseUrl/tasks";

    // 构建JSON请求体
    final jsonBody = jsonEncode({
      "func_id": funcId,
      "func_params": funcParams,
      "background_tasks": autoStart,
    });

    // 发送POST请求，使用JSON请求体
    final response = await http.post(
      Uri.parse(url),
      headers: {"Content-Type": "application/json"},
      body: jsonBody,
    );

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      final taskId = responseData["task_id"];
      return taskId;
    } else {
      print('创建任务失败: ${response.statusCode} ${response.body}');
      throw Exception('创建任务失败: ${response.statusCode} ${response.body}');
    }
  }

  /// 获取单个任务信息
  Future<TaskInfo> getTask(String taskId) async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/tasks/$taskId'));

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body) as Map<String, dynamic>;
        return TaskInfo.fromJson(json);
      } else {
        throw Exception('获取任务信息失败: ${response.statusCode}');
      }
    } catch (e) {
      print('获取任务信息出错: $e');
      rethrow;
    }
  }

  /// 获取所有任务信息
  Future<List<TaskInfo>> getAllTasks() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/tasks'));

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body) as List;
        return json
            .map((item) => TaskInfo.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('获取所有任务信息失败: ${response.statusCode}');
      }
    } catch (e) {
      print('获取所有任务信息出错: $e');
      rethrow;
    }
  }

  /// 订阅任务进度，但不更新progressController
  /// 改为使用回调函数来处理不同的事件
  void subscribeToTaskProgress(
    String taskId, {
    Function(TaskInfo)? onProgress,
    Function(TaskInfo)? onCompleted,
    Function(TaskInfo)? onError,
    Function(String)? onConnectionError,
  }) {
    // 如果已经在监听此任务，则先取消
    cancelTaskSubscription(taskId);

    // 使用Python服务器中定义的SSE路由
    final url = "$baseUrl/tasks/$taskId/progress";

    // 使用SSEClient进行SSE连接
    final subscription = SSEClient.subscribeToSSE(
      method: SSERequestType.GET,
      url: url,
      header: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      },
    ).listen(
      (sseModel) {
        print('收到SSE消息类型: ${sseModel.event}, 数据: ${sseModel.data}');

        if (sseModel.data == null) return;

        try {
          // 处理不同的事件类型
          final eventType = sseModel.event ?? 'progress';
          final taskInfo = _parseTaskInfo(sseModel.data!);

          if (eventType == 'progress') {
            // 处理进度更新事件
            if (onProgress != null) {
              onProgress(taskInfo);
            }

            // 更新本地任务信息
            if (_tasks.containsKey(taskId)) {
              _tasks[taskId]?.value = taskInfo;
            } else {
              _tasks[taskId] = Rx<TaskInfo>(taskInfo);
            }
          } else if (eventType == 'completed') {
            // 处理完成事件
            if (onCompleted != null) {
              onCompleted(taskInfo);
            }

            // 更新本地任务信息
            if (_tasks.containsKey(taskId)) {
              _tasks[taskId]?.value = taskInfo;
            } else {
              _tasks[taskId] = Rx<TaskInfo>(taskInfo);
            }

            // 完成后关闭订阅
            cancelTaskSubscription(taskId);
          } else if (eventType == 'error') {
            // 处理错误事件
            if (onError != null) {
              onError(taskInfo);
            }

            // 更新本地任务信息
            if (_tasks.containsKey(taskId)) {
              _tasks[taskId]?.value = taskInfo;
            } else {
              _tasks[taskId] = Rx<TaskInfo>(taskInfo);
            }

            // 错误后关闭订阅
            cancelTaskSubscription(taskId);
          } else {
            print('未知的事件类型: $eventType');
          }
        } catch (e) {
          print('解析SSE消息失败: $e');

          // 调用连接错误回调函数
          if (onConnectionError != null) {
            onConnectionError("解析SSE消息失败: $e");
          }

          // 解析错误后关闭订阅
          cancelTaskSubscription(taskId);
        }
      },
      onError: (error) {
        print('SSE错误: $error');

        // 调用连接错误回调函数
        if (onConnectionError != null) {
          onConnectionError("SSE连接错误: $error");
        }

        // 连接错误后关闭订阅
        cancelTaskSubscription(taskId);
      },
      onDone: () {
        print('SSE连接关闭: $taskId');
        _subscriptions.remove(taskId);
      },
    );

    // 保存订阅对象
    _subscriptions[taskId] = subscription;
  }

  /// 订阅所有任务进度
  void subscribeToAllTasksProgress({
    Function(List<TaskInfo> taskInfoList)? onProgressAll,
    Function(dynamic error)? onConnectionError,
  }) {
    final subscriptionId = 'all_tasks';

    // 先取消之前的订阅
    if (_subscriptions.containsKey(subscriptionId)) {
      _subscriptions[subscriptionId]?.cancel();
      _subscriptions.remove(subscriptionId);
    }

    // 创建SSE连接并订阅
    final subscription = SSEClient.subscribeToSSE(
      method: SSERequestType.GET,
      url: '$baseUrl/tasks/progress/all',
      header: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      },
    ).listen(
      (sseModel) {
        final eventType = sseModel.event;
        if (eventType == null || sseModel.data == null) return;

        try {
          // 处理所有任务进度更新
          if (eventType == 'progress_all') {
            final dataList = _safeJsonDecode(sseModel.data!) as List? ?? [];
            final taskInfoList = <TaskInfo>[];

            for (var item in dataList) {
              if (item is Map<String, dynamic>) {
                final taskInfo = TaskInfo.fromJson(item);
                final taskId = taskInfo.id;

                // 更新本地任务信息
                if (_tasks.containsKey(taskId)) {
                  _tasks[taskId]?.value = taskInfo;
                } else {
                  _tasks[taskId] = Rx<TaskInfo>(taskInfo);
                }

                taskInfoList.add(taskInfo);
              }
            }

            // 执行所有任务进度回调
            onProgressAll?.call(taskInfoList);
          }
        } catch (e, stackTrace) {
          print('处理所有任务进度更新出错: $e');
          print('原始数据: ${sseModel.data}');
          print('堆栈: $stackTrace');

          // 执行连接错误回调
          onConnectionError?.call(e);

          if (_subscriptions.containsKey(subscriptionId)) {
            _subscriptions[subscriptionId]?.cancel();
            _subscriptions.remove(subscriptionId);
          }
        }
      },
      onError: (error) {
        print('所有任务进度监听出错: $error');

        // 执行连接错误回调
        onConnectionError?.call(error);

        if (_subscriptions.containsKey(subscriptionId)) {
          _subscriptions[subscriptionId]?.cancel();
          _subscriptions.remove(subscriptionId);
        }
      },
      onDone: () {
        print('所有任务进度监听关闭');
        _subscriptions.remove(subscriptionId);
      },
    );

    _subscriptions[subscriptionId] = subscription;
  }

  /// 安全地解析JSON字符串
  dynamic _safeJsonDecode(String data) {
    try {
      // 直接解析标准JSON
      return jsonDecode(data);
    } catch (e) {
      print('JSON解析失败: $e');
      print('原始数据: $data');

      // 作为最后的尝试，构造一个基本的错误响应对象
      return {
        'id': '',
        'name': '解析失败',
        'progress': 0,
        'status': 'error',
        'message': '无法解析服务器返回的数据',
        'created_at': DateTime.now().millisecondsSinceEpoch / 1000,
      };
    }
  }

  /// 解析任务信息，直接使用Python服务器返回的原始格式
  TaskInfo _parseTaskInfo(String jsonStr) {
    final Map<String, dynamic> data = _safeJsonDecode(jsonStr);
    return TaskInfo.fromJson(data);
  }

  /// 取消任务订阅
  void cancelTaskSubscription(String taskId) {
    if (_subscriptions.containsKey(taskId)) {
      _subscriptions[taskId]?.cancel();
      _subscriptions.remove(taskId);
    }
  }

  /// 关闭所有SSE连接和资源
  void dispose() {
    // 取消所有任务订阅
    for (final subscription in _subscriptions.values) {
      subscription.cancel();
    }
    _subscriptions.clear();
  }

  /// 改造startTask方法，匹配Python服务器API
  Future<void> startTask(String taskId) async {
    final url = "$baseUrl/tasks/$taskId/start";
    final response = await http.post(
      Uri.parse(url),
      headers: {"Content-Type": "application/json"},
    );

    if (response.statusCode != 200) {
      print('启动任务失败: ${response.statusCode} ${response.body}');
      throw Exception('启动任务失败: ${response.statusCode} ${response.body}');
    }
  }
}
