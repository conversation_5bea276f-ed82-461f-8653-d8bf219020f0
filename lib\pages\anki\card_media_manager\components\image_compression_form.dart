import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/card_media_controller.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';

class ImageCompressionForm extends GetView<CardMediaManagerController> {
  const ImageCompressionForm({super.key});

  @override
  Widget build(BuildContext context) {
    final ankiConnectController = Get.find<AnkiConnectController>();

    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadRadioGroupCustom(
                label: 'anki.card_media_manager.compression_source'.tr,
                initialValue: controller.compressionParams.source.value,
                items: controller.compressionParams.sourceTypeList,
                onChanged: (value) {
                  controller.compressionParams.source.value = value;
                },
              ),
              ShadInputWithValidate(
                label: 'anki.card_media_manager.quality'.tr,
                placeholder: 'anki.card_media_manager.quality_placeholder'.tr,
                initialValue:
                    controller.compressionParams.quality.value.toString(),
                onChanged: (value) {
                  final regex = RegExp(r'^\d+$');
                  if (value.isNotEmpty && regex.hasMatch(value)) {
                    controller.compressionParams.quality.value = value;
                  }
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return null;
                  }
                  final regex = RegExp(r'^\d+$');
                  if (!regex.hasMatch(value)) {
                    return "anki.card_media_manager.quality_error".tr;
                  }
                  return null;
                },
              ),
              if (controller.compressionParams.source.value == "deck") ...[
                ShadSelectWithInput(
                  key: ValueKey(
                      "deck-${ankiConnectController.parentDeckList.length}"),
                  label: 'anki.common.target_deck'.tr,
                  placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                  searchPlaceholder:
                      'anki.placeholder.target_deck_search_input'.tr,
                  isMultiple: false,
                  initialValue: [controller.parentDeck.value],
                  options: ankiConnectController.parentDeckList
                      .map((e) => {'value': e, 'label': e})
                      .toList(),
                  onChanged: (value) {
                    logger.i(value);
                    controller.parentDeck.value = value.single;
                  },
                  onAddNew: (newDeckName) {
                    // Add to the deck list if not already present
                    if (!ankiConnectController.parentDeckList
                        .contains(newDeckName)) {
                      ankiConnectController.parentDeckList.add(newDeckName);
                    }
                    // Set as selected deck
                    controller.parentDeck.value = newDeckName;
                  },
                  hasSuffix: true,
                  onRefresh: () async {
                    logger.i("refresh");
                    final result =
                        await ankiConnectController.resetAnkiConnectData();
                    if (result) {
                      showToastNotification(
                          context, 'anki.common.refresh_success'.tr, "");
                    }
                  },
                ),
              ],
              if (controller.compressionParams.source.value == "file") ...[
                ShadSelectCustom(
                  label: 'toolbox.common.outputLocation'.tr,
                  placeholder: 'toolbox.common.selectOutputLocation'.tr,
                  initialValue: [controller.compressionParams.outputMode.value],
                  options: outputModeList,
                  onChanged: (value) {
                    controller.compressionParams.outputMode.value =
                        value.single;
                  },
                ),
                if (controller.compressionParams.outputMode.value == 'custom')
                  ShadInputWithFileSelect(
                    key: ValueKey(
                        "output-dir-${controller.compressionParams.outputDir.value}"),
                    title: 'toolbox.common.outputDirectory'.tr,
                    placeholder: Text('toolbox.common.outputDirectory'.tr),
                    initialValue: [
                      controller.compressionParams.outputDir.value
                    ],
                    isRequired: true,
                    isFolder: true,
                    onFilesSelected: (value) {
                      controller.compressionParams.outputDir.value =
                          value.single;
                    },
                    onValidate: (value, files) async {
                      return await validateOutputDir(value, files);
                    },
                    onValidateError: (error) {},
                  ),
                ShadInputWithFileSelect(
                  key: const ValueKey("input-file"),
                  title: 'toolbox.common.inputFile'.tr,
                  placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                  allowedExtensions: const ['jpg', 'png', 'jpeg'],
                  isRequired: true,
                  allowMultiple: true,
                  initialValue: controller.compressionParams.selectedFilePaths,
                  onFilesSelected: (files) {
                    controller.compressionParams.selectedFilePaths.clear();
                    controller.compressionParams.selectedFilePaths
                        .addAll(files);
                  },
                  onValidate: (value, files) async {
                    return await validateFile(value, files);
                  },
                  onValidateError: (error) {},
                ),
              ],
            ],
          )),
    );
  }
}
