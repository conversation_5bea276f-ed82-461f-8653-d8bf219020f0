#![allow(unused)]

use crate::anki::models::{gen_apkg, get_model, AnkiNote};
use crate::anki::pdf_card::common::{
    calculate_overlap_ratio, clean_page_annotations, convert_cloze, group_page_annotations,
    process_card_image_and_masks,
};
use crate::anki::pdf_utils::{
    delete_annotations, export_pdf_to_images, extract_annotations, get_pdfium, parse_page_ranges,
};
use crate::anki::types::{Annotation, AnnotationType, PdfError};

use genanki_rs::{Deck, Note, Package};
use image::ImageFormat;
use indexmap::indexmap;
use log::{error, warn};
use lopdf::{Dictionary, Document, Object, ObjectId, Outline};
use pdfium_render::prelude::*;
use rand::Rng;
use reqwest::Client;
use rinf::debug_print;
use rust_search::SearchBuilder;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::collections::HashSet;
use std::path::{Path, PathBuf};
use std::thread;
use std::time::Duration;
use tempfile::tempdir;
use thiserror::Error;
use tokio::time;
use ulid::Ulid;

#[derive(Debug, Error)]
pub enum ZoteroError {
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Request error: {0}")]
    Request(#[from] reqwest::Error),
    #[error("Other error: {0}")]
    Other(String),
}

/// Zotero注释信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ZoteroAnnotation {
    pub annotation_author_name: String,
    pub annotation_color: String,
    pub annotation_comment: String,
    pub annotation_page_label: String,
    pub annotation_position: String, // JSON 字符串
    pub annotation_sort_index: String,
    pub annotation_text: Option<String>, // 可选字段
    pub annotation_type: String,
    pub date_added: String,
    pub date_modified: String,
    pub item_type: String,
    pub key: String,
    pub parent_item: String,
    pub relations: serde_json::Value, // 使用 serde_json::Value 以处理动态结构
    pub tags: Vec<Tag>,               // 使用 Tag 结构体来表示标签
    pub version: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Tag {
    pub tag: String,
}

#[derive(Debug)]
pub struct ZoteroCardConfig {
    pub item_id: i64,
    pub page_range: String,
    pub address: String,
    pub deck_name: String,
    pub model_name: String,
    pub annotation_types: Vec<String>,
    pub tags: Vec<String>,
    pub is_show_source: bool,
    pub is_answer_cloze: bool,
    pub cloze_grammar_list: Vec<String>,
    pub extra_info: Option<String>,
    pub output_path: String,
}

pub async fn export_zotero_item(item_id: i64) -> Result<String, ZoteroError> {
    // 创建临时目录
    // let temp_dir = dirs::download_dir()
    let temp_dir = dirs::cache_dir()
        .ok_or_else(|| ZoteroError::Other("Failed to get cache directory".to_string()))?;
    let save_dir = temp_dir.join(Ulid::new().to_string());
    std::fs::create_dir_all(&save_dir)?;
    debug_print!(
        "item_id: {}, save_dir: {}",
        item_id,
        save_dir.to_string_lossy().to_string()
    );
    let mut doc_path = String::new();

    // 准备请求数据
    let payload = json!({
        "id": [item_id],
        "path": save_dir.to_string_lossy().to_string()
    });

    // 发送请求
    let client = Client::new();
    let response = client
        .post("http://127.0.0.1:23119/guru/export_item")
        .json(&payload)
        .send()
        .await?;

    let res: Value = response.json().await?;

    if res["status"] != "error" {
        let files_dir = save_dir.join("files");
        let mut t = 0.0;
        let delta = 0.1;

        while t < 180.0 {
            // 使用 rust_search 查找 PDF 文件
            let search_results = SearchBuilder::default()
                .location(files_dir.to_string_lossy().to_string())
                .ext("pdf")
                .build()
                .collect::<Vec<_>>();

            if !search_results.is_empty() {
                doc_path = search_results[0].clone();
                break;
            }

            thread::sleep(Duration::from_secs_f64(delta));
            t += delta;
        }
    } else {
        let error_message = res["message"]
            .as_str()
            .unwrap_or("Unknown error")
            .to_string();
        error!("res = {:?}", res);
        return Err(ZoteroError::InvalidInput(format!(
            "导出条目失败！{}",
            error_message
        )));
    }

    warn!("doc_path: {}", doc_path);

    if doc_path.is_empty() {
        return Err(ZoteroError::Other("No PDF file found".to_string()));
    }

    Ok(doc_path)
}

pub async fn get_zotero_item_details(item_id: i64) -> Result<Value, ZoteroError> {
    let client = Client::new();
    let response = client
        .get(format!(
            "http://127.0.0.1:23119/guru/get_item?id={}",
            item_id
        ))
        .send()
        .await?;
    let res: Value = response.json().await?;
    if res["status"] != "error" {
        Ok(res)
    } else {
        Err(ZoteroError::Other(
            res["message"]
                .as_str()
                .unwrap_or("Unknown error")
                .to_string(),
        ))
    }
}

pub async fn get_zotero_annotations_by_item_id(
    item_id: i64,
) -> Result<Vec<ZoteroAnnotation>, ZoteroError> {
    // 准备请求数据
    let payload = json!({
        "id": item_id,
    });

    // 发送请求
    let client = Client::new();
    let response = client
        .post("http://127.0.0.1:23119/guru/get_annotations_by_id")
        .json(&payload)
        .send()
        .await?;
    let res: Value = response.json().await?;

    if res["status"] != "error" {
        // 解析数据部分
        let annotations_data = res["data"]
            .as_array()
            .ok_or_else(|| ZoteroError::Other("Failed to parse annotations data".to_string()))?;

        // 将 JSON 数据转换为 ZoteroAnnotation 类型
        let annotations: Vec<ZoteroAnnotation> = annotations_data
            .iter()
            .map(|annotation| {
                let tags = annotation["tags"]
                    .as_array()
                    .unwrap_or(&vec![])
                    .iter()
                    .map(|tag| Tag {
                        tag: tag["tag"].as_str().unwrap_or("").to_string(),
                    })
                    .collect();

                ZoteroAnnotation {
                    annotation_author_name: annotation["annotationAuthorName"]
                        .as_str()
                        .unwrap_or("")
                        .to_string(),
                    annotation_color: annotation["annotationColor"]
                        .as_str()
                        .unwrap_or("")
                        .to_string(),
                    annotation_comment: annotation["annotationComment"]
                        .as_str()
                        .unwrap_or("")
                        .to_string(),
                    annotation_page_label: annotation["annotationPageLabel"]
                        .as_str()
                        .unwrap_or("")
                        .to_string(),
                    annotation_position: annotation["annotationPosition"]
                        .as_str()
                        .unwrap_or("")
                        .to_string(),
                    annotation_sort_index: annotation["annotationSortIndex"]
                        .as_str()
                        .unwrap_or("")
                        .to_string(),
                    annotation_text: annotation
                        .get("annotationText")
                        .and_then(|text| text.as_str().map(|s| s.to_string())),
                    annotation_type: annotation["annotationType"]
                        .as_str()
                        .unwrap_or("")
                        .to_string(),
                    date_added: annotation["dateAdded"].as_str().unwrap_or("").to_string(),
                    date_modified: annotation["dateModified"]
                        .as_str()
                        .unwrap_or("")
                        .to_string(),
                    item_type: annotation["itemType"].as_str().unwrap_or("").to_string(),
                    key: annotation["key"].as_str().unwrap_or("").to_string(),
                    parent_item: annotation["parentItem"].as_str().unwrap_or("").to_string(),
                    relations: annotation["relations"].clone(), // 直接使用 JSON 值
                    tags,
                    version: annotation["version"].as_u64().unwrap_or(0) as u32,
                }
            })
            .collect();

        Ok(annotations)
    } else {
        Err(ZoteroError::Other(
            res["message"]
                .as_str()
                .unwrap_or("Unknown error")
                .to_string(),
        ))
    }
}

pub fn group_zotero_annotations(
    mut annotations: Vec<ZoteroAnnotation>,
) -> Vec<Vec<ZoteroAnnotation>> {
    // 按照页码和坐标排序
    annotations.sort_by(|a, b| {
        let a_position: Value = serde_json::from_str(&a.annotation_position).unwrap();
        let b_position: Value = serde_json::from_str(&b.annotation_position).unwrap();
        let a_page = a_position["pageIndex"].as_i64().unwrap_or(0);
        let b_page = b_position["pageIndex"].as_i64().unwrap_or(0);

        a_page.cmp(&b_page).then_with(|| {
            let empty_vec = Vec::new();
            let a_rects = a_position["rects"].as_array().unwrap_or(&empty_vec);
            let b_rects = b_position["rects"].as_array().unwrap_or(&empty_vec);
            let a_y = a_rects.get(0).and_then(|r| r[3].as_f64()).unwrap_or(0.0);
            let b_y = b_rects.get(0).and_then(|r| r[3].as_f64()).unwrap_or(0.0);
            b_y.partial_cmp(&a_y).unwrap_or(std::cmp::Ordering::Equal)
        })
    });

    let mut groups: Vec<Vec<ZoteroAnnotation>> = Vec::new();
    let mut used = vec![false; annotations.len()];

    // 先处理所有 image 类型的注释
    for (current_idx, current_annotation) in annotations.iter().enumerate() {
        if used[current_idx] || current_annotation.annotation_type != "image" {
            continue;
        }
        // dbg!(&current_annotation);
        let mut group = Vec::new();
        let current_position: Value =
            serde_json::from_str(&current_annotation.annotation_position).unwrap();
        let current_page = current_position["pageIndex"].as_i64().unwrap_or(0);
        let empty_vec = Vec::new();
        let current_rects = current_position["rects"].as_array().unwrap_or(&empty_vec);

        // 添加当前 image 注释作为组的第一个元素
        group.push(current_annotation.clone());
        used[current_idx] = true;

        // 检查其他 image 类型注释是否与左右边界线相交，如果相交则丢弃
        for (other_idx, other_annotation) in annotations.iter().enumerate() {
            if used[other_idx] || other_idx == current_idx {
                continue;
            }

            let other_position: Value =
                serde_json::from_str(&other_annotation.annotation_position).unwrap();
            let other_page = other_position["pageIndex"].as_i64().unwrap_or(0);

            if other_page == current_page && other_annotation.annotation_type == "image" {
                let empty_vec = Vec::new();
                let other_rects = other_position["rects"].as_array().unwrap_or(&empty_vec);
                if !current_rects.is_empty() && !other_rects.is_empty() {
                    let current_rect = &current_rects[0];
                    let other_rect = &other_rects[0];

                    // 获取矩形坐标
                    let current_x1 = current_rect[0].as_f64().unwrap_or(0.0);
                    let current_y1 = current_rect[1].as_f64().unwrap_or(0.0);
                    let current_x2 = current_rect[2].as_f64().unwrap_or(0.0);
                    let current_y2 = current_rect[3].as_f64().unwrap_or(0.0);

                    let other_x1 = other_rect[0].as_f64().unwrap_or(0.0);
                    let other_y1 = other_rect[1].as_f64().unwrap_or(0.0);
                    let other_x2 = other_rect[2].as_f64().unwrap_or(0.0);
                    let other_y2 = other_rect[3].as_f64().unwrap_or(0.0);

                    // 检查左边界穿插
                    let is_left_intersect = current_x1 > other_x1
                        && current_x1 < other_x2
                        && current_y2 >= other_y2
                        && current_y1 <= other_y1;

                    // 检查右边界穿插
                    let is_right_intersect = current_x2 > other_x1
                        && current_x2 < other_x2
                        && current_y2 >= other_y2
                        && current_y1 <= other_y1;

                    if is_left_intersect || is_right_intersect {
                        used[other_idx] = true; // 标记为已使用但不添加到组中
                        continue;
                    }

                    // 检查其他 image 注释是否位于当前image的内部
                    let overlap_ratio = calculate_overlap_ratio(
                        &[
                            current_rect[0].as_f64().unwrap_or(0.0),
                            current_rect[1].as_f64().unwrap_or(0.0),
                            current_rect[2].as_f64().unwrap_or(0.0),
                            current_rect[3].as_f64().unwrap_or(0.0),
                        ],
                        &[
                            other_rect[0].as_f64().unwrap_or(0.0),
                            other_rect[1].as_f64().unwrap_or(0.0),
                            other_rect[2].as_f64().unwrap_or(0.0),
                            other_rect[3].as_f64().unwrap_or(0.0),
                        ],
                    );

                    if overlap_ratio > 0.85 {
                        // 如果重叠率超过85%，认为是位于内部，将其加入组中
                        group.push(other_annotation.clone());
                        used[other_idx] = true;
                    }
                }
            }
        }

        // 查找当前 image 内部的其他类型注释
        for (other_idx, other_annotation) in annotations.iter().enumerate() {
            if used[other_idx] || other_annotation.annotation_type == "image" {
                continue;
            }

            let other_position: Value =
                serde_json::from_str(&other_annotation.annotation_position).unwrap();
            let other_page = other_position["pageIndex"].as_i64().unwrap_or(0);

            if other_page == current_page {
                let empty_vec = Vec::new();
                let other_rects = other_position["rects"].as_array().unwrap_or(&empty_vec);
                if !current_rects.is_empty() && !other_rects.is_empty() {
                    let current_rect = &current_rects[0];
                    let other_rect = &other_rects[0];
                    let overlap_ratio = calculate_overlap_ratio(
                        &[
                            current_rect[0].as_f64().unwrap(),
                            current_rect[1].as_f64().unwrap(),
                            current_rect[2].as_f64().unwrap(),
                            current_rect[3].as_f64().unwrap(),
                        ],
                        &[
                            other_rect[0].as_f64().unwrap(),
                            other_rect[1].as_f64().unwrap(),
                            other_rect[2].as_f64().unwrap(),
                            other_rect[3].as_f64().unwrap(),
                        ],
                    );

                    if overlap_ratio > 0.0 {
                        group.push(other_annotation.clone());
                        used[other_idx] = true;
                    }
                }
            }
        }

        if !group.is_empty() {
            groups.push(group);
        }
    }

    // 处理剩余未使用的非 image 类型注释
    for (idx, annotation) in annotations.iter().enumerate() {
        if !used[idx] {
            groups.push(vec![annotation.clone()]);
            used[idx] = true;
        }
    }

    groups
}

pub async fn make_zotero_annotation_cards(
    config: ZoteroCardConfig,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<(), ZoteroError> {
    // 获取注释列表
    progress_callback(10.0, 100.0, "正在获取注释列表...".to_string());
    let annotations = get_zotero_annotations_by_item_id(config.item_id).await?;

    // 分组注释
    progress_callback(30.0, 100.0, "正在分析注释...".to_string());
    let grouped = group_zotero_annotations(annotations);

    let mut media_files = Vec::new();
    let mut notes = Vec::new();

    // 遍历grouped，生成卡片
    progress_callback(50.0, 100.0, "正在生成卡片...".to_string());
    let total_groups = grouped.len() as f64;
    for (idx, group) in grouped.iter().enumerate() {
        // 更新进度
        let current_progress = 50.0 + (idx as f64 / total_groups * 30.0);
        progress_callback(
            current_progress,
            100.0,
            format!(
                "正在处理第 {} 个注释组，共 {} 个...",
                idx + 1,
                grouped.len()
            ),
        );

        // 判断第一个注释的类型是否位于config.annotation_types中
        let first_annotation = &group[0];
        if config
            .annotation_types
            .contains(&first_annotation.annotation_type)
        {
            // 生成卡片
            let mut comment = first_annotation.annotation_comment.clone();
            let mut is_reverse = false;
            let mut header = String::new();
            let mut body = String::new();
            // 使用正则判断comment是否包含[~～]
            let re_reverse = regex::Regex::new(r"(?s)^(.*)[~～]((?s:.*))$").unwrap();
            if re_reverse.is_match(&comment) {
                is_reverse = true;
                comment = re_reverse.replace(&comment, "${1}${2}").trim().to_string();
            }
            // 使用正则判断是否包含---
            let re_split = regex::Regex::new(r"(?s)(.*?)\n---\n(.*)$").unwrap();
            if let Some(captures) = re_split.captures(&comment) {
                header = captures
                    .get(1)
                    .map_or("", |m| m.as_str())
                    .trim()
                    .to_string();
                body = captures
                    .get(2)
                    .map_or("", |m| m.as_str())
                    .trim()
                    .to_string();
            } else {
                if is_reverse {
                    header = comment.trim().to_string();
                } else {
                    body = comment.trim().to_string();
                }
            }
            if config.is_answer_cloze {
                body = convert_cloze(body, &config.cloze_grammar_list, true);
            }
            // 将header和body中的\n替换为<br>
            header = header.replace("\n", "<br>");
            body = body.replace("\n", "<br>");
            let mut tags = Vec::new();
            for obj in first_annotation.tags.iter() {
                tags.push(obj.tag.clone());
            }
            let mut fields = indexmap! {};
            if first_annotation.annotation_type == "image" {
            } else if first_annotation.annotation_type == "text"
                || first_annotation.annotation_type == "note"
            {
                // 生成卡片
                fields = indexmap! {
                    "原文" => first_annotation.annotation_comment.clone(),
                    "出处" => format!("<a href=\"zotero://open-pdf/library/items/{}?annotation={}\">查看出处</a>", first_annotation.parent_item, first_annotation.key),
                    "备注" => format!("{}发表", first_annotation.date_added),
                    "标题" => String::new(),
                    "笔记" => String::new(),
                    "反转" => "".to_string(),
                };
            } else if first_annotation.annotation_type == "highlight"
                || first_annotation.annotation_type == "underline"
            {
                // 生成卡片
                if is_reverse {
                    fields = indexmap! {
                        "原文" => first_annotation.annotation_text.clone().unwrap_or("".to_string()),
                        "出处" => format!("<a href=\"zotero://open-pdf/library/items/{}?annotation={}\">查看出处</a>", first_annotation.parent_item, first_annotation.key),
                        "备注" => format!("{}发表", first_annotation.date_added),
                        "标题" => header.clone(),
                        "笔记" => body.to_string(),
                        "反转" => "1".to_string(),
                    };
                } else {
                    fields = indexmap! {
                        "原文" => first_annotation.annotation_text.clone().unwrap_or("".to_string()),
                        "出处" => format!("<a href=\"zotero://open-pdf/library/items/{}?annotation={}\">查看出处</a>", first_annotation.parent_item, first_annotation.key),
                        "备注" => format!("{}发表", first_annotation.date_added),
                        "标题" => header.to_string(),
                        "笔记" => body.to_string(),
                        "反转" => "".to_string(),
                    };
                }
            }
            // 创建笔记
            let mut final_tags = config.tags.clone();
            final_tags.extend(tags);
            final_tags.sort();
            final_tags.dedup();
            // 创建笔记
            let note = AnkiNote {
                deck_name: config.deck_name.to_string(),
                model_name: config.model_name.to_string(),
                fields: fields
                    .into_iter()
                    .map(|(key, value)| value.to_string())
                    .collect(),
                tags: if final_tags.is_empty() {
                    None
                } else {
                    Some(final_tags)
                },
                guid: Some(first_annotation.key.clone()),
            };
            notes.push(note);
        }
    }
    dbg!(&notes);

    // 生成apkg文件
    progress_callback(90.0, 100.0, "正在生成Anki牌组...".to_string());
    crate::anki::models::gen_apkg(
        notes,
        Some(media_files),
        &config.output_path,
        None,
        false,
        None,
    )
    .await?;

    progress_callback(100.0, 100.0, "完成！".to_string());
    Ok(())
}

#[cfg(test)]
mod tests {
    use rinf::debug_print;
    use tokio;

    use super::*;
    use std::fs;
    use std::path::Path;
    use std::path::PathBuf;

    // #[tokio::test]
    async fn test_export_zotero_item() {
        let item_id = 554;
        let result = export_zotero_item(item_id as i64).await;
        dbg!(&result);
        assert!(result.is_ok());
        // assert!(false);
    }

    // #[tokio::test]
    async fn test_get_zotero_item_details() {
        let item_id = 554;
        let result = get_zotero_item_details(item_id).await;
        dbg!(&result);
        assert!(result.is_ok());
    }

    // #[tokio::test]
    async fn test_get_zotero_annotations_by_item_id() {
        let item_id = 554;
        let result = get_zotero_annotations_by_item_id(item_id).await;
        dbg!(&result);
        assert!(result.is_ok());
        // assert!(false);
    }

    // #[tokio::test]
    async fn test_group_zotero_annotations() -> Result<(), ZoteroError> {
        let annotations = get_zotero_annotations_by_item_id(554).await?;
        let grouped = group_zotero_annotations(annotations);
        dbg!(&grouped);
        assert!(false);
        Ok(())
    }
    // #[tokio::test]
    async fn test_replace_tilde() {
        use regex::Regex;
        let input = "测试笔记2\n～";
        // 启用多行和"点匹配换行符"模式（`(?s)`）
        let re = Regex::new(r"(?s)^(.*)～((?s:.*))$").unwrap();
        // 替换最后一个 ~ 为 X
        let result = re.replace(input, "${1}${2}").to_string();
        dbg!(result);
        assert!(false);
    }
}
