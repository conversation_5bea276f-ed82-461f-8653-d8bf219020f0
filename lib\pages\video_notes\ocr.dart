import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/ocr/ocr_controller.dart';
import 'package:anki_guru/controllers/anki/ocr/components/ocr_image_viewer.dart';
import 'package:anki_guru/controllers/anki/ocr/components/ocr_image.dart';
import 'package:skeletonizer/skeletonizer.dart';

class VideoOCRPage extends StatefulWidget {
  // 添加图片路径参数
  final String? imagePath;

  const VideoOCRPage({
    super.key,
    this.imagePath,
  });

  @override
  State<VideoOCRPage> createState() => _VideoOCRPageState();
}

class _VideoOCRPageState extends State<VideoOCRPage> {
  late final OCRController ocrController;
  final RxBool _isLoading = true.obs;
  // 页面自己维护图像对象，而不使用控制器的imageCards
  final Rx<OcrImage?> _ocrImage = Rx<OcrImage?>(null);

  @override
  void initState() {
    super.initState();
    // 获取OCR控制器
    ocrController = Get.find<OCRController>();

    // 初始化图像，优先使用imagePath参数
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initImage();
    });
  }

  // 初始化图像
  Future<void> _initImage() async {
    // 优先使用提供的图片路径
    if (widget.imagePath != null && widget.imagePath!.isNotEmpty) {
      try {
        final file = File(widget.imagePath!);
        if (await file.exists()) {
          final bytes = await file.readAsBytes();
          final localImage = OcrImage(imageData: bytes);
          localImage.imagePath = widget.imagePath!;
          _ocrImage.value = localImage;

          // 直接开始OCR识别
          await _runOcr();
          return;
        }
      } catch (e) {
        debugPrint('从文件路径加载图片失败: $e');
      }
    }

    // 如果没有提供路径或路径无效，使用控制器的图像
    if (ocrController.imageCards.isNotEmpty) {
      // 创建图像的副本，避免引用控制器中的对象
      final sourceImage = ocrController.imageCards.first;
      final localImage = OcrImage(imageData: sourceImage.imageData);
      if (sourceImage.imagePath.isNotEmpty) {
        localImage.imagePath = sourceImage.imagePath;
      }
      _ocrImage.value = localImage;

      // 开始OCR识别
      await _runOcr();
    }
  }

  // 执行OCR识别
  Future<void> _runOcr() async {
    if (_ocrImage.value == null) return;

    _isLoading.value = true;

    try {
      // 使用单图片OCR识别，它返回处理后的图像对象
      final processedImage =
          await ocrController.recognizeSingleImage(_ocrImage.value!);

      if (processedImage != null) {
        // 更新本地图像状态
        _ocrImage.value = processedImage;
      }
    } catch (e) {
      Get.snackbar(
        '识别失败',
        '无法识别图片: $e',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('识别结果'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _runOcr,
            tooltip: '重新识别',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Obx(() {
          return _ocrImage.value == null
              ? _buildNoImageWidget()
              : SizedBox(
                  // 提供固定高度约束，解决Expanded在无界高度中的问题
                  height: MediaQuery.of(context).size.height -
                      120, // 减去AppBar和Padding的高度
                  child: Skeletonizer(
                    enabled: _isLoading.value,
                    child: OcrImageViewer(
                      image: _ocrImage.value!,
                      onTextCopied: (text) {},
                      useCard: true,
                    ),
                  ),
                );
        }),
      ),
    );
  }

  // 无图片时显示的组件
  Widget _buildNoImageWidget() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 100),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.image_not_supported_outlined,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              const Text(
                '没有可用的图片',
                style: TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () => Get.back(),
                child: const Text('返回'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
