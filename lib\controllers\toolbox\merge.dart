import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFMergePageController extends GetxController {
  // 已有数据
  final mergeModeList = [
    {"value": "file", "label": 'toolbox.merge.mode.file'.tr},
    {"value": "page", "label": 'toolbox.merge.mode.page'.tr},
  ];
  final sortByList = [
    {
      'value': 'selection',
      'label': 'toolbox.merge.sortByOptions.selection'.tr,
    },
    {
      'value': 'numberPrefix',
      'label': 'toolbox.merge.sortByOptions.numberPrefix'.tr,
    },
    {
      'value': 'numberSuffix',
      'label': 'toolbox.merge.sortByOptions.numberSuffix'.tr,
    },
    {
      'value': 'name',
      'label': 'toolbox.merge.sortByOptions.name'.tr,
    },
    {
      'value': 'createDate',
      'label': 'toolbox.merge.sortByOptions.createDate'.tr,
    },
    {
      'value': 'modDate',
      'label': 'toolbox.merge.sortByOptions.modDate'.tr,
    },
  ];
  final directionList = [
    {
      'value': 'ascending',
      'label': 'toolbox.merge.directionOptions.ascending'.tr,
    },
    {
      'value': 'descending',
      'label': 'toolbox.merge.directionOptions.descending'.tr,
    },
  ];
  // 表单参数
  final mergeMode = "file".obs;
  final sortBy = 'selection'.obs;
  final sortDirection = 'ascending'.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'multi');

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.fileSelect.error'.tr,
        type: "error",
      );
      return;
    }
    if(mergeMode.value == 'file' && selectedFilePaths.length < 2){
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.merge.atLeastTwoFilesRequired'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      if (mergeMode.value == 'file') {
    
        final pathUtils = PathUtils(selectedFilePaths.first);
        String outputPath = await pathUtils.convertPath(
          outputMode.value,
          stem_append: 'toolbox.merge.fileNameAppend.multiFile'.tr,
          outputDir: outputDir.value,
        );
        final pathList =
            sortFiles(selectedFilePaths, sortBy.value, sortDirection.value);
        final data = {
          'merge_mode': mergeMode.value,
          'input_paths': pathList,
          'output_path': outputPath,
          'sort_by': sortBy.value,
          'sort_direction': sortDirection.value,
          'page_range': pageRange.value,
          'show_progress': true,
        };
        final resp = await messageController.request(data, 'pdf/merge');
        logger.w("resp: $resp");
        if (resp.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'toolbox.common.process.completed'.tr,
          );
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
          return;
        }
      } else {
        // 页合并模式
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: 'toolbox.merge.page_merge_stem_append'.tr,
            suffix: ".pdf",
            outputDir: outputDir.value,
          );
          String pageRangeStr = pageRange.value;
          if (pageRangeStr.isEmpty) {
            pageRangeStr = "1-N";
          }
          final data = {
            'merge_mode': mergeMode.value,
            'input_path': filePath,
            'output_path': outputPath,
            'page_range': pageRangeStr,
            'show_progress': true,
          };
          final resp = await messageController.request(data, 'pdf/merge');
          logger.w("resp: $resp");
          if (resp.status == "success") {
            progressController.outputPath.value = outputPath;
            progressController.updateProgress(
              status: "completed", message: 'toolbox.common.process.completed'.tr,
            );
          } else {
            progressController.updateProgress(
                status: "error", message: resp.message);
            return;
          }
        }
      }
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: 'toolbox.common.error_with_msg'.trParams({'error': e.toString()}),
      );
    }
  }
}
