#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用PyMuPDF读取PDF文件中的矩形注释并转换为YOLO训练数据集
- item (绿色): 整个题目范围，包括答案
- question (红色): 仅包括题目区域
- answer (蓝色): 仅包括答案区域
"""

import os
import sys
import json
import argparse
import shutil
import fitz  # PyMuPDF
import cv2
import numpy as np
import tempfile
from pathlib import Path
from loguru import logger


# 标签类别定义
CLASSES = {
    "item": 0,       # 绿色 - 整个题目（含答案）
    "question": 1,   # 红色 - 仅题目
    "answer": 2,     # 蓝色 - 仅答案
}


# 默认颜色配置 (HEX格式)
DEFAULT_COLOR_CONFIGS = {
    "item": ["#00FF00", "#00EE00", "#00DD00"],    # 绿色系列
    "question": ["#FF0000", "#EE0000", "#DD0000"], # 红色系列
    "answer": ["#0000FF", "#0000EE", "#0000DD"]    # 蓝色系列
}


def setup_logger(log_level="INFO"):
    """配置日志器"""
    logger.remove()  # 移除默认处理程序
    logger.add(sys.stderr, level=log_level)
    logger.add("annotation_extraction.log", rotation="10 MB", level="DEBUG")


def rgb_to_hex(color):
    """
    将RGB颜色值（0-1范围）转换为十六进制颜色字符串
    
    Args:
        color: RGB颜色元组 (0-1)
        
    Returns:
        str: 十六进制颜色字符串
    """
    r, g, b = color if color else (0, 0, 0)
    return f"#{int(r*255):02x}{int(g*255):02x}{int(b*255):02x}".upper()


def load_color_configs(config_file=None):
    """
    加载自定义颜色配置
    
    Args:
        config_file: 配置文件路径（JSON格式）
        
    Returns:
        dict: 颜色配置字典
    """
    configs = DEFAULT_COLOR_CONFIGS.copy()
    
    if config_file and os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                custom_configs = json.load(f)
            
            # 验证并更新配置
            for label, color_list in custom_configs.items():
                if label in ["item", "question", "answer"]:
                    # 确保颜色是列表
                    if isinstance(color_list, list):
                        # 标准化十六进制颜色（统一大写，确保有#前缀）
                        hex_colors = []
                        for color in color_list:
                            if isinstance(color, str):
                                # 如果是十六进制字符串
                                if color.startswith("#"):
                                    hex_colors.append(color.upper())
                                else:
                                    # 尝试添加#前缀
                                    hex_colors.append(f"#{color}".upper())
                            else:
                                logger.warning(f"跳过无效颜色值: {color}")
                        
                        if hex_colors:
                            configs[label] = hex_colors
                    else:
                        logger.warning(f"配置无效: {label} 的值必须是颜色列表")
                else:
                    logger.warning(f"未知标签类型: {label}")
            
            logger.info(f"已加载自定义颜色配置: {config_file}")
        except Exception as e:
            logger.error(f"加载颜色配置文件失败: {e}")
    
    return configs


def get_label_for_hex_color(hex_color, color_configs):
    """
    根据HEX颜色获取对应的标签
    
    Args:
        hex_color: HEX颜色字符串
        color_configs: 颜色配置字典
        
    Returns:
        str or None: 匹配的标签名称，如果没有匹配则返回None
    """
    # 确保大写比较
    hex_color = hex_color.upper()
    
    for label, hex_colors in color_configs.items():
        if hex_color in [color.upper() for color in hex_colors]:
            return label
    
    return None


def extract_annotations(pdf_path, output_dir, color_configs):
    """
    从PDF文件中提取矩形标注，并转换为YOLO格式的训练数据
    
    Args:
        pdf_path: PDF文件路径
        output_dir: 输出目录路径
        color_configs: 颜色配置字典
    """
    # 创建输出目录
    images_dir = os.path.join(output_dir, "images")
    labels_dir = os.path.join(output_dir, "labels")
    
    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(labels_dir, exist_ok=True)
    
    # 打开PDF文件
    pdf_document = fitz.open(pdf_path)
    
    # 获取PDF文件基本名称（不含扩展名）
    pdf_basename = os.path.splitext(os.path.basename(pdf_path))[0]
    
    # 处理每一页
    for page_index, page in enumerate(pdf_document):
        # 获取页面尺寸
        page_width = page.rect.width
        page_height = page.rect.height
        
        # 获取页面注释
        annotations = page.annots()
        if not annotations:
            logger.warning(f"页面 {page_index + 1} 没有注释，跳过。")
            continue
        
        # 收集矩形注释
        rect_annotations = []
        for annot in annotations:
            if annot.type[0] == 4:  # 矩形批注类型
                rect = annot.rect
                color = annot.colors["stroke"]
                
                # 转换为HEX格式
                hex_color = rgb_to_hex(color)
                
                # 打印颜色信息
                logger.debug(f"页面 {page_index + 1} 注释颜色: {hex_color}")
                
                # 根据颜色确定标签类型
                label = get_label_for_hex_color(hex_color, color_configs)
                
                if not label:
                    logger.warning(f"页面 {page_index + 1} 发现未匹配的注释颜色: {hex_color}")
                    logger.debug(f"可用颜色配置: {json.dumps(color_configs)}")
                    continue
                
                logger.debug(f"页面 {page_index + 1} 注释类型: {label}, 位置: {rect}")
                
                rect_annotations.append({
                    "rect": rect,
                    "label": label
                })
        
        if not rect_annotations:
            logger.warning(f"页面 {page_index + 1} 没有满足条件的矩形注释，跳过。")
            continue
        
        logger.info(f"处理页面 {page_index + 1}，找到 {len(rect_annotations)} 个矩形注释。")
        
        # 创建标签文件
        label_filename = f"{pdf_basename}_page_{page_index + 1}.txt"
        label_path = os.path.join(labels_dir, label_filename)
        
        # 将矩形批注转换为YOLO格式
        with open(label_path, "w", encoding="utf-8") as f:
            for annotation in rect_annotations:
                rect = annotation["rect"]
                label = annotation["label"]
                class_id = CLASSES[label]
                
                # 计算YOLO格式的坐标：中心点(x,y)和宽高(w,h)，所有值归一化到0-1
                # YOLO格式：<class_id> <x_center> <y_center> <width> <height>
                x_center = (rect.x0 + rect.x1) / (2 * page_width)
                y_center = (rect.y0 + rect.y1) / (2 * page_height)
                width = abs(rect.x1 - rect.x0) / page_width
                height = abs(rect.y1 - rect.y0) / page_height
                
                # 写入标签文件
                f.write(f"{class_id} {x_center} {y_center} {width} {height}\n")
                logger.debug(f"添加标签: {label} ({class_id}) 在位置 ({x_center:.4f}, {y_center:.4f}, {width:.4f}, {height:.4f})")
        
        try:
            # 在不显示注释的情况下直接渲染页面
            # 创建一个渲染标志，不显示注释
            render_flags = fitz.ANNOT_HIDE
            
            # 渲染页面
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2), annots=False)  # 2倍放大，不显示注释
            img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.h, pix.w, pix.n)
            
            if pix.n == 4:  # RGBA
                img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)
            
            # 图像文件名
            img_filename = f"{pdf_basename}_page_{page_index + 1}.jpg"
            img_path = os.path.join(images_dir, img_filename)
            
            # 保存图像
            cv2.imwrite(img_path, cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
            logger.debug(f"保存页面图像: {img_path}")
        except Exception as e:
            logger.error(f"页面 {page_index + 1} 渲染失败: {str(e)}")
            
            # 备选方案：创建临时PDF文件并删除注释
            try:
                # 将页面导出为单独的PDF
                temp_pdf = tempfile.NamedTemporaryFile(suffix=".pdf", delete=False).name
                with fitz.open() as doc:
                    doc.insert_pdf(pdf_document, from_page=page_index, to_page=page_index)
                    # 在输出之前删除该页面的所有注释
                    temp_page = doc[0]  # 只有一页
                    for annot in temp_page.annots():
                        temp_page.delete_annot(annot)
                    doc.save(temp_pdf)
                
                # 打开处理后的文档并渲染
                with fitz.open(temp_pdf) as doc:
                    temp_page = doc[0]
                    pix = temp_page.get_pixmap(matrix=fitz.Matrix(2, 2))
                    img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.h, pix.w, pix.n)
                    
                    if pix.n == 4:  # RGBA
                        img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)
                    
                    # 保存图像
                    img_filename = f"{pdf_basename}_page_{page_index + 1}.jpg"
                    img_path = os.path.join(images_dir, img_filename)
                    cv2.imwrite(img_path, cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
                    logger.debug(f"使用备选方法保存页面图像: {img_path}")
                
                # 删除临时文件
                os.unlink(temp_pdf)
            except Exception as inner_e:
                logger.error(f"备选渲染方法也失败: {str(inner_e)}")
    
    pdf_document.close()
    logger.success(f"处理完成。数据已保存至 {output_dir}")


def generate_sample_config():
    """生成示例颜色配置文件"""
    config_path = "color_config.json"
    
    if not os.path.exists(config_path):
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(DEFAULT_COLOR_CONFIGS, f, indent=4)
        logger.info(f"已生成示例颜色配置文件: {config_path}")
        logger.info("颜色配置格式: {label: [hex_color1, hex_color2, ...]}")
        logger.info("例如: {\"item\": [\"#00FF00\"], \"question\": [\"#FF0000\"], \"answer\": [\"#0000FF\"]}")


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description="从PDF文件中提取矩形注释，转换为YOLO训练数据集")
    parser.add_argument("--pdf", required=True, help="输入PDF文件路径")
    parser.add_argument("--output", default="./dataset", help="输出目录路径")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], 
                        help="日志级别")
    parser.add_argument("--color-config", help="颜色配置文件路径 (JSON格式)")
    parser.add_argument("--generate-config", action="store_true", help="生成示例颜色配置文件")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logger(args.log_level)
    
    # 生成示例配置
    if args.generate_config:
        generate_sample_config()
        return
    
    # 验证输入文件是否存在
    if not os.path.exists(args.pdf):
        logger.error(f"错误: PDF文件不存在 - {args.pdf}")
        sys.exit(1)
    
    # 验证输入文件是否为PDF
    if not args.pdf.lower().endswith(".pdf"):
        logger.error("错误: 输入文件必须是PDF格式")
        sys.exit(1)
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 加载颜色配置
    color_configs = load_color_configs(args.color_config)
    
    logger.info(f"开始处理PDF文件: {args.pdf}")
    logger.info(f"输出目录: {args.output}")
    logger.debug(f"使用的颜色配置: {json.dumps(color_configs)}")
    
    extract_annotations(args.pdf, args.output, color_configs)


if __name__ == "__main__":
    main()
