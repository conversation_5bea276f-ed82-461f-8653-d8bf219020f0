import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';

class PDFAnnotationPageController extends GetxController {
  // 基本数据
  final exportFormatList = [
    {
      "label": "xfdf",
      "value": "xfdf",
    },
    {
      "label": "fdf",
      "value": "fdf",
    },
    {
      "label": "json",
      "value": "json",
    },
  ];
  // 表单参数
  final exportFormat = "xfdf".obs;
  final importFormat = "xfdf".obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final annotationFile = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'import');

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      progressController.updateProgress(
          status: "running",
          current: 10,
          total: 100,
          message: 'toolbox.common.processing'.tr);
      for (String filePath in selectedFilePaths) {
        try {
          final pathUtils = PathUtils(filePath);
          String outputPath = "";
          if (tabController.selected == 'import') {
            logger.i(annotationFile.value);
            if (annotationFile.value.isEmpty) {
              progressController.updateProgress(status: "error", message: 'toolbox.annotation.annotationFile'.tr);
              return;
            }
            outputPath = await pathUtils.convertPath(
              outputMode.value,
              stem_append: "_${'toolbox.annotation.annotationImport'.tr}",
              suffix: ".pdf",
              outputDir: outputDir.value,
            );
            final PdfDocument document = PdfDocument(
              inputBytes: File(filePath).readAsBytesSync(),
            );
            if (importFormat.value == "xfdf") {
              document.importAnnotation(
                File(annotationFile.value).readAsBytesSync(),
                PdfAnnotationDataFormat.xfdf,
              );
            } else if (importFormat.value == "fdf") {
              document.importAnnotation(
                File(annotationFile.value).readAsBytesSync(),
                PdfAnnotationDataFormat.fdf,
              );
            } else if (importFormat.value == "json") {
              document.importAnnotation(
                File(annotationFile.value).readAsBytesSync(),
                PdfAnnotationDataFormat.json,
              );
            }
            File(outputPath).writeAsBytesSync(
              await document.save(),
            );
            //Dispose the document.
            document.dispose();
            progressController.outputPath.value = outputPath;
            progressController.updateProgress(
              status: "completed", message: 'toolbox.common.fileProcessSuccess'.tr);
          } else if (tabController.selected == 'export') {
            logger.i("path: $filePath");
            final PdfDocument document = PdfDocument(
              inputBytes: File(filePath).readAsBytesSync(),
            );
            if (exportFormat.value == "xfdf") {
              outputPath = await pathUtils.convertPath(
                outputMode.value,
                stem_append: "_${'toolbox.annotation.annotationExport'.tr}",
                suffix: ".xfdf",
                outputDir: outputDir.value,
              );
              List<int> bytes =
                  document.exportAnnotation(PdfAnnotationDataFormat.xfdf);
              File(outputPath).writeAsBytesSync(bytes);
            } else if (exportFormat.value == "fdf") {
              outputPath = await pathUtils.convertPath(
                outputMode.value,
                stem_append: "_${'toolbox.annotation.annotationExport'.tr}",
                suffix: ".fdf",
                outputDir: outputDir.value,
              );
              List<int> bytes =
                  document.exportAnnotation(PdfAnnotationDataFormat.fdf);
              File(outputPath).writeAsBytesSync(bytes);
            } else if (exportFormat.value == "json") {
              outputPath = await pathUtils.convertPath(
                outputMode.value,
                stem_append: "_${'toolbox.annotation.annotationExport'.tr}",
                suffix: ".json",
                outputDir: outputDir.value,
              );
              List<int> bytes =
                  document.exportAnnotation(PdfAnnotationDataFormat.json);
              File(outputPath).writeAsBytesSync(bytes);
            }
            //Dispose the document.
            document.dispose();
            progressController.outputPath.value = outputPath;
            progressController.updateProgress(
              status: "completed", message: 'toolbox.common.fileProcessSuccess'.tr);
          } else if (tabController.selected == "delete") {
            outputPath = await pathUtils.convertPath(
              outputMode.value,
              stem_append: "_${'toolbox.annotation.annotationDelete'.tr}",
              suffix: ".pdf",
              outputDir: outputDir.value,
            );
            final PdfDocument document = PdfDocument(
              inputBytes: File(filePath).readAsBytesSync(),
            );
            final List<int> pages =
                parsePageRange(pageRange.value, document.pages.count);
            for (int i in pages) {
              PdfPage page = document.pages[i];
              PdfAnnotationCollection annotationCollection = page.annotations;
              for (int j = annotationCollection.count - 1; j >= 0; j--) {
                annotationCollection.remove(annotationCollection[j]);
              }
            }
            File(outputPath).writeAsBytesSync(
              await document.save(),
            );
            document.dispose();
            progressController.outputPath.value = outputPath;
            progressController.updateProgress(
              status: "completed", message: 'toolbox.common.fileProcessSuccess'.tr);
          } else if (tabController.selected == "flatten") {
            outputPath = await pathUtils.convertPath(
              outputMode.value,
              stem_append: "_${'toolbox.annotation.annotationFlatten'.tr}",
              suffix: ".pdf",
              outputDir: outputDir.value,
            );
            final PdfDocument document = PdfDocument(
              inputBytes: File(filePath).readAsBytesSync(),
            );
            final List<int> pages =
                parsePageRange(pageRange.value, document.pages.count);
            for (int i in pages) {
              //Loads the existing PDF page
              PdfPage page = document.pages[i];
              //Gets the annotation collection from page
              PdfAnnotationCollection annotationCollection = page.annotations;

              //Flattens all the annotations in the page
              annotationCollection.flattenAllAnnotations();
            }
            File(outputPath).writeAsBytesSync(
              await document.save(),
            );
            document.dispose();
            progressController.outputPath.value = outputPath;
            progressController.updateProgress(
              status: "completed", message: 'toolbox.common.fileProcessSuccess'.tr);
          }
        } catch (e) {
          progressController.updateProgress(
            status: "error", message: e.toString());
        }
      }
    } catch (e) {
      progressController.updateProgress(
        status: "error", message: e.toString());
    }
  }
}
