import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

/// Centralized asset management for the application
class AssetStore {
  static final AssetStore _instance = AssetStore._internal();
  factory AssetStore() => _instance;
  AssetStore._internal();

  // App assets
  static const String appZip = 'app/app.zip';

  // Image assets
  static const String logo = 'assets/images/logo.png';
  static const String pddIcon = 'assets/images/pdd.ico';
  static const String taobaoIcon = 'assets/images/tb.png';
  static const String bilibiliIcon = 'assets/images/bili.ico';
  static const String qqChannel = 'assets/images/qq_channel.jpg';
  static const String qqGroup = 'assets/images/qq_group.jpg';
  static const String wechatGroup = 'assets/images/wechat_group.jpg';

  // Font assets
  static const String hupoFont = 'assets/font/STHUPO.ttf';
  // Anki assets
  static const String ankiPersistenceJs = 'assets/anki/__anki-persistence.js';

  // Asset paths by category
  static const Map<String, String> images = {
    'logo': logo,
    'pdd': pddIcon,
    'taobao': taobaoIcon,
    'bilibili': bilibiliIcon,
    'qq_channel': qqChannel,
    'qq_group': qqGroup,
    'wechat_group': wechatGroup,
  };

  static const Map<String, String> scripts = {
    'ankiPersistence': ankiPersistenceJs,
  };

  /// Get asset path by name
  static String getAsset(String name) {
    if (images.containsKey(name)) return images[name]!;
    if (scripts.containsKey(name)) return scripts[name]!;
    throw Exception('Asset not found: $name');
  }

  /// Get absolute path for asset by name
  /// This will copy the asset to the app's local storage if it doesn't exist
  static Future<String> getAssetAbsolutePath(String name) async {
    final assetPath = getAsset(name);
    if (assetPath.isEmpty) return '';
    try {
      final directory = await getTemporaryDirectory();
      final String localPath = p.join(directory.path, assetPath);
      // Ensure the directory exists
      final Directory dir = Directory(p.dirname(localPath));
      if (!dir.existsSync()) {
        dir.createSync(recursive: true);
      }
      // Copy asset to local storage if it doesn't exist
      final File localFile = File(localPath);
      final ByteData data = await rootBundle.load(assetPath);
      final List<int> bytes = data.buffer.asUint8List();
      await localFile.writeAsBytes(bytes);
      return localPath;
    } catch (e) {
      print('Error getting absolute path for asset $name: $e');
      return '';
    }
  }

  /// Check if asset exists
  static bool hasAsset(String name) {
    return images.containsKey(name) || scripts.containsKey(name);
  }
}
