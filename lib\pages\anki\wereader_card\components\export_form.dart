import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/wereader_card.dart';

class ExportForm extends GetView<WeReaderCardPageController> {
  const ExportForm({super.key});

  @override
  Widget build(context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: 8,
        children: [
          Obx(() => ShadInputWithValidate(
              key: ValueKey(controller.cookie.value),
              label: 'Cookie',
              placeholder: 'anki.wereader_card.cookie_placeholder'.tr,
              initialValue: controller.cookie.value,
              maxLines: 1,
              onChanged: (value) {
                // controller.cookie.value = value;
              },
              onValidate: (value) async {
                return await controller.validateCookie(value);
              })),
          Obx(() => ShadSelectWithSearch(
                label: 'anki.wereader_card.target_book'.tr,
                placeholder: 'anki.wereader_card.select_target_book'.tr,
                searchPlaceholder: 'anki.wereader_card.input_book'.tr,
                initialValue: controller.book.value.isNotEmpty
                    ? [controller.book.value]
                    : null,
                options: controller.bookList
                    .map((e) => {
                          'value': e['value'] as String,
                          'label': e['label'] as String
                        })
                    .toList(),
                onChanged: (value) {
                  controller.book.value = value.single;
                },
              )),
          Obx(() => ShadRadioGroupCustom(
                label: 'anki.wereader_card.note_source'.tr,
                initialValue: controller.exportParams.source.value,
                items: controller.sourceList,
                onChanged: (value) {
                  controller.exportParams.source.value = value;
                },
              )),
          Obx(() => controller.exportParams.source.value == 'hot'
              ? ShadRadioGroupCustom(
                  label: 'anki.wereader_card.sort_method'.tr,
                  initialValue: controller.exportParams.hotSortMethod.value,
                  items: controller.hotSortMethodList,
                  onChanged: (value) {
                    controller.exportParams.hotSortMethod.value = value;
                  },
                )
              : const SizedBox.shrink()),
          Obx(() {
            if (controller.exportParams.source.value == 'my') {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ShadCheckboxGroup(
                    label: 'anki.wereader_card.export_type'.tr,
                    initialValues:
                        controller.exportParams.exportCategory.toList(),
                    items: controller.noteTypeList.toList(),
                    onChanged: (value) {
                      logger.i(value);
                      controller.exportParams.exportCategory.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.wereader_card.at_least_one_export_type'.tr;
                      }
                      return "";
                    },
                    onValidateError: (error) {},
                  ),
                  ShadRadioGroupCustom(
                    label: 'anki.wereader_card.sort_method'.tr,
                    initialValue: controller.exportParams.mySortMethod.value,
                    items: controller.mySortMethodList,
                    onChanged: (value) {
                      controller.exportParams.mySortMethod.value = value;
                    },
                  ),
                ],
              );
            } else {
              return const SizedBox.shrink();
            }
          }),
          Obx(() => ShadRadioGroupCustom(
                label: 'anki.wereader_card.sort_direction'.tr,
                initialValue: controller.exportParams.sortAscending.value
                    ? 'true'
                    : 'false',
                items: controller.sortDirectionList,
                onChanged: (value) {
                  controller.exportParams.sortAscending.value = value == 'true';
                },
              )),
          Obx(() => ShadRadioGroupCustom(
                label: 'anki.wereader_card.export_format'.tr,
                initialValue: controller.exportParams.exportFormat.value,
                items: controller.exportFormatList,
                onChanged: (value) {
                  controller.exportParams.exportFormat.value = value;
                },
              )),
          Obx(() => ShadInputWithFileSelect(
                key: ValueKey(
                    "output-dir-${controller.exportParams.outputDir.value}"),
                title: 'anki.wereader_card.output_directory'.tr,
                placeholder: Text('anki.wereader_card.select_output_directory'.tr),
                initialValue: [controller.exportParams.outputDir.value],
                isFolder: true,
                onFilesSelected: (files) {
                  controller.exportParams.outputDir.value = files.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {},
              )),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
