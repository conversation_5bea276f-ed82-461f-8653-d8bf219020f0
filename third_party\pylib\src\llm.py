import json  
from openai import OpenAI
from PIL import Image
import io
import base64
import os
import csv
import re
from datetime import datetime
from utils import progress_reporter

client = OpenAI(
    api_key="sk-fyzjfrdqhwfpwapjlecuxhqcdekrtwfxrupfltxfltuiywci", # 从https://cloud.siliconflow.cn/account/ak获取
    base_url="https://api.siliconflow.cn/v1"
)

def convert_image_to_webp_base64(input_image_path):
    try:
        with Image.open(input_image_path) as img:
            byte_arr = io.BytesIO()
            img.save(byte_arr, format='webp')
            byte_arr = byte_arr.getvalue()
            base64_str = base64.b64encode(byte_arr).decode('utf-8')
            return base64_str
    except IOError:
        print(f"Error: Unable to open or convert the image {input_image_path}")
        return None

def generate_anki_cards_from_image(image_path, output_dir=None, model="Qwen/Qwen2-VL-72B-Instruct"):
    """
    Extract knowledge points from an image and generate Anki flashcards in CSV format.
    
    Args:
        image_path (str): Path to the image file
        output_dir (str, optional): Directory to save the CSV file. If None, saves in the same directory as the image.
        model (str, optional): LLM model to use for extraction. Defaults to "Qwen/Qwen2-VL-72B-Instruct".
        
    Returns:
        str: Path to the generated CSV file
    """
    progress_reporter("processing", "Converting image to base64...", {"current": 10, "total": 100})
    
    # Convert image to base64
    base64_image = convert_image_to_webp_base64(image_path)
    if base64_image is None:
        progress_reporter("error", f"Failed to convert image: {image_path}", "")
        return None
    
    progress_reporter("processing", "Extracting knowledge points from image...", {"current": 30, "total": 100})
    
    # Create prompt for knowledge extraction
    system_prompt = """You are an expert at extracting key knowledge points from educational content and creating high-quality Anki flashcards.
Extract the most important concepts, definitions, formulas, and facts from the image.
For each knowledge point, create a question-answer pair suitable for an Anki flashcard.
Format your response as a numbered list with clear separation between questions and answers.
Use the format:
1. Question: [question text]
   Answer: [answer text]
2. Question: [question text]
   Answer: [answer text]
And so on."""
    
    try:
        # Call the LLM to extract knowledge points
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}",
                        }
                    },
                    {
                        "type": "text",
                        "text": "Extract the key knowledge points from this image and create Anki flashcards with questions and answers."
                    }
                ]}
            ]
        )
        
        progress_reporter("processing", "Processing LLM response...", {"current": 70, "total": 100})
        
        # Parse the response
        content = response.choices[0].message.content
        
        # Parse the text response to extract question-answer pairs
        cards = []
        lines = content.strip().split('\n')
        current_card = {}
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Check for new card (numbered item)
            if re.match(r'^\d+\.', line):
                # Save previous card if exists
                if current_card and 'front' in current_card and 'back' in current_card:
                    cards.append(current_card)
                current_card = {}
                # Remove the number prefix
                line = re.sub(r'^\d+\.\s*', '', line)
                
            # Look for question
            question_match = re.search(r'^(?:Question:?\s*)(.*)', line, re.IGNORECASE)
            if question_match:
                current_card['front'] = question_match.group(1).strip()
                continue
                
            # Look for answer
            answer_match = re.search(r'^(?:Answer:?\s*)(.*)', line, re.IGNORECASE)
            if answer_match:
                current_card['back'] = answer_match.group(1).strip()
                continue
                
            # If we have a front but no pattern match, it might be continuation text
            if 'front' in current_card and 'back' not in current_card:
                current_card['front'] += " " + line
            elif 'back' in current_card:
                current_card['back'] += " " + line
        
        # Add the last card if it exists
        if current_card and 'front' in current_card and 'back' in current_card:
            cards.append(current_card)
        
        # If no cards were extracted, try a simpler parsing approach
        if not cards:
            # Split by numbered items and try to extract Q&A pairs
            sections = re.split(r'\n\s*\d+\.\s*', '\n' + content)
            for section in sections[1:]:  # Skip the first empty item
                parts = section.split('\n', 1)
                if len(parts) >= 2:
                    front = parts[0].strip()
                    back = parts[1].strip()
                    cards.append({'front': front, 'back': back})
        
        # Determine output path
        if output_dir is None:
            output_dir = os.path.dirname(image_path)
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate CSV filename based on timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        image_name = os.path.splitext(os.path.basename(image_path))[0]
        csv_filename = f"anki_cards_{image_name}_{timestamp}.csv"
        csv_path = os.path.join(output_dir, csv_filename)
        
        # Write to CSV file
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['front', 'back', 'tags', 'source']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for card in cards:
                # Add source information
                card['tags'] = 'auto_generated'
                card['source'] = f"Generated from {os.path.basename(image_path)}"
                writer.writerow(card)
        
        progress_reporter("completed", f"Successfully generated {len(cards)} Anki cards in {csv_path}", csv_path)
        return csv_path
        
    except Exception as e:
        progress_reporter("error", f"Error generating Anki cards: {str(e)}", "")
        return None

def example1():
    response = client.chat.completions.create(
            model="deepseek-ai/DeepSeek-V2.5",
            messages=[
                {"role": "system", "content": "You are a helpful assistant designed to output JSON."},
                {"role": "user", "content": "? 2020 年世界奥运会乒乓球男子和女子单打冠军分别是谁? "
                "Please respond in the format {\"男子冠军\": ..., \"女子冠军\": ...}"}
            ],
            response_format={"type": "json_object"}
        )

    print(response.choices[0].message.content)

def example2():
    # img_path = "/Users/<USER>/Downloads/Snipaste_2025-04-29_20-52-16.png"
    img_path = "/Users/<USER>/Downloads/20240824105154.png"
    base64_image=convert_image_to_webp_base64(img_path)
    response = client.chat.completions.create(
            model="Qwen/Qwen2-VL-72B-Instruct",
            messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}",
                        }
                    },
                    {
                        "type": "text",
                        "text": "识别图片中小红色矩形内的文字"
                    }
                ]
            }],
            stream=True
    )

    for chunk in response:
        chunk_message = chunk.choices[0].delta.content
        print(chunk_message, end='', flush=True)

def generate_anki_cards_batch(image_paths, output_dir=None, model="Qwen/Qwen2-VL-72B-Instruct"):
    """
    Process multiple images and generate Anki flashcards for each one.
    
    Args:
        image_paths (list): List of paths to image files
        output_dir (str, optional): Directory to save the CSV files
        model (str, optional): LLM model to use for extraction
        
    Returns:
        list: Paths to all generated CSV files
    """
    csv_paths = []
    total = len(image_paths)
    
    for i, image_path in enumerate(image_paths):
        progress_reporter("processing", f"Processing image {i+1}/{total}: {os.path.basename(image_path)}", 
                         {"current": i, "total": total})
        
        csv_path = generate_anki_cards_from_image(image_path, output_dir, model)
        if csv_path:
            csv_paths.append(csv_path)
    
    return csv_paths

if __name__ == "__main__":
    # Run examples
    # example1()
    # example2()
    
    # Example of generating Anki cards from an image
    # """
    img_path = "/Users/<USER>/Pictures/Snipaste_2025-03-01_22-10-36.png"
    csv_path = generate_anki_cards_from_image(img_path)
    print(f"Generated Anki cards saved to: {csv_path}")
    # """
    
    # Example of batch processing multiple images
    """
    image_paths = ["/path/to/image1.jpg", "/path/to/image2.png"]
    csv_paths = generate_anki_cards_batch(image_paths, output_dir="/path/to/output")
    print(f"Generated {len(csv_paths)} CSV files with Anki cards")
    """