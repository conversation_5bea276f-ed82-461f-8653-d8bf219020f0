import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';
import 'package:hotkey_manager/hotkey_manager.dart';
import 'package:pasteboard/pasteboard.dart';
import 'package:anki_guru/pages/hotkey.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:dio/dio.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';

class PDFNoteController extends GetxController {
  // 基本数据
  final _storage = StorageManager();
  final linkFormatList = [
    {"label": "anki.pdf_note.formats.markdown".tr, "value": "markdown"},
    {"label": "anki.pdf_note.formats.html".tr, "value": "html"},
    {"label": "anki.pdf_note.formats.url".tr, "value": "url"},
    {"label": "anki.pdf_note.formats.custom".tr, "value": "custom"},
  ];
  final linkProtocolList = [
    {"label": "guru2://", "value": "guru2://"},
    {"label": "http://", "value": "http://"},
  ];
  final hotkeyNames = [
    "insertPathLink",
    "insertPageLink",
    "insertCommentLink",
    "insertBookmarksLink",
    "insertComment",
    "insertNote",
    "insertAllNotes",
    "insertPageScreenshot",
    "insertPageText",
    "ocr",
    "goHome",
    "goEnd",
    "goPrev",
    "goNext",
    "setFlagA",
    "setFlagB",
    "jumpToFlag",
    "clearFlag",
    "scrollUp",
    "scrollDown",
  ];
  final disableAllShortcuts = false.obs;
  final shortcutMap = <String, HotKey?>{}.obs;
  final shortcutHandlerMap = <String, Future<void> Function(HotKey)>{}.obs;
  final autoPaste = true.obs;
  final linkProtocol = "guru2://".obs;
  final linkFormat = "markdown".obs;
  final customLinkFormat = "".obs;
  // 标记点
  final flagA = Rx<int?>(null);
  final flagB = Rx<int?>(null);
  // 控制器
  final tabController = ShadTabsController(value: 'shortcuts');
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final clipboardController = Get.find<ClipboardController>();

  @override
  void onInit() async {
    super.onInit();
    disableAllShortcuts.value =
        _storage.read(StorageBox.pdfNote, "disableAllShortcuts") ?? false;
    linkFormat.value =
        _storage.read(StorageBox.pdfNote, "linkFormat") ?? "markdown";
    customLinkFormat.value =
        _storage.read(StorageBox.pdfNote, "customLinkFormat") ?? "";
    autoPaste.value = _storage.read(StorageBox.pdfNote, "autoPaste") ?? true;
    linkProtocol.value =
        _storage.read(StorageBox.pdfNote, "linkProtocol") ?? "guru2://";

    // 注册快捷键处理函数
    shortcutHandlerMap["insertPathLink"] = (hotKey) async {
      await insertPathLink();
    };
    shortcutHandlerMap["insertPageLink"] = (hotKey) async {
      await insertPageLink();
    };
    shortcutHandlerMap["insertCommentLink"] = (hotKey) async {
      await insertAnnotationLink();
    };
    shortcutHandlerMap["insertBookmarksLink"] = (hotKey) async {
      await insertBookmarkLink();
    };
    shortcutHandlerMap["insertComment"] = (hotKey) async {
      await insertAnnotation();
    };
    shortcutHandlerMap["insertNote"] = (hotKey) async {
      await insertNote();
    };
    shortcutHandlerMap["insertAllNotes"] = (hotKey) async {
      await insertAllNotes();
    };
    shortcutHandlerMap["insertPageScreenshot"] = (hotKey) async {
      await insertPageSnapshot();
    };
    shortcutHandlerMap["insertPageText"] = (hotKey) async {
      await extractPageText();
    };
    shortcutHandlerMap["ocr"] = (hotKey) async {
      await ocrImage();
    };
    shortcutHandlerMap["goHome"] = (hotKey) async {
      await goHome();
    };
    shortcutHandlerMap["goEnd"] = (hotKey) async {
      await goEnd();
    };
    shortcutHandlerMap["goPrev"] = (hotKey) async {
      await goPrev();
    };
    shortcutHandlerMap["goNext"] = (hotKey) async {
      await goNext();
    };
    shortcutHandlerMap["setFlagA"] = (hotKey) async {
      await setFlagA();
    };
    shortcutHandlerMap["setFlagB"] = (hotKey) async {
      await setFlagB();
    };
    shortcutHandlerMap["jumpToFlag"] = (hotKey) async {
      await jumpToFlag();
    };
    shortcutHandlerMap["clearFlag"] = (hotKey) async {
      await clearFlag();
    };
    shortcutHandlerMap["scrollUp"] = (hotKey) async {
      await scrollUp();
    };
    shortcutHandlerMap["scrollDown"] = (hotKey) async {
      await scrollDown();
    };

    // 初始化快捷键
    await initHotKey();
  }

  Future<void> initHotKey() async {
    if (!disableAllShortcuts.value) {
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        await initAllShortcutsHandler();
      }
    }
  }

  Future<void> disableAllShortcutsHandler() async {
    if (Platform.isAndroid || Platform.isIOS) {
      return;
    }
    _storage.write(
      StorageBox.pdfNote,
      "disableAllShortcuts",
      disableAllShortcuts.value,
    );
    if (disableAllShortcuts.value) {
      await hotKeyManager.unregisterAll();
      showToastNotification(null, 'anki.pdf_note.notifications.shortcuts_disabled'.tr, '', type: "success");
    } else {
      await initAllShortcutsHandler();
      showToastNotification(null, 'anki.pdf_note.notifications.shortcuts_enabled'.tr, '', type: "success");
    }
  }

  Future<void> initAllShortcutsHandler() async {
    for (var name in hotkeyNames) {
      try {
        final hotKey = _storage.read(StorageBox.pdfNote, name);
        // logger.i("name: $name, hotKey: $hotKey");
        if (hotKey != null) {
          shortcutMap[name] = HotKey.fromJson(jsonDecode(hotKey));
        } else {
          shortcutMap[name] = null;
          HotKey? hotKey;
          switch (name) {
            case "insertPathLink":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyP,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "insertPageLink":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyT,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "insertBookmarksLink":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyO,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "insertCommentLink":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyN,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "insertComment":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyM,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "insertPageScreenshot":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyS,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "insertPageText":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyX,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "insertNote":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyI,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "insertAllNotes":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyU,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "ocr":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyA,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "goHome":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyH,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "goEnd":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyL,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "goPrev":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyJ,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "goNext":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyK,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "setFlagA":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.bracketLeft,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "setFlagB":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.bracketRight,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "jumpToFlag":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyF,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "clearFlag":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyC,
                modifiers: [HotKeyModifier.meta, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "scrollUp":
              // 不设置默认快捷键
              break;
            case "scrollDown":
              // 不设置默认快捷键
              break;
          }
          if (hotKey != null) {
            _storage.write(
              StorageBox.pdfNote,
              name,
              jsonEncode(hotKey.toJson()),
            );
          }
        }
        if (shortcutMap.containsKey(name) &&
            shortcutHandlerMap.containsKey(name)) {
          registerHotKey(name, shortcutMap[name], shortcutHandlerMap[name]!);
        }
      } catch (e) {
        logger.e(e.toString());
      }
    }
  }

  // 修改热键注册的通用方法
  Future<void> registerHotKey(
    String name,
    HotKey? newHotKey,
    Future<void> Function(HotKey) handler,
  ) async {
    if (Platform.isAndroid || Platform.isIOS) {
      return;
    }
    if (newHotKey != null) {
      // 取消注册旧的热键
      if (shortcutMap.containsKey(name) && shortcutMap[name] != null) {
        // 创建一个副本来避免并发修改错误
        final registeredHotKeyList = [...hotKeyManager.registeredHotKeyList];
        List<HotKey> hotKeysToUnregister = [];
        for (var hotKey in registeredHotKeyList) {
          if (hotKey.identifier == shortcutMap[name]!.identifier) {
            hotKeysToUnregister.add(hotKey);
          }
        }
        for (var hotKey in hotKeysToUnregister) {
          await hotKeyManager.unregister(hotKey);
        }
      }
      shortcutMap[name] = newHotKey;
      await hotKeyManager.register(
        newHotKey,
        keyDownHandler: (hotKey) => handler(hotKey),
      );
      // 保存热键设置
      _storage.write(
        StorageBox.pdfNote,
        name,
        jsonEncode(newHotKey.toJson()),
      );
    } else {
      shortcutMap[name] = null;
      _storage.remove(StorageBox.pdfNote, name);
    }
  }

  void showPasteHotKeyRecorder(BuildContext context, String name) async {
    final hotKey = await HotKeyInputDialog.show(
      context,
      initialHotKey: shortcutMap[name],
    );

    if (hotKey != null) {
      registerHotKey(name, hotKey, shortcutHandlerMap[name]!);
    }
  }

  // 获取人类可读的快捷键字符串
  String getHotKeyDisplayText(HotKey? hotKey) {
    if (hotKey == null) return 'anki.pdf_note.shortcuts.not_set'.tr;
    final modifierNames = (hotKey.modifiers ?? []).map((m) {
      switch (m) {
        case HotKeyModifier.alt:
          return 'Alt';
        case HotKeyModifier.control:
          return 'Ctrl';
        case HotKeyModifier.meta:
          if (Platform.isMacOS) {
            return 'Cmd';
          } else {
            return 'Win';
          }
        case HotKeyModifier.shift:
          return 'Shift';
        case HotKeyModifier.capsLock:
          return 'CapsLock';
        case HotKeyModifier.fn:
          return 'Fn';
        default:
          return '';
      }
    }).where((name) => name.isNotEmpty);

    // 获取主键名称（去掉"Key "前缀）
    String keyName = keyNames[hotKey.physicalKey.usbHidUsage] ?? '';
    if (keyName.startsWith('Key ')) {
      keyName = keyName.substring(4);
    }
    if (keyName.startsWith('Digit ')) {
      keyName = keyName.substring(6);
    }

    // 组合所有部分
    final parts = [...modifierNames, keyName];
    return parts.join(' + ');
  }

  Future<void> insertPathLink() async {
    final dio = Dio();
    try {
      // 获取Zotero PDF信息
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (response.data['status'] != 'success') {
        throw Exception('anki.pdf_note.errors.get_pdf_info_failed'.tr);
      }

      final info = response.data['data'];

      // 构建基础URL
      var url = 'zotero://open-pdf/library/items/${info['item_key']}?page=1';
      if (linkProtocol.value == 'http://') {
        final serverPort = settingController.serverPort.value;
        final guruUrl = Uri.parse('guru2://seek').replace(
            queryParameters: {'category': 'zotero', 'path': url}).toString();
        url = Uri.http('localhost:$serverPort', '/guru_jump',
            {'url': Uri.encodeComponent(guruUrl)}).toString();
      }

      final title = '${info['title']}';
      String link = "";

      switch (linkFormat.value) {
        case 'html':
          link = '<a href="$url">$title</a>';
          await clipboardController.copyHtml(link);
          break;
        case 'markdown':
          link = '[$title]($url)';
          await clipboardController.copyText(link);
          break;
        case 'url':
          await clipboardController.copyText(url);
          break;
        case 'custom':
          final template = customLinkFormat.value;
          // 直接替换模板中的变量，避免循环替换
          link = template
              .replaceAll(r'$name', title)
              .replaceAll(r'$url', url)
              .replaceAll(r'$page', '0');
          await clipboardController.copyText(link);
          break;
        default:
          throw Exception('anki.pdf_note.errors.unsupported_link_format'.tr);
      }
      logger.d('已复制链接: $link');
      if (autoPaste.value) {
        await AnkiConnectController().simulatePaste();
      }
      showLocalNotification('anki.pdf_note.actions.success'.tr, 'anki.pdf_note.notifications.link_copied_success'.tr);
    } catch (e) {
      logger.e('插入路径链接失败: $e');
      showLocalNotification('anki.pdf_note.actions.failed'.tr, 'anki.pdf_note.notifications.link_copy_failed'.tr);
    }
  }

  Future<void> insertPageLink() async {
    final dio = Dio();
    try {
      // 获取Zotero PDF信息
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (response.data['status'] != 'success') {
        throw Exception('anki.pdf_note.errors.get_pdf_info_failed'.tr);
      }

      final info = response.data['data'];

      // 构建基础URL
      var url =
          'zotero://open-pdf/library/items/${info['item_key']}?page=${info['page']}';
      if (linkProtocol.value == 'http://') {
        final serverPort = settingController.serverPort.value;
        final guruUrl = Uri.parse('guru2://seek').replace(
            queryParameters: {'category': 'zotero', 'path': url}).toString();
        url = Uri.http('localhost:$serverPort', '/guru_jump',
            {'url': Uri.encodeComponent(guruUrl)}).toString();
      }

      final title = '${info['title']} (p${info['page']})';
      String link = "";

      switch (linkFormat.value) {
        case 'html':
          link = '<a href="$url">$title</a>';
          await clipboardController.copyHtml(link);
          break;
        case 'markdown':
          link = '[$title]($url)';
          await clipboardController.copyText(link);
          break;
        case 'url':
          await clipboardController.copyText(url);
          break;
        case 'custom':
          final template = customLinkFormat.value;
          link = template
              .replaceAll('\$name', title)
              .replaceAll('\$url', url)
              .replaceAll('\$page', '0');
          await clipboardController.copyText(link);
          break;
        default:
          throw Exception('anki.pdf_note.errors.unsupported_link_format'.tr);
      }
      logger.d('已复制链接: $link');
      if (autoPaste.value) {
        await AnkiConnectController().simulatePaste();
      }
      showLocalNotification('anki.pdf_note.actions.success'.tr, 'anki.pdf_note.notifications.link_copied_success'.tr);
    } catch (e) {
      logger.e('插入路径链接失败: $e');
      showLocalNotification('anki.pdf_note.actions.failed'.tr, 'anki.pdf_note.notifications.link_copy_failed'.tr);
    }
  }

  Future<void> insertAnnotationLink() async {
    final dio = Dio();
    try {
      // 获取Zotero PDF信息
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (response.data['status'] != 'success') {
        throw Exception('anki.pdf_note.errors.get_pdf_info_failed'.tr);
      }
      final annotResponse =
          await dio.get("http://127.0.0.1:23119/guru/get_selected_annotation");
      if (annotResponse.data['status'] != 'success' ||
          !annotResponse.data.containsKey('data')) {
        throw Exception('anki.pdf_note.errors.get_selected_annotation_failed'.tr);
      }
      final annotInfo = annotResponse.data['data'];
      final info = response.data['data'];
      logger.d('annotInfo: $annotInfo');
      // 构建基础URL
      final annotation_id = annotInfo['id'];
      var url =
          'zotero://open-pdf/library/items/${info['item_key']}?annotation=${annotation_id}';
      if (linkProtocol.value == 'http://') {
        final serverPort = settingController.serverPort.value;
        final guruUrl = Uri.parse('guru2://seek').replace(
            queryParameters: {'category': 'zotero', 'path': url}).toString();
        url = Uri.http('localhost:$serverPort', '/guru_jump',
            {'url': Uri.encodeComponent(guruUrl)}).toString();
      }

      final title = '${info['title']} (p${info['page']})';
      String link = "";

      switch (linkFormat.value) {
        case 'html':
          link = '<a href="$url">$title</a>';
          await clipboardController.copyHtml(link);
          break;
        case 'markdown':
          link = '[$title]($url)';
          await clipboardController.copyText(link);
          break;
        case 'url':
          await clipboardController.copyText(url);
          break;
        case 'custom':
          final template = customLinkFormat.value;
          link = template
              .replaceAll('\$name', title)
              .replaceAll('\$url', url)
              .replaceAll('\$page', '0');
          await clipboardController.copyText(link);
          break;
        default:
          throw Exception('anki.pdf_note.errors.unsupported_link_format'.tr);
      }
      logger.d('已复制链接: $link');
      if (autoPaste.value) {
        await AnkiConnectController().simulatePaste();
      }
      showLocalNotification('anki.pdf_note.actions.success'.tr, 'anki.pdf_note.notifications.link_copied_success'.tr);
    } catch (e) {
      logger.e('插入路径链接失败: $e');
      showLocalNotification('anki.pdf_note.actions.failed'.tr, 'anki.pdf_note.notifications.link_copy_failed'.tr);
    }
  }

  Future<void> insertBookmarkLink() async {
    final dio = Dio();
    try {
      // 获取Zotero PDF信息
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (response.data['status'] != 'success') {
        throw Exception('anki.pdf_note.errors.get_pdf_info_failed'.tr);
      }
      final info = response.data['data'];

      // 获取PDF文件路径
      final pdfPath = info['attachment_path'];
      if (pdfPath == null) {
        throw Exception('anki.pdf_note.errors.pdf_path_empty'.tr);
      }

      // 加载PDF文件
      final PdfDocument document =
          PdfDocument(inputBytes: await File(pdfPath).readAsBytes());

      // 获取所有书签
      final bookmarks = document.bookmarks;
      if (bookmarks == null || bookmarks.count == 0) {
        showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.errors.no_bookmarks_in_pdf".tr);
        return;
      }

      // 构建书签链接列表
      final List<String> bookmarkLinks = [];
      for (int i = 0; i < bookmarks.count; i++) {
        final bookmark = bookmarks[i];
        // 获取书签目标页码
        final pageNumber = bookmark.destination?.page != null
            ? document.pages.indexOf(bookmark.destination!.page!) + 1
            : 1;

        // 构建基础URL
        var url =
            'zotero://open-pdf/library/items/${info['item_key']}?page=$pageNumber';
        if (linkProtocol.value == 'http://') {
          final serverPort = settingController.serverPort.value;
          final guruUrl = Uri.parse('guru2://seek').replace(
              queryParameters: {'category': 'zotero', 'path': url}).toString();
          url = Uri.http('localhost:$serverPort', '/guru_jump',
              {'url': Uri.encodeComponent(guruUrl)}).toString();
        }

        final title = '${bookmark.title} (p$pageNumber)';
        String link = "";

        switch (linkFormat.value) {
          case 'html':
            link = '<a href="$url">$title</a>';
            break;
          case 'markdown':
            link = '[$title]($url)';
            break;
          case 'url':
            link = url;
            break;
          case 'custom':
            final template = customLinkFormat.value;
            link = template
                .replaceAll('\$name', title)
                .replaceAll('\$url', url)
                .replaceAll('\$page', pageNumber.toString());
            break;
          default:
            throw Exception('anki.pdf_note.errors.unsupported_link_format'.tr);
        }
        bookmarkLinks.add(link);
      }

      // 将所有书签链接合并为一个字符串
      final allLinks = bookmarkLinks.join('\n');

      // 复制到剪贴板
      await clipboardController.copyText(allLinks);
      logger.d('已复制书签链接: $allLinks');

      if (autoPaste.value) {
        await AnkiConnectController().simulatePaste();
      }
      showLocalNotification('anki.pdf_note.actions.success'.tr, 'anki.pdf_note.notifications.bookmark_links_copied_success'.tr);

      // 释放PDF文档
      document.dispose();
    } catch (e) {
      logger.e('插入书签链接失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.bookmark_links_copy_failed".tr);
    }
  }

  Future<void> insertAnnotation() async {
    final dio = Dio();
    try {
      // 获取选中的注释
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_selected_annotation");
      if (response.data['status'] != 'success' ||
          !response.data.containsKey('data')) {
        throw Exception('anki.pdf_note.errors.no_selected_annotation'.tr);
      }

      final annotationData = response.data['data'];
      if (annotationData['type'] == 'image') {
        // 处理图片类型的注释
        final base64Image = annotationData['image'];
        if (base64Image == null) {
          throw Exception('anki.pdf_note.errors.image_data_empty'.tr);
        }

        // 移除Base64图片数据的前缀
        final cleanBase64 =
            base64Image.replaceFirst(RegExp(r'data:image/\w+;base64,'), '');

        // 解码Base64图片数据
        final imageBytes = base64Decode(cleanBase64);

        // 复制图片到剪贴板
        await clipboardController.copyImage(imageBytes, "png");
        logger.d('已复制图片注释到剪贴板');
      } else {
        // 处理文本类型的注释
        final text = annotationData['text'];
        if (text == null) {
          throw Exception('anki.pdf_note.errors.text_data_empty'.tr);
        }

        // 复制文本到剪贴板
        await clipboardController.copyText(text);
        logger.d('已复制文本注释到剪贴板');
      }

      if (autoPaste.value) {
        await AnkiConnectController().simulatePaste();
      }
      showLocalNotification('anki.pdf_note.actions.success'.tr, 'anki.pdf_note.notifications.annotation_copied_success'.tr);
    } catch (e) {
      logger.e('插入注释失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.annotation_copy_failed".tr);
    }
  }

  Future<void> insertNote() async {
    final dio = Dio();
    try {
      // Get PDF info
      final pdfResponse =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (pdfResponse.data['status'] != 'success') {
        throw Exception('anki.pdf_note.errors.get_pdf_info_failed'.tr);
      }
      final info = pdfResponse.data['data'];
      final itemKey = info['item_key'];

      // Get selected annotation
      final annotResponse =
          await dio.get("http://127.0.0.1:23119/guru/get_selected_annotation");
      if (annotResponse.data['status'] != 'success' ||
          !annotResponse.data.containsKey('data')) {
        throw Exception('anki.pdf_note.errors.get_selected_annotation_failed'.tr);
      }
      final annot = annotResponse.data['data'];
      logger.d('annot: $annot');

      // Process annotation content
      String comment = annot['comment'] ?? '';
      String header = '';
      String footer = '';
      String body = '';

      // Handle body content based on annotation type
      if (annot['type'] == 'image') {
        body = 'anki.pdf_note.content.image'.tr;
      } else {
        body = annot['text'] ?? '';
      }

      // Add source link to body
      final url =
          'zotero://open-pdf/library/items/$itemKey?annotation=${annot['id']}';
      body = '$body ${'anki.pdf_note.content.view_original'.tr}($url)';

      // Process comment content
      final sep = '---';
      if (RegExp(r'^[~～]$', multiLine: true).hasMatch(comment)) {
        comment = comment.replaceAll(RegExp(r'^[~～]$', multiLine: true), '');
        header = comment;
      } else {
        final parts = comment.split(RegExp('^$sep\$', multiLine: true));
        if (parts.length > 1) {
          header = parts[0];
          footer = parts.sublist(1).join('\n');
        } else {
          footer = comment;
        }
      }

      // Format final content
      final content = '$header\n> $body\n\n$footer';
      logger.d('content: $content');

      // Copy to clipboard
      await clipboardController.copyText(content);
      if (autoPaste.value) {
        await AnkiConnectController().simulatePaste();
      }
      showLocalNotification('anki.pdf_note.actions.success'.tr, 'anki.pdf_note.notifications.note_copied_success'.tr);
    } catch (e) {
      logger.e('插入笔记失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.note_copy_failed".tr);
    }
  }

  Future<void> insertAllNotes() async {
    final dio = Dio();
    try {
      // Get PDF info
      final pdfResponse =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (pdfResponse.data['status'] != 'success') {
        throw Exception('anki.pdf_note.errors.get_pdf_info_failed'.tr);
      }
      final info = pdfResponse.data['data'];
      logger.d('info: $info');
      final itemId = info['item_id'];

      // Get all annotations
      final annotResponse = await dio.post(
        "http://127.0.0.1:23119/guru/get_annotations_by_id",
        data: {"id": itemId},
      );
      if (annotResponse.data['status'] != 'success') {
        throw Exception('anki.pdf_note.errors.get_item_notes_failed'.tr);
      }
      final allAnnotations = annotResponse.data['data'];

      // Process each annotation
      final List<String> result = [];
      final sep = '---';
      for (final annot in allAnnotations) {
        String comment = annot['annotationComment'] ?? '';
        String header = '';
        String footer = '';
        String body = '';

        // Handle body content based on annotation type
        if (annot['annotationType'] == 'image') {
          body = 'anki.pdf_note.content.image'.tr;
        } else {
          body = annot['annotationText'] ?? '';
        }

        // Add source link to body
        final url =
            'zotero://open-pdf/library/items/${info['item_key']}?annotation=${annot['key']}';
        body = '$body ${'anki.pdf_note.content.view_original'.tr}($url)';

        // Process comment content
        if (RegExp(r'^[~～]$', multiLine: true).hasMatch(comment)) {
          comment = comment.replaceAll(RegExp(r'^[~～]$', multiLine: true), '');
          header = comment;
        } else {
          final parts = comment.split(RegExp('^$sep\$', multiLine: true));
          if (parts.length > 1) {
            header = parts[0];
            footer = parts.sublist(1).join('\n');
          } else {
            footer = comment;
          }
        }

        // Format content for this annotation
        final content = '$header\n> $body\n\n$footer';
        result.add(content);
      }

      // Join all annotations with double newlines
      final finalContent = result.join('\n\n');

      // Copy to clipboard
      await clipboardController.copyText(finalContent);
      if (autoPaste.value) {
        await AnkiConnectController().simulatePaste();
      }
      showLocalNotification('anki.pdf_note.actions.success'.tr, 'anki.pdf_note.notifications.all_notes_copied_success'.tr);
    } catch (e) {
      logger.e('插入所有笔记失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.all_notes_copy_failed".tr);
    }
  }

  Future<void> insertPageSnapshot() async {
    final dio = Dio();
    try {
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (response.data['status'] != 'success') {
        throw Exception('anki.pdf_note.errors.get_pdf_info_failed'.tr);
      }
      // TODO: 实现页面截图功能
      showLocalNotification("anki.pdf_note.actions.in_development".tr, "anki.pdf_note.notifications.page_screenshot_in_development".tr);
    } catch (e) {
      logger.e('插入页面截图失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.page_screenshot_failed".tr);
    }
  }

  Future<void> extractPageText() async {
    final dio = Dio();
    try {
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (response.data['status'] != 'success') {
        throw Exception('anki.pdf_note.errors.get_pdf_info_failed'.tr);
      }
      // TODO: 实现页面文本提取功能
      showLocalNotification("anki.pdf_note.actions.in_development".tr, "anki.pdf_note.notifications.page_text_extract_in_development".tr);
    } catch (e) {
      logger.e('提取页面文本失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.page_text_extract_failed".tr);
    }
  }

  Future<void> ocrImage() async {
    try {
      // TODO: 实现OCR功能
      showLocalNotification("anki.pdf_note.actions.in_development".tr, "anki.pdf_note.notifications.ocr_in_development".tr);
    } catch (e) {
      logger.e('OCR失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.ocr_failed".tr);
    }
  }

  Future<void> goHome() async {
    final dio = Dio();
    try {
      await dio.get("http://127.0.0.1:23119/guru/pdf_jump?page=first");
    } catch (e) {
      logger.e('跳转到首页失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.go_home_failed".tr);
    }
  }

  Future<void> goEnd() async {
    final dio = Dio();
    try {
      await dio.get("http://127.0.0.1:23119/guru/pdf_jump?page=last");
    } catch (e) {
      logger.e('跳转到末页失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.go_end_failed".tr);
    }
  }

  Future<void> goPrev() async {
    final dio = Dio();
    try {
      await dio.get("http://127.0.0.1:23119/guru/pdf_jump?page=prev");
    } catch (e) {
      logger.e('跳转到上一页失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.go_prev_failed".tr);
    }
  }

  Future<void> goNext() async {
    final dio = Dio();
    try {
      await dio.get("http://127.0.0.1:23119/guru/pdf_jump?page=next");
    } catch (e) {
      logger.e('跳转到下一页失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.go_next_failed".tr);
    }
  }

  Future<void> setFlagA() async {
    final dio = Dio();
    try {
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (response.data['status'] == 'success') {
        flagA.value = response.data['data']['page'];
        showLocalNotification("anki.pdf_note.actions.success".tr, "anki.pdf_note.notifications.flag_a_set_success".tr);
      }
    } catch (e) {
      logger.e('设置标记点A失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.flag_a_set_failed".tr);
    }
  }

  Future<void> setFlagB() async {
    final dio = Dio();
    try {
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (response.data['status'] == 'success') {
        flagB.value = response.data['data']['page'];
        showLocalNotification("anki.pdf_note.actions.success".tr, "anki.pdf_note.notifications.flag_b_set_success".tr);
      }
    } catch (e) {
      logger.e('设置标记点B失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.flag_b_set_failed".tr);
    }
  }

  Future<void> jumpToFlag() async {
    final dio = Dio();
    try {
      final response =
          await dio.get("http://127.0.0.1:23119/guru/get_pdf_info");
      if (response.data['status'] == 'success') {
        final currentPage = response.data['data']['page'];
        if (currentPage == flagA.value && flagB.value != null) {
          await dio
              .get("http://127.0.0.1:23119/guru/pdf_jump?page=${flagB.value}");
        } else if (currentPage == flagB.value && flagA.value != null) {
          await dio
              .get("http://127.0.0.1:23119/guru/pdf_jump?page=${flagA.value}");
        } else if (flagA.value != null) {
          await dio
              .get("http://127.0.0.1:23119/guru/pdf_jump?page=${flagA.value}");
        } else if (flagB.value != null) {
          await dio
              .get("http://127.0.0.1:23119/guru/pdf_jump?page=${flagB.value}");
        } else {
          showLocalNotification("anki.pdf_note.actions.hint".tr, "anki.pdf_note.notifications.please_set_flag_first".tr);
        }
      }
    } catch (e) {
      logger.e('标记点跳转失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.flag_jump_failed".tr);
    }
  }

  Future<void> clearFlag() async {
    flagA.value = null;
    flagB.value = null;
    showLocalNotification("anki.pdf_note.actions.success".tr, "anki.pdf_note.notifications.flag_cleared_success".tr);
  }

  Future<void> scrollUp() async {
    final dio = Dio();
    try {
      await dio.get("http://127.0.0.1:23119/guru/scroll?delta=20");
    } catch (e) {
      logger.e('向上滚动失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.scroll_up_failed".tr);
    }
  }

  Future<void> scrollDown() async {
    final dio = Dio();
    try {
      await dio.get("http://127.0.0.1:23119/guru/scroll_down?delta=-20");
    } catch (e) {
      logger.e('向下滚动失败: $e');
      showLocalNotification("anki.pdf_note.actions.failed".tr, "anki.pdf_note.notifications.scroll_down_failed".tr);
    }
  }

  void submit(BuildContext context) async {
    if (tabController.selected == 'config') {
      if (linkFormat.value == "custom") {
        if (customLinkFormat.value.isEmpty) {
          showToastNotification(context, 'anki.pdf_note.actions.failed'.tr, 'anki.pdf_note.errors.custom_link_format_empty'.tr, type: "error");
          return;
        }
      }
      _storage.write(
        StorageBox.pdfNote,
        "linkFormat",
        linkFormat.value,
      );
      _storage.write(
        StorageBox.pdfNote,
        "customLinkFormat",
        customLinkFormat.value,
      );
      _storage.write(
        StorageBox.pdfNote,
        "autoPaste",
        autoPaste.value,
      );
      _storage.write(
        StorageBox.pdfNote,
        "linkProtocol",
        linkProtocol.value,
      );
      showToastNotification(context, 'anki.pdf_note.notifications.settings_saved_success'.tr, '', type: "success");
    }
  }
}
