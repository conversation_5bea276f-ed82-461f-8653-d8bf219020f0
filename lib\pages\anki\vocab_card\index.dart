import 'package:anki_guru/pages/anki/vocab_card/components/txt_form.dart';
import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/pdf_form.dart';
import 'components/dict_form.dart';
import 'package:anki_guru/controllers/anki/vocab_card.dart';
import 'package:anki_guru/pages/common.dart';

class VocabCardPage extends StatefulWidget {
  const VocabCardPage({super.key});

  @override
  State<VocabCardPage> createState() => _VocabCardPageState();
}

class _VocabCardPageState extends State<VocabCardPage> {
  final controller = Get.put(VocabCardPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('单词制卡', style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('功能说明', style: defaultPageTitleStyle),
                Text('支持欧陆词典、PDF、TXT等多种形式单词制卡', style: theme.textTheme.muted),
                const SizedBox(height: 16),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 3;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        print(controller.tabController.selected);
                      },
                      tabs: [
                        ShadTab(
                          value: 'dict',
                          content: const DictForm(),
                          width: tabWidth,
                          child: const Text('词典制卡'),
                        ),
                        ShadTab(
                          value: 'pdf',
                          content: const PDFForm(),
                          width: tabWidth,
                          child: const Text('PDF制卡'),
                        ),
                        ShadTab(
                          value: 'txt',
                          content: const TXTForm(),
                          width: tabWidth,
                          child: const Text('TXT制卡'),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
