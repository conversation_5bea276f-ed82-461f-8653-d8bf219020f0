#![allow(unused)]

// 在此文件之外，您需要有一个 DocxError 的定义。
use crate::DocxError;
use once_cell::sync::Lazy;
use quick_xml::events::{BytesStart, Event};
use quick_xml::Reader;
use std::collections::HashMap;

// --- 静态映射表 (与上一版完全相同) ---
static SYMBOL_MAP: Lazy<HashMap<&'static str, &'static str>> = Lazy::new(|| {
    HashMap::from([
        ("α", "\\alpha"),
        ("β", "\\beta"),
        ("γ", "\\gamma"),
        ("δ", "\\delta"),
        ("ε", "\\varepsilon"),
        ("ζ", "\\zeta"),
        ("η", "\\eta"),
        ("θ", "\\theta"),
        ("ι", "\\iota"),
        ("κ", "\\kappa"),
        ("λ", "\\lambda"),
        ("μ", "\\mu"),
        ("ν", "\\nu"),
        ("ξ", "\\xi"),
        ("π", "\\pi"),
        ("ρ", "\\rho"),
        ("σ", "\\sigma"),
        ("τ", "\\tau"),
        ("υ", "\\upsilon"),
        ("φ", "\\phi"),
        ("χ", "\\chi"),
        ("ψ", "\\psi"),
        ("ω", "\\omega"),
        ("Α", "A"),
        ("Β", "B"),
        ("Γ", "\\Gamma"),
        ("Δ", "\\Delta"),
        ("Θ", "\\Theta"),
        ("Λ", "\\Lambda"),
        ("Ξ", "\\Xi"),
        ("Π", "\\Pi"),
        ("Σ", "\\Sigma"),
        ("Υ", "\\Upsilon"),
        ("Φ", "\\Phi"),
        ("Ψ", "\\Psi"),
        ("Ω", "\\Omega"),
        ("±", "\\pm"),
        ("∓", "\\mp"),
        ("×", "\\times"),
        ("÷", "\\div"),
        ("⋅", "\\cdot"),
        ("=", "="),
        ("≠", "\\neq"),
        ("≈", "\\approx"),
        ("≡", "\\equiv"),
        ("≤", "\\leq"),
        ("≥", "\\geq"),
        ("<", "\\lt"),
        (">", "\\gt"),
        ("∞", "\\infty"),
        ("∂", "\\partial"),
        ("∫", "\\int"),
        ("∑", "\\sum"),
        ("∏", "\\prod"),
        ("√", "\\sqrt"),
        ("∛", "\\sqrt[3]"),
        ("∜", "\\sqrt[4]"),
        ("∈", "\\in"),
        ("∉", "\\notin"),
        ("⊂", "\\subset"),
        ("⊃", "\\supset"),
        ("⊆", "\\subseteq"),
        ("⊇", "\\supseteq"),
        ("∧", "\\wedge"),
        ("∨", "\\vee"),
        ("¬", "\\neg"),
        ("∀", "\\forall"),
        ("∃", "\\exists"),
        ("∄", "\\nexists"),
        ("∴", "\\therefore"),
        ("∵", "\\because"),
        ("…", "\\ldots"),
        ("→", "\\to"),
        ("−", "-"),
    ])
});
static FUNCTION_MAP: Lazy<HashMap<&'static str, &'static str>> = Lazy::new(|| {
    HashMap::from([
        ("sin", "\\sin"),
        ("cos", "\\cos"),
        ("tan", "\\tan"),
        ("cot", "\\cot"),
        ("sec", "\\sec"),
        ("csc", "\\csc"),
        ("arcsin", "\\arcsin"),
        ("arccos", "\\arccos"),
        ("arctan", "\\arctan"),
        ("sinh", "\\sinh"),
        ("cosh", "\\cosh"),
        ("tanh", "\\tanh"),
        ("log", "\\log"),
        ("ln", "\\ln"),
        ("exp", "\\exp"),
        ("lim", "\\lim"),
        ("det", "\\det"),
        ("max", "\\max"),
        ("min", "\\min"),
    ])
});
static DELIMITER_MAP: Lazy<HashMap<&'static str, &'static str>> = Lazy::new(|| {
    HashMap::from([
        ("(", "("),
        (")", ")"),
        ("[", "["),
        ("]", "]"),
        ("{", "\\{"),
        ("}", "\\}"),
        ("|", "|"),
    ])
});

pub struct FormulaConverter;

impl FormulaConverter {
    pub fn new() -> Self {
        FormulaConverter
    }

    /// 将OMML格式的公式转换为标准的LaTeX格式。
    /// 这个函数现在总是返回一个标准的、包含单个反斜杠的LaTeX字符串。
    pub fn convert(&self, omml: &str) -> Result<String, DocxError> {
        // 1. 预处理
        let mt_tag_re = regex::Regex::new(r"(?s)<m:t>(.*?)</m:t>").unwrap();
        let well_formed_omml = mt_tag_re.replace_all(omml, |caps: &regex::Captures| {
            let content = &caps[1];
            let escaped_content = content.replace('<', "<").replace('>', ">");
            format!("<m:t>{}</m:t>", escaped_content)
        });

        // 2. 解析和转换
        let mut reader = Reader::from_str(&well_formed_omml);
        reader.trim_text(true);
        let mut root = FormulaNode::new(NodeType::Root);
        self.build_tree(&mut reader, &mut root, None)?;

        // 3. 直接返回标准的LaTeX结果
        let latex = self.convert_tree_to_latex(&root);
        Ok(latex)
    }

    fn build_tree(
        &self,
        reader: &mut Reader<&[u8]>,
        parent: &mut FormulaNode,
        until_tag: Option<&[u8]>,
    ) -> Result<(), DocxError> {
        let mut buf = Vec::new();
        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) => {
                    let mut node = self.create_node_from_tag(&e, reader);
                    self.build_tree(reader, &mut node, Some(e.name().as_ref()))?;
                    parent.add_child(node);
                }
                Event::Empty(e) => {
                    parent.add_child(self.create_node_from_tag(&e, reader));
                }
                Event::Text(e) => {
                    let text = e.unescape()?.to_string();
                    let decoded_text = html_escape::decode_html_entities(&text).to_string();
                    if !decoded_text.trim().is_empty() {
                        parent.add_child(FormulaNode::new_with_text(NodeType::Text, &decoded_text));
                    }
                }
                Event::End(e) => {
                    if Some(e.name().as_ref()) == until_tag {
                        break;
                    }
                }
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }
        Ok(())
    }

    fn create_node_from_tag(&self, e: &BytesStart, reader: &Reader<&[u8]>) -> FormulaNode {
        let mut node = match e.name().as_ref() {
            b"m:oMathPara" => FormulaNode::new(NodeType::OMathPara),
            b"m:oMath" => FormulaNode::new(NodeType::OMath),
            b"m:f" => FormulaNode::new(NodeType::Fraction),
            b"m:fPr" => FormulaNode::new(NodeType::FractionProperties),
            b"m:type" => FormulaNode::new(NodeType::Type),
            b"m:num" => FormulaNode::new(NodeType::Numerator),
            b"m:den" => FormulaNode::new(NodeType::Denominator),
            b"m:sSup" => FormulaNode::new(NodeType::Superscript),
            b"m:sSub" => FormulaNode::new(NodeType::Subscript),
            b"m:sSubSup" => FormulaNode::new(NodeType::SubSuperscript),
            b"m:e" => FormulaNode::new(NodeType::Base),
            b"m:sup" => FormulaNode::new(NodeType::Exponent),
            b"m:sub" => FormulaNode::new(NodeType::SubscriptValue),
            b"m:rad" => FormulaNode::new(NodeType::Radical),
            b"m:deg" => FormulaNode::new(NodeType::Degree),
            b"m:func" => FormulaNode::new(NodeType::Function),
            b"m:fName" => FormulaNode::new(NodeType::FunctionName),
            b"m:r" => FormulaNode::new(NodeType::Run),
            b"m:d" => FormulaNode::new(NodeType::Delimiter),
            b"m:dPr" => FormulaNode::new(NodeType::DelimiterProperties),
            b"m:begChr" => FormulaNode::new(NodeType::BeginChar),
            b"m:endChr" => FormulaNode::new(NodeType::EndChar),
            b"m:nary" => FormulaNode::new(NodeType::Nary),
            b"m:naryPr" => FormulaNode::new(NodeType::NaryProperties),
            b"m:chr" => FormulaNode::new(NodeType::Char),
            b"m:m" => FormulaNode::new(NodeType::Matrix),
            b"m:mPr" => FormulaNode::new(NodeType::MatrixProperties),
            b"m:mr" => FormulaNode::new(NodeType::MatrixRow),
            _ => FormulaNode::new(NodeType::Other),
        };
        for attr_result in e.attributes() {
            if let Ok(attr) = attr_result {
                let key = String::from_utf8_lossy(attr.key.as_ref()).to_string();
                let value = attr
                    .decode_and_unescape_value(reader)
                    .unwrap_or_default()
                    .to_string();
                node.attrs.push((key, value));
            }
        }
        node
    }

    fn process_text(&self, text: &str) -> String {
        let mut result = String::new();
        let mut chars = text.chars().peekable();
        while let Some(c) = chars.next() {
            let char_as_str = c.to_string();
            if let Some(symbol) = SYMBOL_MAP.get(char_as_str.as_str()) {
                result.push_str(symbol);
                if symbol.starts_with('\\') {
                    if let Some(next_char) = chars.peek() {
                        if next_char.is_alphabetic() {
                            result.push(' ');
                        }
                    }
                }
            } else {
                match c {
                    ' ' => result.push_str(" "),
                    '\\' => result.push_str("\\textbackslash{}"),
                    '{' => result.push_str("\\{"),
                    '}' => result.push_str("\\}"),
                    '&' => result.push_str("\\&"),
                    '%' => result.push_str("\\%"),
                    '$' => result.push_str("\\$"),
                    '#' => result.push_str("\\#"),
                    '_' => result.push_str("\\_"),
                    '^' => result.push_str("\\^{}"),
                    '~' => result.push_str("\\textasciitilde{}"),
                    _ => result.push(c),
                }
            }
        }
        result
    }

    fn convert_tree_to_latex(&self, node: &FormulaNode) -> String {
        match node.node_type {
            NodeType::Root
            | NodeType::OMathPara
            | NodeType::OMath
            | NodeType::Run
            | NodeType::Other
            | NodeType::Numerator
            | NodeType::Denominator
            | NodeType::Base
            | NodeType::Exponent
            | NodeType::SubscriptValue
            | NodeType::Degree
            | NodeType::FunctionName
            | NodeType::FractionProperties
            | NodeType::Type
            | NodeType::DelimiterProperties
            | NodeType::BeginChar
            | NodeType::EndChar
            | NodeType::MatrixProperties => {
                return node
                    .children
                    .iter()
                    .map(|c| self.convert_tree_to_latex(c))
                    .collect::<String>()
            }
            _ => {}
        }

        match node.node_type {
            NodeType::Fraction => {
                let num = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Numerator)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                let den = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Denominator)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                let is_binom = node.children.iter().any(|child| {
                    if child.node_type == NodeType::FractionProperties {
                        child.children.iter().any(|grandchild| {
                            if grandchild.node_type == NodeType::Type {
                                grandchild
                                    .attrs
                                    .iter()
                                    .any(|(k, v)| k == "m:val" && v == "nobar")
                            } else {
                                false
                            }
                        })
                    } else {
                        false
                    }
                });
                if is_binom {
                    format!("\\binom{{{}}}{{{}}}", num, den)
                } else {
                    format!("\\frac{{{}}}{{{}}}", num, den)
                }
            }
            NodeType::Superscript => {
                let base = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Base)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                let exp = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Exponent)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                format!("{{{}}}^{{{}}}", base, exp)
            }
            NodeType::Subscript => {
                let base = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Base)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                let sub = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::SubscriptValue)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                format!("{{{}}}_{{{}}}", base, sub)
            }
            NodeType::SubSuperscript => {
                let base = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Base)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                let sub = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::SubscriptValue)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                let sup = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Exponent)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                format!("{{{}}}_{{{}}}^{{{}}}", base, sub, sup)
            }
            NodeType::Radical => {
                let content = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Base)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                if let Some(deg_node) = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Degree)
                {
                    let degree = self.convert_tree_to_latex(deg_node);
                    format!("\\sqrt[{}]{{{}}}", degree, content)
                } else {
                    format!("\\sqrt{{{}}}", content)
                }
            }
            NodeType::Function => {
                let mut fname = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::FunctionName)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                if let Some(latex_func) = FUNCTION_MAP.get(fname.trim()) {
                    fname = latex_func.to_string();
                } else {
                    fname = format!("\\text{{{}}}", fname.trim());
                }
                let arg = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Base)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                if fname.starts_with('\\') && !arg.starts_with("\\left") {
                    format!("{} {}", fname, arg)
                } else {
                    format!("{}{}", fname, arg)
                }
            }
            NodeType::Text | NodeType::Char => {
                if let Some(val_attr) = node.attrs.iter().find(|(k, _)| k == "m:val") {
                    self.process_text(&val_attr.1)
                } else if let Some(text) = &node.text {
                    self.process_text(text)
                } else {
                    String::new()
                }
            }
            NodeType::Delimiter => {
                let is_wrapping_binom = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Base)
                    .map_or(false, |base_node| {
                        base_node.children.iter().any(|grandchild| {
                            if grandchild.node_type == NodeType::Fraction {
                                return grandchild.children.iter().any(|prop_node| {
                                    if prop_node.node_type == NodeType::FractionProperties {
                                        prop_node.children.iter().any(|type_node| {
                                            if type_node.node_type == NodeType::Type {
                                                type_node
                                                    .attrs
                                                    .iter()
                                                    .any(|(k, v)| k == "m:val" && v == "nobar")
                                            } else {
                                                false
                                            }
                                        })
                                    } else {
                                        false
                                    }
                                });
                            }
                            false
                        })
                    });

                let content = node
                    .children
                    .iter()
                    .filter(|c| c.node_type == NodeType::Base)
                    .map(|c| self.convert_tree_to_latex(c))
                    .collect::<String>();

                if is_wrapping_binom {
                    format!("{{{}}}", content)
                } else {
                    let mut begin_char = "(".to_string();
                    let mut end_char = ")".to_string();
                    if let Some(dpr_node) = node
                        .children
                        .iter()
                        .find(|c| c.node_type == NodeType::DelimiterProperties)
                    {
                        if let Some(beg_node) = dpr_node
                            .children
                            .iter()
                            .find(|c| c.node_type == NodeType::BeginChar)
                        {
                            if let Some(val_attr) =
                                beg_node.attrs.iter().find(|(k, _)| k == "m:val")
                            {
                                begin_char = DELIMITER_MAP
                                    .get(val_attr.1.as_str())
                                    .map(|s| *s)
                                    .unwrap_or(&val_attr.1)
                                    .to_string();
                            }
                        }
                        if let Some(end_node) = dpr_node
                            .children
                            .iter()
                            .find(|c| c.node_type == NodeType::EndChar)
                        {
                            if let Some(val_attr) =
                                end_node.attrs.iter().find(|(k, _)| k == "m:val")
                            {
                                end_char = DELIMITER_MAP
                                    .get(val_attr.1.as_str())
                                    .map(|s| *s)
                                    .unwrap_or(&val_attr.1)
                                    .to_string();
                            }
                        }
                    }
                    format!("\\left{} {} \\right{}", begin_char, content, end_char)
                }
            }
            NodeType::Nary => {
                let op_char_node = node.children.iter().find_map(|pr_node| {
                    if pr_node.node_type == NodeType::NaryProperties {
                        pr_node
                            .children
                            .iter()
                            .find(|c| c.node_type == NodeType::Char)
                    } else {
                        None
                    }
                });
                let mut operator =
                    op_char_node.map_or("?".to_string(), |c| self.convert_tree_to_latex(c));
                let sub = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::SubscriptValue)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                let sup = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Exponent)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                let expr = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::Base)
                    .map_or(String::new(), |c| self.convert_tree_to_latex(c));
                if !sub.is_empty() {
                    operator.push_str(&format!("_{{{}}}", sub));
                }
                if !sup.is_empty() {
                    operator.push_str(&format!("^{{{}}}", sup));
                }
                if !expr.is_empty() {
                    operator.push_str(" ");
                }
                operator.push_str(&expr);
                operator
            }
            NodeType::Matrix => {
                let mut env = "pmatrix";
                if let Some(mpr_node) = node
                    .children
                    .iter()
                    .find(|c| c.node_type == NodeType::MatrixProperties)
                {
                    if let Some(beg_node) = mpr_node
                        .children
                        .iter()
                        .find(|c| c.node_type == NodeType::BeginChar)
                    {
                        if let Some(val_attr) = beg_node.attrs.iter().find(|(k, _)| k == "m:val") {
                            match val_attr.1.as_str() {
                                "[" => env = "bmatrix",
                                "{" => env = "Bmatrix",
                                "|" => env = "vmatrix",
                                "||" => env = "Vmatrix",
                                _ => env = "pmatrix",
                            }
                        }
                    }
                }

                let rows: Vec<String> = node
                    .children
                    .iter()
                    .filter(|c| c.node_type == NodeType::MatrixRow)
                    .map(|row_node| {
                        let elements: Vec<String> = row_node
                            .children
                            .iter()
                            .filter(|c| c.node_type == NodeType::Base)
                            .map(|element_node| self.convert_tree_to_latex(element_node))
                            .collect();
                        elements.join(" & ")
                    })
                    .collect();

                format!(
                    "\\begin{{{}}}\n{} \n\\end{{{}}}",
                    env,
                    rows.join(" \\\\ \n"),
                    env
                )
            }
            NodeType::NaryProperties | NodeType::MatrixRow => String::new(),
            _ => String::new(),
        }
    }
}

#[derive(Debug, PartialEq, Clone)]
enum NodeType {
    Root,
    OMathPara,
    OMath,
    Run,
    Other,
    Numerator,
    Denominator,
    Base,
    Exponent,
    SubscriptValue,
    Degree,
    FunctionName,
    Fraction,
    Superscript,
    Subscript,
    SubSuperscript,
    Radical,
    Function,
    Text,
    Delimiter,
    Nary,
    NaryProperties,
    Char,
    FractionProperties,
    Type,
    DelimiterProperties,
    BeginChar,
    EndChar,
    Matrix,
    MatrixProperties,
    MatrixRow,
}

#[derive(Debug, Clone)]
struct FormulaNode {
    node_type: NodeType,
    text: Option<String>,
    attrs: Vec<(String, String)>,
    children: Vec<FormulaNode>,
}

impl FormulaNode {
    fn new(node_type: NodeType) -> Self {
        Self {
            node_type,
            text: None,
            attrs: Vec::new(),
            children: Vec::new(),
        }
    }
    fn new_with_text(node_type: NodeType, text: &str) -> Self {
        Self {
            node_type,
            text: Some(text.to_string()),
            attrs: Vec::new(),
            children: Vec::new(),
        }
    }
    fn add_child(&mut self, child: FormulaNode) {
        self.children.push(child);
    }
}
