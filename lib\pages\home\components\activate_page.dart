import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:url_launcher/url_launcher.dart';

class ActivatePage extends StatelessWidget {
  ActivatePage({super.key});

  final _isLoading = false.obs;
  Future<void> _requestTrial(BuildContext context) async {
    await showShadDialog<bool>(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32.0),
        child: ShadDialog(
          title: Text('activate.trialRequest.title'.tr),
          description: Text('activate.trialRequest.description'.tr),
          actions: [
            ShadButton.outline(
              child: Text('activate.trialRequest.cancel'.tr),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ShadButton(
              child: Text('activate.trialRequest.confirm'.tr),
              onPressed: () {
                Navigator.of(context).pop(true);
                final licenseController = Get.find<LicenseController>();
                licenseController.requestTrial(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _requestActivate(BuildContext context) async {
    _isLoading.value = true;
    try {
      final licenseController = Get.find<LicenseController>();
      final machineId = await licenseController.getDeviceSerial();
      logger.i('machineId: $machineId');
      // 验证参数
      if (licenseController.code.value.isEmpty) {
        showToastNotification(context, 'activate.activationCode'.tr, "",
            type: "error");
        return;
      }
      licenseController.requestActivate(context);
    } finally {
      _isLoading.value = false;
    }
  }

  void _showPurchaseDialog(BuildContext context) {
    showShadDialog(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32.0),
        child: ShadDialog(
          title: Text('activate.purchase.title'.tr),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                spacing: 16,
                children: [
                  Text('${'activate.purchase.officialWebsite'.tr}'),
                  ShadButton(
                    onPressed: () => launchUrl(
                        Uri.parse('https://guru.kevin2li.com/docs/price/')),
                    child: Text('activate.purchase.goToWebsite'.tr),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Text('${'activate.purchase.onlineStore'.tr}'),
                ],
              ),
              const SizedBox(height: 16),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      QrImageView(
                        data: 'https://m.tb.cn/h.gXqw5ex9qU6MjSW',
                        embeddedImage:
                            AssetImage(AssetStore.getAsset('taobao')),
                        version: QrVersions.auto,
                        size: 200.0,
                        backgroundColor: Colors.white,
                      ),
                      Text('activate.purchase.taobao'.tr),
                      const SizedBox(height: 16),
                      QrImageView(
                        data:
                            'https://mobile.yangkeduo.com/goods2.html?goods_id=614590020138',
                        embeddedImage: AssetImage(AssetStore.getAsset('pdd')),
                        version: QrVersions.auto,
                        size: 200.0,
                        backgroundColor: Colors.white,
                      ),
                      Text('activate.purchase.pdd'.tr),
                      // const SizedBox(height: 16),
                      // QrImageView(
                      //   data:
                      //       'https://mall.bilibili.com/neul-next/index.html?page=mall-up_itemDetail&noTitleBar=1&from=items_share&msource=items_share&itemsId=1103168048',
                      //   embeddedImage:
                      //       AssetImage(AssetStore.getAsset('bilibili')),
                      //   version: QrVersions.auto,
                      //   size: 200.0,
                      //   backgroundColor: Colors.white,
                      // ),
                      // const Text('哔哩哔哩'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 添加注销设备的方法
  Future<void> _deactivateDevice(BuildContext context) async {
    await showShadDialog<bool>(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32.0),
        child: ShadDialog(
          title: Text('activate.deactivate.title'.tr),
          description: Text('activate.deactivate.description'.tr),
          actions: [
            ShadButton.destructive(
              child: Text('activate.deactivate.confirm'.tr),
              onPressed: () {
                Navigator.of(context).pop(false);
                final licenseController = Get.find<LicenseController>();
                licenseController.requestUnregister(context);
              },
            ),
            ShadButton.outline(
              child: Text('activate.deactivate.cancel'.tr),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final licenseController = Get.find<LicenseController>();
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('activate.title'.tr),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: ListView(padding: const EdgeInsets.all(8), children: [
          ShadCard(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                spacing: 8,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ShadInputWithValidate(
                      label: 'activate.activationCode'.tr,
                      placeholder: 'activate.enterActivationCode'.tr,
                      onChanged: (value) {
                        licenseController.code.value = value;
                      },
                      initialValue: licenseController.code.value,
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return 'activate.activationCode'.tr;
                        }
                        return "";
                      }),
                  // const SizedBox(height: 24),
                  Obx(() => ShadButton(
                        onPressed: _isLoading.value
                            ? null
                            : () => _requestActivate(context),
                        child: _isLoading.value
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : Text(
                                'activate.activate'.tr,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      )),
                  ShadButton.outline(
                    onPressed: () => _requestTrial(context),
                    child: Text(
                      'activate.requestTrial'.tr,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  ShadButton.link(
                    onPressed: () => _showPurchaseDialog(context),
                    child: Text(
                      'activate.buyActivationCode'.tr,
                      style: const TextStyle(
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16), // 添加间距
          ShadCard(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            title: Padding(
              padding: const EdgeInsets.fromLTRB(8, 8, 0, 0),
              child: Text(
                'activate.activationInfo'.tr,
                style: defaultCardTitleStyle,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 激活信息内容
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Column(
                    children: [
                      Obx(() => licenseController.isActivated.value
                          ? _buildInfoRow(
                              'activate.state'.tr, 'activate.activated'.tr)
                          : _buildInfoRow('activate.state'.tr,
                              'activate.not_activated'.tr)),
                      Obx(() => licenseController.isActivated.value
                          ? _buildInfoRow(
                              'activate.activate_time'.tr,
                              licenseController.licenseInfo.value
                                  .getIssueTime())
                          : _buildInfoRow('activate.activate_time'.tr, 'N/A')),
                      Obx(() => licenseController.isActivated.value
                          ? _buildInfoRow(
                              'userCenter.expiryDate'.tr,
                              licenseController.licenseInfo.value
                                  .getExpireTime())
                          : _buildInfoRow('userCenter.expiryDate'.tr, 'N/A')),
                      Obx(() => licenseController.isActivated.value
                          ? _buildInfoRow(
                              'activate.allowed_divices'.tr,
                              licenseController.licenseInfo.value.device_count
                                  .toString())
                          : _buildInfoRow(
                              'activate.allowed_divices'.tr, 'N/A')),
                      const SizedBox(height: 8),
                      Obx(() => licenseController.isActivated.value &&
                              (licenseController
                                      .licenseInfo.value.can_unregister ??
                                  false)
                          ? OutlinedButton(
                              style: OutlinedButton.styleFrom(
                                minimumSize: const Size.fromHeight(48),
                                foregroundColor:
                                    Theme.of(context).colorScheme.error,
                                side: BorderSide(
                                    color: Theme.of(context).colorScheme.error),
                              ),
                              onPressed: () => _deactivateDevice(context),
                              child: Text(
                                'activate.unregister_device'.tr,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            )
                          : const SizedBox.shrink()),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16), // 添加间距
          ShadCard(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            title: Padding(
              padding: const EdgeInsets.fromLTRB(8, 8, 0, 0),
              child: Text(
                'activate.faq'.tr,
                style: defaultCardTitleStyle,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: ShadAccordion<({String content, String title})>(
                children: licenseController.details.map(
                  (detail) => ShadAccordionItem(
                    value: detail,
                    title: Text(detail.title,
                        style: const TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold)),
                    child: Text(detail.content,
                        style: const TextStyle(fontSize: 13.5)),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8), // 添加间距
        ]),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(color: Colors.grey)),
          Text(value),
        ],
      ),
    );
  }
}
