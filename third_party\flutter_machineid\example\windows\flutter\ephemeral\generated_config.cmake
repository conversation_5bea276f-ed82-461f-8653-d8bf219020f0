# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\fvm\\versions\\3.32.8" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\code\\anki_guru\\third_party\\flutter_machineid\\example" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.32.8"
  "PROJECT_DIR=C:\\Users\\<USER>\\code\\anki_guru\\third_party\\flutter_machineid\\example"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.32.8"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\code\\anki_guru\\third_party\\flutter_machineid\\example\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\code\\anki_guru\\third_party\\flutter_machineid\\example"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\code\\anki_guru\\third_party\\flutter_machineid\\example\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuOA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZWRhZGE3YzU2ZQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZWYwY2QwMDA5MQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\code\\anki_guru\\third_party\\flutter_machineid\\example\\.dart_tool\\package_config.json"
)
