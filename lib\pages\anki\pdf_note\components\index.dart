import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/pdf_note.dart';

class PDFNoteForm extends GetView<PDFNoteController> {
  const PDFNoteForm({super.key});

  @override
  Widget build(context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 16),
      // footer: Row(
      //   mainAxisAlignment: MainAxisAlignment.start,
      //   children: [
      //     Expanded(
      //       child: ShadButton(
      //         size: ShadButtonSize.lg,
      //         onPressed: () {
      //           controller.submit(context);
      //         },
      //         child: const Text('启动'),
      //       ),
      //     )
      //   ],
      // ),
      child: Obx(
        () => Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          spacing: 8,
          children: [
            ShadSwitchCustom(
              label: 'anki.pdf_note.shortcuts.disable_all'.tr,
              initialValue: controller.disableAllShortcuts.value,
              onChanged: (v) {
                controller.disableAllShortcuts.value = v;
                controller.disableAllShortcutsHandler();
              },
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.insert_path_link'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller.getHotKeyDisplayText(
                  controller.shortcutMap["insertPathLink"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "insertPathLink",
                      null,
                      controller.shortcutHandlerMap["insertPathLink"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "insertPathLink",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.insert_page_link'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller.getHotKeyDisplayText(
                  controller.shortcutMap["insertPageLink"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "insertPageLink",
                      null,
                      controller.shortcutHandlerMap["insertPageLink"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "insertPageLink",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.insert_comment_link'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller.getHotKeyDisplayText(
                  controller.shortcutMap["insertCommentLink"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "insertCommentLink",
                      null,
                      controller.shortcutHandlerMap["insertCommentLink"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "insertCommentLink",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.insert_bookmarks_link'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller.getHotKeyDisplayText(
                  controller.shortcutMap["insertBookmarksLink"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "insertBookmarksLink",
                      null,
                      controller.shortcutHandlerMap["insertBookmarksLink"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "insertBookmarksLink",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.insert_comment'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller.getHotKeyDisplayText(
                  controller.shortcutMap["insertComment"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "insertComment",
                      null,
                      controller.shortcutHandlerMap["insertComment"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "insertComment",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.insert_note'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller
                  .getHotKeyDisplayText(controller.shortcutMap["insertNote"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "insertNote",
                      null,
                      controller.shortcutHandlerMap["insertNote"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "insertNote",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.insert_all_notes'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller.getHotKeyDisplayText(
                  controller.shortcutMap["insertAllNotes"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "insertAllNotes",
                      null,
                      controller.shortcutHandlerMap["insertAllNotes"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "insertAllNotes",
                    ),
                  ),
                ],
              ),
            ),
            // ListTile(
            //   contentPadding: EdgeInsets.zero,
            //   minVerticalPadding: 0,
            //   title: Text('插入页面截图', style: defaultTitleStyle),
            //   subtitle: Obx(() => Text(controller.getHotKeyDisplayText(
            //       controller.shortcutMap["insertPageScreenshot"]))),
            //   trailing: Row(
            //     mainAxisSize: MainAxisSize.min,
            //     children: [
            //       ShadIconButtonCustom(
            //         icon: Icons.delete,
            //         size: 22,
            //         onPressed: () => controller.registerHotKey(
            //           "insertPageScreenshot",
            //           null,
            //           controller.shortcutHandlerMap["insertPageScreenshot"]!,
            //         ),
            //       ),
            //       ShadIconButtonCustom(
            //         icon: Icons.edit,
            //         size: 22,
            //         onPressed: () => controller.showPasteHotKeyRecorder(
            //           context,
            //           "insertPageScreenshot",
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            // ListTile(
            //   contentPadding: EdgeInsets.zero,
            //   minVerticalPadding: 0,
            //   title: Text('提取页面文本', style: defaultTitleStyle),
            //   subtitle: Obx(() => Text(controller.getHotKeyDisplayText(
            //       controller.shortcutMap["insertPageText"]))),
            //   trailing: Row(
            //     mainAxisSize: MainAxisSize.min,
            //     children: [
            //       ShadIconButtonCustom(
            //         icon: Icons.delete,
            //         size: 22,
            //         onPressed: () => controller.registerHotKey(
            //           "insertPageText",
            //           null,
            //           controller.shortcutHandlerMap["insertPageText"]!,
            //         ),
            //       ),
            //       ShadIconButtonCustom(
            //         icon: Icons.edit,
            //         size: 22,
            //         onPressed: () => controller.showPasteHotKeyRecorder(
            //           context,
            //           "insertPageText",
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            // ListTile(
            //   contentPadding: EdgeInsets.zero,
            //   minVerticalPadding: 0,
            //   title: Text('OCR识别', style: defaultTitleStyle),
            //   subtitle: Obx(() => Text(controller
            //       .getHotKeyDisplayText(controller.shortcutMap["ocr"]))),
            //   trailing: Row(
            //     mainAxisSize: MainAxisSize.min,
            //     children: [
            //       ShadIconButtonCustom(
            //         icon: Icons.delete,
            //         size: 22,
            //         onPressed: () => controller.registerHotKey(
            //           "ocr",
            //           null,
            //           controller.shortcutHandlerMap["ocr"]!,
            //         ),
            //       ),
            //       ShadIconButtonCustom(
            //         icon: Icons.edit,
            //         size: 22,
            //         onPressed: () => controller.showPasteHotKeyRecorder(
            //           context,
            //           "ocr",
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.go_home'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller
                  .getHotKeyDisplayText(controller.shortcutMap["goHome"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "goHome",
                      null,
                      controller.shortcutHandlerMap["goHome"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "goHome",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.go_end'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller
                  .getHotKeyDisplayText(controller.shortcutMap["goEnd"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "goEnd",
                      null,
                      controller.shortcutHandlerMap["goEnd"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "goEnd",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.go_prev'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller
                  .getHotKeyDisplayText(controller.shortcutMap["goPrev"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "goPrev",
                      null,
                      controller.shortcutHandlerMap["goPrev"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "goPrev",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.go_next'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller
                  .getHotKeyDisplayText(controller.shortcutMap["goNext"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "goNext",
                      null,
                      controller.shortcutHandlerMap["goNext"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "goNext",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.set_flag_a'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller
                  .getHotKeyDisplayText(controller.shortcutMap["setFlagA"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "setFlagA",
                      null,
                      controller.shortcutHandlerMap["setFlagA"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "setFlagA",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.set_flag_b'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller
                  .getHotKeyDisplayText(controller.shortcutMap["setFlagB"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "setFlagB",
                      null,
                      controller.shortcutHandlerMap["setFlagB"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "setFlagB",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.jump_to_flag'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller
                  .getHotKeyDisplayText(controller.shortcutMap["jumpToFlag"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "jumpToFlag",
                      null,
                      controller.shortcutHandlerMap["jumpToFlag"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "jumpToFlag",
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.pdf_note.shortcuts.clear_flag'.tr, style: defaultTitleStyle),
              subtitle: Obx(() => Text(controller
                  .getHotKeyDisplayText(controller.shortcutMap["clearFlag"]))),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadIconButtonCustom(
                    icon: Icons.delete,
                    size: 22,
                    onPressed: () => controller.registerHotKey(
                      "clearFlag",
                      null,
                      controller.shortcutHandlerMap["clearFlag"]!,
                    ),
                  ),
                  ShadIconButtonCustom(
                    icon: Icons.edit,
                    size: 22,
                    onPressed: () => controller.showPasteHotKeyRecorder(
                      context,
                      "clearFlag",
                    ),
                  ),
                ],
              ),
            ),
            // ListTile(
            //   contentPadding: EdgeInsets.zero,
            //   minVerticalPadding: 0,
            //   title: Text('向上滚动', style: defaultTitleStyle),
            //   subtitle: Obx(() => Text(controller
            //       .getHotKeyDisplayText(controller.shortcutMap["scrollUp"]))),
            //   trailing: Row(
            //     mainAxisSize: MainAxisSize.min,
            //     children: [
            //       ShadIconButtonCustom(
            //         icon: Icons.delete,
            //         size: 22,
            //         onPressed: () => controller.registerHotKey(
            //           "scrollUp",
            //           null,
            //           controller.shortcutHandlerMap["scrollUp"]!,
            //         ),
            //       ),
            //       ShadIconButtonCustom(
            //         icon: Icons.edit,
            //         size: 22,
            //         onPressed: () => controller.showPasteHotKeyRecorder(
            //           context,
            //           "scrollUp",
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            // ListTile(
            //   contentPadding: EdgeInsets.zero,
            //   minVerticalPadding: 0,
            //   title: Text('向下滚动', style: defaultTitleStyle),
            //   subtitle: Obx(() => Text(controller
            //       .getHotKeyDisplayText(controller.shortcutMap["scrollDown"]))),
            //   trailing: Row(
            //     mainAxisSize: MainAxisSize.min,
            //     children: [
            //       ShadIconButtonCustom(
            //         icon: Icons.delete,
            //         size: 22,
            //         onPressed: () => controller.registerHotKey(
            //           "scrollDown",
            //           null,
            //           controller.shortcutHandlerMap["scrollDown"]!,
            //         ),
            //       ),
            //       ShadIconButtonCustom(
            //         icon: Icons.edit,
            //         size: 22,
            //         onPressed: () => controller.showPasteHotKeyRecorder(
            //           context,
            //           "scrollDown",
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
