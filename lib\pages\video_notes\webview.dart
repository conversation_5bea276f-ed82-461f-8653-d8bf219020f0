import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/video_notes/index.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:url_launcher/url_launcher_string.dart';

class VideoWebView extends StatefulWidget {
  VideoWebView({super.key});

  @override
  State<VideoWebView> createState() => _VideoWebViewState();
}

class _VideoWebViewState extends State<VideoWebView> {
  final webviewController = Get.find<WebviewController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(
      //   title: const Text("在线视频笔记"),
      //   backgroundColor: Theme.of(context).colorScheme.surface,
      //   leading: IconButton(
      //     icon: const Icon(Icons.arrow_back),
      //     onPressed: () {
      //       webviewController.disposeWebView();
      //       Get.back();
      //     },
      //   ),
      // ),
      body: SafeArea(
        child: Column(
          children: [
            Row(children: [
              ShadButton(
                onPressed: () {
                  Get.back();
                },
                child: const Text("返回"),
              ),
              ShadButton(
                onPressed: () {
                  webviewController.webViewController?.loadUrl(
                      urlRequest:
                          URLRequest(url: WebUri("https://www.bilibili.com/")));
                },
                child: const Text("打开B站视频"),
              ),
              ShadButton(
                onPressed: () {
                  webviewController.getSnapshot();
                },
                child: const Text("复制截图"),
              )
            ]),
            Expanded(
              child: Stack(
                children: [
                  InAppWebView(
                    initialSettings: webviewController.settings,
                    initialUrlRequest:
                        URLRequest(url: WebUri("https://www.bilibili.com/")),
                    pullToRefreshController:
                        webviewController.pullToRefreshController,
                    shouldOverrideUrlLoading:
                        (controller, shouldOverrideUrlLoadingRequest) async {
                      var uri = shouldOverrideUrlLoadingRequest.request.url;
                      if (uri == null) {
                        // controller.goBack();
                        return NavigationActionPolicy.CANCEL;
                      }
                      final uriString = uri.toString();
                      if (uriString.startsWith('http://') ||
                          uriString.startsWith('https://')) {
                        return NavigationActionPolicy.ALLOW;
                      } else {
                        // controller.goBack();
                        await launchUrlString(
                          uriString,
                          mode: LaunchMode.platformDefault,
                        );
                        return NavigationActionPolicy.CANCEL;
                      }
                    },
                    onCreateWindow: (controller, action) async {
                      var uri = action.request.url;
                      if (uri == null) {
                        // controller.goBack();
                        return false;
                      }
                      final uriString = uri.toString();
                      if (uriString.startsWith('http://') ||
                          uriString.startsWith('https://')) {
                        return true;
                      } else {
                        // controller.goBack();
                        await launchUrlString(
                          uriString,
                          mode: LaunchMode.platformDefault,
                        );
                        return false;
                      }
                    },
                    onWebViewCreated: (controller) {
                      webviewController.webViewController = controller;
                    },
                    onLoadStop: (controller, url) async {
                      webviewController.pullToRefreshController
                          ?.endRefreshing();
                    },
                    onReceivedError: (controller, request, error) {
                      webviewController.pullToRefreshController
                          ?.endRefreshing();
                    },
                    onProgressChanged: (controller, progress) {
                      if (progress == 100) {
                        webviewController.pullToRefreshController
                            ?.endRefreshing();
                      }
                      webviewController.progress.value = progress / 100;
                    },
                  ),
                  Obx(() => webviewController.progress.value < 1.0
                      ? LinearProgressIndicator(
                          value: webviewController.progress.value)
                      : Container()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
