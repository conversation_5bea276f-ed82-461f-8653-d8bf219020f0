import 'dart:io';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'dart:convert';
import 'dart:async';
import 'package:flex_color_picker/flex_color_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf_pdf;
import 'package:path/path.dart' as p;
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFBackgroundPageController extends GetxController {
  // 基础数据
  late final List<Map<String, String>> backgroundTypeList;
  // 表单参数
  final backgroundType = "color".obs;
  final backgroundColor = const Color(0xFFFFFFFF).obs;
  final backgroundImage = ''.obs;
  final opacity = 0.5.obs;
  final angle = 0.0.obs;
  final xOffset = 0.0.obs;
  final yOffset = 0.0.obs;
  final scale = 1.0.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'annotate');

  @override
  void onInit() {
    super.onInit();
    // 初始化时根据当前语言设置backgroundTypeList
    backgroundTypeList = [
      {"value": "color", "label": 'toolbox.background.colorBackground'.tr},
      {"value": "image", "label": 'toolbox.background.imageBackground'.tr},
    ];
  }

  // 获取PDF文档尺寸
  Future<Map<String, double>> getPdfDimensions(String filePath) async {
    final file = File(filePath);
    if (!await file.exists()) {
      throw Exception('toolbox.background.fileNotExist'.tr);
    }

    // 使用Syncfusion PDF库读取PDF文档
    final document = sf_pdf.PdfDocument(inputBytes: await file.readAsBytes());
    try {
      // 获取第一页尺寸
      final page = document.pages[0];
      final width = page.size.width;
      final height = page.size.height;
      return {
        'width': width,
        'height': height,
      };
    } finally {
      // 关闭文档释放资源
      document.dispose();
    }
  }

  // 创建纯色背景的PDF
  Future<String> createColorBackgroundPdf(
      String docPath, double width, double height) async {
    final pdf = pw.Document();
    final color = PdfColor.fromHex(backgroundColor.value.hex);

    pdf.addPage(
      pw.Page(
        build: (pw.Context context) {
          return pw.Stack(
            children: [
              pw.Positioned.fill(
                child: pw.Transform.rotate(
                  angle: angle.value * math.pi / 180, // 角度转弧度
                  child: pw.Opacity(
                    opacity: opacity.value,
                    child: pw.Container(
                      margin: pw.EdgeInsets.only(
                        left: xOffset.value,
                        top: yOffset.value,
                      ),
                      width: width,
                      height: height,
                      color: color,
                    ),
                  ),
                ),
              ),
            ],
          );
        },
        pageFormat: PdfPageFormat(width, height),
      ),
    );

    final bgPdfPath = p.join(p.dirname(docPath), 'tmp_bg.pdf');
    final file = File(bgPdfPath);
    await file.writeAsBytes(await pdf.save());
    return bgPdfPath;
  }

  // 创建图片背景的PDF
  Future<String> createImageBackgroundPdf(
      String docPath, double width, double height) async {
    if (backgroundImage.value.isEmpty) {
      throw Exception('toolbox.background.imageNotSelected'.tr);
    }

    final imageFile = File(backgroundImage.value);
    if (!await imageFile.exists()) {
      throw Exception('toolbox.background.imageFileNotExist'.tr);
    }

    final pdf = pw.Document();
    final imageBytes = await imageFile.readAsBytes();
    final image = pw.MemoryImage(imageBytes);

    // 加载图片获取尺寸
    final completer = Completer<ui.Image>();
    ui.decodeImageFromList(imageBytes, completer.complete);
    final img = await completer.future;
    final imgWidth = img.width.toDouble();
    final imgHeight = img.height.toDouble();
    final scaledWidth = imgWidth * scale.value;
    final scaledHeight = imgHeight * scale.value;

    pdf.addPage(
      pw.Page(
        build: (pw.Context context) {
          return pw.Stack(
            children: [
              pw.Positioned(
                left: width / 2 - scaledWidth / 2 + xOffset.value,
                top: height / 2 - scaledHeight / 2 + yOffset.value,
                child: pw.Transform.rotate(
                  angle: angle.value * math.pi / 180, // 角度转弧度
                  child: pw.Opacity(
                    opacity: opacity.value,
                    child: pw.Image(
                      image,
                      width: scaledWidth,
                      height: scaledHeight,
                    ),
                  ),
                ),
              ),
            ],
          );
        },
        pageFormat: PdfPageFormat(width, height),
      ),
    );

    final bgPdfPath = p.join(p.dirname(docPath), 'tmp_bg.pdf');
    final file = File(bgPdfPath);
    await file.writeAsBytes(await pdf.save());
    return bgPdfPath;
  }

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      for (String filePath in selectedFilePaths) {
        progressController.updateProgress(
          status: "running",
          message: "${'toolbox.background.processingFile'.tr}: ${PathUtils(filePath).name}",
        );
        final outputPath = await PathUtils(filePath).convertPath(
          outputMode.value,
          stem_append: "_${'home.tools.pdfBackground'.tr}",
          suffix: ".pdf",
          outputDir: outputDir.value,
        );

        // 直接在Flutter端获取PDF尺寸
        final dimensions = await getPdfDimensions(filePath);
        final width = dimensions['width']!;
        final height = dimensions['height']!;

        // 根据类型生成背景PDF
        String bgPdfPath;
        try {
          if (backgroundType.value == 'color') {
            bgPdfPath = await createColorBackgroundPdf(filePath, width, height);
          } else if (backgroundType.value == 'image') {
            bgPdfPath = await createImageBackgroundPdf(filePath, width, height);
          } else {
            throw Exception(
                '${'toolbox.background.unknownBackgroundType'.tr}: ${backgroundType.value}');
          }
        } catch (e) {
          progressController.updateProgress(
            status: "error",
            message: "${'toolbox.background.generateBackgroundFailed'.tr}: $e",
          );
          return;
        }

        // 发送请求到Rust端
        final data = {
          'input_path': filePath,
          'background_path': bgPdfPath,
          'page_range': pageRange.value,
          'output_path': outputPath,
          'show_progress': true,
        };

        final resp = await messageController.request(data, 'pdf/background');

        // 清理临时文件
        try {
          await File(bgPdfPath).delete();
        } catch (e) {
          logger.e("删除临时背景PDF失败: $e");
        }

        if (resp.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed",
            message: 'toolbox.background.completed'.tr,
          );
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
          return;
        }
      }
      progressController.updateProgress(status: "completed", message: "");
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error",
        message: "${'toolbox.common.operationFailed'.tr}: $e",
      );
    }
  }
}
