﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\code\anki_guru\third_party\flutter_machineid\example\build\windows\x64\x64\Debug\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\code\anki_guru\third_party\flutter_machineid\example\build\windows\x64\flutter\x64\Debug\flutter_assemble</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\code\anki_guru\third_party\flutter_machineid\example\build\windows\x64\plugins\flutter_machineid\Debug\flutter_machineid_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\code\anki_guru\third_party\flutter_machineid\example\build\windows\x64\runner\Debug\flutter_machineid_example.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\code\anki_guru\third_party\flutter_machineid\example\build\windows\x64\plugins\flutter_machineid\Debug\flutter_machineid_test.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\code\anki_guru\third_party\flutter_machineid\example\build\windows\x64\x64\Debug\ALL_BUILD</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\code\anki_guru\third_party\flutter_machineid\example\build\windows\x64\x64\Debug\INSTALL</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>