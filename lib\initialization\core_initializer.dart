import 'dart:io';
import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';                      // Provides [Player], [Media], [Playlist] etc.
import 'package:media_kit_video/media_kit_video.dart';          // Provides [VideoController] & [Video] etc.
import 'package:hotkey_manager/hotkey_manager.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:rinf/rinf.dart';

/// Core system initialization module
/// Handles fundamental system setup that must occur before other components
class CoreInitializer {
  /// Initialize core Flutter and system components
  /// This must be called first in main() before any other initialization
  static Future<void> initialize() async {
    // Ensure Flutter binding is initialized
    WidgetsFlutterBinding.ensureInitialized();
    
    // Initialize media library
    MediaKit.ensureInitialized();
    
    // Clear any existing hotkeys on desktop platforms
    if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
      await hotKeyManager.unregisterAll();
    }
    
    // Initialize core services in the correct order
    await _initializeCoreServices();
  }
  
  /// Initialize core services that other components depend on
  static Future<void> _initializeCoreServices() async {
    // Initialize logging service first (other services may need logging)
    await LoggerService.init();
    
    // Initialize storage manager (controllers need storage)
    await StorageManager().init();
    
    // Initialize Rust backend (many features depend on Rust)
    await initializeRust(assignRustSignal);
    
    logger.i('Core system initialization completed');
  }
}
