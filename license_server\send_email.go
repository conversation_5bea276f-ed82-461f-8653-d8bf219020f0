package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/smtp"
	"os"
	"time"

	"github.com/gocarina/gocsv"
	"github.com/jordan-wright/email"
)

type User struct {
	Email string `csv:"email"`
	Code  string `csv:"code"`
}

type OrderItem struct {
	OrderID                 int64       `json:"orderId"`
	BizOrderType            int         `json:"bizOrderType"`
	ItemsTotalMoney         int         `json:"itemsTotalMoney"`
	OriginalItemsTotalMoney int         `json:"originalItemsTotalMoney"`
	AdjustPrice             int         `json:"adjustPrice"`
	PayedPrice              int         `json:"payedPrice"`
	ExpressTotalMoney       int         `json:"expressTotalMoney"`
	PayTime                 int         `json:"payTime"`
	ReceiveTime             int         `json:"receiveTime"`
	PayEndTime              interface{} `json:"payEndTime"`
	ServerTime              int64       `json:"serverTime"`
	Mid                     int64       `json:"mid"`
	Face                    string      `json:"face"`
	NickName                string      `json:"nickName"`
	Status                  int         `json:"status"`
	SubStatus               int         `json:"subStatus"`
	RefundStatus            int         `json:"refundStatus"`
	RefundType              interface{} `json:"refundType"`
	SubRefundStatus         interface{} `json:"subRefundStatus"`
	ChangePriceStatus       int         `json:"changePriceStatus"`
	DelayFinishStatus       int         `json:"delayFinishStatus"`
	ProcessStatus           int         `json:"processStatus"`
	ProcessName             interface{} `json:"processName"`
	LatestOpt               struct {
		OptType      int    `json:"optType"`
		ValueType    int    `json:"valueType"`
		ValueContent string `json:"valueContent"`
		OptTime      int    `json:"optTime"`
	} `json:"latestOpt"`
	IsCanDelete int `json:"isCanDelete"`
	OrderSku    struct {
		ItemsID          int           `json:"itemsId"`
		ItemsVersion     int           `json:"itemsVersion"`
		SaleType         int           `json:"saleType"`
		ItemsType        int           `json:"itemsType"`
		ItemsCateType    int           `json:"itemsCateType"`
		ItemTag          []string      `json:"itemTag"`
		ItemsName        string        `json:"itemsName"`
		ItemsImg         string        `json:"itemsImg"`
		Price            int           `json:"price"`
		SkuNum           int           `json:"skuNum"`
		ItemsSaleTagList []interface{} `json:"itemsSaleTagList"`
		ItemsTypeTagList []interface{} `json:"itemsTypeTagList"`
		SpecDesc         string        `json:"specDesc"`
	} `json:"orderSku"`
	SupplyMoneyList      []interface{} `json:"supplyMoneyList"`
	DiscountList         []interface{} `json:"discountList"`
	DeliveryUploadStatus int           `json:"deliveryUploadStatus"`
	OrderRefundDetail    struct {
		RefundID              interface{} `json:"refundId"`
		RefundCtime           interface{} `json:"refundCtime"`
		RefundType            interface{} `json:"refundType"`
		RefundStatus          int         `json:"refundStatus"`
		SubRefundStatus       interface{} `json:"subRefundStatus"`
		RefundSourceType      interface{} `json:"refundSourceType"`
		ArbitrationID         interface{} `json:"arbitrationId"`
		ArbitrationStatus     int         `json:"arbitrationStatus"`
		ArbitrationSourceType interface{} `json:"arbitrationSourceType"`
		RefundProcessEndTime  interface{} `json:"refundProcessEndTime"`
		ArbitrationEndTime    interface{} `json:"arbitrationEndTime"`
		RejectReason          interface{} `json:"rejectReason"`
		RefundAmount          interface{} `json:"refundAmount"`
		RefundMoney           interface{} `json:"refundMoney"`
		RefundExplain         interface{} `json:"refundExplain"`
		DistVO                interface{} `json:"distVO"`
		DeliveryDetailVO      interface{} `json:"deliveryDetailVO"`
		ProofImgs             interface{} `json:"proofImgs"`
		SettleMoney           interface{} `json:"settleMoney"`
		ExpressMoney          interface{} `json:"expressMoney"`
		BackSubsidyMoney      interface{} `json:"backSubsidyMoney"`
	} `json:"orderRefundDetail"`
	DeliverEndTime    int `json:"deliverEndTime"`
	IsCanComment      int `json:"isCanComment"`
	IsRefundAvailable int `json:"isRefundAvailable"`
	ProductionEndTime int `json:"productionEndTime"`
}

type OrderResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		SellerOrderPage struct {
			PageNum    int         `json:"pageNum"`
			PageSize   int         `json:"pageSize"`
			Total      int         `json:"total"`
			List       []OrderItem `json:"list"`
			IsLastPage bool        `json:"isLastPage"`
		} `json:"sellerOrderPage"`
		BuyerOrderPage     interface{} `json:"buyerOrderPage"`
		View               int         `json:"view"`
		OrderStatusCountVO struct {
			OrderSubmitDoneTotal  int `json:"orderSubmitDoneTotal"`
			OrderPayDoneTotal     int `json:"orderPayDoneTotal"`
			OrderAcceptedTotal    int `json:"orderAcceptedTotal"`
			OrderNeedSendTotal    int `json:"orderNeedSendTotal"`
			OrderNeedReceiveTotal int `json:"orderNeedReceiveTotal"`
		} `json:"orderStatusCountVO"`
	} `json:"data"`
	Errtag int `json:"errtag"`
}

func QueryOrder() (OrderResp, error) {
	url := "https://mall.bilibili.com/mall-up-c/order/list"

	payload := []byte(`{"orderCtime":[],"pageNum":1,"pageSize":10,"orderListType":2,"source":"pc","prePageOrderIds":[]}`)

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(payload))

	req.Header.Set("authority", "mall.bilibili.com")
	req.Header.Set("accept", "application/json, text/plain, */*")
	req.Header.Set("accept-language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
	req.Header.Set("content-type", "application/json")
	req.Header.Set("cookie", `buvid3=E05ED6BB-DCD2-265A-E647-70A934185CC376267infoc; b_nut=1668394676; _uuid=C39FAC36-BF33-B975-1D48-FA5AE48DF109777042infoc; buvid4=4963FEE0-8430-D22E-2505-116124337F2A77475-022111410-a4FtS1gnzAQLPI1ntc5n4w%3D%3D; rpdid=|(J~J~|ku)uJ0J\'uYY))YY|)Y; buvid_fp_plain=undefined; DedeUserID=369356107; DedeUserID__ckMd5=bb27f6050addab96; i-wanna-go-back=-1; b_ut=5; nostalgia_conf=-1; LIVE_BUVID=AUTO5516715981812789; is-2022-channel=1; header_theme_version=CLOSE; CURRENT_PID=61a943d0-ce28-11ed-9e01-9759edb161b0; FEED_LIVE_VERSION=V8; home_feed_column=5; browser_resolution=1946-1130; hit-new-style-dyn=1; hit-dyn-v2=1; CURRENT_FNVAL=4048; CURRENT_QUALITY=80; fingerprint=cb97f9c438025afd0c0208dca46d62b4; buvid_fp=cb97f9c438025afd0c0208dca46d62b4; bp_article_offset_369356107=842315435480186932; SESSDATA=f7b2468f%2C1710659096%2C71ec3%2A91CjBK-Ludo-GJqL8Vj_q4IdbV10nTE2ADW0Euay1EjkyEbgOt6qJST-Wgunn-QsnwMqoSVkFnT0RmeEZJcjUxcGtyQTdfbER4bzZhbDJGcVU0bTNTdnd2OE5JSWNKVHpaQUZLWTJEelMwRlZNMldMaUNxd1F3bGRjOWFMOWtwRjdzeUZkQVpRYk13IIEC; bili_jct=769ac88967167581718e218e09c1c342; sid=4mmnbjni; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2OTU0Mjk3NDAsImlhdCI6MTY5NTE3MDU0MCwicGx0IjotMX0.goyV4W4DTGboEg0syGZ8MsPs3vB_4zrEtLMEjJ1JaZ0; bili_ticket_expires=1695429740; PVID=2; bp_video_offset_369356107=843332449371947009; b_lsid=85BE996E_18AB696C232`)
	req.Header.Set("origin", "https://gf.bilibili.com")
	req.Header.Set("referer", "https://gf.bilibili.com/")
	req.Header.Set("sec-ch-ua", "\"Microsoft Edge\";v=\"117\", \"Not;A=Brand\";v=\"8\", \"Chromium\";v=\"117\"")
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-platform", "\"Windows\"")
	req.Header.Set("sec-fetch-mode", "cors")
	req.Header.Set("sec-fetch-dest", "empty")
	req.Header.Set("sec-fetch-site", "same-site")
	req.Header.Set("sec-gpc", "1")
	req.Header.Set("user-agent", "Mozilla/5.0 (Windows NT 10.0;Win64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36")
	var resp OrderResp

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		fmt.Println("请求失败:", err)
		return resp, err
	}
	defer res.Body.Close()
	err = json.NewDecoder(res.Body).Decode(&resp)
	if err != nil {
		fmt.Println("Failed to parse response:", err)
		return resp, err
	}
	// fmt.Printf("%+v\n", resp)
	return resp, nil
}

func SendEmail(to string, code string) error {
	e := email.NewEmail()
	e.From = "Kevin <<EMAIL>>"
	e.To = []string{to}
	e.Subject = "PDF Guru Anki更新：v1.1.1"
	// e.Text = []byte("Text Body is, of course, supported!")
	e.HTML = []byte(fmt.Sprintf(`
	<h1>更新内容</h1>
	<ul>
	<li>添加激活码</li>
	<li>修复同步服务器无法修改数据存储路径</li>
	<li>图片挖空支持添加笔记、跨页挖空</li>
	<li>修复其他已知问题</li>
	</ul>
	<div>你的激活码：%s</div>
	<div>下载地址：<br> 外链:https://kevin2li.lanzouw.com/b0cu804id 密码:5g61</div>
	`, code))
	// e.AttachFile("C:/Users/<USER>/code/pdf-guru-anki2/build/bin/pdf-guru-anki-windowns-amd64-v1.1.1.zip")
	err := e.Send("smtp.qq.com:587", smtp.PlainAuth("", "<EMAIL>", "agjnaiqunnluhgcb", "smtp.qq.com"))
	if err != nil {
		return err
	}
	fmt.Println("Email sent successfully!")
	return nil
}

func SendOrderEmail(to string, order OrderItem) error {
	e := email.NewEmail()
	e.From = "Kevin <<EMAIL>>"
	e.To = []string{to}
	e.Subject = "收到新订单"
	t := time.Unix(int64(order.PayTime), 0)
	formated_time := t.Format("2006-01-02 15:04:05")
	e.HTML = []byte(fmt.Sprintf(`
	<h1>收到新订单</h1>
	<ul>
	<li>订单编号：%d</li>
	<li>用户昵称：%s</li>
	<li>商品名称：%s</li>
	<li>支付时间：%s</li>
	<li>规格：%s</li>
	<li>单价：%.2f元</li>
	<li>数量：%d</li>
	</ul>
	`, order.OrderID, order.NickName, order.OrderSku.ItemsName, formated_time, order.OrderSku.SpecDesc, float32(order.OrderSku.Price/100.), order.OrderSku.SkuNum))
	// e.AttachFile("C:/Users/<USER>/code/pdf-guru-anki2/build/bin/pdf-guru-anki-windowns-amd64-v1.1.1.zip")
	err := e.Send("smtp.qq.com:587", smtp.PlainAuth("", "<EMAIL>", "agjnaiqunnluhgcb", "smtp.qq.com"))
	if err != nil {
		return err
	}
	fmt.Println("Email sent successfully!")
	return nil
}

func ReadCsv(path string) ([]User, error) {
	clientsFile, err := os.OpenFile(path, os.O_RDWR|os.O_CREATE, os.ModePerm)
	if err != nil {
		return nil, err
	}
	defer clientsFile.Close()
	clients := []User{}
	if err := gocsv.UnmarshalFile(clientsFile, &clients); err != nil { // Load clients from file
		return nil, err
	}
	// for _, client := range clients {
	// 	fmt.Println(client.Email, client.Code)
	// }
	return clients, nil
}

// func main() {
// 	users, err := ReadCsv("db.csv")
// 	if err != nil {
// 		panic(err)
// 	}
// 	failed := []User{}
// 	for i, user := range users {
// 		fmt.Printf("[%d/%d] send email to %s\n", i+1, len(users), user.Email)
// 		err = SendEmail(user.Email, user.Code)
// 		if err != nil {
// 			fmt.Println(err)
// 			failed = append(failed, user)
// 		}
// 		time.Sleep(2 * time.Second)
// 	}
// 	// save fialed users to csv
// 	f, err := os.OpenFile("failed.csv", os.O_RDWR|os.O_CREATE, os.ModePerm)
// 	if err != nil {
// 		panic(err)
// 	}
// 	defer f.Close()
// 	if err := gocsv.MarshalFile(&failed, f); err != nil {
// 		panic(err)
// 	}
// 	// SendEmail("<EMAIL>", "123")
// }

// func main() {
// 	orders, err := QueryOrder()
// 	if err != nil {
// 		fmt.Println(err)
// 	}
// 	to := "<EMAIL>"
// 	err = SendOrderEmail(to, orders.Data.SellerOrderPage.List[0])
// 	if err != nil {
// 		fmt.Println(err)
// 	}
// }
