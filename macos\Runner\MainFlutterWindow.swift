import Cocoa
import FlutterMacOS
import IOKit.ps
import LaunchAtLogin
import ApplicationServices
import CoreGraphics

class MainFlutterWindow: NSWindow {
  override func awakeFromNib() {
    let flutterViewController = FlutterViewController()
    let windowFrame = self.frame
    self.contentViewController = flutterViewController
    self.setFrame(windowFrame, display: true)
  
    let batteryChannel = FlutterMethodChannel(
      name: "samples.flutter.dev/battery",
      binaryMessenger: flutterViewController.engine.binaryMessenger)
    batteryChannel.setMethodCallHandler { (call, result) in
      switch call.method {
        case "getBatteryLevel":
          guard let level = self.getBatteryLevel() else {
            result(
              FlutterError(
                code: "UNAV<PERSON><PERSON><PERSON><PERSON>",
                message: "Battery level not available",
                details: nil))
            return
          }
          result(level)
        default:
          result(FlutterMethodNotImplemented)
      }
    }

    FlutterMethodChannel(
      name: "launch_at_startup", binaryMessenger: flutterViewController.engine.binaryMessenger
    )
    .setMethodCallHandler { (_ call: FlutterMethodCall, result: @escaping FlutterResult) in
      switch call.method {
      case "launchAtStartupIsEnabled":
        result(LaunchAtLogin.isEnabled)
      case "launchAtStartupSetEnabled":
        if let arguments = call.arguments as? [String: Any] {
          LaunchAtLogin.isEnabled = arguments["setEnabledValue"] as! Bool
        }
        result(nil)
      default:
        result(FlutterMethodNotImplemented)
      }
    }

    FlutterMethodChannel(
      name: "accessibility_permission",
      binaryMessenger: flutterViewController.engine.binaryMessenger
    )
    .setMethodCallHandler { (_ call: FlutterMethodCall, result: @escaping FlutterResult) in
      switch call.method {
      case "requestAccessibilityPermission":
        let options: NSDictionary = [kAXTrustedCheckOptionPrompt.takeUnretainedValue() as String: true]
        let accessEnabled = AXIsProcessTrustedWithOptions(options)
        
        if !accessEnabled {
          result(FlutterError(
            code: "PERMISSION_DENIED",
            message: "Accessibility permission not granted",
            details: nil
          ))
        } else {
          result(true)
        }
      case "checkAccessibilityPermission":
        let options: NSDictionary = [kAXTrustedCheckOptionPrompt.takeUnretainedValue() as String: false]
        let accessEnabled = AXIsProcessTrustedWithOptions(options)
        result(accessEnabled)
      case "simulatePaste":
        let source = CGEventSource(stateID: .hidSystemState)
        
        // Command (⌘) + V
        let cmdDown = CGEvent(keyboardEventSource: source, virtualKey: 0x37, keyDown: true)  // Command key
        let vDown = CGEvent(keyboardEventSource: source, virtualKey: 0x09, keyDown: true)    // V key
        let vUp = CGEvent(keyboardEventSource: source, virtualKey: 0x09, keyDown: false)     // V key
        let cmdUp = CGEvent(keyboardEventSource: source, virtualKey: 0x37, keyDown: false)   // Command key
        
        // Set Command modifier
        vDown?.flags = .maskCommand
        vUp?.flags = .maskCommand
        
        // Post events
        cmdDown?.post(tap: .cghidEventTap)
        vDown?.post(tap: .cghidEventTap)
        vUp?.post(tap: .cghidEventTap)
        cmdUp?.post(tap: .cghidEventTap)
        
        result(true)
      default:
        result(FlutterMethodNotImplemented)
      }
    }
    RegisterGeneratedPlugins(registry: flutterViewController)

    super.awakeFromNib()
  }

  private func getBatteryLevel() -> Int? {
    let info = IOPSCopyPowerSourcesInfo().takeRetainedValue()
    let sources: Array<CFTypeRef> = IOPSCopyPowerSourcesList(info).takeRetainedValue() as Array
    if let source = sources.first {
      let description =
        IOPSGetPowerSourceDescription(info, source).takeUnretainedValue() as! [String: AnyObject]
      if let level = description[kIOPSCurrentCapacityKey] as? Int {
        return level
      }
    }
    return nil
  }
}
