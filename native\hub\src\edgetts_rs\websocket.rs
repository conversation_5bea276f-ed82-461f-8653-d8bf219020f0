use futures_util::{SinkExt, StreamExt};
use std::sync::Arc;
use std::time::Duration;
use thiserror::Error;
use tokio::net::TcpStream;
use tokio::sync::Mutex;
use tokio::time::{sleep, timeout};
use tokio_tungstenite::tungstenite::client::IntoClientRequest;
use tokio_tungstenite::tungstenite::http::{Request, StatusCode};
use tokio_tungstenite::tungstenite::Error as TungsteniteError;
use tokio_tungstenite::{
    connect_async, tungstenite::protocol::Message,
    tungstenite::protocol::WebSocketConfig as TungsteniteConfig, MaybeTlsStream, WebSocketStream,
};

const MAX_RETRIES: u32 = 3;
const RETRY_DELAY_MS: u64 = 1000;
const CONNECTION_TIMEOUT_MS: u64 = 5000;
const HEARTBEAT_INTERVAL_MS: u64 = 30000;

#[derive(Error, Debug)]
pub enum WebSocketError {
    #[error("WebSocket connection error: {0}")]
    ConnectionError(String),
    #[error("WebSocket send error: {0}")]
    SendError(String),
    #[error("WebSocket receive error: {0}")]
    ReceiveError(String),
    #[error("WebSocket timeout error: {0}")]
    TimeoutError(String),
    #[error("WebSocket retry error: {0}")]
    RetryError(String),
    #[error("WebSocket HTTP error: {0}")]
    HttpError(StatusCode),
}

#[derive(Debug)]
pub struct WebSocketConfig {
    pub url: String,
    pub headers: Vec<(String, String)>,
    pub connection_timeout: Duration,
    pub heartbeat_interval: Duration,
    pub retry_delay: Duration,
    pub max_retries: u32,
}

impl Default for WebSocketConfig {
    fn default() -> Self {
        Self {
            url: String::new(),
            headers: Vec::new(),
            connection_timeout: Duration::from_millis(CONNECTION_TIMEOUT_MS),
            heartbeat_interval: Duration::from_millis(HEARTBEAT_INTERVAL_MS),
            retry_delay: Duration::from_millis(RETRY_DELAY_MS),
            max_retries: MAX_RETRIES,
        }
    }
}

type WebSocketConnection = WebSocketStream<MaybeTlsStream<TcpStream>>;

pub struct RobustWebSocket {
    config: WebSocketConfig,
    ws: Arc<Mutex<Option<WebSocketConnection>>>,
}

impl RobustWebSocket {
    pub fn new(config: WebSocketConfig) -> Self {
        Self {
            config,
            ws: Arc::new(Mutex::new(None)),
        }
    }

    pub async fn connect(&self) -> Result<(), WebSocketError> {
        let mut retries = 0;
        let mut last_error = None;

        while retries < self.config.max_retries {
            match self.try_connect().await {
                Ok(ws) => {
                    let mut ws_guard = self.ws.lock().await;
                    *ws_guard = Some(ws);
                    self.start_heartbeat();
                    return Ok(());
                }
                Err(e) => {
                    last_error = Some(e);
                    retries += 1;
                    if retries < self.config.max_retries {
                        sleep(self.config.retry_delay).await;
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| {
            WebSocketError::RetryError("Failed to connect after max retries".to_string())
        }))
    }

    async fn try_connect(&self) -> Result<WebSocketConnection, WebSocketError> {
        // Create request with headers
        let mut request = self.config.url.clone().into_client_request().map_err(|e| {
            println!("创建请求失败: {}", e);
            WebSocketError::ConnectionError(format!("Failed to create request: {}", e))
        })?;

        {
            let headers = request.headers_mut();
            for (key, value) in &self.config.headers {
                let header_name =
                    tokio_tungstenite::tungstenite::http::header::HeaderName::from_bytes(
                        key.as_bytes(),
                    )
                    .map_err(|e| {
                        println!("无效的请求头名称 '{}': {}", key, e);
                        WebSocketError::ConnectionError(format!("Invalid header name: {}", key))
                    })?;
                let header_value =
                    tokio_tungstenite::tungstenite::http::header::HeaderValue::from_str(value)
                        .map_err(|e| {
                            println!("无效的请求头值 '{}': {}", value, e);
                            WebSocketError::ConnectionError(format!(
                                "Invalid header value: {}",
                                value
                            ))
                        })?;
                headers.insert(header_name, header_value);
            }
        }

        // Configure WebSocket
        let ws_config = TungsteniteConfig::default();

        // Attempt connection with timeout
        let (ws, response) = timeout(self.config.connection_timeout, async {
            println!("尝试连接WebSocket: {}", self.config.url);
            connect_async(request).await
        })
        .await
        .map_err(|e| {
            println!("WebSocket连接超时: {}", e);
            WebSocketError::TimeoutError(e.to_string())
        })?
        .map_err(|e| match e {
            TungsteniteError::Http(response) => {
                println!("HTTP错误: 状态码 {}", response.status());
                WebSocketError::HttpError(response.status())
            }
            _ => {
                println!("WebSocket连接错误: {}", e);
                WebSocketError::ConnectionError(e.to_string())
            }
        })?;

        // Check status code
        if response.status() != StatusCode::SWITCHING_PROTOCOLS {
            println!("未能切换到WebSocket协议: 状态码 {}", response.status());
            return Err(WebSocketError::HttpError(response.status()));
        }

        println!("WebSocket连接成功");
        Ok(ws)
    }

    fn start_heartbeat(&self) {
        let ws = Arc::clone(&self.ws);
        let interval = self.config.heartbeat_interval;

        tokio::spawn(async move {
            loop {
                sleep(interval).await;

                let mut ws_guard = ws.lock().await;
                if let Some(ws) = ws_guard.as_mut() {
                    if let Err(_) = ws.send(Message::Ping(vec![])).await {
                        // Connection might be dead, try to reconnect
                        *ws_guard = None;
                        break;
                    }
                } else {
                    break;
                }
            }
        });
    }

    pub async fn send(&self, message: Message) -> Result<(), WebSocketError> {
        let mut retries = 0;
        let mut last_error = None;

        while retries < self.config.max_retries {
            let mut ws_guard = self.ws.lock().await;
            if let Some(ws) = ws_guard.as_mut() {
                match ws.send(message.clone()).await {
                    Ok(_) => return Ok(()),
                    Err(e) => {
                        println!(
                            "发送消息失败(重试 {}/{}): {}",
                            retries + 1,
                            self.config.max_retries,
                            e
                        );
                        last_error = Some(WebSocketError::SendError(e.to_string()));
                        retries += 1;
                        if retries < self.config.max_retries {
                            drop(ws_guard);
                            sleep(self.config.retry_delay).await;
                            println!("尝试重新连接WebSocket");
                            self.connect().await?;
                        }
                    }
                }
            } else {
                println!("WebSocket未连接，尝试连接");
                drop(ws_guard);
                self.connect().await?;
                continue;
            }
        }

        let error_msg = format!("发送消息失败，已重试{}次", self.config.max_retries);
        println!("{}", error_msg);
        Err(last_error.unwrap_or_else(|| {
            WebSocketError::RetryError("Failed to send message after max retries".to_string())
        }))
    }

    pub async fn receive(&self) -> Result<Option<Message>, WebSocketError> {
        let mut retries = 0;
        let mut last_error = None;

        while retries < self.config.max_retries {
            let mut ws_guard = self.ws.lock().await;
            if let Some(ws) = ws_guard.as_mut() {
                match ws.next().await {
                    Some(Ok(msg)) => return Ok(Some(msg)),
                    Some(Err(e)) => {
                        println!(
                            "接收消息失败(重试 {}/{}): {}",
                            retries + 1,
                            self.config.max_retries,
                            e
                        );
                        last_error = Some(WebSocketError::ReceiveError(e.to_string()));
                        retries += 1;
                        if retries < self.config.max_retries {
                            drop(ws_guard);
                            sleep(self.config.retry_delay).await;
                            println!("尝试重新连接WebSocket");
                            self.connect().await?;
                        }
                    }
                    None => {
                        println!("WebSocket连接已关闭");
                        return Ok(None);
                    }
                }
            } else {
                println!("WebSocket未连接，尝试连接");
                drop(ws_guard);
                self.connect().await?;
                continue;
            }
        }

        let error_msg = format!("接收消息失败，已重试{}次", self.config.max_retries);
        println!("{}", error_msg);
        Err(last_error.unwrap_or_else(|| {
            WebSocketError::RetryError("Failed to receive message after max retries".to_string())
        }))
    }
}
