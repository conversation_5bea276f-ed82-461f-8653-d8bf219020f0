import json
import re
import httpx
from loguru import logger
HEADERS = {
  "Connection": "keep-alive",
  "Accept": "*/*",
  "Accept-Encoding": "gzip, deflate, br",
}

def query_word(word: str):
    try:
        resp = httpx.get(f"https://www.bing.com/api/v7/dictionarywords/search?q={word}&appid=371E7B2AF0F9B84EC491D731DF90A55719C7D209&mkt=zh-cn&pname=bingdict&img=1", headers=HEADERS)
        print(resp)
        if resp.status_code == 200:
            return resp.json()
        else:
            logger.error(resp)
            return ""
    except:
        return ""

def clean_sentence_markup(raw_text: str) -> str:
    """
    清理必应词典中类似 "{1#只见$1}{##*{8#缝$8}*$$}" 的特殊标记。
    它通过查找 "#" 和 "$" 之间的文本并拼接起来，实现文本的提取。
    """
    if not isinstance(raw_text, str):
        return ""
    # 正则表达式查找所有在 # 和 $ 之间的内容
    parts = re.findall(r'#([^$]*)\$', raw_text)
    return "".join(parts)

def transform_bing_response(data: dict) -> dict:
    """
    将必应词典的JSON响应转换为目标格式。
    """
    if not data or 'value' not in data or not data['value']:
        return {"error": "无效或空的输入数据"}

    source_data = data['value'][0]

    # 初始化目标数据结构
    target = {
        "word": source_data.get('name', ''),
        "pronunciation": {"uk": None, "us": None},
        "audio_url": {
            "uk": source_data.get('pronunciationAudio', {}).get('contentUrl'),
            "us": source_data.get('pronunciationAudio', {}).get('contentUrl')
        },
        "inflections": {},
        "tags": [],  # 源数据中未提供此信息
        "definitions": [],
        "sentences": [],
        "image_urls": [],  # 源数据中未提供此信息
        "phrases": [],
        "dictionaries": [
            {
                "name": "必应词典",
                "url_template": f"https://cn.bing.com/dict/search?q={source_data.get('name', '')}"
            }
        ],
        "synonyms": "",
        "antonyms": "",  # 源数据中未提供此信息
        "etymology": ""  # 源数据中未提供此信息
    }

    # 用于映射词性和词形变化的字典
    pos_map = {"名词": "n.", "动词": "v.", "形容词": "adj.", "副词": "adv."}
    inflection_map = {"一般现在时": "third_person", "现在分词": "present_participle", "过去式": "past_tense"}
    all_synonyms = []

    # 遍历 meaningGroups 提取详细信息
    for group in source_data.get('meaningGroups', []):
        if not group.get('partsOfSpeech'):
            continue

        pos_info = group['partsOfSpeech'][0]
        description = pos_info.get('description', '')
        pos_name = pos_info.get('name', '')

        # --- 提取发音 ---
        if description in ['发音', '权威英汉双解发音']:
            key = 'us' if pos_name.lower() == 'us' else 'uk'
            if group.get('meanings'):
                pron = group['meanings'][0].get('richDefinitions', [{}])[0].get('fragments', [{}])[0].get('text')
                if pron:
                    target['pronunciation'][key] = f"/{pron}/"

        # --- 提取词形变化 ---
        elif pos_name == '变形':
            if group.get('meanings'):
                fragments = group['meanings'][0].get('richDefinitions', [{}])[0].get('fragments', [])
                for frag in fragments:
                    text = frag.get('text', '')
                    if '：' in text:
                        term, value = text.split('：', 1)
                        if term in inflection_map:
                            target['inflections'][inflection_map[term]] = value

        # --- 提取释义 (从 "权威英汉双解") ---
        elif description == '权威英汉双解':
            pos_abbr = pos_map.get(pos_name, pos_name)
            if group.get('meanings'):
                for sense_block in group['meanings'][0].get('richDefinitions', []):
                    # 每个 sense_block 对应一个独立的词义
                    definition_entry = {"pos": pos_abbr, "meaning_en": "", "meaning_zh": "", "sentences": []}
                    
                    for item in sense_block.get('subDefinitions', []):
                        domains = item.get('domains', [])
                        fragments = item.get('fragments', [])
                        
                        if 'Definition' in domains and len(fragments) >= 2:
                            definition_entry['meaning_en'] = fragments[0].get('text', '')
                            definition_entry['meaning_zh'] = fragments[1].get('text', '')
                        elif 'Examples' in domains and 'Sentence' in domains and len(fragments) >= 2:
                            example = {
                                "sentence_en": fragments[0].get('text', ''),
                                "sentence_zh": fragments[1].get('text', ''),
                                "image_url": None, "audio_url": None
                            }
                            definition_entry['sentences'].append(example)
                    
                    if definition_entry['meaning_en'] or definition_entry['meaning_zh']:
                        target['definitions'].append(definition_entry)

        # --- 提取顶层例句 ---
        elif description == '例句':
            if group.get('meanings'):
                for rich_def in group['meanings'][0].get('richDefinitions', []):
                    examples = rich_def.get('examples', [])
                    # 数据格式为 [中文, ID, 英文, ID, ...]
                    for i in range(0, len(examples), 4):
                        if i + 3 < len(examples):
                            sentence_zh = clean_sentence_markup(examples[i])
                            sentence_en = clean_sentence_markup(examples[i+2])
                            if sentence_en and sentence_zh:
                                target['sentences'].append({
                                    "sentence_en": sentence_en, "sentence_zh": sentence_zh,
                                    "image_url": None, "audio_url": None
                                })

        # --- 提取词组 ---
        elif description == '词组':
            if group.get('meanings'):
                for rich_def in group['meanings'][0].get('richDefinitions', []):
                    examples = rich_def.get('examples', [])
                    # 数据格式为 [英文, 中文, 英文, 中文, ...]
                    for i in range(0, len(examples), 2):
                        if i + 1 < len(examples):
                            target['phrases'].append({
                                "phrase_en": examples[i],
                                "phrase_zh": examples[i+1]
                            })
        
        # --- 提取同义词 ---
        elif description == '分类词典':
            if group.get('meanings'):
                for syn in group['meanings'][0].get('synonyms', []):
                    if 'name' in syn:
                        all_synonyms.append(syn['name'])

    # --- 后处理 ---
    # 合并同义词并去重
    if all_synonyms:
        target['synonyms'] = ", ".join(sorted(list(set(all_synonyms))))
        
    # 如果没有找到UK/US音标，使用顶层音标作为备用
    if target['pronunciation']['us'] is None and target['pronunciation']['uk'] is None:
        top_pron = source_data.get('pronunciation')
        if top_pron:
            pron_text = f"/{top_pron}/"
            target['pronunciation']['us'] = pron_text
            target['pronunciation']['uk'] = pron_text
            
    return target


if __name__ == "__main__":
    json_response = query_word("crack")
    # --- 执行转换 ---
    # 2. 调用转换函数
    transformed_data = transform_bing_response(json_response)

    # 3. 打印格式化后的结果
    # 使用 ensure_ascii=False 来正确显示中文字符
    # indent=2 用于美化输出
    print(json.dumps(transformed_data, ensure_ascii=False, indent=2))
