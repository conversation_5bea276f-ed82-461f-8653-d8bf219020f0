import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/crop.dart';

class PDFCropPage extends StatefulWidget {
  const PDFCropPage({super.key});

  @override
  State<PDFCropPage> createState() => _PDFCropPageState();
}

class _PDFCropPageState extends State<PDFCropPage> {
  final controller = Get.put(PDFCropPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.crop.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.crop.description'.tr, style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        const SizedBox(height: 4),
                        ShadRadioGroupCustom(
                          label: 'toolbox.crop.cropType'.tr,
                          initialValue: controller.cropMode.value,
                          items: controller.cropModeList,
                          onChanged: (value) {
                            controller.cropMode.value = value;
                          },
                        ),
                        if (controller.cropMode.value == "annotate") ...[
                          ShadSwitchCustom(
                            label: 'toolbox.crop.expandMode'.tr,
                            initialValue: controller.expandMode.value,
                            onChanged: (v) {
                              controller.expandMode.value = v;
                            },
                          ),
                        ],
                        if (controller.cropMode.value == "margin") ...[
                          ShadRadioGroupCustom(
                            label: 'toolbox.crop.unit'.tr,
                            initialValue: controller.unit.value,
                            items: controller.unitList,
                            onChanged: (value) {
                              controller.unit.value = value;
                            },
                          ),
                          ShadInputWithValidate(
                              label: 'toolbox.crop.margin.top'.tr,
                              placeholder:
                                  'toolbox.crop.margin.topPlaceholder'.tr,
                              initialValue: controller.top.value.toString(),
                              onChanged: (value) {
                                controller.top.value = double.tryParse(value) ??
                                    controller.top.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.crop.margin.topPlaceholder'
                                      .tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return "toolbox.validation.enterNonNegativeNumber".tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              label: 'toolbox.crop.margin.bottom'.tr,
                              placeholder:
                                  'toolbox.crop.margin.bottomPlaceholder'.tr,
                              initialValue: controller.bottom.value.toString(),
                              onChanged: (value) {
                                controller.bottom.value =
                                    double.tryParse(value) ??
                                        controller.bottom.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.crop.margin.bottomPlaceholder'
                                      .tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return "toolbox.validation.enterNonNegativeNumber".tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              label: 'toolbox.crop.margin.left'.tr,
                              placeholder:
                                  'toolbox.crop.margin.leftPlaceholder'.tr,
                              initialValue: controller.left.value.toString(),
                              onChanged: (value) {
                                controller.left.value =
                                    double.tryParse(value) ??
                                        controller.left.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.crop.margin.leftPlaceholder'
                                      .tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return "toolbox.validation.enterNonNegativeNumber".tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              label: 'toolbox.crop.margin.right'.tr,
                              placeholder:
                                  'toolbox.crop.margin.rightPlaceholder'.tr,
                              initialValue: controller.right.value.toString(),
                              onChanged: (value) {
                                controller.right.value =
                                    double.tryParse(value) ??
                                        controller.right.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.crop.margin.rightPlaceholder'
                                      .tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return "toolbox.validation.enterNonNegativeNumber".tr;
                                }
                                return "";
                              }),
                        ],
                        ShadSwitchCustom(
                          label: 'toolbox.crop.keepPaperSize'.tr,
                          initialValue: controller.keepPaperSize.value,
                          onChanged: (v) {
                            controller.keepPaperSize.value = v;
                          },
                        ),
                        ShadRadioGroupCustom(
                          label: 'toolbox.crop.outputFormat'.tr,
                          initialValue: controller.outputFormat.value,
                          items: controller.outputFormatList,
                          onChanged: (value) {
                            controller.outputFormat.value = value;
                          },
                        ),
                        ShadInputWithValidate(
                            key: const ValueKey("split-range"),
                            label: 'toolbox.common.pageRange'.tr,
                            placeholder:
                                'toolbox.common.pageRangePlaceholder'.tr,
                            initialValue: controller.pageRange.value,
                            onChanged: (value) {
                              controller.pageRange.value = value;
                            },
                            onValidate: (value) async {
                              if (validatePageRange(value)) {
                                return "";
                              }
                              return 'toolbox.common.enterPageRange'.tr;
                            }),
                        if (PathUtils.isDesktop)
                          ShadSelectCustom(
                            key: const ValueKey("output-position"),
                            label: 'toolbox.common.outputLocation'.tr,
                            placeholder:
                                'toolbox.common.selectOutputLocation'.tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.common.outputDirectory'.tr,
                            placeholder:
                                Text('toolbox.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.common.inputFile'.tr,
                          placeholder:
                              Text('toolbox.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['pdf'],
                          isRequired: true,
                          allowMultiple: true,
                          initialValue: controller.selectedFilePaths,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
