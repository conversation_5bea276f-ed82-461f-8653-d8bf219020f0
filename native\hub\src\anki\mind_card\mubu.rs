#![allow(unused)]

use crate::anki::mind_card::common::{
    mind_node_to_anki_notes, normalize_color, ClozeContext, MindNode, StyleAttribute,
};
use crate::anki::models::{get_mindmap_card_assets, AnkiNote};
use crate::anki::utils::save_to_temp_file;
use rust_search::SearchBuilder;
use scraper::{Html, Selector};
use serde::{Deserialize, Serialize};
use std::fs;
use std::io;
use std::io::BufWriter;
use std::path::Path;
use std::path::PathBuf;
use tempfile::TempDir;
use ulid::Ulid;
use urlencoding;

#[derive(Debug)]
pub struct MubuCardConfig {
    pub file_path: String,
    pub deck_name: String,
    pub model_name: String,
    pub cloze_styles: Vec<String>,
    pub text_colors: Vec<String>,
    pub highlight_colors: Vec<String>,
    pub output_path: String,
    pub tags: Vec<String>,
    pub map_id: String,
}

// 新增幕布图片结构
#[derive(Deserialize, Debug)]
struct MubuImage {
    #[serde(default)]
    uri: Option<String>,
    #[serde(default)]
    id: Option<String>,
    #[serde(default)]
    ow: Option<i32>,
    #[serde(default)]
    oh: Option<i32>,
    #[serde(default)]
    w: Option<i32>,
}

// 为幕布JSON格式添加必要的结构体
#[derive(Deserialize, Debug)]
struct MubuJsonNode {
    id: String,
    #[serde(default)]
    text: String,
    #[serde(default)]
    children: Vec<MubuJsonNode>,
    #[serde(default)]
    heading: Option<i32>,
    #[serde(default)]
    note: Option<String>,
    #[serde(default)]
    modified: Option<i64>,
    #[serde(default)]
    images: Option<Vec<MubuImage>>,
    #[serde(default)]
    collapsed: Option<bool>,
}

#[derive(Deserialize, Debug)]
struct MubuJsonDefinition {
    nodes: Vec<MubuJsonNode>,
    #[serde(default)]
    structure: Option<String>,
    #[serde(default)]
    structureSetting: Option<String>,
}

#[derive(Deserialize, Debug)]
struct MubuJsonDocument {
    id: String,
    name: String,
    #[serde(default)]
    folderId: Option<String>,
    #[serde(default)]
    createTime: Option<i64>,
    #[serde(default)]
    updateTime: Option<i64>,
    #[serde(default)]
    itemCount: Option<i32>,
    #[serde(default)]
    encrypted: Option<bool>,
    #[serde(default)]
    author: Option<serde_json::Value>,
    definition: MubuJsonDefinition,
    #[serde(default)]
    directory: Option<Vec<serde_json::Value>>,
}

// OPML解析实现
fn parse_opml(
    content: &str,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
    images: &mut Vec<String>,
) -> Option<MindNode> {
    let doc = roxmltree::Document::parse(content).ok()?;
    let outline = doc.descendants().find(|n| n.has_tag_name("body"))?;
    Some(parse_opml_node(
        &outline,
        0,
        cloze_styles,
        cloze_ctx,
        images,
    ))
}

fn parse_opml_node(
    node: &roxmltree::Node,
    depth: usize,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
    images: &mut Vec<String>,
) -> MindNode {
    let raw_text = node.attribute("_mubu_text").unwrap_or_default();
    let mut content = decode_mubu_html(raw_text);
    content = convert_cloze(content, cloze_styles, cloze_ctx);

    // 图片解析
    if let Some(raw_images_json) = node.attribute("_mubu_images") {
        let image_content = decode_mubu_html(raw_images_json);
        if let Ok(images_json) = serde_json::from_str::<Vec<MubuImage>>(&image_content) {
            for img in images_json {
                if let Some(uri) = img.uri {
                    if uri.starts_with("document_image/") {
                        // 构建完整的幕布文档图片URL
                        let full_url = format!("https://document-image.mubu.com/{}", uri);
                        dbg!(&uri);

                        // 添加图片HTML
                        content.push_str(&format!(
                            r#"<div><img src="{}" style="width: {}px;"/></div>"#,
                            full_url,
                            img.w.unwrap_or(400)
                        ));

                        // 不再查找本地图片文件
                    } else {
                        // 处理其他类型的图片URI
                        content.push_str(&format!(
                            r#"<div><img src="{}" style="width: {}px;"/></div>"#,
                            uri,
                            img.w.unwrap_or(400)
                        ));
                    }
                }
            }
        } else {
            eprintln!("Failed to parse images JSON: {}", content);
        }
    }
    // 备注解析
    let raw_notes = node.attribute("_mubu_note").unwrap_or_default();
    let mut notes = decode_mubu_html(raw_notes);
    if !notes.is_empty() {
        content.push_str(&format!(
            r#"<div style="font-size: 0.7em; color: #888;">{}</div>"#,
            notes
        ));
    }

    // 子节点解析
    let children = node
        .children()
        .filter(|n| n.has_tag_name("outline"))
        .map(|n| parse_opml_node(&n, depth + 1, cloze_styles, cloze_ctx, images))
        .collect();

    MindNode {
        id: Ulid::new().to_string(),
        content,
        children,
    }
}

// Freemind解析实现
fn parse_freemind(
    content: &str,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
    images: &mut Vec<String>,
) -> Option<MindNode> {
    let doc = roxmltree::Document::parse(content).ok()?;
    let root = doc.descendants().find(|n| n.has_tag_name("node"))?;
    Some(parse_freemind_node(
        &root,
        0,
        cloze_styles,
        cloze_ctx,
        images,
    ))
}

fn parse_freemind_node(
    node: &roxmltree::Node,
    depth: usize,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
    images: &mut Vec<String>,
) -> MindNode {
    // 使用正确的attribute方法
    let mut raw_text = node.attribute("_mubu_text").unwrap_or_default();
    if raw_text.is_empty() {
        raw_text = node.attribute("TEXT").unwrap_or_default();
    }
    let content = decode_mubu_html(raw_text);
    let mut content = convert_cloze(content, cloze_styles, cloze_ctx);

    // 递归处理子节点
    let children = node
        .children()
        .filter(|n| n.has_tag_name("node"))
        .map(|n| parse_freemind_node(&n, depth + 1, cloze_styles, cloze_ctx, images))
        .collect();

    MindNode {
        id: Ulid::new().to_string(),
        content,
        children,
    }
}

// 幕布HTML解码（同时处理样式）
fn decode_mubu_html(encoded: &str) -> String {
    let decoded = urlencoding::decode(encoded)
        .unwrap_or_default()
        .into_owned();
    decoded
}

pub fn get_mubu_roaming_path() -> Option<PathBuf> {
    #[cfg(target_os = "windows")]
    {
        dirs::config_dir().map(|path| {
            path.join("Mubu")
                .join("mubu_app_data")
                .join("mubu_data")
                .join("caches")
        })
    }

    #[cfg(target_os = "macos")]
    {
        dirs::config_dir().map(|path| {
            path.join("Mubu")
                .join("mubu_app_data")
                .join("mubu_data")
                .join("caches")
        })
    }

    #[cfg(target_os = "linux")]
    {
        None // Linux 暂不支持
    }
    #[cfg(target_os = "android")]
    {
        None // Android 暂不支持
    }

    #[cfg(target_os = "ios")]
    {
        None // iOS 暂不支持
    }
}

fn convert_cloze(
    html: String,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
) -> String {
    if cloze_styles.is_none() {
        return html;
    }

    let cloze_styles = cloze_styles.unwrap();
    let document = Html::parse_fragment(&html);
    let mut output = html.clone();
    let mut replacements = Vec::new();

    // 构建所有需要查找的选择器
    let mut selectors = Vec::new();

    for style in cloze_styles {
        match style {
            StyleAttribute::Colors(colors) => {
                for color in colors {
                    let class_name = format!("text-color-{}", color);
                    if let Ok(selector) = Selector::parse(&format!(".{}", class_name)) {
                        selectors.push(selector);
                    }
                }
            }
            StyleAttribute::Highlights(colors) => {
                for color in colors {
                    let class_name = format!("highlight-{}", color);
                    if let Ok(selector) = Selector::parse(&format!(".{}", class_name)) {
                        selectors.push(selector);
                    }
                }
            }
            other_style => {
                let class_name = match other_style {
                    StyleAttribute::Bold => "bold",
                    StyleAttribute::Italic => "italic",
                    StyleAttribute::Underline => "underline",
                    StyleAttribute::StrikeThrough => "strikethrough",
                    _ => continue,
                };
                if let Ok(selector) = Selector::parse(&format!(".{}", class_name)) {
                    selectors.push(selector);
                }
            }
        }
    }

    // 一次性查找所有需要修改的元素
    for selector in selectors {
        for element in document.select(&selector) {
            let node = element.value();
            if let Some(original_class) = node.attr("class") {
                if !original_class.contains("cloze activated") {
                    let new_class = format!("{} cloze activated", original_class);

                    // 检查是否已有 onclick 属性
                    let onclick_attr = if let Some(original_onclick) = node.attr("onclick") {
                        if !original_onclick.contains("classList.toggle") {
                            format!("{};this.classList.toggle('activated')", original_onclick)
                        } else {
                            original_onclick.to_string()
                        }
                    } else {
                        "this.classList.toggle('activated')".to_string()
                    };

                    // 如果元素已有 onclick
                    if node.attr("onclick").is_some() {
                        replacements.push((
                            format!(
                                "class=\"{}\" onclick=\"{}\"",
                                original_class,
                                node.attr("onclick").unwrap_or_default()
                            ),
                            format!("class=\"{}\" onclick=\"{}\"", new_class, onclick_attr),
                        ));
                    } else {
                        // 如果元素没有 onclick
                        replacements.push((
                            format!("class=\"{}\"", original_class),
                            format!("class=\"{}\" onclick=\"{}\"", new_class, onclick_attr),
                        ));
                    }
                }
            }
        }
    }

    // 按照位置从后向前替换，避免位置偏移
    replacements.sort_by_key(|(old, _)| std::cmp::Reverse(output.find(old).unwrap_or(0)));

    for (old, new) in replacements {
        if let Some(start_pos) = output.find(&old) {
            output.replace_range(start_pos..start_pos + old.len(), &new);
        }
    }

    output
}

// JSON格式解析实现
fn parse_json(
    content: &str,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
    images: &mut Vec<String>,
) -> Option<MindNode> {
    // 解析JSON
    let json_doc: MubuJsonDocument = serde_json::from_str(content).ok()?;
    let document_id = json_doc.id.clone();

    // 创建根节点，使用文档名称作为内容
    let mut root = MindNode {
        id: format!("mubu_{}", document_id),
        content: format!("<h1>{}</h1>", json_doc.name.clone()),
        children: Vec::new(),
    };

    // 解析所有子节点
    for node in &json_doc.definition.nodes {
        root.children.push(parse_json_node(
            node,
            cloze_styles,
            cloze_ctx,
            images,
            &document_id,
        ));
    }

    Some(root)
}

fn parse_json_node(
    node: &MubuJsonNode,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
    images: &mut Vec<String>,
    document_id: &str,
) -> MindNode {
    // 处理文本内容 - 保持原始HTML格式
    let mut content = node.text.clone();

    // 幕布JSON中的文本内容已经是HTML格式，不需要像OPML那样解码
    // 但需要应用挖空处理
    content = convert_cloze(content, cloze_styles, cloze_ctx);

    // 处理图片
    if let Some(node_images) = &node.images {
        for img in node_images {
            if let Some(uri) = &img.uri {
                if uri.starts_with("document_image/") {
                    // 构建完整的幕布文档图片URL
                    let full_url = format!("https://document-image.mubu.com/{}", uri);

                    // 添加图片HTML
                    content.push_str(&format!(
                        r#"<div><img src="{}" style="width: {}px;"/></div>"#,
                        full_url,
                        img.w.unwrap_or(400)
                    ));

                    // 不再查找本地图片文件
                } else {
                    // 处理其他类型的图片URI
                    content.push_str(&format!(
                        r#"<div><img src="{}" style="width: {}px;"/></div>"#,
                        uri,
                        img.w.unwrap_or(400)
                    ));
                }
            }
        }
    }

    // 处理备注
    if let Some(note) = &node.note {
        if !note.is_empty() {
            content.push_str(&format!(
                r#"<div style="font-size: 0.7em; color: #888;">{}</div>"#,
                note
            ));
        }
    }

    // 递归处理子节点
    let children = node
        .children
        .iter()
        .map(|child| parse_json_node(child, cloze_styles, cloze_ctx, images, document_id))
        .collect();

    // 构造节点ID: mubu_文档ID_节点ID
    let node_id = format!("mubu_{}_{}", document_id, node.id);

    MindNode {
        id: node_id,
        content,
        children,
    }
}

// 主处理函数
pub async fn make_mubu_card(
    config: MubuCardConfig,
    progress_callback: impl Fn(f64, f64, String),
) -> Result<(), io::Error> {
    // 解析文件内容
    let content = fs::read_to_string(&config.file_path)?;
    // 处理样式配置
    let cloze_styles: Vec<StyleAttribute> = config
        .cloze_styles
        .iter()
        .filter_map(|s| match s.as_str() {
            "bold" => Some(StyleAttribute::Bold),
            "italic" => Some(StyleAttribute::Italic),
            "underline" => Some(StyleAttribute::Underline),
            "strikethrough" => Some(StyleAttribute::StrikeThrough),
            "text_color" => Some(StyleAttribute::Colors(config.text_colors.clone())),
            "text_highlight" => Some(StyleAttribute::Highlights(config.highlight_colors.clone())),
            _ => None,
        })
        .collect();

    // 生成思维节点树
    let mut cloze_ctx = ClozeContext::new();
    let mut images = vec![];
    let extension = Path::new(&config.file_path)
        .extension()
        .and_then(|s| s.to_str())
        .unwrap_or("");
    dbg!(&extension);
    progress_callback(20.0, 100.0, "正在解析思维导图...".to_string());
    let mut root = match extension {
        "opml" => parse_opml(&content, Some(&cloze_styles), &mut cloze_ctx, &mut images)
            .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Invalid OPML file format")),
        "mm" => parse_freemind(&content, Some(&cloze_styles), &mut cloze_ctx, &mut images)
            .ok_or_else(|| {
                io::Error::new(io::ErrorKind::InvalidData, "Invalid Freemind file format")
            }),
        "json" => parse_json(&content, Some(&cloze_styles), &mut cloze_ctx, &mut images)
            .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Invalid JSON file format")),
        _ => Err(io::Error::new(
            io::ErrorKind::InvalidData,
            "Unsupported file format",
        )),
    }?;

    // 如果是OPML格式，设置标题
    if extension == "opml" {
        let doc = roxmltree::Document::parse(&content)
            .map_err(|_| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse XML"))?;
        let head = doc
            .descendants()
            .find(|n| n.has_tag_name("head"))
            .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "No head found in XML"))?;
        let title = head
            .descendants()
            .find(|n| n.has_tag_name("title"))
            .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "No title found in XML"))?;
        root.content = title.text().unwrap_or_default().to_string();
    }

    // 调试输出图片列表
    dbg!(&images);

    progress_callback(40.0, 100.0, "思维导图解析完成".to_string());
    progress_callback(50.0, 100.0, "正在生成 Anki 笔记...".to_string());

    // 生成Anki笔记
    let mut map_id = "".to_string();
    if config.map_id.is_empty() {
        map_id = Ulid::new().to_string();
    } else {
        map_id = config.map_id.clone();
    }
    let notes: Vec<AnkiNote> = mind_node_to_anki_notes(
        map_id.to_string(),
        &root,
        config.deck_name.clone(),
        config.tags.clone(),
    );
    progress_callback(80.0, 100.0, "Anki 笔记生成完成".to_string());

    // 现在只需要处理tree.js文件，不再处理图片
    let temp_dir = crate::anki::utils::get_temp_dir()
        .await
        .map_err(|e| io::Error::new(io::ErrorKind::Other, e.to_string()))?;
    let tree_js_path = PathBuf::from(temp_dir.clone()).join(format!("__{}.js", map_id.to_string()));
    fs::write(&tree_js_path, &root.to_js())?;
    let media_files = vec![tree_js_path.to_string_lossy().to_string()];

    dbg!(&media_files);
    progress_callback(90.0, 100.0, "正在生成 apkg 文件...".to_string());
    // 修改APKG生成调用
    crate::anki::models::gen_apkg(
        notes,
        Some(media_files),
        &config.output_path,
        None,
        false,
        Some(vec!["mindmap_card".to_string()]),
    )
    .await?;
    progress_callback(100.0, 100.0, "导出完成".to_string());

    Ok(())
}

#[cfg(test)]
mod tests {
    use rinf::debug_print;
    use tokio;

    use super::*;
    use std::fs;
    use std::path::Path;
    use std::path::PathBuf;
    const TEST_OPML_PATH: &str = "/Users/<USER>/Downloads/✨21天塑造习惯2.opml";
    const TEST_FREEMIND_PATH: &str = "/Users/<USER>/Downloads/✨21天塑造习惯.mm";
    const TEST_JSON_PATH: &str = "/Users/<USER>/code/anki-guru/anki_guru/native/hub/src/anki/mind_card/糖尿病病理生理与临床管理.json";
    const TEST_OUTPUT_PATH: &str = "src/tests/decoded_output.opml";

    // #[tokio::test]
    async fn test_make_mubu_card_opml() {
        let mind_path = TEST_OPML_PATH;
        let parent_dir = Path::new(mind_path).parent().unwrap();
        let output_path = parent_dir.join("output.apkg");

        let config = MubuCardConfig {
            file_path: mind_path.to_string(),
            deck_name: "Test Deck".to_string(),
            model_name: "Kevin Mindmap Card v3".to_string(),
            cloze_styles: vec!["text_color".to_string()],
            text_colors: vec!["red".to_string()],
            highlight_colors: vec!["yellow".to_string()],
            tags: vec![],
            map_id: Ulid::new().to_string(),
            output_path: output_path.to_string_lossy().to_string(),
        };

        let result = make_mubu_card(config, |p, t, m| {
            println!("Progress: {}/{} - {}", p, t, m);
        })
        .await;
        dbg!(&result);
        assert!(result.is_ok());
        assert!(Path::new(&output_path).exists());
    }

    // #[tokio::test]
    async fn test_make_mubu_card_freemind() {
        let mind_path = TEST_FREEMIND_PATH;
        let parent_dir = Path::new(mind_path).parent().unwrap();
        let output_path = parent_dir.join("output.apkg");

        let config = MubuCardConfig {
            file_path: mind_path.to_string(),
            deck_name: "Test Deck".to_string(),
            model_name: "Kevin Mindmap Card v3".to_string(),
            cloze_styles: vec!["text_color".to_string()],
            text_colors: vec!["red".to_string()],
            highlight_colors: vec!["yellow".to_string()],
            tags: vec![],
            output_path: output_path.to_string_lossy().to_string(),
            map_id: Ulid::new().to_string(),
        };

        let result = make_mubu_card(config, |p, t, m| {
            println!("Progress: {}/{} - {}", p, t, m);
        })
        .await;
        dbg!(&result);
        assert!(result.is_ok());
        assert!(Path::new(&output_path).exists());
    }

    #[tokio::test]
    async fn test_make_mubu_card_json() {
        let mind_path = TEST_JSON_PATH;
        let output_path = "output_json.apkg";

        let config = MubuCardConfig {
            file_path: mind_path.to_string(),
            deck_name: "Test JSON Deck".to_string(),
            model_name: "Kevin Mindmap Card v3".to_string(),
            cloze_styles: vec![
                "text_color".to_string(),
                "text_highlight".to_string(),
                "bold".to_string(),
            ],
            text_colors: vec!["red".to_string(), "green".to_string(), "yellow".to_string()],
            highlight_colors: vec!["yellow".to_string(), "red".to_string()],
            tags: vec![],
            map_id: Ulid::new().to_string(),
            output_path: output_path.to_string(),
        };

        let result = make_mubu_card(config, |p, t, m| {
            println!("Progress: {}/{} - {}", p, t, m);
        })
        .await;
        dbg!(&result);
        assert!(result.is_ok());
        assert!(Path::new(&output_path).exists());
    }
}
