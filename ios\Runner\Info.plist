<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>PDF Guru Anki</string>
		<key>CFBundleDocumentTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeName</key>
				<string>ShareHandler</string>
				<key>LSHandlerRank</key>
				<string>Alternate</string>
				<key>LSItemContentTypes</key>
				<array>
					<string>public.file-url</string>
					<string>public.image</string>
					<string>public.text</string>
					<string>public.movie</string>
					<string>public.url</string>
					<string>public.data</string>
				</array>
			</dict>
		</array>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>PDF Guru Anki</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>NSUserActivityTypes</key>
		<array>
			<string>INSendMessageIntent</string>
		</array>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>ShareMedia-$(PRODUCT_BUNDLE_IDENTIFIER)</string>
					<string>guru2</string>
				</array>
			</dict>
		</array>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Photos can be shared to and used in this app</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<string>Yes</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIStatusBarHidden</key>
		<false />
		<key>UISupportsDocumentBrowser</key>
		<true />
		<key>FlutterDeepLinkingEnabled</key>
		<false />
		<key>UIFileSharingEnabled</key>
		<true />
		<key>ITSAppUsesNonExemptEncryption</key>
		<false />
	</dict>
</plist>