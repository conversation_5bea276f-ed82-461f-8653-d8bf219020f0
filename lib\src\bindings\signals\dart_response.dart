// ignore_for_file: type=lint, type=warning
part of 'signals.dart';


@immutable
class DartResponse {
  const DartResponse({
    required this.interactionId,
    required this.status,
    required this.message,
    required this.data,
  });

  static DartResponse deserialize(BinaryDeserializer deserializer) {
    deserializer.increaseContainerDepth();
    final instance = DartResponse(
      interactionId: deserializer.deserializeString(),
      status: deserializer.deserializeString(),
      message: deserializer.deserializeString(),
      data: deserializer.deserializeString(),
    );
    deserializer.decreaseContainerDepth();
    return instance;
  }

  static DartResponse bincodeDeserialize(Uint8List input) {
    final deserializer = BincodeDeserializer(input);
    final value = DartResponse.deserialize(deserializer);
    if (deserializer.offset < input.length) {
      throw Exception('Some input bytes were not read');
    }
    return value;
  }

  final String interactionId;
  final String status;
  final String message;
  final String data;

  DartResponse copyWith({
    String? interactionId,
    String? status,
    String? message,
    String? data,
  }) {
    return DartResponse(
      interactionId: interactionId ?? this.interactionId,
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  void serialize(BinarySerializer serializer) {
    serializer.increaseContainerDepth();
    serializer.serializeString(interactionId);
    serializer.serializeString(status);
    serializer.serializeString(message);
    serializer.serializeString(data);
    serializer.decreaseContainerDepth();
  }

  Uint8List bincodeSerialize() {
      final serializer = BincodeSerializer();
      serialize(serializer);
      return serializer.bytes;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;

    return other is DartResponse
      && interactionId == other.interactionId
      && status == other.status
      && message == other.message
      && data == other.data;
  }

  @override
  int get hashCode => Object.hash(
        interactionId,
        status,
        message,
        data,
      );

  @override
  String toString() {
    String? fullString;

    assert(() {
      fullString = '$runtimeType('
        'interactionId: $interactionId, '
        'status: $status, '
        'message: $message, '
        'data: $data'
        ')';
      return true;
    }());

    return fullString ?? 'DartResponse';
  }
}

extension DartResponseDartSignalExt on DartResponse {
  /// Sends the signal to Rust.
  /// Passing data from Rust to Dart involves a memory copy
  /// because Rust cannot own data managed by Dart's garbage collector.
  void sendSignalToRust() {
    final messageBytes = bincodeSerialize();
    final binary = Uint8List(0);
    sendDartSignal(
      'rinf_send_dart_signal_dart_response',
      messageBytes,
      binary,
    );
  }
}
