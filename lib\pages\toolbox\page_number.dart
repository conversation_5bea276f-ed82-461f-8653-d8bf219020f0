import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/page_number.dart';

class PDFPageNumberPage extends StatefulWidget {
  const PDFPageNumberPage({super.key});

  @override
  State<PDFPageNumberPage> createState() => _PDFPageNumberPageState();
}

class _PDFPageNumberPageState extends State<PDFPageNumberPage> {
  final controller = Get.put(PDFPageNumberController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title:
            Text('toolbox.pageNumber.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.pageNumber.description'.tr,
                  style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        const SizedBox(height: 4),
                        ShadSelectCustom(
                          key: const ValueKey("number-pos"),
                          label: 'toolbox.pageNumber.numberPosition'.tr,
                          placeholder:
                              'toolbox.pageNumber.selectNumberPosition'.tr,
                          initialValue: [controller.numberPos.value],
                          options: controller.numberPosList,
                          onChanged: (value) {
                            controller.numberPos.value = value.single;
                          },
                        ),
                        ShadSelectCustom(
                          key: const ValueKey("align"),
                          label: 'toolbox.pageNumber.alignment'.tr,
                          placeholder: 'toolbox.pageNumber.selectAlignment'.tr,
                          initialValue: [controller.align.value],
                          options: controller.alignList,
                          onChanged: (value) {
                            controller.align.value = value.single;
                          },
                        ),
                        ShadSelectCustom(
                          key: const ValueKey("style"),
                          label: 'toolbox.pageNumber.style'.tr,
                          placeholder: 'toolbox.pageNumber.selectStyle'.tr,
                          initialValue: [controller.style.value],
                          options: controller.styleList,
                          onChanged: (value) {
                            controller.style.value = value.single;
                          },
                        ),
                        if (controller.style.value == 'custom') ...[
                          ShadInputWithValidate(
                            key: const ValueKey("custom-style"),
                            label: 'toolbox.pageNumber.customStyle'.tr,
                            placeholder:
                                'toolbox.pageNumber.customStylePlaceholder'.tr,
                            initialValue: controller.customStyleTemplate.value,
                            onChanged: (value) {
                              controller.customStyleTemplate.value = value;
                            },
                            onValidate: (value) async {
                              return "";
                            },
                          ),
                        ],
                        ShadSwitchCustom(
                          key: const ValueKey("use-custom-font"),
                          label: 'toolbox.pageNumber.customFont'.tr,
                          initialValue: controller.useCustomFont.value,
                          onChanged: (v) {
                            controller.useCustomFont.value = v;
                          },
                        ),
                        if (controller.useCustomFont.value) ...[
                          ShadInputWithFileSelect(
                            key: const ValueKey("custom-font-file"),
                            title: 'toolbox.pageNumber.fontFile'.tr,
                            placeholder: Text(
                                'toolbox.pageNumber.fontFilePlaceholder'.tr),
                            allowedExtensions: const ['ttf'],
                            isRequired: true,
                            allowMultiple: false,
                            initialValue: [controller.customFontFilePath.value],
                            onFilesSelected: (files) {
                              controller.customFontFilePath.value =
                                  files.single;
                            },
                            onValidate: (value, files) async {
                              return await validateFile(value, files);
                            },
                            onValidateError: (error) {},
                          ),
                        ] else ...[
                          ShadSelectCustom(
                            key: const ValueKey("font-family"),
                            label: 'toolbox.pageNumber.fontFamily'.tr,
                            placeholder:
                                'toolbox.pageNumber.selectFontFamily'.tr,
                            isMultiple: false,
                            initialValue: [controller.fontFamily.value],
                            options: controller.fontFamilyList.toList(),
                            onChanged: (value) {
                              logger.i(value);
                              controller.fontFamily.value = value.single;
                            },
                          ),
                        ],
                        ShadInputWithValidate(
                            key: const ValueKey("font-size"),
                            label: 'toolbox.pageNumber.fontSize'.tr,
                            placeholder:
                                'toolbox.pageNumber.fontSizePlaceholder'.tr,
                            initialValue: controller.fontSize.value.toString(),
                            onChanged: (value) {
                              controller.fontSize.value =
                                  double.tryParse(value) ??
                                      controller.fontSize.value;
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return 'toolbox.pageNumber.fontSizeRequired'.tr;
                              }
                              final reg = RegExp(r'^\d+(\.\d+)?$');
                              if (!reg.hasMatch(value)) {
                                return 'toolbox.pageNumber.enterFloatNumber'.tr;
                              }
                              return "";
                            }),
                        ShadColorPickerCustom(
                          label: 'toolbox.pageNumber.fontColor'.tr,
                          initialValue: controller.fontColor.value,
                          onChanged: (color) {
                            controller.fontColor.value = color;
                          },
                        ),
                        ShadInputWithValidate(
                            key: const ValueKey("opacity"),
                            label: 'toolbox.pageNumber.opacity'.tr,
                            placeholder:
                                'toolbox.pageNumber.opacityPlaceholder'.tr,
                            initialValue: controller.opacity.value.toString(),
                            onChanged: (value) {
                              controller.opacity.value =
                                  double.tryParse(value) ??
                                      controller.opacity.value;
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return 'toolbox.pageNumber.opacityRequired'.tr;
                              }
                              final reg = RegExp(r'^\d+(\.\d+)?$');
                              if (!reg.hasMatch(value)) {
                                return 'toolbox.pageNumber.enterFloatNumber'.tr;
                              }
                              return "";
                            }),
                        ShadInputWithValidate(
                            label: 'toolbox.pageNumber.margin.top'.tr,
                            placeholder:
                                'toolbox.pageNumber.margin.topPlaceholder'.tr,
                            initialValue: controller.top.value.toString(),
                            onChanged: (value) {
                              controller.top.value = double.tryParse(value) ??
                                  controller.top.value;
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return 'toolbox.pageNumber.margin.topRequired'
                                    .tr;
                              }
                              final regex =
                                  RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                              if (!regex.hasMatch(value)) {
                                return 'toolbox.pageNumber.margin.invalid'.tr;
                              }
                              return "";
                            }),
                        ShadInputWithValidate(
                            label: 'toolbox.pageNumber.margin.bottom'.tr,
                            placeholder:
                                'toolbox.pageNumber.margin.bottomPlaceholder'
                                    .tr,
                            initialValue: controller.bottom.value.toString(),
                            onChanged: (value) {
                              controller.bottom.value =
                                  double.tryParse(value) ??
                                      controller.bottom.value;
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return 'toolbox.pageNumber.margin.bottomRequired'
                                    .tr;
                              }
                              final regex =
                                  RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                              if (!regex.hasMatch(value)) {
                                return 'toolbox.pageNumber.margin.invalid'.tr;
                              }
                              return "";
                            }),
                        ShadInputWithValidate(
                            label: 'toolbox.pageNumber.margin.left'.tr,
                            placeholder:
                                'toolbox.pageNumber.margin.leftPlaceholder'.tr,
                            initialValue: controller.left.value.toString(),
                            onChanged: (value) {
                              controller.left.value = double.tryParse(value) ??
                                  controller.left.value;
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return 'toolbox.pageNumber.margin.leftRequired'
                                    .tr;
                              }
                              final regex =
                                  RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                              if (!regex.hasMatch(value)) {
                                return 'toolbox.pageNumber.margin.invalid'.tr;
                              }
                              return "";
                            }),
                        ShadInputWithValidate(
                            label: 'toolbox.pageNumber.margin.right'.tr,
                            placeholder:
                                'toolbox.pageNumber.margin.rightPlaceholder'.tr,
                            initialValue: controller.right.value.toString(),
                            onChanged: (value) {
                              controller.right.value = double.tryParse(value) ??
                                  controller.right.value;
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return 'toolbox.pageNumber.margin.rightRequired'
                                    .tr;
                              }
                              final regex =
                                  RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                              if (!regex.hasMatch(value)) {
                                return 'toolbox.pageNumber.margin.invalid'.tr;
                              }
                              return "";
                            }),
                        ShadInputWithValidate(
                            key: const ValueKey("split-range"),
                            label: 'toolbox.common.pageRange'.tr,
                            placeholder:
                                'toolbox.common.pageRangePlaceholder'.tr,
                            initialValue: controller.pageRange.value,
                            onChanged: (value) {
                              controller.pageRange.value = value;
                            },
                            onValidate: (value) async {
                              if (validatePageRange(value)) {
                                return "";
                              }
                              return 'toolbox.common.enterPageRange'.tr;
                            }),
                        if (PathUtils.isDesktop)
                          ShadSelectCustom(
                            key: const ValueKey("output-position"),
                            label: 'toolbox.common.outputLocation'.tr,
                            placeholder:
                                'toolbox.common.selectOutputLocation'.tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.common.outputDirectory'.tr,
                            placeholder:
                                Text('toolbox.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.common.inputFile'.tr,
                          placeholder:
                              Text('toolbox.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['pdf'],
                          isRequired: true,
                          allowMultiple: true,
                          initialValue: controller.selectedFilePaths,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
