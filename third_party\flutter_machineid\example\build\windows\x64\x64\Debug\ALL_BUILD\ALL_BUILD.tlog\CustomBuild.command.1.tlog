^C:\USERS\<USER>\CODE\ANKI_GURU\THIRD_PARTY\FLUTTER_MACHINEID\EXAMPLE\WINDOWS\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/code/anki_guru/third_party/flutter_machineid/example/windows -BC:/Users/<USER>/code/anki_guru/third_party/flutter_machineid/example/build/windows/x64 --check-stamp-file C:/Users/<USER>/code/anki_guru/third_party/flutter_machineid/example/build/windows/x64/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
