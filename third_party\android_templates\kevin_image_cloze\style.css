/* 整体卡片样式 */
.my-card {
  font-family: "Helvetica Neue", Arial, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  /* max-width: 800px; */
  margin: 0 auto;
  padding: 10px 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 标题样式 */
.header {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
  text-align: left;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

/* 图片容器 */
.image-container {
  position: relative;
  width: 100%;
  margin: 15px auto;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

/* 主图片容器 */
.main-image {
  width: 100%;
  position: relative;
}

/* 确保内部图片样式正确 */
.main-image img {
  max-width: 100%;
  max-height: 80vh;
  width: auto;
  height: auto;
  display: block;
  margin: 0 auto;
  object-fit: contain;
}

#masksContainer {
  position: absolute;
  pointer-events: none;
  z-index: 5;
  /* 删除了 top: 0; left: 0; width: 100%; height: 100%; 
     因为这些值将由 JavaScript 动态设置 */
}

/* 遮盖样式 */
.mask {
  position: absolute;
  cursor: pointer;
  transition: opacity 0.2s ease;
  border-radius: 3px;
  pointer-events: auto;
}

/* 主色遮盖 */
.mask.main-mask {
  background-color: var(--main-color, rgba(255, 0, 0, 0.7));
}

/* 副色遮盖 */
.mask.secondary-mask {
  background-color: var(--secondary-color, rgba(0, 255, 0, 0.7));
}

/* 遮盖隐藏状态 */
.mask.revealed {
  opacity: 0;
}

/* 导航按钮容器 */
.navigation {
  display: flex;
  justify-content: center;
  margin: 15px 0;
}

/* 导航按钮 */
.nav-btn {
  padding: 8px 16px;
  margin: 0 10px;
  background-color: #4a86e8;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

/* 导航按钮悬停效果 */
.nav-btn:hover {
  background-color: #3a76d8;
}

/* 来源信息 */
.source {
  font-size: 0.85rem;
  margin-top: 15px;
  color: #666;
  font-style: italic;
}

/* 文本内容 */
.text {
  margin-top: 15px;
  padding: 12px;
  background-color: #fefefe;
  border-radius: 8px;
  border: 1px solid #eee;
  line-height: 1.5;
}

/* 文本容器 */
.text-container {
  margin-top: 15px;
}

/* 文本切换按钮 */
.text-toggle {
  padding: 8px 12px;
  background-color: #f0f0f0;
  color: #555;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  font-size: 0.9rem;
  text-align: center;
  border: 1px solid #eee;
  border-bottom: none;
}

/* 文本切换按钮悬停效果 */
.text-toggle:hover {
  background-color: #e5e5e5;
}

/* 隐藏的内容 */
.hidden {
  display: none;
}

/* 笔记内容 */
.notes {
  margin-top: 15px;
  padding: 12px;
  background-color: #fffde7;
  border-radius: 8px;
  border: 1px solid #f9f5d7;
  line-height: 1.5;
}

/* 刮刮乐画布容器 */
.scratch-canvas-container {
  position: absolute;
  pointer-events: none;
  z-index: 10;
  /* 同样删除了固定的位置和尺寸，改为动态设置 */
}

/* 刮刮乐画布 */
.scratch-canvas {
  position: absolute;
  cursor: pointer;
  pointer-events: auto;
  border-radius: 3px;
}

/* 媒体查询：适配移动设备 */
@media (max-width: 600px) {
  .card {
    padding: 12px;
  }

  .header {
    font-size: 1rem;
    padding: 8px;
  }

  .nav-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}


/* 导航间距元素 */
.nav-spacer {
  width: 20px;
  display: inline-block;
}


/* 更新导航样式以允许将切换按钮推到右侧 */
.navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 15px auto;
  gap: 20px;
  /* 按钮之间的间距 */
  width: 100%;
  max-width: 300px;
  /* 限制最大宽度 */
}

/* 导航按钮样式 */
.nav-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: #4a86e8;
  color: white;
  border: none;
  border-radius: 50%;
  /* 圆形按钮 */
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  text-decoration: none;
  padding: 0;
}

/* 按钮悬停效果 */
.nav-btn:hover {
  background-color: #3a76d8;
  transform: scale(1.05);
}

/* 按钮激活效果 */
.nav-btn:active {
  transform: scale(0.95);
}

/* SVG 图标样式 */
.nav-btn svg {
  stroke: white;
  width: 20px;
  height: 20px;
}

/* 媒体查询：适配移动设备 */
@media (max-width: 600px) {
  .navigation {
    gap: 15px;
  }

  .nav-btn {
    width: 36px;
    height: 36px;
  }

  .nav-btn svg {
    width: 18px;
    height: 18px;
  }
}

/* 笔记标题 */
.notes-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
  color: #333;
}

/* 笔记内容 */
.notes {
  margin-top: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border-left: 4px solid #4a86e8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  line-height: 1.6;
  color: #333;
}

.notes p {
  margin: 0 0 10px 0;
}

.notes p:last-child {
  margin-bottom: 0;
}