{"name": "flutter-i18n-helper", "displayName": "Flutter I18n Helper", "description": "A VS Code extension to streamline Flutter/Dart i18n workflows with get/getx.", "version": "0.5.36", "publisher": "<EMAIL>", "repository": {"type": "git", "url": "https://github.com/kevin2li/flutter-i18n-helper.git"}, "engines": {"vscode": "^1.96.0"}, "categories": ["Programming Languages", "Linters", "Other"], "activationEvents": ["onLanguage:dart"], "main": "./out/extension.js", "contributes": {"configuration": {"title": "Flutter I18n Helper", "properties": {"i18n-helper.translationProvider": {"type": "string", "default": "Gemini", "enum": ["Gemini", "OpenAI"], "description": "Select the AI provider to use for batch translation."}, "i18n-helper.i18nDir": {"type": "string", "default": "assets/i18n", "description": "Path to the directory containing i18n JSON files (relative to workspace root)."}, "i18n-helper.sourceLanguage": {"type": "string", "default": "zh-CN", "description": "The source language code (e.g., 'zh-CN' for zh-CN.json)."}, "i18n-helper.inlineDisplayLanguage": {"type": "string", "default": "zh-CN", "description": "The target language to display for inline translation previews."}, "i18n-helper.geminiApiKey": {"type": "string", "default": "", "description": "Your Google Gemini API Key. Required if Translation Provider is 'Gemini'.", "markdownDescription": "Your Google Gemini API Key. [Get your key here](https://aistudio.google.com/app/apikey)."}, "i18n-helper.geminiModel": {"type": "string", "default": "gemini-pro", "description": "The Gemini model to use for translation."}, "i18n-helper.openai": {"type": "object", "title": "OpenAI Provider Settings", "description": "Settings for using OpenAI-compatible APIs (including services like OpenRouter, Together.ai, or local models). Required if Translation Provider is 'OpenAI'.", "properties": {"baseUrl": {"type": "string", "default": "https://api.openai.com/v1", "description": "The custom API address (base URL) for the service. Change this for OpenRouter, local models, etc."}, "apiKey": {"type": "string", "default": "", "description": "Your API Key for the service. For some services, this might be a simple placeholder."}, "model": {"type": "string", "default": "gpt-4-turbo", "description": "The model to use for translation (e.g., 'openai/gpt-4-turbo', 'mistralai/mistral-7b-instruct', etc.)."}, "prompt": {"type": "string", "default": "You are a professional JSON translator. Translate the values in the following JSON object from language code '{sourceLang}' to language code '{targetLang}'. Your response must be only the translated JSON object, without any additional text, explanations, or markdown code fences.", "description": "The system prompt to use for OpenAI translation. Use '{sourceLang}' and '{targetLang}' as placeholders.", "markdownDescription": "The system prompt to use for OpenAI translation. Use `{sourceLang}` and `{targetLang}` as placeholders."}}, "default": {"baseUrl": "https://api.openai.com/v1", "apiKey": "", "model": "gpt-4-turbo", "prompt": "You are a professional JSON translator. Translate the values in the following JSON object from language code '{sourceLang}' to language code '{targetLang}'. Your response must be only the translated JSON object, without any additional text, explanations, or markdown code fences."}}, "i18n-helper.replacementPattern": {"type": "string", "default": "'{key}'.tr", "description": "The code pattern to replace the selected text with. Use '{key}' as a placeholder for the translation key."}, "i18n-helper.keyStructure": {"type": "string", "default": "flat", "enum": ["flat", "nested"], "description": "The structure to use when creating new keys in JSON files. 'flat' creates keys like 'a.b.c', 'nested' creates {'a': {'b': {'c': ...}}}"}, "i18n-helper.diagnostics.enableHardcodedStrings": {"type": "boolean", "default": true, "description": "Enable diagnostics for hardcoded strings in Dart files. This helps find text that should be translated."}, "i18n-helper.diagnostics.enableKeySuggestions": {"type": "boolean", "default": true, "description": "Enable suggestions for similar keys when a key is not found."}, "i18n-helper.diagnostics.suggestionDistanceThreshold": {"type": "number", "default": 5, "description": "The maximum Levenshtein distance for a key to be considered a suggestion. A lower number means stricter matching."}, "i18n-helper.quickPick.commands": {"type": "array", "description": "A list of command IDs to show in the 'I18n Helper: Show All Commands' quick pick list. If empty, all commands with the 'I18n Helper' category will be shown.", "default": ["i18n-helper.extractValue", "i18n-helper.extract<PERSON>ey", "i18n-helper.syncKeys", "i18n-helper.batchTranslate", "i18n-helper.findMissingTranslations", "i18n-helper.find<PERSON><PERSON>ed<PERSON>eys", "i18n-helper.git.diffWithPrevious", "i18n-helper.git.diffWithNext"], "items": {"type": "string", "enum": ["i18n-helper.showCommands", "i18n-helper.syncKeys", "i18n-helper.batchTranslate", "i18n-helper.findMissingTranslations", "i18n-helper.find<PERSON><PERSON>ed<PERSON>eys", "i18n-helper.extract<PERSON>ey", "i18n-helper.extractValue", "i18n-helper.git.diffWithPrevious", "i18n-helper.git.diffWithNext"]}}}}, "viewsContainers": {"activitybar": [{"id": "i18n-helper-view-container", "title": "I18n Helper", "icon": "$(globe)"}]}, "views": {"i18n-helper-view-container": [{"id": "i18n-key-view", "name": "Translations", "type": "tree", "contextualTitle": "I18n Helper"}]}, "commands": [{"command": "i18n-helper.showCommands", "title": "Show All Commands", "category": "I18n Helper"}, {"command": "i18n-helper.syncKeys", "title": "Synchronize JSON Keys", "category": "I18n Helper"}, {"command": "i18n-helper.batchTranslate", "title": "<PERSON><PERSON> Translate with AI", "category": "I18n Helper"}, {"command": "i18n-helper.findMissingTranslations", "title": "Find All Missing Translations in Workspace", "category": "I18n Helper"}, {"command": "i18n-helper.find<PERSON><PERSON>ed<PERSON>eys", "title": "Find All Unused Translations in Workspace", "category": "I18n Helper"}, {"command": "i18n-helper.extract<PERSON>ey", "title": "Extract as Translation Key", "category": "I18n Helper"}, {"command": "i18n-helper.extractValue", "title": "Extract as Translation Value", "category": "I18n Helper"}, {"command": "i18n-helper.view.refresh", "title": "Refresh", "icon": "$(refresh)", "category": "I18n Helper"}, {"command": "i18n-helper.view.search", "title": "Search Keys", "icon": "$(search)", "category": "I18n Helper"}, {"command": "i18n-helper.view.clearSearch", "title": "Clear Search", "icon": "$(clear-all)", "category": "I18n Helper"}, {"command": "i18n-helper.view.goToDefinition", "title": "Go to Definition", "icon": "$(go-to-file)", "category": "I18n Helper"}, {"command": "i18n-helper.view.rename<PERSON>ey", "title": "<PERSON><PERSON>", "icon": "$(symbol-string)", "category": "I18n Helper"}, {"command": "i18n-helper.git.diffWithPrevious", "title": "Compare with Previous Version", "category": "I18n Helper"}, {"command": "i18n-helper.git.diffWithNext", "title": "Compare with Next Version", "category": "I18n Helper"}], "menus": {"editor/context": [{"when": "editorHasSelection", "command": "i18n-helper.extract<PERSON>ey", "group": "i18n@1"}, {"when": "editorHasSelection", "command": "i18n-helper.extractValue", "group": "i18n@2"}, {"when": "resourceScheme == 'file'", "command": "i18n-helper.git.diffWithPrevious", "group": "navigation@1"}], "view/title": [{"command": "i18n-helper.view.search", "when": "view == i18n-key-view", "group": "navigation@1"}, {"command": "i18n-helper.view.clearSearch", "when": "view == i18n-key-view && i18n-helper.view.searchVisible", "group": "navigation@2"}, {"command": "i18n-helper.view.refresh", "when": "view == i18n-key-view", "group": "navigation@3"}], "view/item/context": [{"command": "i18n-helper.view.rename<PERSON>ey", "when": "view == i18n-key-view && viewItem == 'keyItem'", "group": "modification"}, {"command": "i18n-helper.view.goToDefinition", "when": "view == i18n-key-view && viewItem == 'translationItem'", "group": "navigation"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "vscode-test"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/node-fetch": "^2.6.12", "@types/vscode": "^1.96.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "eslint": "^9.25.1", "typescript": "^5.8.3"}, "dependencies": {"@google/generative-ai": "^0.24.1", "https-proxy-agent": "^7.0.6", "node-fetch": "^2.7.0"}}