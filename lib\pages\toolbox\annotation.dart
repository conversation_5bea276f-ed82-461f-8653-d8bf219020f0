import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/annotation.dart';

class PDFAnnotationPage extends StatefulWidget {
  const PDFAnnotationPage({super.key});

  @override
  State<PDFAnnotationPage> createState() => _PDFAnnotationPageState();
}

class _PDFAnnotationPageState extends State<PDFAnnotationPage> {
  final controller = Get.put(PDFAnnotationPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.annotation.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('toolbox.common.functionDescription'.tr,
                style: defaultPageTitleStyle),
            Text('toolbox.annotation.description'.tr,
                style: theme.textTheme.muted),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 3;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        logger.i(controller.tabController.selected);
                      },
                      tabs: [
                        ShadTab(
                          value: 'import',
                          content: const ImportForm(),
                          width: tabWidth,
                          child: Text('toolbox.annotation.importTab'.tr),
                        ),
                        ShadTab(
                          value: 'export',
                          content: const ExportForm(),
                          width: tabWidth,
                          child: Text('toolbox.annotation.exportTab'.tr),
                        ),
                        ShadTab(
                          value: 'delete',
                          content: const DeleteForm(),
                          width: tabWidth,
                          child: Text('toolbox.annotation.deleteTab'.tr),
                        ),
                        ShadTab(
                          value: 'flatten',
                          content: const FlattenForm(),
                          width: tabWidth,
                          child: Text('toolbox.annotation.flattenTab'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ImportForm extends GetView<PDFAnnotationPageController> {
  const ImportForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Obx(
          () => Column(
            spacing: 6,
            children: [
              ShadRadioGroupCustom(
                label: 'toolbox.annotation.importFormat'.tr,
                initialValue: controller.importFormat.value,
                items: controller.exportFormatList.toList(),
                onChanged: (value) {
                  controller.importFormat.value = value;
                },
              ),
              ShadInputWithFileSelect(
                key: const ValueKey("annotation-file"),
                title: 'toolbox.annotation.annotationFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const ['xfdf', 'fdf', 'json'],
                isRequired: true,
                allowMultiple: false,
                initialValue: [controller.annotationFile.value],
                onFilesSelected: (files) {
                  logger.w("files final: $files");
                  controller.annotationFile.value = files.single;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
              if (PathUtils.isDesktop)
                ShadSelectCustom(
                  label: 'toolbox.common.outputLocation'.tr,
                  placeholder: 'toolbox.common.selectOutputLocation'.tr,
                  initialValue: [controller.outputMode.value],
                  options: outputModeList,
                  onChanged: (value) {
                    controller.outputMode.value = value.single;
                  },
                ),
              if (controller.outputMode.value == 'custom')
                ShadInputWithFileSelect(
                  key: ValueKey("output-dir-${controller.outputDir.value}"),
                  title: 'toolbox.common.outputDirectory'.tr,
                  placeholder: Text('toolbox.common.outputDirectory'.tr),
                  initialValue: [controller.outputDir.value],
                  isRequired: true,
                  isFolder: true,
                  onFilesSelected: (value) {
                    controller.outputDir.value = value.single;
                  },
                  onValidate: (value, files) async {
                    return await validateOutputDir(value, files);
                  },
                  onValidateError: (error) {
                    controller.outputDirError.value = error;
                  },
                ),
              ShadInputWithFileSelect(
                key: const ValueKey("input-file"),
                title: 'toolbox.common.inputFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const ['pdf'],
                isRequired: true,
                allowMultiple: true,
                initialValue: controller.selectedFilePaths.value,
                onFilesSelected: (files) {
                  controller.selectedFilePaths.value = files;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ExportForm extends GetView<PDFAnnotationPageController> {
  const ExportForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Obx(
          () => Column(
            spacing: 6,
            children: [
              ShadRadioGroupCustom(
                label: 'toolbox.annotation.exportFormat'.tr,
                initialValue: controller.exportFormat.value,
                items: controller.exportFormatList.toList(),
                onChanged: (value) {
                  controller.exportFormat.value = value;
                },
              ),
              ShadInputWithValidate(
                  label: 'toolbox.common.pageRange'.tr,
                  placeholder: 'toolbox.common.pageRangePlaceholder'.tr,
                  initialValue: controller.pageRange.value,
                  onChanged: (value) {
                    controller.pageRange.value = value;
                  },
                  onValidate: (value) async {
                    if (validatePageRange(value)) {
                      return "";
                    }
                    return 'toolbox.common.enterPageRange'.tr;
                  }),
              if (PathUtils.isDesktop)
                ShadSelectCustom(
                  label: 'toolbox.common.outputLocation'.tr,
                  placeholder: 'toolbox.common.selectOutputLocation'.tr,
                  initialValue: [controller.outputMode.value],
                  options: outputModeList,
                  onChanged: (value) {
                    controller.outputMode.value = value.single;
                  },
                ),
              if (controller.outputMode.value == 'custom')
                ShadInputWithFileSelect(
                  key: ValueKey("output-dir-${controller.outputDir.value}"),
                  title: 'toolbox.common.outputDirectory'.tr,
                  placeholder: Text('toolbox.common.outputDirectory'.tr),
                  initialValue: [controller.outputDir.value],
                  isRequired: true,
                  isFolder: true,
                  onFilesSelected: (value) {
                    controller.outputDir.value = value.single;
                  },
                  onValidate: (value, files) async {
                    return await validateOutputDir(value, files);
                  },
                  onValidateError: (error) {
                    controller.outputDirError.value = error;
                  },
                ),
              ShadInputWithFileSelect(
                key: const ValueKey("input-file"),
                title: 'toolbox.common.inputFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const ['pdf'],
                isRequired: true,
                allowMultiple: true,
                initialValue: controller.selectedFilePaths.value,
                onFilesSelected: (files) {
                  controller.selectedFilePaths.value = files;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DeleteForm extends GetView<PDFAnnotationPageController> {
  const DeleteForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Obx(
          () => Column(
            spacing: 6,
            children: [
              ShadInputWithValidate(
                  label: 'toolbox.common.pageRange'.tr,
                  placeholder: 'toolbox.common.pageRangePlaceholder'.tr,
                  initialValue: controller.pageRange.value,
                  onChanged: (value) {
                    controller.pageRange.value = value;
                  },
                  onValidate: (value) async {
                    if (validatePageRange(value)) {
                      return "";
                    }
                    return 'toolbox.common.enterPageRange'.tr;
                  }),
              if (PathUtils.isDesktop)
                ShadSelectCustom(
                  label: 'toolbox.common.outputLocation'.tr,
                  placeholder: 'toolbox.common.selectOutputLocation'.tr,
                  initialValue: [controller.outputMode.value],
                  options: outputModeList,
                  onChanged: (value) {
                    controller.outputMode.value = value.single;
                  },
                ),
              if (controller.outputMode.value == 'custom')
                ShadInputWithFileSelect(
                  key: ValueKey("output-dir-${controller.outputDir.value}"),
                  title: 'toolbox.common.outputDirectory'.tr,
                  placeholder: Text('toolbox.common.outputDirectory'.tr),
                  initialValue: [controller.outputDir.value],
                  isRequired: true,
                  isFolder: true,
                  onFilesSelected: (value) {
                    controller.outputDir.value = value.single;
                  },
                  onValidate: (value, files) async {
                    return await validateOutputDir(value, files);
                  },
                  onValidateError: (error) {
                    controller.outputDirError.value = error;
                  },
                ),
              ShadInputWithFileSelect(
                key: const ValueKey("input-file"),
                title: 'toolbox.common.inputFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const ['pdf'],
                isRequired: true,
                allowMultiple: true,
                initialValue: controller.selectedFilePaths.value,
                onFilesSelected: (files) {
                  controller.selectedFilePaths.value = files;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FlattenForm extends GetView<PDFAnnotationPageController> {
  const FlattenForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Obx(
          () => Column(
            spacing: 6,
            children: [
              ShadInputWithValidate(
                  label: 'toolbox.common.pageRange'.tr,
                  placeholder: 'toolbox.common.pageRangePlaceholder'.tr,
                  initialValue: controller.pageRange.value,
                  onChanged: (value) {
                    controller.pageRange.value = value;
                  },
                  onValidate: (value) async {
                    if (validatePageRange(value)) {
                      return "";
                    }
                    return 'toolbox.common.enterPageRange'.tr;
                  }),
              if (PathUtils.isDesktop)
                ShadSelectCustom(
                  label: 'toolbox.common.outputLocation'.tr,
                  placeholder: 'toolbox.common.selectOutputLocation'.tr,
                  initialValue: [controller.outputMode.value],
                  options: outputModeList,
                  onChanged: (value) {
                    controller.outputMode.value = value.single;
                  },
                ),
              if (controller.outputMode.value == 'custom')
                ShadInputWithFileSelect(
                  key: ValueKey("output-dir-${controller.outputDir.value}"),
                  title: 'toolbox.common.outputDirectory'.tr,
                  placeholder: Text('toolbox.common.outputDirectory'.tr),
                  initialValue: [controller.outputDir.value],
                  isRequired: true,
                  isFolder: true,
                  onFilesSelected: (value) {
                    controller.outputDir.value = value.single;
                  },
                  onValidate: (value, files) async {
                    return await validateOutputDir(value, files);
                  },
                  onValidateError: (error) {
                    controller.outputDirError.value = error;
                  },
                ),
              ShadInputWithFileSelect(
                key: const ValueKey("input-file"),
                title: 'toolbox.common.inputFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const ['pdf'],
                isRequired: true,
                allowMultiple: true,
                initialValue: controller.selectedFilePaths.value,
                onFilesSelected: (files) {
                  controller.selectedFilePaths.value = files;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
          ),
        ),
      ),
    );
  }
}
