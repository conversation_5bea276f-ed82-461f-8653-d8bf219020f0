import os
from pathlib import Path

import fitz
from .utils import parse_range, progress_reporter

def add_doc_background_by_pdf(
    *, 
    doc_path   : str = "",
    output_path: str = "",
    bg_doc_path: str = "",
    page_range : str = "all",
):
    doc: fitz.Document = fitz.open(doc_path)
    bg_doc = fitz.open(bg_doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(doc)})

    for page_index in range(doc.page_count):
        progress_reporter("processing", f"正在处理第{page_index+1}页", data={"current": float(page_index+1), "total": float(len(doc))})
        page = doc[page_index]
        if page_index in roi_indices:
            page.show_pdf_page(page.rect, bg_doc, 0, overlay=False)
            page.clean_contents()
    doc.ez_save(output_path, garbage=4)
    doc.close()
    bg_doc.close()
