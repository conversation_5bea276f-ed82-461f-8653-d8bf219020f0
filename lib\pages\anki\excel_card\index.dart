import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/qa_form.dart';
import 'components/choice_form.dart';
import 'components/judge_form.dart';
import 'package:anki_guru/controllers/anki/excel_card.dart';
import 'package:anki_guru/pages/common.dart';

class ExcelCardPage extends StatefulWidget {
  const ExcelCardPage({super.key});

  @override
  State<ExcelCardPage> createState() => _ExcelCardPageState();
}

class _ExcelCardPageState extends State<ExcelCardPage> {
  final controller = Get.put(ExcelCardPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.excel_card.excel_card_title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('anki.excel_card.function_description'.tr, style: defaultPageTitleStyle),
                Text('anki.excel_card.feature_description'.tr, style: theme.textTheme.muted),
                const SizedBox(height: 16),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 3;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) async {
                        print(controller.tabController.selected);
                        if (controller.tabController.selected == 'choice' ||
                            controller.tabController.selected == 'judge') {
                          controller.cardModel.value = "Kevin Choice Card v2";
                          await controller
                              .updateFieldList(controller.cardModel.value);
                        } else {
                          controller.cardModel.value = "Kevin Text QA Card v2";
                          await controller
                              .updateFieldList(controller.cardModel.value);
                        }
                      },
                      tabs: [
                        ShadTab(
                          value: 'qa',
                          content: const QAForm(),
                          width: tabWidth,
                          child: Text('anki.excel_card.qa_tab'.tr),
                        ),
                        ShadTab(
                          value: 'choice',
                          content: const ChoiceForm(),
                          width: tabWidth,
                          child: Text('anki.excel_card.choice_tab'.tr),
                        ),
                        ShadTab(
                          value: 'judge',
                          content: const JudgeForm(),
                          width: tabWidth,
                          child: Text('anki.excel_card.judge_tab'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
