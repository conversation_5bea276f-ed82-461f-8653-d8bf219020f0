import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFScalePageController extends GetxController {
  // 数据

  final scaleModeList = [
    {"value": "fixed", "label": "toolbox.scale.scaleTypeOptions.fixed".tr},
    {"value": "ratio", "label": "toolbox.scale.scaleTypeOptions.ratio".tr},
    {"value": "custom", "label": "toolbox.scale.scaleTypeOptions.custom".tr},
  ];
  // 表单参数
  final scaleMode = 'fixed'.obs;
  final scaleRatio = 1.0.obs;
  final width = 595.28.obs;
  final height = 841.89.obs;
  final paperSize = "A4".obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;

  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        "common.error".tr,
        "toolbox.common.selectPdfFiles".tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      for (String filePath in selectedFilePaths) {
        progressController.updateProgress(
          status: "running",
          message:
              "${'toolbox.common.process.running'.tr}: ${PathUtils(filePath).name}",
        );
        final outputPath = await PathUtils(filePath).convertPath(
          outputMode.value,
          stem_append: "'toolbox.scale.stem_append'.tr",
          suffix: ".pdf",
          outputDir: outputDir.value,
        );
        final data = {
          'scale_mode': scaleMode.value,
          'scale_ratio': scaleRatio.value,
          'width': width.value,
          'height': height.value,
          'papersize': paperSize.value,
          'input_path': filePath,
          'page_range': pageRange.value,
          'output_path': outputPath,
          'show_progress': true,
        };
        final resp = await messageController.request(data, 'pdf/scale');
        logger.w("resp: $resp");
        if (resp.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: "toolbox.common.process.completed".tr,
          );
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
          return;
        }
      }
      progressController.updateProgress(status: "completed", message: "");
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "toolbox.common.error_with_msg".trParams({'error': e.toString()}),
      );
    }
  }
}
