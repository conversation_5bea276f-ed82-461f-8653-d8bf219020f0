// ignore_for_file: type=lint, type=warning
part of 'signals.dart';


@immutable
class RustRequest {
  /// An async broadcast stream that listens for signals from Rust.
  /// It supports multiple subscriptions.
  /// Make sure to cancel the subscription when it's no longer needed,
  /// such as when a widget is disposed.
  static final rustSignalStream =
      _rustRequestStreamController.stream.asBroadcastStream();
        
  /// The latest signal value received from Rust.
  /// This is updated every time a new signal is received.
  /// It can be null if no signals have been received yet.
  static RustSignalPack<RustRequest>? latestRustSignal = null;

  const RustRequest({
    required this.interactionId,
    required this.msgType,
    required this.params,
  });

  static RustRequest deserialize(BinaryDeserializer deserializer) {
    deserializer.increaseContainerDepth();
    final instance = RustRequest(
      interactionId: deserializer.deserializeString(),
      msgType: deserializer.deserializeString(),
      params: deserializer.deserializeString(),
    );
    deserializer.decreaseContainerDepth();
    return instance;
  }

  static RustRequest bincodeDeserialize(Uint8List input) {
    final deserializer = BincodeDeserializer(input);
    final value = RustRequest.deserialize(deserializer);
    if (deserializer.offset < input.length) {
      throw Exception('Some input bytes were not read');
    }
    return value;
  }

  final String interactionId;
  final String msgType;
  final String params;

  RustRequest copyWith({
    String? interactionId,
    String? msgType,
    String? params,
  }) {
    return RustRequest(
      interactionId: interactionId ?? this.interactionId,
      msgType: msgType ?? this.msgType,
      params: params ?? this.params,
    );
  }

  void serialize(BinarySerializer serializer) {
    serializer.increaseContainerDepth();
    serializer.serializeString(interactionId);
    serializer.serializeString(msgType);
    serializer.serializeString(params);
    serializer.decreaseContainerDepth();
  }

  Uint8List bincodeSerialize() {
      final serializer = BincodeSerializer();
      serialize(serializer);
      return serializer.bytes;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;

    return other is RustRequest
      && interactionId == other.interactionId
      && msgType == other.msgType
      && params == other.params;
  }

  @override
  int get hashCode => Object.hash(
        interactionId,
        msgType,
        params,
      );

  @override
  String toString() {
    String? fullString;

    assert(() {
      fullString = '$runtimeType('
        'interactionId: $interactionId, '
        'msgType: $msgType, '
        'params: $params'
        ')';
      return true;
    }());

    return fullString ?? 'RustRequest';
  }
}

final _rustRequestStreamController =
    StreamController<RustSignalPack<RustRequest>>();
