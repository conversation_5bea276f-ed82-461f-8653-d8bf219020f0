import * as vscode from 'vscode';
import { getI18nDirUri, readJsonFile, getValueFromPath, setValueByPath, deleteValueByPath } from './utils';

async function renameInJsonFiles(oldKey: string, newKey: string): Promise<boolean> {
    const i18nDirUri = await getI18nDirUri();
    if (!i18nDirUri) {
        vscode.window.showErrorMessage('I18n directory not configured.');
        return false;
    }

    try {
        const entries = await vscode.workspace.fs.readDirectory(i18nDirUri);
        for (const [fileName, fileType] of entries) {
            if (fileType === vscode.FileType.File && fileName.endsWith('.json')) {
                const fileUri = vscode.Uri.joinPath(i18nDirUri, fileName);
                let data = await readJsonFile(fileUri);

                if (data) {
                    const oldValue = getValueFromPath(data, oldKey);
                    
                    if (oldValue !== undefined) {
                        const newKeyExists = getValueFromPath(data, newKey) !== undefined;
                        if (newKeyExists) {
                           const overwrite = await vscode.window.showWarningMessage(
                               `Key '${newKey}' already exists in ${fileName}. Overwrite?`, { modal: true }, 'Yes'
                           );
                           if (overwrite !== 'Yes') return false;
                        }
                        
                        // Detect if the old key was a direct property (flat)
                        const isOldKeyFlat = Object.prototype.hasOwnProperty.call(data, oldKey);
                        
                        // Delete the old key using a robust method
                        deleteValueByPath(data, oldKey);

                        // Set the new key while preserving the structure
                        if (isOldKeyFlat && !newKey.includes('.')) {
                            // If old key was flat and new key is simple, keep it flat
                            data[newKey] = oldValue;
                        } else {
                            // Otherwise (old was nested, or new key implies nesting), use nested structure
                            setValueByPath(data, newKey, oldValue);
                        }
                        
                        const orderedData = Object.keys(data).sort().reduce(
                            (obj, key) => { obj[key] = data[key]; return obj; }, {} as Record<string, any>
                        );
                        
                        const newContent = JSON.stringify(orderedData, null, 2);
                        await vscode.workspace.fs.writeFile(fileUri, Buffer.from(newContent, 'utf8'));
                    }
                }
            }
        }
        return true;
    } catch (error) {
        vscode.window.showErrorMessage(`Error renaming keys in JSON files: ${error}`);
        return false;
    }
}

async function renameInDartFiles(oldKey: string, newKey: string): Promise<void> {
    const dartFiles = await vscode.workspace.findFiles('**/*.dart');
    const edit = new vscode.WorkspaceEdit();

    const oldKeyEscaped = oldKey.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(['"])${oldKeyEscaped}\\1(\\.tr)`, 'g');

    for (const fileUri of dartFiles) {
        const document = await vscode.workspace.openTextDocument(fileUri);
        const text = document.getText();
        
        let match;
        while ((match = regex.exec(text)) !== null) {
            const startPos = document.positionAt(match.index);
            const endPos = document.positionAt(match.index + match[0].length);
            const range = new vscode.Range(startPos, endPos);
            const newText = match[0].replace(new RegExp(`(['"])${oldKeyEscaped}\\1`), `$1${newKey}$1`);
            edit.replace(fileUri, range, newText);
        }
    }

    await vscode.workspace.applyEdit(edit);
}

export async function renameKeyProcess(oldKey: string): Promise<boolean> {
    if (!oldKey) return false;

    const newKey = await vscode.window.showInputBox({
        prompt: `Rename i18n key '${oldKey}'`,
        value: oldKey,
        // --- CHANGE IS HERE ---
        ignoreFocusOut: true,
        validateInput: (value) => {
            if (!value || value.trim().length === 0) {
                return 'Key cannot be empty.';
            }
            if (value.includes(' ')) {
                return 'Key cannot contain spaces.';
            }
            if (value === oldKey) {
                return 'New key cannot be the same as the old key.';
            }
            return null;
        }
    });

    if (!newKey || newKey === oldKey) {
        return false;
    }

    let success = false;
    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: `Renaming key '${oldKey}' to '${newKey}'...`,
        cancellable: false
    }, async (progress) => {
        progress.report({ message: 'Updating JSON files...' });
        const jsonSuccess = await renameInJsonFiles(oldKey, newKey);
        
        if (!jsonSuccess) {
            success = false;
            return;
        }

        progress.report({ message: 'Updating Dart files...' });
        await renameInDartFiles(oldKey, newKey);
        
        success = true;
    });

    if (success) {
        vscode.window.showInformationMessage(`Successfully renamed key to '${newKey}'.`);
    }
    
    return success;
}