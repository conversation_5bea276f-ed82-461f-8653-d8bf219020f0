<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON><PERSON>-Templates -->

<link rel="stylesheet" href="__pico.blue.min.css">
<section>
  <article>
    <header style="padding: 0 0.5em;">
      <div class="row">
        <span id="type_container" style="display: flex;">选择题</span>
        <a onclick="toggleConfig()" style="cursor: pointer">
          <svg xmlns="http://www.w3.org/2000/svg" height="1em" fill="#6c757d" viewBox="0 0 512 512">
            <path
              d="M 256 0 Q 282 0 306 5 Q 312 6 321 10 Q 329 15 335 25 Q 338 30 340 37 L 349 75 L 349 75 Q 351 80 356 83 Q 361 86 366 85 L 404 74 L 404 74 Q 410 72 416 72 Q 428 72 436 77 Q 445 82 449 87 Q 482 125 498 174 Q 501 180 501 189 Q 502 199 496 209 Q 492 214 488 219 L 459 246 L 459 246 Q 456 250 456 256 Q 456 262 459 266 L 488 293 L 488 293 Q 492 298 496 303 Q 501 314 501 323 Q 501 333 498 338 Q 482 387 449 425 Q 445 430 436 435 Q 428 440 416 440 Q 410 440 404 438 L 366 427 L 366 427 Q 361 426 356 429 Q 351 432 349 437 L 340 475 L 340 475 Q 338 482 335 487 Q 329 497 321 502 Q 312 506 306 507 Q 282 512 256 512 Q 230 512 206 507 Q 200 506 191 502 Q 183 497 177 487 Q 174 482 172 475 L 163 437 L 163 437 Q 161 432 156 429 Q 151 426 146 427 L 108 438 L 108 438 Q 102 440 96 440 Q 84 440 76 435 Q 67 430 64 425 Q 30 387 14 338 Q 11 332 11 323 Q 10 313 16 303 Q 20 298 24 293 L 53 266 L 53 266 Q 56 262 56 256 Q 56 250 53 246 L 24 219 L 24 219 Q 20 214 16 209 Q 10 198 11 189 Q 11 179 14 174 Q 30 126 64 87 Q 67 82 76 77 Q 84 72 96 72 Q 102 72 108 74 L 146 85 L 146 85 Q 151 86 156 83 Q 161 80 163 75 L 172 37 L 172 37 Q 174 30 177 25 Q 183 15 191 10 Q 200 6 206 5 Q 230 0 256 0 L 256 0 Z M 218 51 L 210 87 L 218 51 L 210 87 Q 203 111 180 124 Q 157 137 132 131 L 98 121 L 98 121 Q 73 150 60 187 L 86 212 L 86 212 Q 104 230 104 256 Q 104 282 86 301 L 60 325 L 60 325 Q 73 362 98 391 L 133 381 L 133 381 Q 157 374 180 388 Q 203 401 210 425 L 218 461 L 218 461 Q 256 467 294 461 L 303 425 L 303 425 Q 310 401 332 388 Q 355 375 380 381 L 414 391 L 414 391 Q 439 362 452 325 L 426 300 L 426 300 Q 408 282 408 256 Q 408 230 426 211 L 452 187 L 452 187 Q 439 150 414 121 L 380 131 L 380 131 Q 355 138 332 124 Q 310 111 303 86 L 294 51 L 294 51 Q 256 45 218 51 L 218 51 Z M 208 256 Q 209 283 232 298 Q 256 310 280 298 Q 303 283 304 256 Q 303 229 280 214 Q 256 202 232 214 Q 209 229 208 256 L 208 256 Z M 256 352 Q 230 352 208 339 L 208 339 L 208 339 Q 186 326 173 304 Q 160 281 160 256 Q 160 231 173 208 Q 186 186 208 173 Q 230 160 256 160 Q 282 160 304 173 Q 326 186 339 208 Q 352 231 352 256 Q 352 281 339 304 Q 326 326 304 339 Q 282 352 256 352 L 256 352 Z" />
          </svg>
        </a>
      </div>
    </header>

    <article>
      <div class="question">
        <div class="q-body">{{Question}}</div>
        <div id="q-clozes"></div>
        <div class="q-answers" id="q-options"></div>
        <div><a onclick="recover_order()" style="display:none;" id="recover_order" class="secondary">还原顺序</a></div>
        <div id="audio" style="display: none">>
          <audio controls src="__correct.mp3" id="correct_audio"></audio>
          <audio controls src="__wrong.mp3" id="wrong_audio"></audio>
        </div>
      </div>
      <footer>
        <details id="show_footer" >
          <summary onclick="localStorage.setItem('show_footer', !document.getElementById('show_footer').open)">
            <div style="display: inline-block;">
              <svg xmlns="http://www.w3.org/2000/svg" height="1em" fill="currentColor" viewBox="0 0 512 512">
                <path
                  d="M 256 32 Q 317 33 368 62 L 368 62 L 368 62 Q 419 92 450 144 Q 480 197 480 256 Q 480 315 450 368 Q 419 420 368 450 Q 317 479 256 480 Q 195 479 144 450 Q 93 420 62 368 Q 32 315 32 256 Q 32 197 62 144 Q 93 92 144 62 Q 195 33 256 32 L 256 32 Z M 256 512 Q 326 511 384 478 L 384 478 L 384 478 Q 442 444 478 384 Q 512 323 512 256 Q 512 189 478 128 Q 442 68 384 34 Q 326 1 256 0 Q 186 1 128 34 Q 70 68 34 128 Q 0 189 0 256 Q 0 323 34 384 Q 70 444 128 478 Q 186 511 256 512 L 256 512 Z M 208 352 Q 193 353 192 368 Q 193 383 208 384 L 304 384 L 304 384 Q 319 383 320 368 Q 319 353 304 352 L 272 352 L 272 352 L 272 240 L 272 240 Q 271 225 256 224 L 216 224 L 216 224 Q 201 225 200 240 Q 201 255 216 256 L 240 256 L 240 256 L 240 352 L 240 352 L 208 352 L 208 352 Z M 256 184 Q 278 182 280 160 Q 278 138 256 136 Q 234 138 232 160 Q 234 182 256 184 L 256 184 Z" />
              </svg>
            </div>
          </summary>
          <div id="source_container" style="display: none;">
          </div>
          <div id="tag_container" style="display: none;">
          </div>
          <span id="deck_container" style="display: none;"></span>
          <div id="time_container" style="display: none;">00:00
          </div>
        </details>
      </footer>
    </article>
    <article>
      <div class="heading">答案解析</div>
      <hr>
      <div id="choice_div">
        <div id="answer_div"></div>
      </div>
      <div style="margin-top: 0.5em;">{{Remarks}}</div>
    </article>
    {{#Notes}}
    <article>
      <div class="heading">我的笔记</div>
      <hr>
      <div id="notes" class="container-fluid">{{Notes}}</div>
    </article>
    {{/Notes}}
    {{#Extra}}
    <article>
      <div class="heading">额外补充</div>
      <hr>
      <div id="notes" class="container-fluid">{{Extra}}</div>
    </article>
    {{/Extra}}
    <div id="statis_div">
      <article>
        <div class="heading row">
          <span>
            答题统计
          </span>
          <a href="" onclick="resetStatis()" class="reset_btn">
          </a>
        </div>
        <hr>
        <table id="stats_table">
          <thead>
            <tr>
              <td>本次作答题数</td>
              <td>答对题数</td>
              <td>答错题数</td>
              <td>正确率</td>
            </tr>
          </thead>
          <tbody>
            <tr id="stats_tr">
              <td>5</td>
              <td>2</td>
              <td>3</td>
              <td>33.33%</td>
            </tr>
          </tbody>
        </table>
      </article>
    </div>
  </article>
</section>
<div id="audio" style="display: none">>
  <audio controls src="__correct.mp3" id="correct_audio"></audio>
  <audio controls src="__wrong.mp3" id="wrong_audio"></audio>
</div>
<dialog id="config-dialog" class="modal" style="backdrop-filter: blur(0.05rem);">
  <article>
    <header>
      <button aria-label="Close" rel="prev" onclick="toggleConfig()"></button>
      <p>
        <svg xmlns="http://www.w3.org/2000/svg" height="1em" fill="currentColor" viewBox="0 0 512 512">
          <path
            d="M 256 0 Q 282 0 306 5 Q 312 6 321 10 Q 329 15 335 25 Q 338 30 340 37 L 349 75 L 349 75 Q 351 80 356 83 Q 361 86 366 85 L 404 74 L 404 74 Q 410 72 416 72 Q 428 72 436 77 Q 445 82 449 87 Q 482 125 498 174 Q 501 180 501 189 Q 502 199 496 209 Q 492 214 488 219 L 459 246 L 459 246 Q 456 250 456 256 Q 456 262 459 266 L 488 293 L 488 293 Q 492 298 496 303 Q 501 314 501 323 Q 501 333 498 338 Q 482 387 449 425 Q 445 430 436 435 Q 428 440 416 440 Q 410 440 404 438 L 366 427 L 366 427 Q 361 426 356 429 Q 351 432 349 437 L 340 475 L 340 475 Q 338 482 335 487 Q 329 497 321 502 Q 312 506 306 507 Q 282 512 256 512 Q 230 512 206 507 Q 200 506 191 502 Q 183 497 177 487 Q 174 482 172 475 L 163 437 L 163 437 Q 161 432 156 429 Q 151 426 146 427 L 108 438 L 108 438 Q 102 440 96 440 Q 84 440 76 435 Q 67 430 64 425 Q 30 387 14 338 Q 11 332 11 323 Q 10 313 16 303 Q 20 298 24 293 L 53 266 L 53 266 Q 56 262 56 256 Q 56 250 53 246 L 24 219 L 24 219 Q 20 214 16 209 Q 10 198 11 189 Q 11 179 14 174 Q 30 126 64 87 Q 67 82 76 77 Q 84 72 96 72 Q 102 72 108 74 L 146 85 L 146 85 Q 151 86 156 83 Q 161 80 163 75 L 172 37 L 172 37 Q 174 30 177 25 Q 183 15 191 10 Q 200 6 206 5 Q 230 0 256 0 L 256 0 Z M 218 51 L 210 87 L 218 51 L 210 87 Q 203 111 180 124 Q 157 137 132 131 L 98 121 L 98 121 Q 73 150 60 187 L 86 212 L 86 212 Q 104 230 104 256 Q 104 282 86 301 L 60 325 L 60 325 Q 73 362 98 391 L 133 381 L 133 381 Q 157 374 180 388 Q 203 401 210 425 L 218 461 L 218 461 Q 256 467 294 461 L 303 425 L 303 425 Q 310 401 332 388 Q 355 375 380 381 L 414 391 L 414 391 Q 439 362 452 325 L 426 300 L 426 300 Q 408 282 408 256 Q 408 230 426 211 L 452 187 L 452 187 Q 439 150 414 121 L 380 131 L 380 131 Q 355 138 332 124 Q 310 111 303 86 L 294 51 L 294 51 Q 256 45 218 51 L 218 51 Z M 208 256 Q 209 283 232 298 Q 256 310 280 298 Q 303 283 304 256 Q 303 229 280 214 Q 256 202 232 214 Q 209 229 208 256 L 208 256 Z M 256 352 Q 230 352 208 339 L 208 339 L 208 339 Q 186 326 173 304 Q 160 281 160 256 Q 160 231 173 208 Q 186 186 208 173 Q 230 160 256 160 Q 282 160 304 173 Q 326 186 339 208 Q 352 231 352 256 Q 352 281 339 304 Q 326 326 304 339 Q 282 352 256 352 L 256 352 Z" />
        </svg>
        <strong> 配置</strong>
      </p>
    </header>
    <section>
      <section class="row">
        <label for="dark_mode">显示题型</label>
        <input id="show_type" type="checkbox" role="switch" data-config-key="show_type" />
      </section>
      <section class="row">
        <label for="show_deck">显示牌组</label>
        <input id="show_deck" type="checkbox" role="switch" data-config-key="show_deck" />
      </section>
      <section class="row">
        <label for="show_tag">显示标签</label>
        <input id="show_tag" type="checkbox" role="switch" data-config-key="show_tag" />
      </section>
      <section class="row">
        <label for="show_tag">随机选项</label>
        <input id="random_option" type="checkbox" role="switch" data-config-key="random_option" />
      </section>
      <section class="row">
        <label for="show_tag">自动翻转</label>
        <input id="auto_flip" type="checkbox" role="switch" data-config-key="auto_flip" />
      </section>
      <section class="row">
        <label for="show_tag">做题统计</label>
        <input id="statistics" type="checkbox" role="switch" data-config-key="statistics" />
      </section>
      <section class="row">
        <label for="show_tag">播放音效</label>
        <input id="play_sound" type="checkbox" role="switch" data-config-key="play_sound" />
      </section>
      <section class="row">
        <label for="show_tag">填空模式</label>
        <input id="cloze_mode" type="checkbox" role="switch" data-config-key="cloze_mode" />
      </section>
      <section class="row">
        <label for="dark_mode">暗黑模式</label>
        <input id="dark_mode" type="checkbox" role="switch" data-config-key="dark_mode" />
      </section>
      <section class="row">
        <label for="show_source">显示出处</label>
        <input id="show_source" type="checkbox" role="switch" data-config-key="show_source" />
      </section>
      <section class="row">
        <label for="show_time">显示用时</label>
        <input id="show_time" type="checkbox" role="switch" data-config-key="show_time" />
      </section>
    </section>
  </article>
</dialog>
<script>
  initializeConfig();
  var show_type = document.getElementById("show_type");
  var show_deck = document.getElementById("show_deck");
  var show_tag = document.getElementById("show_tag");
  var random_option = document.getElementById("random_option");
  var auto_flip = document.getElementById("auto_flip");
  var statistics = document.getElementById("statistics");
  var play_sound = document.getElementById("play_sound");
  var cloze_mode = document.getElementById("cloze_mode");
  var night_mode = document.getElementById("dark_mode");


  show_type.onchange = function () {
    localStorage.setItem("show_type", show_type.checked);
    update_type();
  };


  random_option.onchange = function () {
    localStorage.setItem("random_option", random_option.checked);
    updateOptions();
  };

  cloze_mode.onchange = function () {
    localStorage.setItem("cloze_mode", cloze_mode.checked);
    update_type();
    if (cloze_mode.checked) {
      updateClozes();
    } else {
      var choice_div = document.getElementById("choice_div");
      choice_div.style.display = "block";
      var q_options_div = document.getElementById("q-options");
      q_options_div.style.display = "block";
      var q_cloze_div = document.getElementById("q-clozes");
      q_cloze_div.style.display = "none";
      show_answers();
      updateOptions();
      updateStats();
    }
  }
  function update_type() {
    var q_type = document.getElementById("type_container");
    var answers = `{{Answers}}`.split("||");
    var show_type = eval(localStorage.getItem("show_type"));
    if (!eval(localStorage.getItem("cloze_mode"))) {
      if (show_type) {
        if (answers.length > 1) {
          q_type.innerText = "多选题";
        } else {
          q_type.innerText = "单选题";
        }
      } else {
        q_type.innerText = "选择题";
      }
    } else {
      q_type.innerText = "填空题";
    }
  }
  function init() {
    console.log("cloze_mode: ", eval(localStorage.getItem("cloze_mode")));
    // 非填空模式
    if (!eval(localStorage.getItem("cloze_mode"))) {
      show_answers();
      updateOptions();
      updateStats();
      // 选项还原
      if (eval(localStorage.getItem("random_option"))) {
        document.getElementById("recover_order").style.display = "block";
      }
    } else {
      // 填空模式
      updateClozes();
    }
    update_type();
    // 主题
    let is_dark_mode = localStorage.getItem("dark_mode") ?? "false";
    let ele = document.querySelector("html");
    if (is_dark_mode === "true") {
      ele.setAttribute("data-theme", "dark");
    } else {
      ele.setAttribute("data-theme", "light");
    }
    // 显示牌组
    let deck_container = document.getElementById("deck_container");
    let deck = "{{Deck}}";
    deck_container.innerText = `${deck.replaceAll("::", " / ")}`;
    if (eval(localStorage.getItem("show_deck"))) {
      deck_container.style.display = "flex";
    } else {
      deck_container.style.display = "none";
    }

    // 显示标签
    let tag_container = document.getElementById("tag_container");
    let tags = `{{Tags}}`;
    if (tags.trim() !== "") {
      const tagArray = tags.split(' ').map(tag => tag.trim()).filter(tag => tag !== ""); // 过滤掉空字符串
      const tagSpans = tagArray.map(tag => `<span class="tag">${tag}</span>`).join('');
      tag_container.innerHTML = `${tagSpans}`;
    } else {
      tag_container.innerHTML = "暂无标签";
    }
    // 显示出处
    let source_container = document.getElementById("source_container");
    let source = "{{Source}}".trim();
    source_container.innerHTML = source;
    if (localStorage.getItem("show_source") === "true" && source !== "") {
      source_container.style.display = "flex";
    } else {
      source_container.style.display = "none";
    }
    // 显示用时
    let time_container = document.getElementById("time_container");
    // 清除定时器
    let interval_id = Number.parseInt(localStorage.getItem("timer_interval") || "0");
    if (interval_id > 0) {
      clearInterval(interval_id);
    }
    let total_seconds = Number.parseInt(localStorage.getItem("time_elapsed") || "0");
    let minutes = Math.floor(total_seconds / 60);
    let seconds = total_seconds % 60;
    time_container.innerText = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    let show_footer_detail = document.getElementById("show_footer");
    console.log("show_footer: ", localStorage.getItem("show_footer"));
    show_footer_detail.open = localStorage.getItem("show_footer") === "true";
  }
  init();

  // 显示答案
  function show_answers(option_recover = false) {
    var options = `{{Options}}`.split("||");
    var correctAnswers = `{{Answers}}`.split("||");
    var selectedOptions = [];
    selectedOptions = localStorage.getItem("selectedOptions").split(",") || [];
    console.log("selectedOptions: ", selectedOptions);
    var answer_div = document.getElementById("answer_div");
    var ans_letters = [];
    var selected_letters = [];
    var seqs = [...Array(options.length).keys()].map((x) => x);
    if (!option_recover) {
      seqs = localStorage.getItem("seqs").split(",");
    }
    console.log({ seqs });
    console.log({ selectedOptions });
    for (let i = 0; i < seqs.length; i++) {
      let val = eval(seqs[i]);
      console.log(i, val);
      if (correctAnswers.includes((val + 1).toString())) {
        if (option_recover) {
          ans_letters.push(String.fromCharCode(65 + val));
        } else {
          ans_letters.push(String.fromCharCode(65 + i));
        }
      }
      if (selectedOptions.includes((val + 1).toString())) {
        if (option_recover) {
          selected_letters.push(String.fromCharCode(65 + val));
        } else {
          selected_letters.push(String.fromCharCode(65 + i));
        }
      }
    }
    console.log({ ans_letters, selected_letters });
    var ans_letters_str = ans_letters.join("");
    var selected_letters_str = selected_letters.join("");
    var is_correct = false;
    if (ans_letters_str == selected_letters_str) {
      answer_div.innerHTML = `正确答案: <font color="green">${ans_letters_str}</font>&nbsp;&nbsp;你的答案：<font color="green">${selected_letters_str}</font>`;
      is_correct = true;
    } else {
      answer_div.innerHTML = `正确答案: <font color="green">${ans_letters_str}</font>&nbsp;&nbsp;你的答案：<font color="#ff0000">${selected_letters_str}</font>`;
    }
    if (option_recover) {
      return;
    }
    var is_play_sound = eval(localStorage.getItem("play_sound"));
    if (is_play_sound) {
      if (is_correct) {
        var audio = document.getElementById("correct_audio");
        audio.play();
      } else {
        var audio = document.getElementById("wrong_audio");
        audio.play();
      }
    }
    if (ans_letters_str == selected_letters_str) {
      if (
        localStorage.getItem("correct") == null ||
        localStorage.getItem("correct") == undefined
      ) {
        localStorage.setItem("correct", 1);
      } else {
        localStorage.setItem("correct", eval(localStorage.getItem("correct")) + 1);
      }
    } else {
      if (
        localStorage.getItem("wrong") == null ||
        localStorage.getItem("wrong") == undefined
      ) {
        localStorage.setItem("wrong", 1);
      } else {
        localStorage.setItem("wrong", eval(localStorage.getItem("wrong")) + 1);
      }
    }
  }
  // 更新选项
  function updateOptions(option_recover = false) {
    var options = `{{Options}}`.split("||");
    var correctAnswers = `{{Answers}}`.split("||");
    var selectedOptions = [];
    selectedOptions = localStorage.getItem("selectedOptions").split(",") || [];
    var q_options_div = document.getElementById("q-options");
    q_options_div.innerHTML = "";
    var seqs = [...Array(options.length).keys()].map((x) => x);
    if (!option_recover) {
      seqs = localStorage.getItem("seqs").split(",");
    }
    console.log("seqs: ", seqs);
    for (let i = 0; i < seqs.length; i++) {
      let data = eval(seqs[i]);
      var option_div = document.createElement("div");
      let val = (data + 1).toString();
      option_div.setAttribute("data", val);
      option_div.innerHTML = `<span>${String.fromCharCode(65 + i)}. ${options[data]}</span>`;
      if (selectedOptions.includes(val) && correctAnswers.includes(val)) {
        option_div.className = "q-answer correct";
        option_div.innerHTML += `<img src="__correct.svg" class="icon" />`;
      } else if (
        selectedOptions.includes(val) &&
        !correctAnswers.includes(val)
      ) {
        option_div.className = "q-answer incorrect";
        option_div.innerHTML += `<img src="__error.svg" class="icon" />`;
      } else if (
        !selectedOptions.includes(val) &&
        correctAnswers.includes(val)
      ) {
        if (correctAnswers.length > 1) {
          option_div.className = "q-answer should-select";
          option_div.innerHTML += `<img src="__warning.png" class="icon" />`;
        } else {
          option_div.className = "q-answer correct";
          option_div.innerHTML += `<img src="__correct.svg" class="icon" />`;
        }
      } else {
        option_div.className = "q-answer normal";
      }
      if (option_recover) {
        option_div.className += " reorder";
        option_div.style.animation = 'reorder 1s forwards';
      }
      q_options_div.appendChild(option_div);
    }
  }
  //   显示统计结果
  function updateStats() {
    var statis_div = document.getElementById("statis_div");
    var is_show_stats = eval(localStorage.getItem("statistics"));
    if (!is_show_stats) {
      statis_div.style.display = "none";
      return;
    }
    var correct_count = 0;
    var wrong_count = 0;
    correct_count = eval(localStorage.getItem("correct")) ?? 0;
    wrong_count = eval(localStorage.getItem("wrong")) ?? 0;
    var total_count = correct_count + wrong_count;
    var percent = (correct_count / total_count) * 100;
    var stats_tr = document.getElementById("stats_tr");
    stats_tr.innerHTML = `
    <td>${total_count}</td>
    <td>${correct_count}</td>
    <td>${wrong_count}</td>
    <td>${percent.toFixed(2)}%</td>
    `;
  }


  function updateClozes() {
    var mask = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
    var correctAnswers = `{{Answers}}`.split("||");
    var choice_div = document.getElementById("choice_div");
    choice_div.style.display = "none";
    var q_options_div = document.getElementById("q-options");
    q_options_div.style.display = "none";
    var q_cloze_div = document.getElementById("q-clozes");
    var options = `{{Options}}`.split("||");
    let final_html_content = "";
    for (let i = 0; i < correctAnswers.length; i++) {
      let option_content = options[parseInt(correctAnswers[i]) - 1];
      let span = document.createElement("span");
      span.setAttribute("cloze_id", `c${i}`);
      span.setAttribute("cloze_text", `${option_content}`);
      span.innerHTML = option_content;
      span.className = "cloze-show";
      final_html_content += `${span.outerHTML}`;
      if (i != correctAnswers.length - 1) final_html_content += "，";
    }
    q_cloze_div.innerHTML = final_html_content;
    document.querySelectorAll(".cloze-hide, .cloze-show").forEach(ele => {
      let cloze_id = ele.getAttribute("cloze_id");
      ele.onclick = () => {
        [].forEach.call(q_cloze_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
          if (ele.className === 'cloze-hide') {
            ele.innerHTML = ele.getAttribute("cloze_text");
            ele.className = "cloze-show";
          } else {
            ele.innerHTML = mask;
            ele.className = "cloze-hide";
          }
        })
      }
    });
  }
  function resetStatis() {
    localStorage.setItem("correct", 0);
    localStorage.setItem("wrong", 0);
    updateStats();
  }
  function recover_order() {
    show_answers(true);
    updateOptions(true);
    updateStats();
  }

</script>