// ignore_for_file: type=lint, type=warning
part of 'signals.dart';


@immutable
class DartRequest {
  const DartRe<PERSON>({
    required this.interactionId,
    required this.msgType,
    required this.params,
  });

  static DartRequest deserialize(BinaryDeserializer deserializer) {
    deserializer.increaseContainerDepth();
    final instance = DartRequest(
      interactionId: deserializer.deserializeString(),
      msgType: deserializer.deserializeString(),
      params: deserializer.deserializeString(),
    );
    deserializer.decreaseContainerDepth();
    return instance;
  }

  static DartRequest bincodeDeserialize(Uint8List input) {
    final deserializer = BincodeDeserializer(input);
    final value = DartRequest.deserialize(deserializer);
    if (deserializer.offset < input.length) {
      throw Exception('Some input bytes were not read');
    }
    return value;
  }

  final String interactionId;
  final String msgType;
  final String params;

  DartRequest copyWith({
    String? interactionId,
    String? msgType,
    String? params,
  }) {
    return DartRequest(
      interactionId: interactionId ?? this.interactionId,
      msgType: msgType ?? this.msgType,
      params: params ?? this.params,
    );
  }

  void serialize(BinarySerializer serializer) {
    serializer.increaseContainerDepth();
    serializer.serializeString(interactionId);
    serializer.serializeString(msgType);
    serializer.serializeString(params);
    serializer.decreaseContainerDepth();
  }

  Uint8List bincodeSerialize() {
      final serializer = BincodeSerializer();
      serialize(serializer);
      return serializer.bytes;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;

    return other is DartRequest
      && interactionId == other.interactionId
      && msgType == other.msgType
      && params == other.params;
  }

  @override
  int get hashCode => Object.hash(
        interactionId,
        msgType,
        params,
      );

  @override
  String toString() {
    String? fullString;

    assert(() {
      fullString = '$runtimeType('
        'interactionId: $interactionId, '
        'msgType: $msgType, '
        'params: $params'
        ')';
      return true;
    }());

    return fullString ?? 'DartRequest';
  }
}

extension DartRequestDartSignalExt on DartRequest {
  /// Sends the signal to Rust.
  /// Passing data from Rust to Dart involves a memory copy
  /// because Rust cannot own data managed by Dart's garbage collector.
  void sendSignalToRust() {
    final messageBytes = bincodeSerialize();
    final binary = Uint8List(0);
    sendDartSignal(
      'rinf_send_dart_signal_dart_request',
      messageBytes,
      binary,
    );
  }
}
