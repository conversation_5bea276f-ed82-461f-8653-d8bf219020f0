// This is a basic Flutter integration test.
//
// Since integration tests run in a full Flutter application, they can interact
// with the host side of a plugin implementation, unlike Dart unit tests.
//
// For more information about Flutter integration tests, please see
// https://flutter.dev/to/integration-testing

import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:flutter_machineid/flutter_machineid.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('FlutterMachineid Integration Tests', () {
    testWidgets('getId returns a valid machine ID', (WidgetTester tester) async {
      final String? machineId = await FlutterMachineid.id;

      // Machine ID should not be null or empty
      expect(machineId, isNotNull);
      expect(machineId!.isNotEmpty, true);

      // Machine ID should be consistent across calls
      final String? machineId2 = await FlutterMachineid.id;
      expect(machineId, equals(machineId2));

      print('Machine ID: $machineId');
    });

    testWidgets('protectedID returns a valid protected ID', (WidgetTester tester) async {
      const String appId = 'com.example.flutter_machineid_test';
      final String? protectedId = await FlutterMachineid.protectedID(appId);

      // Protected ID should not be null or empty
      expect(protectedId, isNotNull);
      expect(protectedId!.isNotEmpty, true);

      // Protected ID should be consistent for the same app ID
      final String? protectedId2 = await FlutterMachineid.protectedID(appId);
      expect(protectedId, equals(protectedId2));

      // Protected ID should be different for different app IDs
      final String? protectedId3 = await FlutterMachineid.protectedID('different.app.id');
      expect(protectedId, isNot(equals(protectedId3)));

      // Protected ID should be a hex string (HMAC-SHA256 output)
      expect(protectedId.length, equals(64));
      expect(RegExp(r'^[a-f0-9]+$').hasMatch(protectedId), true);

      print('Protected ID for $appId: $protectedId');
    });

    testWidgets('machine ID and protected ID are different', (WidgetTester tester) async {
      final String? machineId = await FlutterMachineid.id;
      final String? protectedId = await FlutterMachineid.protectedID('test.app');

      expect(machineId, isNotNull);
      expect(protectedId, isNotNull);
      expect(machineId, isNot(equals(protectedId)));

      print('Machine ID: $machineId');
      print('Protected ID: $protectedId');
    });

    testWidgets('getPlatformVersion (legacy) works', (WidgetTester tester) async {
      final FlutterMachineid plugin = FlutterMachineid();
      final String? version = await plugin.getPlatformVersion();

      // The version string depends on the host platform running the test, so
      // just assert that some non-empty string is returned.
      expect(version?.isNotEmpty, true);

      print('Platform version: $version');
    });

    testWidgets('machine ID format validation', (WidgetTester tester) async {
      final String? machineId = await FlutterMachineid.id;
      expect(machineId, isNotNull);

      // Machine ID should be a valid UUID-like format or similar
      // The exact format depends on the platform, but it should be alphanumeric with possible hyphens
      expect(RegExp(r'^[a-fA-F0-9\-]+$').hasMatch(machineId!), true);

      // Should be at least 8 characters long (reasonable minimum for a machine ID)
      expect(machineId.length, greaterThanOrEqualTo(8));
    });

    testWidgets('error handling with invalid app ID', (WidgetTester tester) async {
      // Test with empty app ID
      final String? protectedId = await FlutterMachineid.protectedID('');

      // Should still work with empty string (though not recommended)
      expect(protectedId, isNotNull);
      expect(protectedId!.length, equals(64));
    });

    testWidgets('performance test - multiple calls', (WidgetTester tester) async {
      final stopwatch = Stopwatch()..start();

      // Make multiple calls to ensure performance is reasonable
      for (int i = 0; i < 10; i++) {
        final machineId = await FlutterMachineid.id;
        final protectedId = await FlutterMachineid.protectedID('test.app.$i');

        expect(machineId, isNotNull);
        expect(protectedId, isNotNull);
      }

      stopwatch.stop();
      print('10 calls took ${stopwatch.elapsedMilliseconds}ms');

      // Should complete within reasonable time (adjust as needed)
      expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5 seconds max
    });
  });
}
