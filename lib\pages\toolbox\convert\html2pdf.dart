import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/toolbox/convert.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';

class HTML2PDFPage extends StatefulWidget {
  const HTML2PDFPage({super.key});

  @override
  State<HTML2PDFPage> createState() => _HTML2PDFPageState();
}

class _HTML2PDFPageState extends State<HTML2PDFPage> {
  final controller = Get.put(PDFConvertPageController());

  @override
  void initState() {
    super.initState();
    controller.convertType.value = 'html2pdf';
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.convert.html2pdf.title'.tr,
            style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.convert.html2pdf.description'.tr,
                  style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.convert.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        ShadSwitchCustom(
                          key: const ValueKey("use-custom-font"),
                          label: 'toolbox.convert.html2pdf.customFont'.tr,
                          initialValue: controller.useCustomFont.value,
                          onChanged: (v) {
                            controller.useCustomFont.value = v;
                          },
                        ),
                        if (controller.useCustomFont.value) ...[
                          ShadInputWithFileSelect(
                            key: const ValueKey("custom-font-file"),
                            title: 'toolbox.convert.html2pdf.fontFile'.tr,
                            placeholder: Text(
                                'toolbox.convert.html2pdf.fontFilePlaceholder'
                                    .tr),
                            allowedExtensions: const ['ttf'],
                            isRequired: true,
                            allowMultiple: false,
                            initialValue: [controller.customFontFilePath.value],
                            onFilesSelected: (files) {
                              controller.customFontFilePath.value =
                                  files.single;
                            },
                            onValidate: (value, files) async {
                              return await validateFile(value, files);
                            },
                            onValidateError: (error) {},
                          ),
                        ] else ...[
                          ShadSelectCustom(
                            key: const ValueKey("font-family"),
                            label: 'toolbox.convert.html2pdf.font'.tr,
                            placeholder:
                                'toolbox.convert.html2pdf.selectFont'.tr,
                            isMultiple: false,
                            initialValue: [controller.fontName.value],
                            options: controller.fontFamilyList.toList(),
                            onChanged: (value) {
                              controller.fontName.value = value.single;
                            },
                          ),
                        ],
                        ShadInputWithValidate(
                            key: const ValueKey("font-size"),
                            label: 'toolbox.convert.html2pdf.fontSize'.tr,
                            placeholder:
                                'toolbox.convert.html2pdf.fontSizePlaceholder'
                                    .tr,
                            initialValue: controller.fontSize.value.toString(),
                            onChanged: (value) {
                              controller.fontSize.value =
                                  double.tryParse(value) ??
                                      controller.fontSize.value;
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return 'toolbox.convert.html2pdf.fontSizeError1'
                                    .tr;
                              }
                              final reg = RegExp(r'^\d+(\.\d+)?$');
                              if (!reg.hasMatch(value)) {
                                return 'toolbox.convert.html2pdf.fontSizeError2'
                                    .tr;
                              }
                              return "";
                            }),
                        if (PathUtils.isDesktop) ...[
                          const SizedBox(height: 4),
                          ShadSelectCustom(
                            label: 'toolbox.convert.common.outputLocation'.tr,
                            placeholder:
                                'toolbox.convert.common.selectOutputLocation'
                                    .tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        ],
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.convert.common.outputDirectory'.tr,
                            placeholder: Text(
                                'toolbox.convert.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.convert.common.inputFile'.tr,
                          placeholder: Text(
                              'toolbox.convert.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['html', 'htm'],
                          isRequired: true,
                          allowMultiple: true,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
