<div class="media-container">
  {{#Video}}
  <video id="mediaPlayer" class="media-player" preload="auto">
      <source src="{{Video}}" type="video/mp4">
      <source src="{{Video}}" type="video/webm">
      您的浏览器不支持视频播放
  </video>
  {{/Video}}

  {{^Video}}
  <audio id="mediaPlayer" class="media-player" preload="auto">
      <source src="{{Audio}}" type="audio/mpeg">
      您的浏览器不支持音频播放
  </audio>
  {{/Video}}

  <div class="controls-container">
      <div class="progress-bar">
          <input type="range" id="progress" value="0" step="0.1">
      </div>

      <div class="control-buttons">
          <button id="playPauseBtn" class="btn-icon">▶</button>
          <span class="time-display">
              <span id="currentTime">00:00</span> /
              <span id="duration">00:00</span>
          </span>
          <button id="muteBtn" class="btn-icon">🔊</button>
          <button id="loopBtn" class="btn-icon">🔄</button>
          <div class="speed-control">
              <button id="speedBtn" class="btn-icon">×1.0</button>
              <div class="speed-options">
                  <button data-speed="0.5">0.5×</button>
                  <button data-speed="0.75">0.75×</button>
                  <button data-speed="1.0">1.0×</button>
                  <button data-speed="1.25">1.25×</button>
                  <button data-speed="1.5">1.5×</button>
                  <button data-speed="2.0">2.0×</button>
              </div>
          </div>
      </div>
  </div>
</div>

<script>
  (() => {
      const player = document.getElementById('mediaPlayer');
      const playPauseBtn = document.getElementById('playPauseBtn');
      const progress = document.getElementById('progress');

      // 播放/暂停控制
      playPauseBtn.addEventListener('click', () => {
          player.paused ? player.play() : player.pause();
      });

      // 更新播放状态指示
      player.addEventListener('play', () => playPauseBtn.textContent = '⏸');
      player.addEventListener('pause', () => playPauseBtn.textContent = '▶');

      // 进度条初始化
      player.addEventListener('loadedmetadata', () => {
          progress.max = player.duration;
          document.getElementById('duration').textContent =
              formatTime(player.duration);
          // 初始化进度条样式
          progress.style.setProperty('--progress', '0%');
          // 初始化按钮状态
          playPauseBtn.textContent = player.paused ? '▶' : '⏸';
      });

      // 实时更新时间显示
      player.addEventListener('timeupdate', () => {
          progress.value = player.currentTime;
          const progressPercent = (player.currentTime / player.duration) * 100;
          progress.style.setProperty('--progress', `${progressPercent}%`);
          document.getElementById('currentTime').textContent =
              formatTime(player.currentTime);
      });

      // 拖动进度条
      progress.addEventListener('input', () => {
          player.currentTime = progress.value;
      });

      // 静音控制
      document.getElementById('muteBtn').addEventListener('click', () => {
          player.muted = !player.muted;
          document.getElementById('muteBtn').textContent =
              player.muted ? '🔇' : '🔊';
      });

      // 循环控制
      let isLooping = false;
      document.getElementById('loopBtn').addEventListener('click', () => {
          isLooping = !isLooping;
          player.loop = isLooping;
          const loopBtn = document.getElementById('loopBtn');
          loopBtn.setAttribute('data-looping', isLooping);
      });

      // 播放速度控制
      const speedBtn = document.getElementById('speedBtn');
      const speedOptions = document.querySelector('.speed-options');
      let currentSpeed = 1.0;

      speedOptions.querySelectorAll('button').forEach(btn => {
          btn.addEventListener('click', (e) => {
              currentSpeed = parseFloat(e.target.dataset.speed);
              player.playbackRate = currentSpeed;
              speedBtn.textContent = `×${currentSpeed}`;

              // 移除所有激活状态
              speedOptions.querySelectorAll('button').forEach(b => b.classList.remove('active'));
              // 添加当前选中状态
              e.target.classList.add('active');
          });
      });

      // 点击外部关闭菜单
      document.addEventListener('click', (e) => {
          if (!e.target.closest('.speed-control')) {
              speedOptions.style.display = 'none';
          }
      });

      // 切换菜单显示
      speedBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          speedOptions.style.display =
              speedOptions.style.display === 'block' ? 'none' : 'block';
      });

      // 时间格式化函数
      function formatTime(seconds) {
          const minutes = Math.floor(seconds / 60);
          seconds = Math.floor(seconds % 60);
          return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
      }
  })();
</script>