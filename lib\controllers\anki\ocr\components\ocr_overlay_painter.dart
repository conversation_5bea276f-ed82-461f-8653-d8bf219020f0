import 'dart:math';
import 'package:flutter/material.dart';
import 'package:anki_guru/controllers/anki/ocr/components/ocr_image.dart';

/// OCR覆盖层绘制类，用于在图像上绘制OCR识别到的文本块
class OcrOverlayPainter extends CustomPainter {
  /// OCR图像对象
  final OcrImage imageObj;

  /// 绘制区域的大小
  final Size widgetSize;

  /// 是否正在拖动选择
  final bool isDragging;

  /// 拖动模式：true为选中模式，false为取消模式
  final bool isSelectMode;

  /// 边框颜色
  final Color borderColor;

  /// 选中区域的填充颜色
  final Color fillColor;

  OcrOverlayPainter({
    required this.imageObj,
    required this.widgetSize,
    this.isDragging = false,
    this.isSelectMode = true,
    this.borderColor = Colors.red,
    Color? fillColor,
  }) : fillColor = fillColor ?? borderColor.withOpacity(0.3);

  @override
  void paint(Canvas canvas, Size size) {
    if (!imageObj.isProcessed ||
        imageObj.image == null ||
        widgetSize == Size.zero) return;

    // 计算缩放和偏移，使图像适应屏幕（BoxFit.contain）
    // 改进的缩放计算，确保准确匹配图像边界
    final imageWidth = imageObj.image!.width.toDouble();
    final imageHeight = imageObj.image!.height.toDouble();

    final double scale =
        min(widgetSize.width / imageWidth, widgetSize.height / imageHeight);

    // 计算渲染后的图像尺寸
    final Size renderedImageSize = Size(
      imageWidth * scale,
      imageHeight * scale,
    );

    // 计算图像在容器中的居中偏移量
    final Offset offset = Offset(
        (widgetSize.width - renderedImageSize.width) / 2,
        (widgetSize.height - renderedImageSize.height) / 2);

    // 未选中的矩形使用边框色
    final unselectedPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // 选中的矩形使用半透明填充色
    final selectedPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill;

    // 绘制每个文本块的边界框
    for (int i = 0; i < imageObj.textBlocks.length; i++) {
      final textBlock = imageObj.textBlocks[i];

      // 获取文本块的边界矩形
      final blockRect = textBlock.getBoundingRect();

      // 缩放并转换坐标
      final Rect screenRect = Rect.fromLTWH(
        blockRect.left * scale + offset.dx,
        blockRect.top * scale + offset.dy,
        blockRect.width * scale,
        blockRect.height * scale,
      );

      // 确定当前文本块是否被选中
      final bool isSelected = imageObj.selectedIndices.contains(i);

      // 绘制文本块
      if (isSelected) {
        // 选中的文本块：填充半透明
        canvas.drawRect(screenRect, selectedPaint);

        // 正常选中的边框
        canvas.drawRect(
            screenRect,
            Paint()
              ..color = borderColor
              ..style = PaintingStyle.stroke
              ..strokeWidth = 2.0);
      } else {
        // 未选中的文本块：仅绘制边框
        canvas.drawRect(screenRect, unselectedPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant OcrOverlayPainter oldDelegate) {
    return oldDelegate.imageObj != imageObj ||
        oldDelegate.widgetSize != widgetSize ||
        oldDelegate.isDragging != isDragging ||
        oldDelegate.isSelectMode != isSelectMode ||
        oldDelegate.borderColor != borderColor ||
        oldDelegate.fillColor != fillColor;
  }
}
