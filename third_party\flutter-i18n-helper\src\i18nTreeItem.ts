// --- START OF MODIFIED FILE i18nTreeItem.ts ---

import * as vscode from 'vscode';

export class I18nTreeItem extends vscode.TreeItem {
    constructor(
        // The label can now be a string or a rich label object with highlights
        public readonly label: string | vscode.TreeItemLabel,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly keyPath: string,
        public readonly lang?: string
    ) {
        super(label, collapsibleState);
        
        // Use the string part of the label for the tooltip
        const displayLabel = typeof label === 'string' ? label : label.label;
        this.tooltip = `Key: ${this.keyPath}${this.lang ? `\nLanguage: ${this.lang}` : ''}`;
        
        if (this.lang) {
            // 这是子节点 (e.g., "🇺🇸 en: Hello")，是翻译项
            this.contextValue = 'translationItem';
            // 设置点击行为为跳转到定义
            this.command = {
                command: 'i18n-helper.view.goToDefinition',
                title: 'Go to Definition',
                arguments: [this]
            };
        } else if (this.collapsibleState !== vscode.TreeItemCollapsibleState.None) {
            // 这是父节点 (e.g., "common.hello")，是 Key 项
            this.contextValue = 'keyItem';
            // 不设置 command，这样点击时就会执行默认的展开/折叠行为
        } else {
            // 这是特殊状态的节点 (e.g., "Loading...", "No keys found...")
            this.contextValue = 'infoItem';
        }
    }
}
// --- END OF MODIFIED FILE i18nTreeItem.ts ---