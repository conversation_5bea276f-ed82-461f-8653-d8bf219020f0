#!/bin/bash

# 定义输出目录
OUTPUT_DIR=".output"
MV2_OUTPUT="${OUTPUT_DIR}/guru-connector-extension-1.0.8-chrome-mv2.zip"
MV3_OUTPUT="${OUTPUT_DIR}/guru-connector-extension-1.0.8-chrome-mv3.zip"

# 创建MV2和MV3版本的扩展
echo "🔨 构建MV2版本..."
pnpm build:mv2
pnpm zip:mv2

echo "🔨 构建MV3版本..."
pnpm build:mv3
pnpm zip:mv3

# 重命名文件以区分MV2和MV3版本
echo "📦 重命名输出文件..."
mv "${OUTPUT_DIR}/guru-connector-extension-1.0.8-chrome.zip" "${MV2_OUTPUT}"
pnpm zip:mv3
mv "${OUTPUT_DIR}/guru-connector-extension-1.0.8-chrome.zip" "${MV3_OUTPUT}"

# 输出结果信息
echo "✅ 构建完成！"
echo "MV2版本: ${MV2_OUTPUT} ($(du -h ${MV2_OUTPUT} | cut -f1))"
echo "MV3版本: ${MV3_OUTPUT} ($(du -h ${MV3_OUTPUT} | cut -f1))"
