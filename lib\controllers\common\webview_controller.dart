import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'dart:io';
import 'dart:convert';
import 'package:flutter_js/flutter_js.dart';

class WebviewController extends GetxController {
  // HeadlessInAppWebView? headlessWebView;
  final headlessWebView = HeadlessInAppWebView(
      initialData: InAppWebViewInitialData(data: "<html><body></body></html>"),
      onLoadStop: (controller, _) async {
        await Future.wait([
          controller.injectJavascriptFileFromAsset(
              assetFilePath: 'assets/markmap.index.mjs'),
          controller.injectJavascriptFileFromAsset(
              assetFilePath: 'assets/mammoth.browser.min.js'),
          controller.injectJavascriptFileFromAsset(
              assetFilePath: 'assets/parse_html.js'),
          controller.injectJavascriptFileFromAsset(
              assetFilePath: 'assets/jsonrepair.min.js'),
        ]);
      });
  WebViewEnvironment? webViewEnvironment;
  PullToRefreshController? pullToRefreshController;
  String url = "https://www.bilibili.com/";
  final progress = 0.0.obs;
  final currentUrl = "".obs;
  InAppWebViewController? webViewController;
  InAppWebViewSettings settings = InAppWebViewSettings(
      isInspectable: true,
      mediaPlaybackRequiresUserGesture: false,
      allowsInlineMediaPlayback: true,
      iframeAllow: "camera; microphone",
      iframeAllowFullscreen: true);
  CookieManager cookieManager = CookieManager.instance();
  final clipboardController = Get.find<ClipboardController>();

  late JavascriptRuntime? jsRuntime;

  @override
  void onInit() async {
    try {
      try {
        jsRuntime = getJavascriptRuntime();
        // Load and evaluate the jsonrepair.js file
        final jsonRepairJs =
            await rootBundle.loadString('assets/jsonrepair.min.js');
        jsRuntime?.evaluateAsync(jsonRepairJs);
      } catch (e) {
        logger.e('Failed to initialize JavaScript runtime: $e');
        jsRuntime = null;
      }
      await headlessWebView.run();

      if (Platform.isWindows) {
        final availableVersion = await WebViewEnvironment.getAvailableVersion();
        assert(availableVersion != null,
            'Failed to find an installed WebView2 Runtime or non-stable Microsoft Edge installation.');
        webViewEnvironment = await WebViewEnvironment.create(
            settings:
                WebViewEnvironmentSettings(userDataFolder: 'custom_path'));
      }

      if (Platform.isAndroid) {
        await InAppWebViewController.setWebContentsDebuggingEnabled(true);
      }

      pullToRefreshController = (Platform.isWindows ||
              Platform.isLinux ||
              Platform.isMacOS)
          ? null
          : PullToRefreshController(
              settings: PullToRefreshSettings(
                color: Colors.blue,
              ),
              onRefresh: () async {
                if (Platform.isAndroid) {
                  webViewController?.reload();
                } else if (Platform.isIOS) {
                  webViewController?.loadUrl(
                      urlRequest:
                          URLRequest(url: await webViewController?.getUrl()));
                }
              },
            );
    } catch (e, stackTrace) {
      logger.e('Error initializing WebviewController: $e\n$stackTrace');
    }

    super.onInit();
  }

  @override
  void onClose() {
    disposeWebView();
    jsRuntime?.dispose();
    super.onClose();
  }

  void disposeWebView() {
    webViewController?.dispose();
    webViewController = null;
    headlessWebView.dispose();
    pullToRefreshController?.dispose();
  }

  Future<List<Cookie>> getCookies(String url) async {
    return await cookieManager.getCookies(
        url: WebUri(url), webViewController: webViewController);
  }

  Future<void> loadUrl(String url) async {
    await webViewController?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
  }

  Future<String> browserRunJS(String source) async {
    final result = await webViewController?.evaluateJavascript(source: source);
    return result.toString();
  }

  Future<void> getSnapshot() async {
    const getSnapshotJs = '''
var v = document.querySelector("video");
v.setAttribute("crossOrigin", "anonymous"); 
var canvas = document.createElement("canvas");
canvas.width = v.videoWidth;
canvas.height = v.videoHeight;
const ctx = canvas.getContext("2d");
ctx.drawImage(v, 0, 0, v.videoWidth, v.videoHeight);
canvas.toDataURL("image/png");
''';

    final result = await browserRunJS(getSnapshotJs);
    logger.i(result);
    if (result.isNotEmpty) {
      final data = result.replaceFirst('data:image/png;base64,', '');
      if (data.isNotEmpty) {
        await clipboardController.copyImageBase64(data);
        logger.i('Screenshot saved to clipboard');
      } else {
        logger.e('Failed to get valid screenshot data');
      }
    } else {
      logger.e('Failed to get valid screenshot data');
    }
  }

  Future<void> getTime() async {
    const getTimeJs =
        'document.querySelector("span.bpx-player-ctrl-time-current").innerText';
    final result = await browserRunJS(getTimeJs);
    await clipboardController.copyText(result);
  }

  Future<String> runJS(String source) async {
    final result = await headlessWebView.webViewController
        ?.evaluateJavascript(source: source);
    return result.toString();
  }

  Future<String> convertMD2JSON(String source) async {
    final result = await runJS("""
        var { Transformer } = window.markmap; 
        var transformer = new Transformer(); 
        var { root, features } = transformer.transform(`$source`); 
        JSON.stringify({ root, features });
  """);
    return result;
  }

  Future<Map<String, List<String>>> splitHTML(
    String html,
    String splitRegex,
    String parentDeck,
    String deckPrefix,
  ) async {
    final result = await runJS("""
        var html = `$html`;
        var splitRegex = `$splitRegex`;
        var parentDeck = `$parentDeck`;
        var deckPrefix = `$deckPrefix`;
        window.splitHtml(html, splitRegex, parentDeck,deckPrefix);
  """);

    // 将JavaScript返回的JSON字符串解析为Dart对象
    final Map<String, dynamic> jsonResult = jsonDecode(result);
    // 转换为指定的返回类型
    final Map<String, List<String>> typedResult = {};

    jsonResult.forEach((deckName, texts) {
      final List<dynamic> textsList = texts as List<dynamic>;
      typedResult[deckName] = textsList.map((text) => text.toString()).toList();
    });

    return typedResult;
  }

  Future<List<String>> parseHtmlToParts(String html) async {
    final result = await runJS("""
        var html = ${jsonEncode(html)};
        window.parseHtmlToParts(html);
    """);
    return List<String>.from(jsonDecode(result));
  }

  Future<Map<String, List<Map<String, String>>>> parseHtmlToCards(
      String html,
      Map<String, String> fieldPatterns,
      String parentDeck,
      String deckPrefix) async {
    final result = await runJS("""
        var html = ${jsonEncode(html)};
        var fieldPatterns = ${jsonEncode(fieldPatterns)};
        var parentDeck = ${jsonEncode(parentDeck)};
        var deckPrefix = ${jsonEncode(deckPrefix)};
        window.parseHtmlToCards(html, fieldPatterns, parentDeck, deckPrefix);
    """);

    final Map<String, dynamic> jsonResult = jsonDecode(result);
    final Map<String, List<Map<String, String>>> typedResult = {};

    jsonResult.forEach((deckName, cards) {
      final List<dynamic> cardsList = cards as List<dynamic>;
      typedResult[deckName] = cardsList.map((card) {
        return Map<String, String>.from(card as Map<String, dynamic>);
      }).toList();
    });

    return typedResult;
  }

  Future<String> htmlToText(String html) async {
    final result = await runJS("""
        var html = ${jsonEncode(html)};
        window.htmlToText(html);
    """);

    return result;
  }

  Future<String> jsonRepair(String content) async {
    try {
      if (jsRuntime != null) {
        // Use JS Interop with flutter_js if available
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final variableName = 'jsonContent_$timestamp';

        final jsResult = jsRuntime?.evaluate('''
          const $variableName = ${json.encode(content)};
          JSONRepair.jsonrepair($variableName);
        ''');

        if (jsResult != null) {
          return jsResult.stringResult;
        }
      }

      // Fallback to headless WebView if jsRuntime is null or evaluation failed
      final result = await runJS('''
        if (typeof JSONRepair === 'undefined') {
          // If JSONRepair is not defined, return the original content
          ${json.encode(content)};
        } else {
          JSONRepair.jsonrepair(${json.encode(content)});
        }
      ''');

      return result;
    } catch (e) {
      logger.e('Error in jsonRepair: $e');
      return content; // Return original content on error
    }
  }

  Future<String> convertCloze(String html, List<String> clozeStyles,
      List<String> textColors, List<String> highlightColors) async {
    final result = await runJS("""
        var html = ${jsonEncode(html)};
        var clozeStyles = ${jsonEncode(clozeStyles)};
        var textColors = ${jsonEncode(textColors)};
        var highlightColors = ${jsonEncode(highlightColors)};
        window.html_convert_cloze(html, clozeStyles, textColors, highlightColors);
    """);
    return result;
  }

  Future<String> convertClozeForMubu(String html, List<String> clozeStyles,
      List<String> textColors, List<String> highlightColors) async {
    final result = await runJS("""
        var html = ${jsonEncode(html)};
        var clozeStyles = ${jsonEncode(clozeStyles)};
        var textColors = ${jsonEncode(textColors)};
        var highlightColors = ${jsonEncode(highlightColors)};
        window.html_convert_cloze_for_mubu(html, clozeStyles, textColors, highlightColors);
    """);
    return result;
  }

  Future<String> convertClozeForMarkdownMindmap(
      String html,
      List<String> clozeStyles,
      List<String> textColors,
      List<String> highlightColors) async {
    final result = await runJS("""
        var html = ${jsonEncode(html)};
        var clozeStyles = ${jsonEncode(clozeStyles)};
        var textColors = ${jsonEncode(textColors)};
        var highlightColors = ${jsonEncode(highlightColors)};
        window.html_convert_cloze_for_markdown_mindmap(html, clozeStyles, textColors, highlightColors);
    """);
    return result;
  }

  Future<String> getHtmlTextContent(String html) async {
    final result = await runJS("""
        var html = ${jsonEncode(html)};
        // 创建临时DOM元素
        var tempDiv = document.createElement('div');
        // 设置HTML内容
        tempDiv.innerHTML = html;
        // 获取纯文本内容
        var textContent = tempDiv.textContent || tempDiv.innerText || '';
        // 清理文本（移除多余空格、换行等）
        textContent = textContent.replace(/\\s+/g, ' ').trim();
        return textContent;
    """);
    return result;
  }

  /// Extract media files from HTML content with detailed context
  Future<Map<String, dynamic>> extractMediaPathsFromHtmlWithContext(
    String html, {
    required List<String> mediaTypeList,
  }) async {
    try {
      final result = await runJS("""
        var html = ${jsonEncode(html)};
        var mediaTypeList = ${jsonEncode(mediaTypeList)};
        window.extractMediaPathsFromHtml(html, mediaTypeList);
      """);

      final Map<String, dynamic> extractionResult = jsonDecode(result);
      return extractionResult;
    } catch (e) {
      logger.e('Error extracting media paths from HTML: $e');
      return {'files': [], 'references': []};
    }
  }





  /// Replace media file references in HTML content with new URLs using context
  Future<String> replaceMediaReferencesInHtml(
    String htmlContent,
    Map<String, String> fileMapping, {
    List<Map<String, dynamic>>? mediaReferences,
  }) async {
    try {
      final result = await runJS("""
        var htmlContent = ${jsonEncode(htmlContent)};
        var fileMapping = ${jsonEncode(fileMapping)};
        var mediaReferences = ${jsonEncode(mediaReferences)};
        window.replaceMediaReferencesInHtml(htmlContent, fileMapping, mediaReferences);
      """);

      return result;
    } catch (e) {
      logger.e('Error replacing media references in HTML: $e');
      return htmlContent; // Return original content on error
    }
  }

  Future<String> convertImgToAbsPath(String html, String baseDir) async {
    try {
      if (jsRuntime != null) {
        // Use JS Interop with flutter_js if available
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final htmlVarName = 'html_$timestamp';
        final baseDirVarName = 'baseDir_$timestamp';

        final jsResult = jsRuntime?.evaluate('''
          const $htmlVarName = ${json.encode(html)};
          const $baseDirVarName = ${json.encode(baseDir)};
          
          function convertImgToAbsPath(htmlContent, baseDir) {
            try {
              // 确保baseDir以斜杠结尾
              const baseWithSlash = baseDir.endsWith('/') ? baseDir : baseDir + '/';
              
              // 使用正则表达式查找所有img标签
              const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
              
              // 替换所有匹配的img标签
              const result = htmlContent.replace(imgRegex, function(match, src) {
                // 检查是否是相对路径
                if (!src.match(/^(https?:\\/\\/|data:|file:\\/\\/|\\/)/i)) {
                  // 构建绝对路径
                  const absolutePath = baseWithSlash + src;
                  // 替换src属性
                  return match.replace(src, absolutePath);
                }
                return match;
              });
              
              return result;
            } catch (e) {
              console.error('转换图片路径时出错:', e);
              return htmlContent; // 出错时返回原始内容
            }
          }
          
          convertImgToAbsPath($htmlVarName, $baseDirVarName);
        ''');

        if (jsResult != null) {
          return jsResult.stringResult;
        }
      }

      // Fallback to headless WebView if jsRuntime is null or evaluation failed
      final result = await runJS('''
        function convertImgToAbsPath(htmlContent, baseDir) {
          try {
            // 确保baseDir以斜杠结尾
            const baseWithSlash = baseDir.endsWith('/') ? baseDir : baseDir + '/';
            
            // 使用正则表达式查找所有img标签
            const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
            
            // 替换所有匹配的img标签
            const result = htmlContent.replace(imgRegex, function(match, src) {
              // 检查是否是相对路径
              if (!src.match(/^(https?:\\/\\/|data:|file:\\/\\/|\\/)/i)) {
                // 构建绝对路径
                const absolutePath = baseWithSlash + src;
                // 替换src属性
                return match.replace(src, absolutePath);
              }
              return match;
            });
            
            return result;
          } catch (e) {
            console.error('转换图片路径时出错:', e);
            return htmlContent; // 出错时返回原始内容
          }
        }
        
        convertImgToAbsPath(${json.encode(html)}, ${json.encode(baseDir)});
      ''');

      return result;
    } catch (e) {
      logger.e('Error in convertImgToAbsPath: $e');
      return html; // 出错时返回原始内容
    }
  }

  Future<String> convertDocx2HTML(String path) async {
    final base64 = await fileToBase64(path);
    logger.i(base64.substring(0, 100)); // 只打印前100个字符避免日志过长

    final result = await runJS("""
        const base64Data = "$base64".split(";base64,").pop();
        const binaryString = window.atob(base64Data ?? "");
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        var options = {
          ignoreEmptyParagraphs: false,
        };
        window.mammoth.convertToHtml({ arrayBuffer: bytes }, options)
    """);
    return result;
  }

  @override
  Future onLoadStop(url) async {
    logger.i("Stopped $url");
    currentUrl.value = url?.toString() ?? "";
  }
}

class MyInAppBrowser extends InAppBrowser {
  MyInAppBrowser({super.webViewEnvironment});

  @override
  Future onBrowserCreated() async {
    logger.i("Browser Created!");
  }

  @override
  Future onLoadStart(url) async {
    logger.i("Started $url");
  }

  @override
  Future onLoadStop(url) async {
    logger.i("Stopped $url");
  }

  @override
  void onReceivedError(WebResourceRequest request, WebResourceError error) {
    logger.e("Can't load ${request.url}.. Error: ${error.description}");
  }

  @override
  void onProgressChanged(progress) {
    logger.i("Progress: $progress");
  }

  @override
  void onExit() {
    logger.i("Browser closed!");
  }
}
