<style>

.card {
    font-family: -apple-system-font, BlinkMacSystemFont, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial, sans-serif;
    font-size: 20px;
    text-align: left;
    color: var(--pico-color);
    background-color: var(--pico-background-color);
}

html,
body,
#content,
#qa,
.container {
    height: 100%;
    margin: 0;
    padding: 0;
}

article{
    color: var(--pico-color);
    background-color: var(--pico-background-color);
}
#deck_container,
#deck_container_fallback,
#tag_container,
#source_container,
#time_container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 0.8em;
    color: var(--pico-muted-color);
    margin-top: 0.5em;
    padding: 4px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
}

#tag_container span.tag {
    display: inline-block;
    /* 让 span 可以设置 padding 和 margin */
    padding: 2px 6px;
    background-color: #007bff;
    /* 主要的品牌蓝色 */
    color: white;
    border-radius: 4px;
    /* 圆角 */
    font-size: 0.8em;
    font-weight: 500;
    /* 中等粗细的字体 */
    transition: background-color 0.3s ease, transform 0.2s ease;
    /* 添加过渡效果 */
    cursor: default;
    /* 默认光标，如果你希望点击可以做些什么，可以改成 pointer */
    white-space: nowrap;
    /* 防止标签内的文本换行 */
}

/* 鼠标悬停时的样式 */
#tag_container span.tag:hover {
    background-color: #0056b3;
    /* 深一点的蓝色 */
    transform: translateY(-1px);
    /* 轻微向上移动 */
}

#time_container::before {
    content: "用时:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}

/* 第一个标签前的 "标签:" 文本样式 */
#tag_container::before {
    content: "标签:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}

#source_container::before {
    content: "出处:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}

.heading {
    text-align: left;
    padding-left: 0.5em;
    border-left: 4px solid #4891e7;
    color: var(--pico-color);
    font-weight: bold;
}


.row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* --- CLOZE (FREE GUESS) STYLES --- */
span.cloze {
    position: relative;
    display: inline-block;
    cursor: pointer;
    transition: color 0.3s ease;
    font-weight: 600;
    border-bottom: 1px solid var(--pico-primary-underline);
}

span.cloze.activated {
    color: transparent;
}

span.cloze.activated::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    height: 1em;
    opacity: 0.85;
    transition: all 0.3s ease;
}

span.cloze.activated:hover::after {
    opacity: 1;
}

/* --- HINT STYLES --- */
.hint_underline {
    display: block;
    padding: 0 5px;
    text-decoration: underline;
    text-decoration-color: var(--pico-primary-underline);
    text-underline-offset: 3px;
    cursor: pointer;
    /* font-weight: bold;  */
}

.hint-hidden-text {
    color: var(--pico-primary-underline);
}

.hint-revealed-text {
    color: var(--pico-primary-underline);
    font-weight: normal;
}


/* --- Navigation --- */
.navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: clamp(0.5rem, 2.5vw, 1rem);
  margin: 16px auto;
}

.nav-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: clamp(2.1rem, 7vw, 2.4rem);
  height: clamp(2.1rem, 7vw, 2.4rem);
  background-color: #4a86e8;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  text-decoration: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.nav-btn:hover { background-color: #3a76d8; transform: scale(1.05); }
.nav-btn:active { transform: scale(0.95); }

.nav-btn svg {
  stroke: white;
  width: 55%;
  height: 55%;
}


/* --- MISC STYLES --- */
table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
}

/* 幕布文本样式 */
.text-color-dark { color: #51565d; }
.text-color-red { color: #dc2d1e; }
.text-color-yellow { color: #ffaf38; }
.text-color-green { color: #75c940; }
.text-color-blue { color: #3da8f5; }
.text-color-purple { color: #797ec9; }
.bold { font-weight: bold; }
.underline { text-decoration: underline; }
.strikethrough { text-decoration: line-through; }
.italic { font-style: italic; }
.highlight-yellow { background-color: #f9f989; }
.highlight-red { background-color: #f5bdbc; }
.highlight-blue { background-color: #c5edfd; }
.highlight-grey { background-color: #d1d3d8; }
.highlight-olive { background-color: #e4fec1; }
.highlight-pink { background-color: #f1ccfc; }
.highlight-cyan { background-color: #cffdde; }


</style>

<script>
    // 定义配置项的默认值，方便管理
    var configDefaults = {
        "dark_mode": false,
        "show_deck": true,
        "show_tag": true,
        "show_source": true,
        "show_time": true,
    };

    // 监听器用于处理所有复选框的变更
    function handleCheckboxChange(event) {
        console.log(event);
        const checkbox = event.target;
        const configKey = checkbox.dataset.configKey;
        const isChecked = checkbox.checked;
        localStorage.setItem(configKey, isChecked.toString());

        console.log({
            configKey, isChecked
        });

        if (configKey === "dark_mode") {
            console.log("dark mode");
            let ele = document.querySelector("html");

            if (isChecked) {
                ele.setAttribute("data-theme", "dark");
            }

            else {
                ele.setAttribute("data-theme", "light");
            }
        }

        else if (configKey === "show_deck") {

            if (isChecked) {
                document.getElementById("deck_container").style.display = "flex";
                document.getElementById("deck_container_fallback").style.display = "none";
            }

            else {
                document.getElementById("deck_container").style.display = "none";
                document.getElementById("deck_container_fallback").style.display = "flex";
            }
        }

        else if (configKey === "show_tag") {
            if (isChecked) {
                document.getElementById("tag_container").style.display = "flex";
            }

            else {
                document.getElementById("tag_container").style.display = "none";
            }
        }
        else if (configKey === "show_source") {
            if (isChecked) {
                document.getElementById("source_container").style.display = "flex";
            }

            else {
                document.getElementById("source_container").style.display = "none";
            }
        }
        else if (configKey === "show_time") {
            if (isChecked) {
                document.getElementById("time_container").style.display = "flex";
            }

            else {
                document.getElementById("time_container").style.display = "none";
            }
        }
    }

    function checkAndSetTheme() {
        if (localStorage.getItem("dark_mode") === "false" || localStorage.getItem("dark_mode") === "true") {
            return;
        }

        const htmlElement = document.querySelector("html");
        const bodyElement = document.querySelector("body");

        if (!htmlElement || !bodyElement) {
            console.error("HTML or Body element not found!");
            return;
        }

        const nightModeClasses = ["night-mode", "night_mode", "nightMode"];

        // 检查 html 元素
        const isHtmlNightMode = nightModeClasses.some(className => htmlElement.classList.contains(className));

        // 检查 body 元素
        const isBodyNightMode = nightModeClasses.some(className => bodyElement.classList.contains(className));

        // 如果 html 或 body 中任何一个包含了夜间模式类，则认为是夜间模式
        const isDarkMode = isHtmlNightMode || isBodyNightMode;

        if (isDarkMode) {
            console.log("dark");
            htmlElement.setAttribute("data-theme", "dark");
            localStorage.setItem("dark_mode", true);
        }

        else {
            console.log("light");
            htmlElement.setAttribute("data-theme", "light");
            localStorage.setItem("dark_mode", false);
        }
    }

    // 初始化配置
    function initializeConfig() {
        checkAndSetTheme();
        const configCheckboxes = document.querySelectorAll('input[type="checkbox"][role="switch"][data-config-key]');

        configCheckboxes.forEach(checkbox => {
            const configKey = checkbox.dataset.configKey;
            let storedValue = localStorage.getItem(configKey);

            console.log({
                configKey
            });

            // 根据不同的配置项，解析 localStorage 中的值
            if (storedValue === null) {
                // 如果 localStorage 中没有该项，使用默认值
                checkbox.checked = configDefaults[configKey];
                localStorage.setItem(configKey, configDefaults[configKey]);
            }

            else {
                checkbox.checked = storedValue === "true";
            }

            // 添加事件监听器
            checkbox.addEventListener("change", handleCheckboxChange);

            if (configKey === "show_deck") {
                if (checkbox.checked) {
                    document.getElementById("deck_container").style.display = "flex";
                }

                else {
                    document.getElementById("deck_container").style.display = "none";
                }
            }

            else if (configKey === "show_tag") {
                if (checkbox.checked) {
                    document.getElementById("tag_container").style.display = "flex";
                }

                else {
                    document.getElementById("tag_container").style.display = "none";
                }
            }

            else if (configKey === "show_time") {
                if (checkbox.checked) {
                    let interval_id = Number.parseInt(localStorage.getItem("timer_interval") || "0");

                    if (interval_id > 0) {
                        clearInterval(interval_id);
                    }

                    document.getElementById("time_container").style.display = "flex";
                }

                else {
                    document.getElementById("time_container").style.display = "none";
                }
            }
        });

        // 绑定关闭按钮事件
        const closeBtn = document.getElementById("close-config-btn");

        if (closeBtn) {
            closeBtn.addEventListener("click", () => {
                const dialog = document.getElementById("config-dialog");

                if (dialog) {
                    dialog.close();
                }
            });
        }
    }


    function toggleConfig() {
        console.log("toggleConfig");
        const configDialog = document.getElementById('config-dialog');

        if (configDialog.open) {
            configDialog.close();
        }

        else {
            configDialog.showModal();
        }
    }

</script>
<script>
    var show_all_clips_flag = true;
    function initializeFreeGuess() {
        const targetDiv = document.querySelector("#qa");

        if (!targetDiv) {
            return;
        }

        const clozeRegex = /\[\[c(\d+)::(.*?)\]\]/gm;
        const text = targetDiv.innerHTML;
        let final_html_content = "";
        let last_pos = 0;
        let hasCloze = false;
        let matches;

        while ((matches = clozeRegex.exec(text)) !== null) {
            console.log(matches);
            hasCloze = true;
            let [fullMatch,
                index,
                content] = matches;
            let start = matches.index;

            // Append text before the cloze
            final_html_content += text.slice(last_pos, start);

            // Create and append the cloze span
            let clozeId = `c${index}`;

            final_html_content += `<span class="cloze activated" cloze_id="${clozeId}" onclick="toggleClip(this)">${content}</span>`;
            last_pos = start + fullMatch.length;
        }

        // Append remaining text after the last cloze
        final_html_content += text.slice(last_pos);

        if (hasCloze) {
            targetDiv.innerHTML = final_html_content;
            const showAllContainer = document.getElementById("show_all_container");

            if (showAllContainer) {
                showAllContainer.style.display = "block";
            }
        }
    }

    function toggleClip(element) {
        // Find all clips with the same cloze_id and toggle them
        const clozeId = element.getAttribute("cloze_id");

        if (clozeId) {
            document.querySelectorAll(`span[cloze_id="${clozeId}"]`).forEach(el => {
                el.classList.toggle("activated");
            });
        }
    }

    function toggleAllClips() {
        const allClips = document.querySelectorAll("span.cloze");

        allClips.forEach(clip => {
            if (show_all_clips_flag) {
                // If showing all, remove 'activated' class to reveal text
                clip.classList.remove("activated");
            }

            else {
                // If hiding all, add 'activated' class to hide text
                clip.classList.add("activated");
            }
        });
        // Flip the flag for the next click
        show_all_clips_flag = !show_all_clips_flag;
    }


</script>