import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:file_picker/file_picker.dart';

class PDFReplacePageController extends GetxController {
  // 表单参数
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'blank');

  Future<void> submit(BuildContext context) async {
    print(selectedFilePaths.value);
  }
}
