import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFMetaController extends GetxController {
  // 基础数据

  // 表单参数
  final title = ''.obs;
  final author = ''.obs;
  final subject = ''.obs;
  final keywords = ''.obs;
  final creator = ''.obs;
  final producer = ''.obs;
  final creationDate = ''.obs;
  final modDate = ''.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'annotate');

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      for (String filePath in selectedFilePaths) {
        progressController.updateProgress(
          status: "running",
          message: "${'toolbox.common.processing'.tr} ${PathUtils(filePath).name}",
        );
        final outputPath = await PathUtils(filePath).convertPath(
          outputMode.value,
          stem_append: "_${'toolbox.meta.title'.tr}",
          suffix: ".pdf",
          outputDir: outputDir.value,
        );
        final data = {
          'title': title.value,
          'author': author.value,
          'subject': subject.value,
          'keywords': keywords.value,
          'creator': creator.value,
          'producer': producer.value,
          'creation_date': creationDate.value,
          'mod_date': modDate.value,
          'input_path': filePath,
          'page_range': pageRange.value,
          'output_path': outputPath,
          'show_progress': true,
        };
        final resp = await messageController.request(data, 'pdf/meta');
        logger.w("resp: $resp");
        if (resp.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'toolbox.common.process.completed'.tr,
          );
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
          return;
        }
      }
      progressController.updateProgress(status: "completed", message: "");
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "${'toolbox.common.operationFailed'.tr}: $e",
      );
    }
  }
}
