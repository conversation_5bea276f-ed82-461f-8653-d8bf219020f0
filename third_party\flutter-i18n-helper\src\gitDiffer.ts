// --- START OF THE FINAL, POINTER-BASED, CORRECT IMPLEMENTATION OF gitDiffer.ts ---

import * as vscode from 'vscode';
import * as path from 'path';
import { exec } from 'child_process';
import { Logger } from './logger';

interface DiffHistory {
    fileUriString: string;
    // A complete list of commit hashes that changed the file, from newest to oldest.
    commits: string[]; 
    // An index pointing to the currently viewed commit in the `commits` array.
    currentIndex: number; 
}
let activeHistory: DiffHistory | null = null;


vscode.window.onDidChangeActiveTextEditor(editor => {
    // If the user focuses a file that is not part of the active diff session, clear the history.
    if (editor && editor.document.uri.scheme === 'file') {
        if (activeHistory && activeHistory.fileUriString !== editor.document.uri.toString()) {
            Logger.log(`Editor focus changed to a different file. Ending diff session.`);
            activeHistory = null;
        }
    }
});


function execShell(command: string, cwd: string): Promise<string> {
    Logger.log(`Executing shell command in [${cwd}]`, command);
    return new Promise((resolve, reject) => {
        exec(command, { cwd }, (error, stdout, stderr) => {
            if (error) { reject(new Error(`...`)); return; }
            resolve(stdout.trim());
        });
    });
}

function createGitUri(fileUri: vscode.Uri, ref: string): vscode.Uri {
    const params = { path: fileUri.fsPath, ref };
    return vscode.Uri.parse(`git:${fileUri.fsPath}?${JSON.stringify(params)}`);
}

async function openDiff(fileUri: vscode.Uri, commitHash: string) {
    const fileName = path.basename(fileUri.fsPath);
    const leftUri = createGitUri(fileUri, commitHash);
    const title = `${fileName} (${commitHash.substring(0, 7)}) ↔ (Current Workspace)`;
    await vscode.commands.executeCommand('vscode.diff', leftUri, fileUri, title);
}

async function initializeHistory(fileUri: vscode.Uri): Promise<boolean> {
    Logger.log('Initializing diff history for file:', fileUri.toString());
    const workspaceFolder = vscode.workspace.getWorkspaceFolder(fileUri);
    if (!workspaceFolder) return false;
    
    try {
        const repoRoot = await execShell('git rev-parse --show-toplevel', workspaceFolder.uri.fsPath);
        const relativePath = path.relative(repoRoot, fileUri.fsPath).replace(/\\/g, '/');
        const command = `git log --pretty=%H -- "${relativePath}"`;
        const logOutput = await execShell(command, repoRoot);
        
        if (!logOutput) {
            vscode.window.showInformationMessage('No commit history found for this file.');
            return false;
        }

        const commits = logOutput.split('\n');
        
        activeHistory = {
            fileUriString: fileUri.toString(),
            commits: commits, // e.g., ['XXX', 'YYY', 'ZZZ']
            currentIndex: 0,   // Start pointer at the latest commit (XXX)
        };
        Logger.log('History initialized successfully.', activeHistory);
        return true;
    } catch (error) {
        Logger.error('Failed to initialize history.', error);
        return false;
    }
}

export async function diffWithPrevious() {
    Logger.log('--- Executing diffWithPrevious ---');
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    try {
        let fileUri: vscode.Uri;

        // If command is run from a file editor, and it's a new session, initialize history.
        if (editor.document.uri.scheme === 'file' && (!activeHistory || activeHistory.fileUriString !== editor.document.uri.toString())) {
            fileUri = editor.document.uri;
            if (!(await initializeHistory(fileUri))) {
                return; // Initialization failed
            }
        } else if (activeHistory) {
            fileUri = vscode.Uri.parse(activeHistory.fileUriString);
        } else {
            vscode.window.showInformationMessage('Please start a new diff session from a file editor.');
            return;
        }

        if (!activeHistory) {
             Logger.error("CRITICAL: History is null after initialization attempt.");
             return;
        }

        // The current index points to the LATEST version. We want the one AFTER it in the array (older).
        if (activeHistory.currentIndex + 1 >= activeHistory.commits.length) {
            Logger.log('No more previous commits in the list.');
            vscode.window.showInformationMessage('Reached the earliest version in the file\'s history.');
            return;
        }

        activeHistory.currentIndex++;
        const targetCommit = activeHistory.commits[activeHistory.currentIndex];
        Logger.log('Navigating to previous. New state:', activeHistory);
        
        await openDiff(fileUri, targetCommit);

    } catch (error: any) {
        Logger.error('An error occurred in diffWithPrevious.', error);
        vscode.window.showErrorMessage(`Operation failed: ${error.message}`);
        activeHistory = null; // Reset on error
    }
}

export async function diffWithNext() {
    Logger.log('--- Executing diffWithNext ---');
    if (!activeHistory) {
        vscode.window.showInformationMessage('No active diff session. Use "Diff with Previous" first.');
        return;
    }

    try {
        if (activeHistory.currentIndex - 1 < 0) {
            Logger.log('No more next commits in the list (already at newest).');
            vscode.window.showInformationMessage('Already at the most recent version that changed this file.');
            return;
        }

        const fileUri = vscode.Uri.parse(activeHistory.fileUriString);
        activeHistory.currentIndex--;
        const targetCommit = activeHistory.commits[activeHistory.currentIndex];
        Logger.log('Navigating to next. New state:', activeHistory);
        
        await openDiff(fileUri, targetCommit);

    } catch (error: any) {
        Logger.error('An error occurred in diffWithNext.', error);
        vscode.window.showErrorMessage(`Operation failed: ${error.message}`);
        activeHistory = null; // Reset on error
    }
}
// --- END OF THE FINAL, POINTER-BASED, CORRECT IMPLEMENTATION OF gitDiffer.ts ---