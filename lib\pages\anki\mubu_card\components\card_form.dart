import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/mubu.dart';
import 'dart:io';

class CardForm extends GetView<MubuCardPageController> {
  const CardForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
              onAddNew: (newDeckName) {
                // Add to the deck list if not already present
                if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                  ankiConnectController.parentDeckList.add(newDeckName);
                }

                // Set as selected deck
                controller.parentDeck.value = newDeckName;
              },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              ShadSwitchCustom(
                key: ValueKey("subdeck-${controller.isCreateSubDeck.hashCode}"),
                label: 'anki.common.create_subdeck'.tr,
                initialValue: controller.isCreateSubDeck.value,
                onChanged: (value) {
                  controller.isCreateSubDeck.value = value;
                },
              ),
              ShadInputWithValidate(
                  key: ValueKey("cookie-${controller.cookie.value}"),
                  label: 'anki.mubu.cookie'.tr,
                  placeholder: 'anki.mubu.input_cookie'.tr,
                  initialValue: controller.cookie.value,
                  maxLines: 1,
                  onChanged: (value) {},
                  onValidate: (value) async {
                    try {
                      final result = await controller.validateCookie(value);
                      // 返回验证结果
                      return result;
                    } catch (e) {
                      logger.e("Cookie validation error: $e");
                      return "${'anki.mubu.cookie_validation_error'.tr}: $e";
                    }
                  }),
              ShadSelectWithSearch(
                key: const ValueKey("document"),
                label: 'anki.mubu.target_document'.tr,
                placeholder: 'anki.mubu.select_target_document'.tr,
                searchPlaceholder: 'anki.mubu.input_document'.tr,
                initialValue: controller.document.value.isNotEmpty
                    ? [controller.document.value]
                    : null,
                options: controller.documentList
                    .map((e) => {
                          'value': e['value'] as String,
                          'label': e['label'] as String
                        })
                    .toList(),
                onChanged: (value) {
                  controller.document.value = value.single;
                },
              ),
              ShadRadioGroupCustom(
                key: const ValueKey("mode"),
                label: 'anki.common.card_mode'.tr,
                initialValue: controller.cardMode.value,
                items: controller.cardModeList,
                onChanged: (value) {
                  controller.cardMode.value = value;
                },
              ),
              if (controller.cardMode.value != "mindmap") ...[
                if (controller.cardMode.value == "hierarchy") ...[
                  ShadSelectWithSearch(
                    key: const ValueKey("level"),
                    label: 'anki.mubu.q_node_level'.tr,
                    placeholder: 'anki.mubu.select_q_node_level'.tr,
                    searchPlaceholder: 'anki.mubu.select_q_node_level'.tr,
                    initialValue: [controller.questionNodeLevel.value],
                    options: controller.questionNodeLevelList,
                    onChanged: (value) {
                      controller.questionNodeLevel.value = value.single;
                    },
                  ),
                ],
                ShadRadioGroupCustom(
                  key: const ValueKey("card_type"),
                  label: 'anki.mubu.card_type'.tr,
                  initialValue: controller.cardType.value,
                  items: controller.cardTypeList,
                  onChanged: (value) {
                    controller.cardType.value = value;
                    logger.i(value);
                    if (value == "qa") {
                      controller.cardModel.value = "Kevin Text QA Card v2";
                      controller.updateFieldList(controller.cardModel.value);
                    } else if (value == "cloze") {
                      controller.cardModel.value = "Kevin Text Cloze v3";
                      controller.updateFieldList(controller.cardModel.value);
                    }
                  },
                ),
                if (controller.cardType.value == "qa") ...[
                  ShadSelectCustom(
                    key: ValueKey(controller.cardModel.value),
                    label: "anki.mubu.card_template".tr,
                    placeholder: "anki.mubu.select_card_template".tr,
                    initialValue: [controller.cardModel.value],
                    options: ankiConnectController.modelList
                        .map((e) => {"value": e, "label": e})
                        .toList(),
                    onChanged: (value) {
                      controller.cardModel.value = value.single;
                      controller.updateFieldList(controller.cardModel.value);
                    },
                  ),
                  ShadFieldMappingTable(
                    key: ValueKey(
                        "field_table_${controller.cardModel.value}_${ankiConnectController.fieldList.length}_qa"),
                    fieldList: ankiConnectController.fieldList,
                    optionsList: {},
                    defaultOptionsList: controller.commonRegexList,
                    cardModel: controller.cardModel.value,
                    onUpdateFieldMapping: (field, patternMatch) {
                      controller.updateFieldMapping(field, patternMatch);
                    },
                    getFieldMappingValue: (field) {
                      return controller.getFieldMappingValue(field);
                    },
                  )
                ],
                if (controller.cardType.value == "cloze") ...[
                  ShadRadioGroupCustom(
                    label: 'anki.mubu.cloze_mode'.tr,
                    initialValue: controller.clozeMode.value,
                    items: controller.clozeModeList,
                    onChanged: (value) {
                      controller.clozeMode.value = value;
                    },
                  ),
                  ShadSwitchCustom(
                    label: 'anki.mubu.one_cloze_per_card'.tr,
                    initialValue: controller.oneClozePeCard.value,
                    onChanged: (v) {
                      controller.oneClozePeCard.value = v;
                    },
                  ),
                ],
                if (controller.cardType.value == "qa") ...[
                  ShadSwitchCustom(
                    label: 'anki.mubu.answer_cloze'.tr,
                    initialValue: controller.isAnswerCloze.value,
                    onChanged: (value) {
                      controller.isAnswerCloze.value = value;
                    },
                  ),
                ],
                if (controller.cardType.value == "cloze" ||
                    (controller.cardType.value == "qa" &&
                        controller.isAnswerCloze.value)) ...[
                  ShadCheckboxGroup(
                    label: 'anki.mubu.cloze_style'.tr,
                    initialValues: controller.maskTypes.toList(),
                    items: controller.clozeStyleList,
                    onChanged: (value) {
                      logger.i(value);
                      controller.maskTypes.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return "anki.mubu.select_cloze_style".tr;
                      }
                      return "";
                    },
                    onValidateError: (error) {},
                  ),
                  if (controller.maskTypes.contains("text_color"))
                    ShadSelectCustom(
                      key: const ValueKey("text_color"),
                      label: 'anki.mubu.text_color'.tr,
                      placeholder: 'anki.mubu.select_text_color'.tr,
                      isMultiple: true,
                      initialValue: controller.textColorList.toList(),
                      options: controller.mubuColorList,
                      onChanged: (value) {
                        logger.i(value);
                        controller.textColorList.value = value;
                      },
                    ),
                  if (controller.maskTypes.contains("text_highlight"))
                    ShadSelectCustom(
                      key: const ValueKey("text_highlight"),
                      label: 'anki.mubu.text_highlight'.tr,
                      placeholder: 'anki.mubu.select_text_highlight'.tr,
                      isMultiple: true,
                      initialValue: controller.highlightColorList.toList(),
                      options: controller.mubuHighlightColorList,
                      onChanged: (value) {
                        logger.i(value);
                        controller.highlightColorList.value = value;
                      },
                    ),
                ],
                ShadSwitchCustom(
                  label: 'anki.mubu.show_source'.tr,
                  initialValue: controller.isShowSource.value,
                  onChanged: (v) {
                    controller.isShowSource.value = v;
                  },
                ),
                ShadSwitchCustom(
                  label: 'anki.mubu.part_card'.tr,
                  initialValue: controller.isPartCard.value,
                  onChanged: (value) {
                    controller.isPartCard.value = value;
                  },
                ),
              ] else ...[
                ShadRadioGroupCustom(
                  key: const ValueKey("card_type"),
                  label: 'anki.mubu.card_type'.tr,
                  initialValue: controller.cardType.value,
                  items: controller.cardTypeList,
                  onChanged: (value) {
                    controller.cardType.value = value;
                    logger.i(value);
                    if (value == "qa") {
                      controller.cardModel.value = "Kevin Text QA Card v2";
                      controller.updateFieldList(controller.cardModel.value);
                    } else if (value == "cloze") {
                      controller.cardModel.value = "Kevin Text Cloze v3";
                      controller.updateFieldList(controller.cardModel.value);
                    }
                  },
                ),
                if (controller.cardType.value == "cloze") ...[
                  ShadCheckboxGroup(
                    label: 'anki.mubu.cloze_style'.tr,
                    initialValues: controller.maskTypes.toList(),
                    items: controller.clozeStyleList,
                    onChanged: (value) {
                      logger.i(value);
                      controller.maskTypes.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return "anki.mubu.select_cloze_style".tr;
                      }
                      return "";
                    },
                    onValidateError: (error) {},
                  ),
                  if (controller.maskTypes.contains("text_color"))
                    ShadSelectCustom(
                      key: const ValueKey("text_color"),
                      label: 'anki.mubu.text_color'.tr,
                      placeholder: 'anki.mubu.select_text_color'.tr,
                      isMultiple: true,
                      initialValue: controller.textColorList.toList(),
                      options: controller.mubuColorList,
                      onChanged: (value) {
                        logger.i(value);
                        controller.textColorList.value = value;
                      },
                    ),
                  if (controller.maskTypes.contains("text_highlight"))
                    ShadSelectCustom(
                      key: const ValueKey("text_highlight"),
                      label: 'anki.mubu.text_highlight'.tr,
                      placeholder: 'anki.mubu.select_text_highlight'.tr,
                      isMultiple: true,
                      initialValue: controller.highlightColorList.toList(),
                      options: controller.mubuHighlightColorList,
                      onChanged: (value) {
                        logger.i(value);
                        controller.highlightColorList.value = value;
                      },
                    ),
                ],
                ShadSwitchCustom(
                  label: 'anki.mubu.show_source'.tr,
                  initialValue: controller.isShowSource.value,
                  onChanged: (v) {
                    controller.isShowSource.value = v;
                  },
                ),
              ],
              ShadSelectWithInput(
                key:
                    ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.mubu.select_tags'.tr,
                searchPlaceholder: 'anki.mubu.input_tags'.tr,
                isMultiple: true,
                initialValue: controller.tags,
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the global tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tags.contains(newTag)) {
                    controller.tags.add(newTag);
                  }
                },
              ),
              const SizedBox(height: 8),
            ],
          )),
    );
  }
}
