import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/background.dart';

class PDFBackgroundPage extends StatefulWidget {
  const PDFBackgroundPage({super.key});

  @override
  State<PDFBackgroundPage> createState() => _PDFBackgroundPageState();
}

class _PDFBackgroundPageState extends State<PDFBackgroundPage> {
  final controller = Get.put(PDFBackgroundPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title:
            Text('toolbox.background.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.background.description'.tr,
                  style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        const SizedBox(height: 4),
                        ShadRadioGroupCustom(
                          label: 'toolbox.background.backgroundType'.tr,
                          initialValue: controller.backgroundType.value,
                          items: controller.backgroundTypeList,
                          onChanged: (value) {
                            controller.backgroundType.value = value;
                          },
                        ),
                        if (controller.backgroundType.value == 'color') ...[
                          ShadColorPickerCustom(
                            label: 'toolbox.background.backgroundColor'.tr,
                            initialValue: controller.backgroundColor.value,
                            onChanged: (color) {
                              controller.backgroundColor.value = color;
                            },
                          ),
                          ShadInputWithValidate(
                              key: const ValueKey("opacity"),
                              label: 'toolbox.background.opacity'.tr,
                              placeholder:
                                  'toolbox.background.opacityPlaceholder'.tr,
                              initialValue: controller.opacity.value.toString(),
                              onChanged: (value) {
                                controller.opacity.value =
                                    double.tryParse(value) ??
                                        controller.opacity.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.background.opacityPlaceholder'.tr;
                                }
                                final reg = RegExp(r'^\d+(\.\d+)?$');
                                if (!reg.hasMatch(value)) {
                                  return "toolbox.validation.enterFloat".tr;
                                }
                                return "";
                              }),
                        ],
                        if (controller.backgroundType.value == 'image') ...[
                          ShadInputWithFileSelect(
                            key: const ValueKey("background-image"),
                            title: 'toolbox.background.backgroundImage'.tr,
                            placeholder:
                                Text('toolbox.common.inputFilePlaceholder'.tr),
                            allowedExtensions: const ['png', 'jpg', 'jpeg'],
                            isRequired: true,
                            allowMultiple: false,
                            initialValue: [controller.backgroundImage.value],
                            onFilesSelected: (files) {
                              controller.backgroundImage.value = files.single;
                            },
                            onValidate: (value, files) async {
                              return await validateFile(value, files);
                            },
                            onValidateError: (error) {},
                          ),
                          ShadInputWithValidate(
                              key: const ValueKey("opacity"),
                              label: 'toolbox.background.opacity'.tr,
                              placeholder:
                                  'toolbox.background.opacityPlaceholder'.tr,
                              initialValue: controller.opacity.value.toString(),
                              onChanged: (value) {
                                controller.opacity.value =
                                    double.tryParse(value) ??
                                        controller.opacity.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.background.opacityPlaceholder'.tr;
                                }
                                final reg = RegExp(r'^\d+(\.\d+)?$');
                                if (!reg.hasMatch(value)) {
                                  return "toolbox.validation.enterFloat".tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              key: const ValueKey("scale"),
                              label: 'toolbox.background.scale'.tr,
                              placeholder:
                                  'toolbox.background.scalePlaceholder'.tr,
                              initialValue: controller.scale.value.toString(),
                              onChanged: (value) {
                                controller.scale.value =
                                    double.tryParse(value) ??
                                        controller.scale.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.background.scalePlaceholder'
                                      .tr;
                                }
                                final reg = RegExp(r'^\d+(\.\d+)?$');
                                if (!reg.hasMatch(value)) {
                                  return "toolbox.validation.enterFloat".tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              key: const ValueKey("x-offset"),
                              label: 'toolbox.background.xOffset'.tr,
                              placeholder:
                                  'toolbox.background.xOffsetPlaceholder'.tr,
                              initialValue: controller.xOffset.value.toString(),
                              onChanged: (value) {
                                controller.xOffset.value =
                                    double.tryParse(value) ??
                                        controller.xOffset.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.background.xOffsetPlaceholder'.tr;
                                }
                                final reg = RegExp(r'^\d+(\.\d+)?$');
                                if (!reg.hasMatch(value)) {
                                  return "toolbox.validation.enterFloat".tr;
                                }
                                final size = double.parse(value);
                                if (size <= 0) {
                                  return "toolbox.validation.mustBeGreaterThanZero".tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              key: const ValueKey("y-offset"),
                              label: 'toolbox.background.yOffset'.tr,
                              placeholder:
                                  'toolbox.background.yOffsetPlaceholder'.tr,
                              initialValue: controller.yOffset.value.toString(),
                              onChanged: (value) {
                                controller.yOffset.value =
                                    double.tryParse(value) ??
                                        controller.yOffset.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.background.yOffsetPlaceholder'.tr;
                                }
                                final reg = RegExp(r'^\d+(\.\d+)?$');
                                if (!reg.hasMatch(value)) {
                                  return "toolbox.validation.enterFloat".tr;
                                }
                                final size = double.parse(value);
                                if (size <= 0) {
                                  return "toolbox.validation.mustBeGreaterThanZero".tr;
                                }
                                return "";
                              }),
                        ],
                        ShadInputWithValidate(
                            key: const ValueKey("split-range"),
                            label: 'toolbox.common.pageRange'.tr,
                            placeholder:
                                'toolbox.common.pageRangePlaceholder'.tr,
                            initialValue: controller.pageRange.value,
                            onChanged: (value) {
                              controller.pageRange.value = value;
                            },
                            onValidate: (value) async {
                              if (validatePageRange(value)) {
                                return "";
                              }
                              return 'toolbox.common.enterPageRange'.tr;
                            }),
                        if (PathUtils.isDesktop)
                          ShadSelectCustom(
                            key: const ValueKey("output-position"),
                            label: 'toolbox.common.outputLocation'.tr,
                            placeholder:
                                'toolbox.common.selectOutputLocation'.tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.common.outputDirectory'.tr,
                            placeholder:
                                Text('toolbox.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.common.inputFile'.tr,
                          placeholder:
                              Text('toolbox.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['pdf'],
                          isRequired: true,
                          allowMultiple: true,
                          initialValue: controller.selectedFilePaths,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
