
<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON>mplates -->

<div id='q_div'>
    {{Front}}
    {{#Hint}}
    <div>
        <div class="hint">提示: {{Hint}}</div>
    </div>
    {{/Hint}}
</div>
<hr>
{{#Back}}
<div id='a_div'>{{Back}}</div>
<div id="show_div" style="text-align: center;display: none"><a class="show_btn" onclick="go_all()">全部显示</a></div>
{{/Back}}
<script type="text/javascript">
    var cloze_regex = /\[\[c(\d+)::(.*?)\]\]/gm;
    var mask = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
    var idx = 0;
    var cloze_ids = new Array();
    var has_cloze = false;
    var show_all_flag = true;
    function free_guess_init() {
        let matches;
        let last_pos = 0;
        final_html_content = "";
        var a_div = document.getElementById("a_div");
        const text = a_div.innerHTML;
        while ((matches = cloze_regex.exec(text)) !== null) {
            has_cloze = true;
            let index = matches[1];
            let content = matches[2];
            let start = matches.index;
            let end = start + matches[0].length;
            if (start >= last_pos) {
                let pos = Math.max(0, last_pos - 1);
                final_html_content += `${text.slice(pos, start)}`
            }
            let span = document.createElement("span");
            span.className = "cloze-hide";
            span.setAttribute("cloze_id", `c${index}`);
            span.setAttribute("cloze_text", content);
            span.innerHTML = mask;
            final_html_content += span.outerHTML;
            last_pos = end + 1;
        }
        if (text.length >= last_pos) {
            let pos = Math.max(0, last_pos - 1);
            final_html_content += `${text.slice(pos, text.length)}`
        }
        a_div.innerHTML = final_html_content;
        document.querySelectorAll("span[cloze_id]").forEach(ele => {
            let cloze_id = ele.getAttribute("cloze_id");
            ele.onclick = ()=>{
                [].forEach.call(a_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                    if (ele.className === 'cloze-hide') {
                        ele.innerHTML = ele.getAttribute("cloze_text");
                        ele.className = "cloze-show";
                    } else {
                        ele.innerHTML = mask;
                        ele.className = "cloze-hide";
                    }
                })
            }
        });
        if(has_cloze){
            var show_div = document.getElementById("show_div");
            show_div.style.display = "block";
        }
    }
    
    function go_all(){
        if(show_all_flag){
            [].forEach.call(a_div.querySelectorAll(`span[cloze_id]`), (ele) => {
                ele.innerHTML = ele.getAttribute("cloze_text");
                ele.className = "cloze-show";
            })
        }else{
            [].forEach.call(a_div.querySelectorAll(`span[cloze_id]`), (ele) => {
                ele.innerHTML = mask;
                ele.className = "cloze-hide";
            })
        }
        show_all_flag = !show_all_flag;
    }

    free_guess_init();
</script>
