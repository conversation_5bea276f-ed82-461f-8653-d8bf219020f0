import 'package:flutter/material.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/hotkey.dart';
import 'dart:io';
import 'package:shadcn_ui/shadcn_ui.dart';

List<Map<String, String>> get clozeModeList => [
      {
        "label": "anki.image_card.mask_one_guess_one".tr,
        "value": "mask_one_guess_one"
      },
      {
        "label": "anki.image_card.mask_all_guess_one".tr,
        "value": "mask_all_guess_one"
      },
      {
        "label": "anki.image_card.mask_all_guess_all".tr,
        "value": "mask_all_guess_all"
      },
      {"label": "anki.image_card.free_guess".tr, "value": "free_guess"},
      {"label": "anki.image_card.scratch_guess".tr, "value": "scratch_guess"},
    ];

class ImageCardConfig extends StatefulWidget {
  const ImageCardConfig({super.key});

  @override
  State<ImageCardConfig> createState() => _ImageCardConfigState();
}

class _ImageCardConfigState extends State<ImageCardConfig> {
  final settingController = Get.find<SettingController>();
  final controller = Get.find<ImageCardController>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final ankiConnectController = Get.find<AnkiConnectController>();

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('anki.image_card.config_title'.tr),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () async {
              await controller.saveSettings();
            },
          ),
        ],
      ),
      body: Obx(() => ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              children: [
                ShadCard(
                  padding: const EdgeInsets.only(
                      left: 16, right: 16, top: 16, bottom: 16),
                  child: Column(
                    children: [
                      ShadSelectWithInput(
                        key: ValueKey(
                            "deck-${ankiConnectController.parentDeckList.length}"),
                        label: 'anki.image_card.default_deck'.tr,
                        placeholder:
                            'anki.placeholder.target_deck_search_input'.tr,
                        searchPlaceholder:
                            'anki.placeholder.target_deck_search_input'.tr,
                        isMultiple: false,
                        initialValue: [controller.parentDeck.value],
                        options: ankiConnectController.parentDeckList
                            .map((e) => {'value': e, 'label': e})
                            .toList(),
                        onChanged: (value) {
                          logger.i(value);
                          controller.parentDeck.value = value.single;
                        },
                        onAddNew: (newDeckName) {
                          // Add to the deck list if not already present
                          if (!ankiConnectController.parentDeckList
                              .contains(newDeckName)) {
                            ankiConnectController.parentDeckList
                                .add(newDeckName);
                          }

                          // Set as selected deck
                          controller.parentDeck.value = newDeckName;
                        },
                        hasSuffix: true,
                        onRefresh: () async {
                          logger.i("refresh");
                          final result = await ankiConnectController
                              .resetAnkiConnectData();
                          if (result) {
                            showToastNotification(
                                context, 'anki.common.refresh_success'.tr, "");
                          }
                        },
                      ),
                      ShadInputWithValidate(
                          label: 'anki.image_card.default_tags'.tr,
                          placeholder:
                              'anki.image_card.default_tags_placeholder'.tr,
                          onChanged: (value) {
                            controller.tags.value = value;
                          },
                          initialValue: controller.tags.value,
                          onValidate: (value) async {
                            return "";
                          }),
                      ShadSelectCustom(
                        label: 'anki.image_card.default_cloze_mode'.tr,
                        placeholder:
                            'anki.image_card.default_cloze_mode_placeholder'.tr,
                        initialValue: [controller.clozeMode.value],
                        options: clozeModeList,
                        isMultiple: false,
                        onChanged: (value) {
                          controller.clozeMode.value = value.single;
                        },
                      ),
                      ShadSwitchCustom(
                        label: 'anki.image_card.one_cloze_per_card'.tr,
                        initialValue: controller.oneClozePeCard.value,
                        onChanged: (v) {
                          controller.oneClozePeCard.value = v;
                        },
                      ),
                      ShadSwitchCustom(
                        label: 'anki.image_card.auto_ocr'.tr,
                        initialValue: controller.autoOCR.value,
                        onChanged: (v) {
                          controller.autoOCR.value = v;
                        },
                      ),
                      ShadColorPickerCustom(
                        key: ValueKey(controller.primaryColor.value.hashCode),
                        label: 'anki.image_card.primary_color'.tr,
                        initialValue: controller.primaryColor.value,
                        onChanged: (value) {
                          controller.primaryColor.value = value;
                        },
                      ),
                      ShadColorPickerCustom(
                        key: ValueKey(controller.secondaryColor.value.hashCode),
                        label: 'anki.image_card.secondary_color'.tr,
                        initialValue: controller.secondaryColor.value,
                        onChanged: (value) {
                          controller.secondaryColor.value = value;
                        },
                      ),
                      if (Platform.isMacOS ||
                          Platform.isWindows ||
                          Platform.isLinux) ...[
                        ListTile(
                          contentPadding: EdgeInsets.zero,
                          minVerticalPadding: 0,
                          title: Text('anki.image_card.snap_hotkey'.tr,
                              style: defaultTitleStyle),
                          subtitle: Obx(() => Text(
                              controller.getHotKeyDisplayText(
                                  controller.snapHotKey.value))),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ShadIconButtonCustom(
                                icon: Icons.delete,
                                size: 22,
                                onPressed: () => controller.setSnapHotKey(null),
                              ),
                              ShadIconButtonCustom(
                                icon: Icons.edit,
                                size: 22,
                                onPressed: () =>
                                    _showSnapHotKeyRecorder(context),
                              ),
                            ],
                          ),
                        ),
                        ListTile(
                          contentPadding: EdgeInsets.zero,
                          minVerticalPadding: 0,
                          title: Text('anki.image_card.paste_hotkey'.tr,
                              style: defaultTitleStyle),
                          subtitle: Obx(() => Text(
                              controller.getHotKeyDisplayText(
                                  controller.pasteHotKey.value))),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ShadIconButtonCustom(
                                icon: Icons.delete,
                                size: 22,
                                onPressed: () =>
                                    controller.setPasteHotKey(null),
                              ),
                              ShadIconButtonCustom(
                                icon: Icons.edit,
                                size: 22,
                                onPressed: () =>
                                    _showPasteHotKeyRecorder(context),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                )
              ])),
    );
  }

  void _showSnapHotKeyRecorder(BuildContext context) async {
    final hotKey = await HotKeyInputDialog.show(
      context,
      initialHotKey: controller.snapHotKey.value,
    );

    if (hotKey != null) {
      controller.setSnapHotKey(hotKey);
    }
  }

  void _showPasteHotKeyRecorder(BuildContext context) async {
    final hotKey = await HotKeyInputDialog.show(
      context,
      initialHotKey: controller.pasteHotKey.value,
    );

    if (hotKey != null) {
      controller.setPasteHotKey(hotKey);
    }
  }
}
