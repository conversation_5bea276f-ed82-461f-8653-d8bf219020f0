import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/translations/app_translations.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late final SettingController controller;
  late final LocaleController localeController;

  @override
  void initState() {
    super.initState();
    controller = Get.find<SettingController>();
    localeController = Get.find<LocaleController>();

    // 使用addPostFrameCallback确保在构建完成后加载设置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 延迟加载设置，避免在构建过程中更新状态
      controller.loadSettings();
    });
  }

  @override
  Widget build(context) {
    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 8),
      children: [
        ShadCard(
          padding: const EdgeInsets.all(0),
          child: Column(
            spacing: 4,
            children: [
              // if (Platform.isIOS) ...[
              // ListTile(
              //   leading: const Icon(LucideIcons.userRound),
              //   title: Text('settings.userCenter'.tr),
              //   trailing: const Icon(LucideIcons.chevronRight),
              //   onTap: () async {
              //     Get.toNamed('/user_center');
              //   },
              // ),
              // ],
              if (Platform.isAndroid ||
                  Platform.isWindows ||
                  Platform.isMacOS ||
                  Platform.isLinux) ...[
                ListTile(
                  leading: const Icon(LucideIcons.key),
                  title: Text('settings.activate'.tr),
                  trailing: const Icon(LucideIcons.chevronRight),
                  onTap: () => Get.toNamed('/activate'),
                ),
                ListTile(
                  leading: const Icon(LucideIcons.wrench),
                  title: Text('settings.preferences'.tr),
                  trailing: const Icon(LucideIcons.chevronRight),
                  onTap: () => Get.toNamed('/anki_config'),
                ),
              ],
              Obx(
                () => ListTile(
                  leading: const Icon(Icons.dark_mode_outlined),
                  title: Text('settings.themeSettings'.tr),
                  trailing: ShadSelect<String>(
                    anchor: const ShadAnchorAuto(),
                    initialValue: controller.theme.value,
                    placeholder: Text('settings.themeSettings'.tr),
                    options: [
                      ShadOption(
                          value: "system",
                          child: Text('settings.theme.system'.tr)),
                      ShadOption(
                          value: "light",
                          child: Text('settings.theme.light'.tr)),
                      ShadOption(
                          value: "dark", child: Text('settings.theme.dark'.tr)),
                    ],
                    selectedOptionBuilder: (context, value) {
                      if (value == "system") {
                        return Text('settings.theme.system'.tr);
                      } else if (value == "light") {
                        return Text('settings.theme.light'.tr);
                      } else if (value == "dark") {
                        return Text('settings.theme.dark'.tr);
                      }
                      return const Text('');
                    },
                    onChanged: (value) {
                      if (value != null) {
                        controller.theme.value = value;
                        controller.saveSettings(showToast: false);
                      }
                    },
                  ),
                ),
              ),
              Obx(
                () => ListTile(
                  leading: const Icon(Icons.language),
                  title: Text('settings.displayLanguage'.tr),
                  trailing: ShadSelect<String>(
                    anchor: const ShadAnchorAuto(),
                    initialValue: controller.language.value.isEmpty
                        ? localeController.currentLanguage.value
                        : controller.language.value,
                    placeholder: Text('settings.displayLanguage'.tr),
                    options: const [
                      ShadOption(value: "en", child: Text("English")),
                      ShadOption(value: "zh_CN", child: Text("简体中文")),
                      ShadOption(value: "zh_TW", child: Text("繁體中文")),
                      ShadOption(value: "ja", child: Text("日本語")),
                    ],
                    selectedOptionBuilder: (context, value) {
                      switch (value) {
                        case "en":
                          return const Text("English");
                        case "zh_CN":
                          return const Text("简体中文");
                        case "zh_TW":
                          return const Text("繁體中文");
                        case "ja":
                          return const Text("日本語");
                        default:
                          return const Text("English");
                      }
                    },
                    onChanged: (value) async {
                      if (value != null) {
                        await controller.setLanguage(value);
                        // 使用LocaleController更改语言
                        localeController.changeLocale(value);
                        controller.saveSettings(showToast: false);
                        // 不需要重启提示，语言已即时生效
                      }
                    },
                  ),
                ),
              ),
              if (Platform.isWindows ||
                  Platform.isMacOS ||
                  Platform.isLinux) ...[
                Obx(() => ListTile(
                      leading: const Icon(LucideIcons.power),
                      title: Text('settings.launchAtStartup'.tr),
                      trailing: ShadSwitch(
                        value: controller.isLaunchAtStartup.value,
                        onChanged: (value) {
                          controller.isLaunchAtStartup.value = value;
                          controller.setLaunchAtStartup(value);
                        },
                      ),
                    )),
              ],
              ListTile(
                leading: const Icon(LucideIcons.info),
                title: Text('settings.aboutAndHelp'.tr),
                trailing: const Icon(LucideIcons.chevronRight),
                onTap: () => Get.toNamed('/about'),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
