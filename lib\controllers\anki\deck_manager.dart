import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';

class DeckManagerController extends GetxController {
  // 已有数据
  final tabController = ShadTabsController(value: 'create');
  final bookList = <Map<String, dynamic>>[].obs;
  final createModeList = [
    {"label": "anki.deck_manager.create_mode_manual".tr, "value": "manual"},
    {"label": "anki.deck_manager.create_mode_file".tr, "value": "file"},
  ].obs;
  final removeModeList = [
    {"label": "anki.deck_manager.remove_mode_manual".tr, "value": "manual"},
    {"label": "anki.deck_manager.remove_mode_empty".tr, "value": "empty"},
  ].obs;
  final removeRangeList = [
    {"label": "anki.deck_manager.remove_range_all".tr, "value": "all"},
    {"label": "anki.deck_manager.remove_range_part".tr, "value": "part"},
  ].obs;
  final exportFormatList = [
    {"label": "apkg", "value": "apkg"},
    {"label": "xlsx", "value": "xlsx"},
    {"label": "csv", "value": "csv"},
    {"label": "json", "value": "json"},
    {"label": "html", "value": "html"},
  ].obs;
  final sortDirectionList = [
    {"label": "anki.deck_manager.sort_direction_asc".tr, "value": "asc"},
    {"label": "anki.deck_manager.sort_direction_desc".tr, "value": "desc"},
    {"label": "anki.deck_manager.sort_direction_random".tr, "value": "random"},
  ].obs;
  final sortMethodList = [
    {"label": "anki.deck_manager.sort_method_field".tr, "value": "field"},
    {"label": "anki.deck_manager.sort_method_add_time".tr, "value": "addTime"},
    {
      "label": "anki.deck_manager.sort_method_modify_time".tr,
      "value": "modifyTime"
    },
    {"label": "anki.deck_manager.sort_method_due_time".tr, "value": "dueTime"},
    {"label": "anki.deck_manager.sort_method_reviews".tr, "value": "reviews"},
    {"label": "anki.deck_manager.sort_method_lapses".tr, "value": "lapses"},
  ].obs;
  final alignList = [
    {"label": "anki.deck_manager.align_top".tr, "value": "top"},
    {"label": "anki.deck_manager.align_center".tr, "value": "center"},
    {"label": "anki.deck_manager.align_bottom".tr, "value": "bottom"},
  ].obs;
  final exportModeList = [
    {"label": "anki.deck_manager.export_mode_qa_merge".tr, "value": "qa_merge"},
    {
      "label": "anki.deck_manager.export_mode_qa_same_page".tr,
      "value": "qa_same_page"
    },
    {
      "label": "anki.deck_manager.export_mode_qa_different_file".tr,
      "value": "qa_different_file"
    },
  ].obs;
  // 表单参数
  final createMode = "manual".obs;
  final removeMode = "manual".obs;
  final exportFormat = "apkg".obs;
  // 创建牌组
  final deckNames = <String>[].obs;
  final deckNamesFile = ''.obs;

  // 克隆牌组
  final srcDeck = ''.obs;
  final destDeck = ''.obs;
  // 删除牌组
  final deckToRemove = <String>[].obs;
  final removeRange = "all".obs;
  final parentDeck = ''.obs;
  // 导出牌组
  final exportMode = "qa_merge".obs;
  final sortDirection = "asc".obs;
  final outputDir = ''.obs;
  final sortMethod = "field".obs;
  final align = "center".obs;
  final breakInLine = true.obs; // 允许行内跨页
  final cellMargin = "0".obs; // 单元格间距
  final cellPadding = "0".obs; // 单元格内边距
  final fontSize = 0.0.obs; // 字体大小
  final cardsPerRow = 1.obs; // 每行卡片数量
  final cardsPerFile = 100.obs; // 每文件卡片数量
  final includeSched = false.obs; // 是否包含复习时间
  // 导入牌组
  final selectedFilePaths = <String>[].obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final ankiConnectController = Get.find<AnkiConnectController>();

  @override
  void onInit() async {
    super.onInit();
    outputDir.value = await PathUtils.downloadDir;
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> submit(BuildContext context) async {
    try {
      if (tabController.selected == "create") {
        // 创建牌组
        progressController.reset(
          showOutputHint: false,
          numberButtons: 0,
        );
        progressController.showProgressDialog(context);

        try {
          if (createMode.value == "manual") {
            // 手动创建牌组
            for (final deckName in deckNames) {
              await AnkiConnectController().createDeck(deckName);
            }
            progressController.updateProgress(status: "completed", message: "anki.deck_manager.messages.create_deck_success".tr);
          } else if (createMode.value == "file") {
            // 从文件导入牌组
            if (deckNamesFile.value.isEmpty) {
              throw Exception("anki.deck_manager.errors.select_deck_file".tr);
            }
            progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.processing".tr, current: 3, total: 100);

            final file = File(deckNamesFile.value);
            final content = await file.readAsString();
            final lines = content
                .split('\n')
                .where((line) => line.trim().isNotEmpty)
                .toList();

            // 处理缩进创建牌组
            var currentPath = <String>[];

            for (final line in lines) {
              final trimmedLine = line.trim();
              if (trimmedLine.isEmpty) continue;

              // 计算缩进级别（使用制表符）
              final level = line.split('\t').length - 1; // 计算制表符数量
              logger.i("level: $level");
              // 更新当前路径
              while (currentPath.length > level) {
                currentPath.removeLast();
              }
              // 添加当前牌组到路径
              currentPath.add(trimmedLine);
              logger.i("currentPath: $currentPath");
              // 构建完整牌组路径
              final fullDeckName = currentPath.join('::');
              logger.i("fullDeckName: $fullDeckName");
              await AnkiConnectController().createDeck(fullDeckName);
            }
            progressController.updateProgress(status: "completed", message: "anki.deck_manager.messages.import_from_file_success".tr);
          }
        } catch (e) {
          logger.e("Create deck error: $e");
          progressController.updateProgress(
              status: "error", message: e.toString());
        }
      } else if (tabController.selected == "clone") {
        // 克隆牌组
        progressController.reset(
          showOutputHint: false,
          numberButtons: 0,
        );
        progressController.showProgressDialog(context);

        try {
          if (srcDeck.value.isEmpty || destDeck.value.isEmpty) {
            throw Exception(
                "anki.deck_manager.errors.select_source_dest_deck".tr);
          }
          progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.processing".tr, current: 3, total: 100);

          final result = await AnkiConnectController()
              .cloneDeck(srcDeck.value, destDeck.value);
          if (result["error"] != null) {
            throw Exception(result["error"]);
          }

          progressController.updateProgress(status: "completed", message: "anki.deck_manager.messages.clone_deck_success".tr);
        } catch (e) {
          logger.e("Clone deck error: $e");
          progressController.updateProgress(
              status: "error", message: e.toString());
        }
      } else if (tabController.selected == "remove") {
        // 删除牌组
        progressController.reset(
          showOutputHint: false,
          numberButtons: 0,
        );
        progressController.showProgressDialog(context);
        progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.processing".tr, current: 3, total: 100);
        try {
          if (removeMode.value == "manual") {
            // 手动删除牌组
            if (deckToRemove.isEmpty) {
              throw Exception(
                  "anki.deck_manager.errors.select_deck_to_remove".tr);
            }

            final result =
                await AnkiConnectController().deleteDecks(deckToRemove);
            if (result.data['error'] != null) {
              throw Exception(result.data['error']);
            }

            progressController.updateProgress(status: "completed", message: "anki.deck_manager.messages.delete_deck_success".tr);
          } else if (removeMode.value == "empty") {
            progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.processing".tr, current: 3, total: 100);
            // 删除空牌组
            final result = await AnkiConnectController().deleteEmptyDecks(
                removeRange.value,
                parentDeck:
                    removeRange.value == "part" ? parentDeck.value : null);

            if (result["error"] != null) {
              throw Exception(result["error"]);
            }

            progressController.updateProgress(status: "completed", message: result["message"] ??
                    "anki.deck_manager.messages.delete_empty_deck_success".tr);
          }
        } catch (e) {
          logger.e("Remove deck error: $e");
          progressController.updateProgress(
              status: "error", message: e.toString());
        }
      } else if (tabController.selected == "export") {
        progressController.reset(
          showOutputHint: true,
          numberButtons: 0,
        );
        progressController.showProgressDialog(context);
        progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.processing".tr, current: 3, total: 100);
        if (outputDir.value.isEmpty) {
          throw Exception("anki.deck_manager.errors.select_output_dir".tr);
        }
        if (parentDeck.value.isEmpty) {
          throw Exception("anki.deck_manager.errors.select_deck".tr);
        }
        // 导出牌组
        if (exportFormat.value == "apkg") {
          // 导出apkg
          progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.processing".tr, current: 3, total: 100);
          final outputPath = PathUtils.join([
            outputDir.value,
            "${parentDeck.value.replaceAll("::", "__")}.apkg",
          ]);

          final result = await AnkiConnectController()
              .exportApkg(parentDeck.value, outputPath, includeSched.value);
          logger.i("result: $result");
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(status: "completed", message: "anki.deck_manager.messages.export_deck_success".tr);
        } else if (exportFormat.value == "json") {
          // 导出json
          progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.processing".tr, current: 3, total: 100);
          final outputPath = PathUtils.join([
            outputDir.value,
            "${parentDeck.value.replaceAll("::", "__").replaceAll(":", "_")}.json",
          ]);

          final result = await AnkiConnectController()
              .exportJson(parentDeck.value, outputPath);
          if (result['result'] == true) {
            progressController.outputPath.value = outputPath;
            progressController.updateProgress(status: "completed", message: "anki.deck_manager.messages.export_json_success"
                    .trParams({'count': result['count'].toString()}));
          } else {
            throw Exception(result['error'] ??
                "anki.deck_manager.errors.export_json_failed".tr);
          }
        } else if (exportFormat.value == "xlsx") {
          // 导出xlsx
          progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.processing".tr, current: 3, total: 100);
          final outputPath = PathUtils.join([
            outputDir.value,
            "${parentDeck.value.replaceAll("::", "__")}.xlsx",
          ]);

          final result = await AnkiConnectController()
              .exportXlsx(parentDeck.value, outputPath);
          if (result['result'] == true) {
            progressController.outputPath.value = outputPath;
            progressController.updateProgress(status: "completed", message: "anki.deck_manager.messages.export_xlsx_success"
                    .trParams({'count': result['count'].toString()}));
          } else {
            throw Exception(result['error'] ??
                "anki.deck_manager.errors.export_xlsx_failed".tr);
          }
        } else if (exportFormat.value == "csv") {
          // 导出csv
          progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.processing".tr, current: 3, total: 100);
          final outputPath = PathUtils.join([
            outputDir.value,
            "${parentDeck.value.replaceAll("::", "__")}.csv",
          ]);

          final result = await AnkiConnectController()
              .exportCSV(parentDeck.value, outputPath);
          if (result['result'] == true) {
            progressController.outputPath.value = outputPath;
            progressController.updateProgress(status: "completed", message: "anki.deck_manager.messages.export_csv_success"
                    .trParams({'count': result['count'].toString()}));
          } else {
            throw Exception(result['error'] ??
                "anki.deck_manager.errors.export_csv_failed".tr);
          }
        } else if (exportFormat.value == "html") {
          // 导出HTML
          progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.querying_cards".tr, current: 10, total: 100);

          try {
            // 获取牌组中的所有卡片
            final cards = await AnkiConnectController()
                .findCards('deck:"${parentDeck.value}"');

            // 根据排序方法和排序方向进行排序
            progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.sorting_cards".tr, current: 20, total: 100);

            final List<dynamic> sortedCards = [...cards];
            if (sortDirection.value == "random") {
              // 随机排序
              sortedCards.shuffle();
            } else {
              final isAsc = sortDirection.value == "asc";
              // 根据排序方法选择排序属性
              if (sortMethod.value == "field") {
                // 按排序字段排序
                sortedCards.sort((a, b) {
                  final fieldOrderA = a['fieldOrder'] ?? 0;
                  final fieldOrderB = b['fieldOrder'] ?? 0;
                  final String? textA = a['fields'].values.firstWhere(
                      (field) => field['order'] == fieldOrderA,
                      orElse: () => {'value': ''})['value'];
                  final String? textB = b['fields'].values.firstWhere(
                      (field) => field['order'] == fieldOrderB,
                      orElse: () => {'value': ''})['value'];
                  return isAsc
                      ? (textA ?? '').compareTo(textB ?? '')
                      : (textB ?? '').compareTo(textA ?? '');
                });
              } else if (sortMethod.value == "addTime") {
                // 按添加时间排序
                sortedCards.sort((a, b) {
                  final numA = a['cardId'] ?? 0;
                  final numB = b['cardId'] ?? 0;
                  return isAsc ? numA.compareTo(numB) : numB.compareTo(numA);
                });
              } else if (sortMethod.value == "modifyTime") {
                // 按修改时间排序
                sortedCards.sort((a, b) {
                  final numA = a['mod'] ?? 0;
                  final numB = b['mod'] ?? 0;
                  return isAsc ? numA.compareTo(numB) : numB.compareTo(numA);
                });
              } else if (sortMethod.value == "dueTime") {
                // 按到期时间排序
                sortedCards.sort((a, b) {
                  final numA = a['due'] ?? 0;
                  final numB = b['due'] ?? 0;
                  return isAsc ? numA.compareTo(numB) : numB.compareTo(numA);
                });
              } else if (sortMethod.value == "reviews") {
                // 按复习次数排序
                sortedCards.sort((a, b) {
                  final numA = a['reps'] ?? 0;
                  final numB = b['reps'] ?? 0;
                  return isAsc ? numA.compareTo(numB) : numB.compareTo(numA);
                });
              } else if (sortMethod.value == "lapses") {
                // 按错题次数排序
                sortedCards.sort((a, b) {
                  final numA = a['lapses'] ?? 0;
                  final numB = b['lapses'] ?? 0;
                  return isAsc ? numA.compareTo(numB) : numB.compareTo(numA);
                });
              }
            }

            // 开始生成HTML文件
            progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.generating_html".tr, current: 30, total: 100);

            final sanitizedDeckName = parentDeck.value.replaceAll("::", "__");
            final outputDirPath =
                PathUtils.join([outputDir.value, sanitizedDeckName]);

            // 确保目录存在
            final outputDirectory = Directory(outputDirPath);
            if (!await outputDirectory.exists()) {
              await outputDirectory.create(recursive: true);
            }

            // 根据导出模式生成HTML
            switch (exportMode.value) {
              case "qa_merge":
                // 问题答案合并成一个文件
                await _exportHtmlMerged(sortedCards, outputDirPath);
                break;
              case "qa_same_page":
                // 问题答案在同一页，左右并列
                await _exportHtmlSamePage(sortedCards, outputDirPath);
                break;
              case "qa_different_file":
                // 问题和答案分开到不同文件
                await _exportHtmlDifferentFiles(sortedCards, outputDirPath);
                break;
              default:
                throw Exception(
                    "anki.deck_manager.errors.unsupported_export_mode"
                        .trParams({'mode': exportMode.value}));
            }

            progressController.outputPath.value = outputDirPath;
            progressController.updateProgress(status: "completed", message: "anki.deck_manager.messages.export_html_success".tr);
          } catch (e) {
            logger.e("HTML export error: $e");
            progressController.updateProgress(status: "error", message: "anki.deck_manager.errors.export_html_failed"
                    .trParams({'error': e.toString()}));
          }
        }
      } else if (tabController.selected == "import") {
        // 导入牌组
        progressController.reset(
          showOutputHint: false,
          numberButtons: 0,
        );
        progressController.showProgressDialog(context);
        progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.processing".tr, current: 3, total: 100);
        logger.i("selectedFilePaths: $selectedFilePaths");
        for (final filePath in selectedFilePaths) {
          final resp = await AnkiConnectController().importApkg(filePath);
          logger.i("resp: $resp");
        }
        progressController.updateProgress(status: "completed", message: "anki.deck_manager.messages.import_deck_success".tr);
      }
    } catch (e) {
      logger.e("Error submitting: $e");
      progressController.updateProgress(
          status: "error", message: e.toString());
    }
  }

  // HTML导出 - 问题答案合并模式
  Future<void> _exportHtmlMerged(List<dynamic> cards, String outputDir) async {
    int total = (cards.length / cardsPerFile.value.toDouble()).ceil();
    final webviewController = Get.find<WebviewController>();
    String mediaDir = "";

    try {
      // 获取Anki媒体目录
      mediaDir = await AnkiConnectController().getMediaDir();
      logger.i("mediaDir: $mediaDir");
    } catch (e) {
      logger.e("anki.deck_manager.messages.get_media_dir_failed".tr);
    }

    for (int fileIndex = 0; fileIndex < total; fileIndex++) {
      int start = fileIndex * cardsPerFile.value;
      int end = start + cardsPerFile.value;
      if (end > cards.length) end = cards.length;

      List<dynamic> pageCards = cards.sublist(start, end);
      String fileName = "cards_${fileIndex + 1}.html";
      String filePath = PathUtils.join([outputDir, fileName]);

      StringBuffer html = StringBuffer();

      // HTML头部
      html.write("""<!DOCTYPE html>
<html>
<head>
  <meta charset='UTF-8'>
  <title>${"anki.deck_manager.html.card_title".trParams({
            'fileIndex': (fileIndex + 1).toString()
          })}</title>
  <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
  <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
  <style>
    body { font-family: Arial, sans-serif; max-width: 100%; overflow-x: hidden; }
${fontSize.value > 0 ? '    body { font-size: ${fontSize.value}px; }\n' : ''}    body > table, div > table { border-collapse: collapse !important; width: 100% !important; table-layout: fixed !important; margin: ${cellMargin.value} !important; }
    body > table > tbody > tr > td, div > table > tbody > tr > td { vertical-align: ${align.value} !important; padding: ${cellPadding.value} !important; box-sizing: border-box !important; border: 1px solid #aaa !important; box-shadow: 0 1px 2px rgba(0,0,0,0.05) !important; }
    img { max-width: 100%; height: auto; object-fit: contain; display: block; }
""");

      // 为每个卡片添加独立的样式隔离
      for (var card in pageCards) {
        if (card['css'] != null && card['css'].toString().isNotEmpty) {
          html.write(
              """    /* ${"anki.deck_manager.html.card_style_comment".trParams({
                'cardId': card['cardId'].toString()
              })} */
    .card-${card['cardId']} ${card['css']}
    .card-${card['cardId']} img { max-width: 100%; height: auto; }
""");
        }
      }

      html.write("  </style>\n</head>\n<body>\n<table>\n");

      // 生成行和单元格
      for (int i = 0; i < pageCards.length; i += cardsPerRow.value) {
        html.write("  <tr>\n");

        for (int j = 0; j < cardsPerRow.value; j++) {
          if (i + j >= pageCards.length) break;

          var card = pageCards[i + j];
          String cardId = "card-${card['cardId']}";

          html.write(
              """    <td style='width: ${100.0 / cardsPerRow.value}%; page-break-inside: ${breakInLine.value ? 'auto' : 'avoid'};'>
      <div class='$cardId'>
        ${card['answer'] ?? ''}
      </div>
    </td>
""");
        }

        html.write("  </tr>\n");
      }

      html.write("</table>\n</body>\n</html>");

      // 转换图片路径为绝对路径
      String htmlContent = html.toString();
      // logger.i("htmlContent: $htmlContent");
      // logger.i("mediaDir: $mediaDir");
      if (mediaDir.isNotEmpty) {
        htmlContent =
            await webviewController.convertImgToAbsPath(htmlContent, mediaDir);
      }
      // logger.i("htmlContent: $htmlContent");
      // 写入文件
      await File(filePath).writeAsString(htmlContent);

      // 更新进度
      progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.generating_html_progress"
              .trParams({
            'current': (fileIndex + 1).toString(),
            'total': total.toString()
          }),
          current: 30.0 + (40.0 * (fileIndex + 1) / total).floor(),
          total: 100.0);
    }
  }

  // HTML导出 - 问题答案同页面并列模式
  Future<void> _exportHtmlSamePage(
      List<dynamic> cards, String outputDir) async {
    int total = (cards.length / cardsPerFile.value.toDouble()).ceil();
    final webviewController = Get.find<WebviewController>();
    String mediaDir = "";

    try {
      // 获取Anki媒体目录
      mediaDir = await AnkiConnectController().getMediaDir();
      logger.i("mediaDir: $mediaDir");
    } catch (e) {
      logger.e("获取媒体目录失败: $e");
    }

    for (int fileIndex = 0; fileIndex < total; fileIndex++) {
      int start = fileIndex * cardsPerFile.value;
      int end = start + cardsPerFile.value;
      if (end > cards.length) end = cards.length;

      List<dynamic> pageCards = cards.sublist(start, end);
      String fileName = "cards_${fileIndex + 1}.html";
      String filePath = PathUtils.join([outputDir, fileName]);

      StringBuffer html = StringBuffer();

      // HTML头部
      html.write("""<!DOCTYPE html>
<html>
<head>
  <meta charset='UTF-8'>
  <title>${"anki.deck_manager.html.card_title".trParams({
            'fileIndex': (fileIndex + 1).toString()
          })}</title>
  <style>
    body { font-family: Arial, sans-serif; max-width: 100%; overflow-x: hidden; }
${fontSize.value > 0 ? '    body { font-size: ${fontSize.value}px; }\n' : ''}    body > table, div > table { border-collapse: collapse !important; width: 100% !important; table-layout: fixed !important; margin: ${cellMargin.value} !important; }
    body > table > tbody > tr > td, div > table > tbody > tr > td { vertical-align: ${align.value} !important; padding: ${cellPadding.value} !important; box-sizing: border-box !important; border: 1px solid #aaa !important; box-shadow: 0 1px 2px rgba(0,0,0,0.05) !important; }
    body > table > tbody > tr > th, div > table > tbody > tr > th { text-align: left !important; padding: 12px !important; font-weight: bold !important; border: 1px solid #999 !important; background-color: #f5f5f5 !important; }
    img { max-width: 100%; height: auto; object-fit: contain; display: block; }
""");

      // 为每个卡片添加独立的样式隔离
      for (var card in pageCards) {
        if (card['css'] != null && card['css'].toString().isNotEmpty) {
          html.write(
              """    /* ${"anki.deck_manager.html.question_style_comment".trParams({
                'cardId': card['cardId'].toString()
              })} */
    .q-${card['cardId']} ${card['css']}
    .q-${card['cardId']} img { max-width: 100%; height: auto; }
    /* ${"anki.deck_manager.html.answer_style_comment".trParams({
                'cardId': card['cardId'].toString()
              })} */
    .a-${card['cardId']} ${card['css']}
    .a-${card['cardId']} img { max-width: 100%; height: auto; }
""");
        }
      }

      html.write("""  </style>
</head>
<body>
<table>
  <tr>
    <th style='width: 50%;'>${"anki.deck_manager.html.question_header".tr}</th>
    <th style='width: 50%;'>${"anki.deck_manager.html.answer_header".tr}</th>
  </tr>
""");

      // 生成每一行卡片
      for (int i = 0; i < pageCards.length; i++) {
        var card = pageCards[i];
        String questionClass = "q-${card['cardId']}";
        String answerClass = "a-${card['cardId']}";

        html.write("""  <tr>
    <td style='page-break-inside: ${breakInLine.value ? 'auto' : 'avoid'};'>
      <div class='$questionClass'>
        ${card['question'] ?? ''}
      </div>
    </td>
    <td style='page-break-inside: ${breakInLine.value ? 'auto' : 'avoid'};'>
      <div class='$answerClass'>
        ${card['answer'] ?? ''}
      </div>
    </td>
  </tr>
""");
      }

      html.write("</table>\n</body>\n</html>");

      // 转换图片路径为绝对路径
      String htmlContent = html.toString();
      if (mediaDir.isNotEmpty) {
        htmlContent =
            await webviewController.convertImgToAbsPath(htmlContent, mediaDir);
      }

      // 写入文件
      await File(filePath).writeAsString(htmlContent);

      // 更新进度
      progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.generating_html_progress"
              .trParams({
            'current': (fileIndex + 1).toString(),
            'total': total.toString()
          }),
          current: 30.0 + (40.0 * (fileIndex + 1) / total).floor(),
          total: 100.0);
    }
  }

  // HTML导出 - 问题答案分离到不同文件模式
  Future<void> _exportHtmlDifferentFiles(
      List<dynamic> cards, String outputDir) async {
    // 创建问题和答案子目录
    final questionsDir = PathUtils.join([outputDir, "questions"]);
    final answersDir = PathUtils.join([outputDir, "answers"]);
    final webviewController = Get.find<WebviewController>();
    String mediaDir = "";

    try {
      // 获取Anki媒体目录
      mediaDir = await AnkiConnectController().getMediaDir();
      logger.i("mediaDir: $mediaDir");
    } catch (e) {
      logger.e("获取媒体目录失败: $e");
    }

    await Directory(questionsDir).create(recursive: true);
    await Directory(answersDir).create(recursive: true);

    int total = (cards.length / cardsPerFile.value.toDouble()).ceil();

    for (int fileIndex = 0; fileIndex < total; fileIndex++) {
      int start = fileIndex * cardsPerFile.value;
      int end = start + cardsPerFile.value;
      if (end > cards.length) end = cards.length;

      List<dynamic> pageCards = cards.sublist(start, end);
      String questionFileName = "questions_${fileIndex + 1}.html";
      String answerFileName = "answers_${fileIndex + 1}.html";

      String questionFilePath =
          PathUtils.join([questionsDir, questionFileName]);
      String answerFilePath = PathUtils.join([answersDir, answerFileName]);

      // 生成问题HTML
      StringBuffer questionsHtml = StringBuffer();
      questionsHtml.write("""<!DOCTYPE html>
<html>
<head>
  <meta charset='UTF-8'>
  <title>${"anki.deck_manager.html.question_title".trParams({
            'fileIndex': (fileIndex + 1).toString()
          })}</title>
  <style>
    body { font-family: Arial, sans-serif; max-width: 100%; overflow-x: hidden; }
${fontSize.value > 0 ? '    body { font-size: ${fontSize.value}px; }\n' : ''}    body > table, div > table { border-collapse: collapse !important; width: 100% !important; table-layout: fixed !important; margin: ${cellMargin.value} !important; }
    body > table > tbody > tr > td, div > table > tbody > tr > td { vertical-align: ${align.value} !important; padding: ${cellPadding.value} !important; box-sizing: border-box !important; border: 1px solid #aaa !important; box-shadow: 0 1px 2px rgba(0,0,0,0.05) !important; }
    img { max-width: 100%; height: auto; object-fit: contain; display: block; }
""");

      // 为每个卡片添加独立的样式隔离
      for (var card in pageCards) {
        if (card['css'] != null && card['css'].toString().isNotEmpty) {
          questionsHtml.write(
              """    /* ${"anki.deck_manager.html.question_style_comment".trParams({
                'cardId': card['cardId'].toString()
              })} */
    .q-${card['cardId']} ${card['css']}
    .q-${card['cardId']} img { max-width: 100%; height: auto; }
""");
        }
      }

      questionsHtml.write("""  </style>
</head>
<body>
<table>
""");

      // 生成答案HTML
      StringBuffer answersHtml = StringBuffer();
      answersHtml.write("""<!DOCTYPE html>
<html>
<head>
  <meta charset='UTF-8'>
  <title>${"anki.deck_manager.html.answer_title".trParams({
            'fileIndex': (fileIndex + 1).toString()
          })}</title>
  <style>
    body { font-family: Arial, sans-serif; max-width: 100%; overflow-x: hidden; }
${fontSize.value > 0 ? '    body { font-size: ${fontSize.value}px; }\n' : ''}    body > table, div > table { border-collapse: collapse !important; width: 100% !important; table-layout: fixed !important; margin: ${cellMargin.value} !important; }
    body > table > tbody > tr > td, div > table > tbody > tr > td { vertical-align: ${align.value} !important; padding: ${cellPadding.value} !important; box-sizing: border-box !important; border: 1px solid #aaa !important; box-shadow: 0 1px 2px rgba(0,0,0,0.05) !important; }
    img { max-width: 100%; height: auto; object-fit: contain; display: block; }
""");

      // 为每个卡片添加独立的样式隔离
      for (var card in pageCards) {
        if (card['css'] != null && card['css'].toString().isNotEmpty) {
          answersHtml.write(
              """    /* ${"anki.deck_manager.html.answer_style_comment".trParams({
                'cardId': card['cardId'].toString()
              })} */
    .a-${card['cardId']} ${card['css']}
    .a-${card['cardId']} img { max-width: 100%; height: auto; }
""");
        }
      }

      answersHtml.write("""  </style>
</head>
<body>
<table>
""");

      // 生成问题和答案表格
      for (int i = 0; i < pageCards.length; i += cardsPerRow.value) {
        // 添加行
        questionsHtml.write("  <tr>\n");
        answersHtml.write("  <tr>\n");

        // 添加单元格
        for (int j = 0; j < cardsPerRow.value; j++) {
          if (i + j >= pageCards.length) break;

          var card = pageCards[i + j];
          String questionClass = "q-${card['cardId']}";
          String answerClass = "a-${card['cardId']}";

          // 问题单元格
          questionsHtml.write(
              """    <td style='width: ${100.0 / cardsPerRow.value}%; page-break-inside: ${breakInLine.value ? 'auto' : 'avoid'};'>
      <div class='$questionClass'>
        ${card['question'] ?? ''}
      </div>
    </td>
""");

          // 答案单元格
          answersHtml.write(
              """    <td style='width: ${100.0 / cardsPerRow.value}%; page-break-inside: ${breakInLine.value ? 'auto' : 'avoid'};'>
      <div class='$answerClass'>
        ${card['answer'] ?? ''}
      </div>
    </td>
""");
        }

        questionsHtml.write("  </tr>\n");
        answersHtml.write("  </tr>\n");
      }

      questionsHtml.write("</table>\n</body>\n</html>");
      answersHtml.write("</table>\n</body>\n</html>");

      // 转换图片路径为绝对路径
      String questionsHtmlContent = questionsHtml.toString();
      String answersHtmlContent = answersHtml.toString();

      if (mediaDir.isNotEmpty) {
        questionsHtmlContent = await webviewController.convertImgToAbsPath(
            questionsHtmlContent, mediaDir);
        answersHtmlContent = await webviewController.convertImgToAbsPath(
            answersHtmlContent, mediaDir);
      }

      // 写入文件
      await File(questionFilePath).writeAsString(questionsHtmlContent);
      await File(answerFilePath).writeAsString(answersHtmlContent);

      // 更新进度
      progressController.updateProgress(status: "running", message: "anki.deck_manager.messages.generating_html_progress"
              .trParams({
            'current': (fileIndex + 1).toString(),
            'total': total.toString()
          }),
          current: 30.0 + (40.0 * (fileIndex + 1) / total).floor(),
          total: 100.0);
    }
  }

  // 获取导出模式的中文名称
  String _getExportModeLabel(String mode) {
    switch (mode) {
      case "qa_merge":
        return "anki.deck_manager.export_modes.qa_merge".tr;
      case "qa_same_page":
        return "anki.deck_manager.export_modes.qa_same_page".tr;
      case "qa_different_file":
        return "anki.deck_manager.export_modes.qa_different_file".tr;
      default:
        return mode;
    }
  }
}
