from pathlib import Path
import fitz
from .utils import parse_range, progress_reporter
from loguru import logger
import traceback
from pdf2docx import Converter as pdf2docx_converter
import json

def convert_pdf2img(
    *,
    doc_path: str = "",
    output_dir: str = "",
    dpi: int = 300,
    is_gray: bool = False,
    format: str = "png",
    page_range: str = "all",
    max_width_pixels: int = 3000,
    max_height_pixels: int = 4000,
    auto_scale_to_a4: bool = True,
):
    """
    Convert PDF pages to images with automatic scaling for oversized pages.

    Args:
        doc_path: Path to the PDF document
        output_dir: Directory to save output images
        dpi: DPI for image conversion
        is_gray: Whether to convert to grayscale
        format: Output image format (png/jpg)
        page_range: Page range to convert (e.g., "1-3,5,7-9" or "all")
        max_width_pixels: Maximum width in pixels before scaling is applied
        max_height_pixels: Maximum height in pixels before scaling is applied
        auto_scale_to_a4: Whether to scale oversized pages to A4 dimensions

    Returns:
        Dictionary mapping page numbers to output file paths
    """
    doc: fitz.Document = fitz.open(doc_path)
    logger.info(f"doc_path: {doc_path}, output_dir: {output_dir}, dpi: {dpi}, is_gray: {is_gray}, page_range: {page_range}")
    logger.info(f"Scaling thresholds: max_width={max_width_pixels}px, max_height={max_height_pixels}px, auto_scale_to_a4={auto_scale_to_a4}")

    roi_indices = parse_range(page_range, doc.page_count)
    p = Path(doc_path)
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    result = {}

    # A4 dimensions in points (1 point = 1/72 inch)
    A4_WIDTH_PT = 595.276  # 8.27 inches * 72 points/inch
    A4_HEIGHT_PT = 841.89  # 11.69 inches * 72 points/inch

    for idx, i in enumerate(roi_indices):
        try:
            progress_reporter("running", f"正在处理第{i+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
            page = doc[i]

            # Get original page dimensions in points
            page_rect = page.rect
            original_width_pt = page_rect.width
            original_height_pt = page_rect.height

            # Calculate what the image dimensions would be at the requested DPI
            potential_width_px = int(original_width_pt * dpi / 72)
            potential_height_px = int(original_height_pt * dpi / 72)

            # Determine if scaling is needed
            needs_scaling = (potential_width_px > max_width_pixels or
                           potential_height_px > max_height_pixels)

            if needs_scaling and auto_scale_to_a4:
                # Calculate scaling factor to fit within A4 while maintaining aspect ratio
                original_aspect_ratio = original_width_pt / original_height_pt
                a4_aspect_ratio = A4_WIDTH_PT / A4_HEIGHT_PT

                if original_aspect_ratio > a4_aspect_ratio:
                    # Page is wider than A4 ratio, scale based on width
                    scale_factor = A4_WIDTH_PT / original_width_pt
                    scaled_width_pt = A4_WIDTH_PT
                    scaled_height_pt = original_height_pt * scale_factor
                else:
                    # Page is taller than A4 ratio, scale based on height
                    scale_factor = A4_HEIGHT_PT / original_height_pt
                    scaled_width_pt = original_width_pt * scale_factor
                    scaled_height_pt = A4_HEIGHT_PT

                # Create scaling matrix that combines DPI conversion and size scaling
                final_scale_factor = scale_factor * dpi / 72
                mat = fitz.Matrix(final_scale_factor, final_scale_factor)

                logger.info(f"Page {i+1}: Scaling from {original_width_pt:.1f}x{original_height_pt:.1f}pt "
                          f"to {scaled_width_pt:.1f}x{scaled_height_pt:.1f}pt "
                          f"(would be {potential_width_px}x{potential_height_px}px, "
                          f"scaled to {int(scaled_width_pt * dpi / 72)}x{int(scaled_height_pt * dpi / 72)}px)")

            elif needs_scaling:
                # Scale down to fit within pixel limits while maintaining aspect ratio
                width_scale = max_width_pixels / potential_width_px
                height_scale = max_height_pixels / potential_height_px
                pixel_scale_factor = min(width_scale, height_scale)

                # Combine pixel scaling with DPI conversion
                final_scale_factor = pixel_scale_factor * dpi / 72
                mat = fitz.Matrix(final_scale_factor, final_scale_factor)

                final_width_px = int(potential_width_px * pixel_scale_factor)
                final_height_px = int(potential_height_px * pixel_scale_factor)

                logger.info(f"Page {i+1}: Scaling from {potential_width_px}x{potential_height_px}px "
                          f"to {final_width_px}x{final_height_px}px to fit within limits")

            else:
                # No scaling needed, use standard DPI conversion
                mat = fitz.Matrix(dpi / 72, dpi / 72)
                logger.debug(f"Page {i+1}: No scaling needed ({potential_width_px}x{potential_height_px}px)")

            colorspace = fitz.csRGB if not is_gray else fitz.csGRAY
            pix = page.get_pixmap(matrix=mat, colorspace=colorspace)
            savepath = str(output_dir / f"{p.stem}-page-{i+1}.png")
            pix.save(savepath)
            if format == "png":
                result[i+1] = savepath
            # elif format == "jpg":
                # img = Image.open(savepath)
                # savepath_jpg = savepath.replace(".png", ".jpg")
                # img.save(savepath_jpg, "JPEG", quality=100)
                # result[i] = savepath_jpg
                # img.close()
                # os.remove(savepath)
        except Exception as e:
            logger.error(f"Error processing page {i+1}: {e}")
            logger.error(traceback.format_exc())
    doc.close()
    logger.info(f"result: {result}")
    progress_reporter("completed", "已完成", result)
    return result


def convert_anydoc2pdf(
    *, 
    doc_path: str = "", 
    output_path: str = "",
):
    """
    supported document types: PDF, XPS, EPUB, MOBI, FB2, CBZ, SVG
    """
    progress_reporter("processing", f"正在处理...", data={"current": 5, "total": 100})
    doc = fitz.open(doc_path)
    b = doc.convert_to_pdf()  # convert to pdf
    pdf: fitz.Document = fitz.open("pdf", b)  # open as pdf

    toc= doc.get_toc()  # table of contents of input
    try:
        pdf.set_toc(toc)  # simply set it for output
    except:
        logger.error(traceback.format_exc())
    meta = doc.metadata  # read and set metadata
    if not meta["producer"]:
        meta["producer"] = "PyMuPDF" + fitz.VersionBind

    if not meta["creator"]:
        meta["creator"] = "PyMuPDF PDF converter"
    meta["modDate"] = fitz.get_pdf_now()
    meta["creationDate"] = meta["modDate"]
    pdf.set_metadata(meta)

    # now process the links
    link_cnti = 0
    link_skip = 0
    for pinput in doc:  # iterate through input pages
        links = pinput.get_links()  # get list of links
        link_cnti += len(links)  # count how many
        pout = pdf[pinput.number]  # read corresp. output page
        for l in links:  # iterate though the links
            if l["kind"] == fitz.LINK_NAMED:  # we do not handle named links
                logger.info("named link page", pinput.number, l)
                link_skip += 1  # count them
                continue
            pout.insert_link(l)  # simply output the others
    pdf.save(output_path, garbage=4, deflate=True)
    progress_reporter("completed", "已完成", output_path)
    return output_path

def convert_pdf2docx(
    *, 
    doc_path: str = "", 
    output_path: str = "",
):
    progress_reporter("processing", f"正在处理...", data={"current": 5, "total": 100})
    
    converter = pdf2docx_converter(doc_path)
    converter.convert(output_path)
    converter.close()
    progress_reporter("completed", "已完成", output_path)
    return output_path

# def convert_ofd2pdf(
#     *, 
#     input_path: str = "", 
#     output_path: str = "",
# ):
#     """效果不好，暂时弃用
#     """
#     progress_reporter("processing", f"正在处理...", data={"current": 5, "total": 100})
#     with open(input_path, "rb") as f:
#         ofdb64 = str(base64.b64encode(f.read()), "utf-8")
#     p = Path(input_path)
#     ofd = OFD()  # 初始化OFD 工具类
#     ofd.read(ofdb64, save_xml=True, xml_name=f"{p.stem}_xml")  # 读取ofdb64
#     # print("ofd.data", ofd.data) # ofd.data 为程序解析结果
#     pdf_bytes = ofd.to_pdf()  # 转pdf
#     ofd.del_data()

#     with open(output_path, "wb") as f:
#         f.write(pdf_bytes)
#     progress_reporter("completed", "已完成", output_path)
#     return output_path
