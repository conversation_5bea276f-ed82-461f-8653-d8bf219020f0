import re
import json
import sys
import colorsys
from typing import Dict, Any, Optional, Callable

# 进度报告器单例类
class ProgressReporter:
    """
    进度报告器单例类，用于处理进度报告和状态更新
    """
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """初始化进度报告器"""
        # 仅在第一次创建实例时执行
        if ProgressReporter._instance is not None:
            return
            
        self._current_task_id = None
        self._task_update_callback = None
    
    def set_current_task_id(self, task_id: str):
        """设置当前正在处理的任务ID"""
        self._current_task_id = task_id
    
    def get_current_task_id(self) -> Optional[str]:
        """获取当前正在处理的任务ID"""
        return self._current_task_id
    
    def set_update_callback(self, callback: Callable[[str, float, float, str, str], None]):
        """
        设置状态更新回调函数
        
        参数:
        - callback: 回调函数，签名为 (task_id, current, total, message, status) -> None
        """
        self._task_update_callback = callback
    
    def report(self, status: str, message: str, data: Any = ""):
        """
        报告进度
        
        参数:
        - status: 状态，可以是 "processing", "completed", "error"
        - message: 消息内容
        - data: 可以是字典 {"current": x, "total": y} 或其他结果数据
        """
        task_id = self.get_current_task_id()
        
        # 如果未设置任务ID或回调函数，则使用默认行为（打印到控制台）
        if task_id is None or self._task_update_callback is None:
            result = json.dumps({"status": status, "message": message, "data": data}, ensure_ascii=False)
            print(result, flush=True, file=sys.stdout)
            return result
            
        # 使用回调函数更新任务状态
        if status == "processing":
            current = data.get("current", 0) if isinstance(data, dict) else 0
            total = data.get("total", 100) if isinstance(data, dict) else 100
            self._task_update_callback(task_id, current, total, message, status)
        elif status == "completed":
            # 任务完成
            self._task_update_callback(task_id, 100, 100, message, status)
        elif status == "error":
            # 错误状态
            error_msg = str(data) if data else message
            self._task_update_callback(task_id, 0, 100, f"错误: {error_msg}", status)

# 创建单例实例
progress_reporter_instance = ProgressReporter.get_instance()

def progress_reporter(status: str, message: str, data=""):
    """
    进度报告函数，通过单例模式实现
    
    参数:
    - status: 状态，可以是 "processing", "completed", "error"
    - message: 消息内容
    - data: 可以是字典 {"current": x, "total": y} 或其他结果数据
    """
    return progress_reporter_instance.report(status, message, data)

def parse_range(page_range: str, page_count: int, is_multi_range: bool = False, is_reverse: bool = False, is_unique: bool = True):
    # e.g.: "1-3,5-6,7-10", "1,4-5", "3-N", "even", "odd"
    page_range = page_range.strip()
    if page_range in ["all", ""]:
        roi_indices = list(range(page_count))
        return roi_indices
    if page_range == "even":
        roi_indices = list(range(0, page_count, 2))
        return roi_indices
    if page_range == "odd":
        roi_indices = list(range(1, page_count, 2))
        return roi_indices
    
    roi_indices = []
    parts = page_range.split(",")
    neg_count = sum([p.startswith("!") for p in parts])
    pos_count = len(parts) - neg_count
    if neg_count > 0 and pos_count > 0:
        raise ValueError("页码格式错误：不能同时使用正向选择和反向选择语法")
    if pos_count > 0:
        for part in parts:
            part = part.strip()
            if re.match("^!?(\d+|N)(\-(\d+|N))?$", part) is None:
                raise ValueError("页码格式错误!")
            out = part.split("-")
            if len(out) == 1:
                if out[0] == "N":
                    roi_indices.append([page_count-1])
                else:
                    roi_indices.append([int(out[0])-1])
            elif len(out) == 2:
                if out[1] == "N":
                    roi_indices.append(list(range(int(out[0])-1, page_count)))
                else:
                    a, b = int(out[0])-1, int(out[1])
                    if a <= b:
                        roi_indices.append(list(range(a, b)))
                    else:
                        roi_indices.append(list(range(a, b-2, -1)))
        if is_multi_range:
            return roi_indices
        roi_indices = [i for v in roi_indices for i in v]
        if is_unique:
            roi_indices = list(set(roi_indices))
            roi_indices.sort()
    if neg_count > 0:
        for part in parts:
            part = part.strip()
            if re.match("^!?(\d+|N)(\-(\d+|N))?$", part) is None:
                raise ValueError("页码格式错误!")
            out = part[1:].split("-")
            if len(out) == 1:
                roi_indices.append([int(out[0])-1])
            elif len(out) == 2:
                if out[1] == "N":
                    roi_indices.append(list(range(int(out[0])-1, page_count)))
                else:
                    roi_indices.append(list(range(int(out[0])-1, int(out[1]))))
        if is_multi_range:
            return roi_indices
        roi_indices = [i for v in roi_indices for i in v]
        if is_unique:
            roi_indices = list(set(range(page_count)) - set(roi_indices))
            roi_indices.sort()
    if is_reverse:
        roi_indices = list(set(range(page_count)) - set(roi_indices))
        roi_indices.sort()
    return roi_indices

def range_compress(arr):
    if not arr:
        return []
    
    result = []
    start = end = arr[0]
    for i in range(1, len(arr)):
        if arr[i] == end + 1:
            end = arr[i]
        else:
            result.append([start, end])
            start = end = arr[i]
    result.append([start, end])
    return result

def convert_length(length, from_unit, to_unit):
    """
    将长度从一个单位转换为另一个单位
    :param length: 长度值
    :param from_unit: 原单位，可选值："pt"、"cm"、"mm"、"in"
    :param to_unit: 目标单位，可选值："pt"、"cm"、"mm"、"in"
    :param dpi: 屏幕或打印机的分辨率，默认为每英寸72个点（即标准屏幕分辨率）
    :return: 转换后的长度值
    """

    units = {"pt": 1, "cm": 2.54/72, "mm": 25.4/72, "in": 1/72}
    if from_unit not in units or to_unit not in units:
        raise ValueError("单位错误")

    pt_length = length / units[from_unit]
    return pt_length * units[to_unit]

def hex_to_rgb(hex_color):
    # 去掉 # 号并解析为十六进制数值
    hex_value = hex_color.lstrip("#")
    # 解析 R、G、B 三个十六进制数值
    r, g, b = tuple(int(hex_value[i:i+2], 16) for i in (0, 2, 4))
    # 将 R、G、B 转换为 RGB 颜色值
    rgb_color = colorsys.rgb_to_hsv(r/255, g/255, b/255)
    return tuple(round(c * 255) for c in colorsys.hsv_to_rgb(*rgb_color))
