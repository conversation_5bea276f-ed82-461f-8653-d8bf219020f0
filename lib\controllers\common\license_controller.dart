import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:anki_guru/controllers/common.dart';
import 'package:flutter_udid/flutter_udid.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart';
import 'package:path/path.dart' as p;
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_machineid/flutter_machineid.dart';

class Resp {
  final String status;
  final String message;
  final String? data;
  Resp({required this.status, required this.message, this.data});
}

class LicenseInfo {
  final String? code;
  final String? productid;
  final String? product_version;
  final bool? can_upgrade;
  final bool? can_unregister;
  final String? machineid;
  final String? issue_time;
  final int? device_count;
  final num? duration;
  final int? function_mode;
  final String? note;
  final String? platform;
  final String? invitation_code;
  final String? sign;

  LicenseInfo({
    this.code,
    this.productid,
    this.product_version,
    this.can_upgrade,
    this.can_unregister,
    this.machineid,
    this.issue_time,
    this.device_count,
    this.duration,
    this.function_mode,
    this.note,
    this.platform,
    this.invitation_code,
    this.sign,
  });

  factory LicenseInfo.fromJson(Map<String, dynamic> json) {
    return LicenseInfo(
      code: json['code'] as String,
      productid: json['productid'] as String,
      product_version: json['product_version'] as String,
      can_upgrade: json['can_upgrade'] as bool,
      can_unregister: json['can_unregister'] as bool,
      machineid: json['machineid'] as String,
      issue_time: json['issue_time'] as String,
      device_count: json['device_count'] as int,
      duration: json['duration'] as num,
      function_mode: json['function_mode'] as int,
      note: json['note'] as String,
      platform: json['platform'] as String,
      invitation_code: json['invitation_code'] as String,
      sign: json['sign'] as String,
    );
  }
  String getIssueTime() {
    if (issue_time == null) return '';
    try {
      DateTime dateTime = DateTime.parse(issue_time!).toLocal();
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  String getExpireTime() {
    if (issue_time == null || duration == null) return '';
    try {
      DateTime issueDateTime = DateTime.parse(issue_time!).toLocal();
      DateTime expireDateTime = issueDateTime
          .add(Duration(milliseconds: (duration! * 3600000).round()));
      return '${expireDateTime.year}-${expireDateTime.month.toString().padLeft(2, '0')}-${expireDateTime.day.toString().padLeft(2, '0')} ${expireDateTime.hour.toString().padLeft(2, '0')}:${expireDateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }
}

class LicenseController extends GetxController {
  final isActivated = false.obs;
  final licenseInfo = Rx<LicenseInfo>(LicenseInfo());
  final code = "".obs;

  // Lazy getters to avoid dependency issues during initialization
  SettingController get settingController => Get.find<SettingController>();
  MessageController get messageController => Get.find<MessageController>();

  // 添加iOS内购相关属性
  final deviceId = "".obs;
  final expiryDate = "".obs;
  final licenseType = "".obs;

  List<({String title, String content})> get details => [
        (
          title: 'license.faq.why_activate.title'.tr,
          content: 'license.faq.why_activate.content'.tr,
        ),
        (
          title: 'license.faq.lifetime_10years.title'.tr,
          content: 'license.faq.lifetime_10years.content'.tr,
        ),
        (
          title: 'license.faq.reinstall.title'.tr,
          content: 'license.faq.reinstall.content'.tr,
        ),
      ];

  // 保存iOS内购许可证信息
  Future<void> saveLicenseInfo(String productId, String expiry) async {
    licenseType.value = productId;
    expiryDate.value = expiry;
    deviceId.value = await getDeviceSerial();
    isActivated.value = true;
  }

  Future<String> getDeviceSerial() async {
    try {
      // Use platform-specific implementations
      if (Platform.isAndroid || Platform.isIOS) {
        // Mobile platforms: Use direct Dart implementation
        String udid = await FlutterUdid.udid;
        return udid;
      } else {
        // Desktop platforms
        String udid = await FlutterMachineid.protectedID('anki') ?? '';
        if (udid.isNotEmpty) {
          return udid;
        } else {
          logger.e('get machineid failed');
          return '';
        }
      }
    } catch (e) {
      logger.e('getDeviceSerial(error): $e');
      return '';
    }
  }

  Future<String> getLicensePath() async {
    try {
      // Use platform-specific implementations
      if (Platform.isAndroid || Platform.isIOS) {
        // Mobile platforms: Use direct Dart implementation
        var externalDir = "";
        if (Platform.isAndroid) {
          externalDir = (await getExternalStorageDirectory())?.path ?? '';
        } else if (Platform.isIOS) {
          externalDir = (await getApplicationSupportDirectory()).path;
        }
        final licensePath = p.join(externalDir, 'LICENSE.json');
        return licensePath;
      } else {
        // Desktop platforms: Use Rust FFI call
        final resp = await messageController.request({}, "get_license_path");
        if (resp.status == "success") {
          return resp.data;
        } else {
          logger.e('getLicensePath(error): ${resp.message}');
          return '';
        }
      }
    } catch (e) {
      logger.e('getLicensePath(error): $e');
      return '';
    }
  }

  Future<void> checkLicenseQuick() async {
    isActivated.value = await verifyLicenseLocal();
    logger.d("verifyLicenseLocal(isActivated): ${isActivated.value}");
  }

  Future<void> checkLicenseRemote() async {
    final res = await verifyLicenseRemote();
    if (!res) {
      isActivated.value = false;
      logger.d("verifyLicenseRemote(isActivated): $res");
      return;
    }
  }

  Future<void> updateLicenseInfo() async {
    final licensePath = await getLicensePath();
    final licenseFile = File(licensePath);
    if (await licenseFile.exists()) {
      try {
        final licenseData = jsonDecode(await licenseFile.readAsString());
        licenseInfo.value = LicenseInfo.fromJson(licenseData);
      } catch (e) {
        logger.e('Failed to parse license file: $e');
      }
    } else {
      logger.i('License file does not exist');
    }
  }

  /// Helper function to handle primary/backup URL requests with retry logic
  Future<Resp> _requestWithFallback({
    required String primaryUrl,
    required String backupUrl,
    required Future<Resp> Function(String url) requestFunction,
  }) async {
    // Try primary URL first
    final primaryResult = await requestFunction(primaryUrl);

    // If primary succeeds, return the result
    if (primaryResult.status == "success") {
      return primaryResult;
    }

    // Primary failed with error or exception, try backup URL
    logger.w(
        'Primary URL failed with status: ${primaryResult.status}, message: ${primaryResult.message}');
    logger.w('Trying backup URL: $backupUrl');
    return await requestFunction(backupUrl);
  }

  /// Helper function to handle response and show appropriate toast notifications
  void _handleLicenseResponse({
    required BuildContext context,
    required Resp response,
    required String successMessage,
    required String errorPrefix,
    bool shouldActivate = false,
  }) {
    switch (response.status) {
      case "success":
        if (shouldActivate) {
          isActivated.value = true;
        }
        showToastNotification(context, successMessage, "", type: "success");
        break;
      case "error":
      case "exception":
        showToastNotification(context, 'license.request.failed'.tr,
            "$errorPrefix: ${response.message}",
            type: "error");
        break;
    }
  }

  Future<Resp> _requestActivate(String url, {String? machineid}) async {
    logger.i(url);
    try {
      final data = {
        'url': url,
        'code': code.value,
      };
      final deviceSerial = machineid ?? await getDeviceSerial();
      if (deviceSerial.isNotEmpty) {
        data['machineid'] = deviceSerial;
      }
      final resp = await messageController.request(data, "request_activate");
      logger.i('请求结果: ${resp}');

      if (resp.status == "success") {
        // Parse the response data to get the actual status
        if (resp.data.isEmpty) {
          logger.w('Response data is empty');
          return Resp(status: "error", message: 'Empty response from server');
        }

        try {
          final responseData = jsonDecode(resp.data);
          if (responseData['status'] == 'success') {
            return Resp(
                status: "success",
                message:
                    responseData['message'] ?? 'license.activation.success'.tr);
          } else {
            return Resp(
                status: "error",
                message:
                    responseData['message'] ?? 'license.activation.failed'.tr);
          }
        } catch (jsonError) {
          logger.w('Failed to parse response JSON: $jsonError');
          logger.w('Response data: ${resp.data}');
          return Resp(
              status: "error", message: 'Invalid response format from server');
        }
      } else {
        return Resp(status: "exception", message: resp.message);
      }
    } catch (e) {
      logger.e('请求失败: $e');
      return Resp(status: "exception", message: e.toString());
    }
  }

  Future<Resp> _requestUnregister(String url) async {
    try {
      final data = {
        'url': url,
      };

      final deviceSerial = await getDeviceSerial();
      if (deviceSerial.isNotEmpty) {
        data['machineid'] = deviceSerial;
      }

      final resp = await messageController.request(data, "request_unregister");
      logger.d('注销请求结果: ${resp.status} - ${resp.message}');

      if (resp.status == "success") {
        // Parse the response data to get the actual status
        if (resp.data.isEmpty) {
          logger.w('Response data is empty');
          return Resp(status: "error", message: 'Empty response from server');
        }

        try {
          final responseData = jsonDecode(resp.data);
          if (responseData['status'] == 'success') {
            isActivated.value = false;
            return Resp(
                status: "success",
                message:
                    responseData['message'] ?? 'license.unregister.success'.tr);
          } else {
            return Resp(
                status: "error",
                message:
                    responseData['message'] ?? 'license.unregister.failed'.tr);
          }
        } catch (jsonError) {
          logger.w('Failed to parse response JSON: $jsonError');
          logger.w('Response data: ${resp.data}');
          return Resp(
              status: "error", message: 'Invalid response format from server');
        }
      } else {
        return Resp(status: "exception", message: resp.message);
      }
    } catch (e) {
      logger.e('请求失败: $e');
      return Resp(status: "exception", message: e.toString());
    }
  }

  Future<void> requestTrial(BuildContext context) async {
    const primaryUrl = "https://anki.kevin2li.top/get_trial";
    const backupUrl = "https://anki.kevin2li.site/get_trial";

    final result = await _requestWithFallback(
      primaryUrl: primaryUrl,
      backupUrl: backupUrl,
      requestFunction: _requestActivate,
    );

    logger.i('Trial request result: ${result.status}');

    _handleLicenseResponse(
      context: context,
      response: result,
      successMessage: 'license.trial.success'.tr,
      errorPrefix: 'license.trial.failed'.tr,
      shouldActivate: true,
    );

    await updateLicenseInfo();
  }

  Future<void> requestActivate(BuildContext context) async {
    const primaryUrl = "https://anki.kevin2li.top/register_code";
    const backupUrl = "https://anki.kevin2li.site/register_code";

    final result = await _requestWithFallback(
      primaryUrl: primaryUrl,
      backupUrl: backupUrl,
      requestFunction: _requestActivate,
    );

    logger.i('Activation request result: ${result.status}');

    _handleLicenseResponse(
      context: context,
      response: result,
      successMessage: 'license.activation.success'.tr,
      errorPrefix: 'license.activation.failed'.tr,
      shouldActivate: true,
    );

    await updateLicenseInfo();
  }

  Future<void> requestUnregister(BuildContext context) async {
    const primaryUrl = "https://anki.kevin2li.top/unregister_code";
    const backupUrl = "https://anki.kevin2li.site/unregister_code";

    final result = await _requestWithFallback(
      primaryUrl: primaryUrl,
      backupUrl: backupUrl,
      requestFunction: _requestUnregister,
    );

    logger.i('Unregister request result: ${result.status}');

    _handleLicenseResponse(
      context: context,
      response: result,
      successMessage: 'license.unregister.success'.tr,
      errorPrefix: 'license.unregister.failed'.tr,
      shouldActivate: false, // Unregistration doesn't activate
    );

    await updateLicenseInfo();
  }

  Future<bool> verifyLicenseRemote() async {
    try {
      final licensePath = await getLicensePath();
      final licenseFile = File(licensePath);
      if (!await licenseFile.exists()) {
        logger.e('License file not found');
        return false;
      }
      final jsonStr = await licenseFile.readAsString();
      final dio = Dio(BaseOptions(
          connectTimeout: const Duration(seconds: 5),
          validateStatus: (status) => status! < 500));
      final response = await dio.post(
        'https://anki.kevin2li.top/verify_license',
        data: jsonDecode(jsonStr),
        options: Options(
          headers: {'Content-Type': 'application/json'},
        ),
      );
      final obj = response.data;
      if (obj['status'] != "success") {
        // License expired
        await licenseFile.delete();
        return false;
      }
      return true;
    } catch (err) {
      logger.e(err);
      return true;
    }
  }

  Future<bool> verifyLicenseLocal() async {
    try {
      final licensePath = await getLicensePath();
      logger.i('License path: $licensePath');
      final licenseFile = File(licensePath);
      if (!await licenseFile.exists()) {
        logger.e('License file not found');
        return false;
      }
      final data = {
        'path': licensePath,
        "machineid": await getDeviceSerial(),
        "show_progress": false,
      };
      final resp = await messageController.request(data, "verify_license");
      logger.i(resp);
      if (resp.status != "success") {
        logger.w('License verification failed: ${resp.message}');
        return false;
      }
      logger.i('License verification passed');
      return true;
    } catch (e) {
      logger.e('Error verifying license: $e');
      return false; // Return false on exceptions, not true
    }
  }
}
