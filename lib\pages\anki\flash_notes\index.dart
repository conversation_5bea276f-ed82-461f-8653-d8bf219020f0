import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/anki/flash_controller.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:markdown/markdown.dart' as md;
import 'package:markdown_quill/markdown_quill.dart';
import 'package:anki_guru/pages/anki/flash_notes/flash_note_editor.dart';
import 'package:flutter/services.dart';

class FlashNotesPage extends StatefulWidget {
  const FlashNotesPage({super.key});

  @override
  State<FlashNotesPage> createState() => _FlashNotesPageState();
}

class _FlashNotesPageState extends State<FlashNotesPage> {
  late FlashNoteController _controller;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 初始化控制器
    if (!Get.isRegistered<FlashNoteController>()) {
      Get.put(FlashNoteController());
    }
    _controller = Get.find<FlashNoteController>();

    // 添加搜索文本变化监听
    _searchController.addListener(() {
      _controller.searchKeyword.value = _searchController.text;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('闪念笔记'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: '新建笔记',
            onPressed: _createNewNote,
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索栏和排序选项
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // 搜索框
                Expanded(
                  child: ShadInput(
                    leading: const Icon(Icons.search, size: 18),
                    placeholder: const Text('搜索笔记...'),
                    controller: _searchController,
                    focusNode: _searchFocusNode,
                    trailing: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear, size: 18),
                            onPressed: () {
                              _searchController.clear();
                            },
                          )
                        : null,
                  ),
                ),
                // 排序选项
                const SizedBox(width: 8),
                Obx(() => ShadButton.ghost(
                      onPressed: _cycleSort,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.sort,
                            size: 18,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _getSortOptionText(),
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
          // 笔记列表
          Expanded(
            child: _buildNoteGrid(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewNote,
        tooltip: '新建笔记',
        child: const Icon(Icons.add),
      ),
    );
  }

  // 循环切换排序选项
  void _cycleSort() {
    String currentOption = _controller.noteSortOption.value;

    // 依次切换: 按修改时间 -> 按创建时间 -> 按字数 -> 按名称 -> 按修改时间
    switch (currentOption) {
      case 'modifyTime':
        _controller.noteSortOption.value = 'createTime';
        break;
      case 'createTime':
        _controller.noteSortOption.value = 'wordCount';
        break;
      case 'wordCount':
        _controller.noteSortOption.value = 'name';
        break;
      case 'name':
        _controller.noteSortOption.value = 'modifyTime';
        break;
      default:
        _controller.noteSortOption.value = 'modifyTime';
    }
  }

  // 获取当前排序选项的展示文本
  String _getSortOptionText() {
    switch (_controller.noteSortOption.value) {
      case 'createTime':
        return '按创建时间';
      case 'modifyTime':
        return '按修改时间';
      case 'wordCount':
        return '按字数';
      case 'name':
        return '按名称';
      default:
        return '按修改时间';
    }
  }

  // 创建新笔记
  void _createNewNote() async {
    final index = await _controller.createNote();
    if (index >= 0) {
      _editNote(_controller.notesList[index], index);
    }
  }

  // 将Markdown内容转换为Quill Document
  Future<Document> _convertMarkdownToDocument(String markdown) async {
    try {
      // 配置Markdown解析器
      final mdDocument = md.Document(
        encodeHtml: false,
        extensionSet: md.ExtensionSet.gitHubFlavored,
      );

      // 创建转换器
      final converter = MarkdownToDelta(markdownDocument: mdDocument);

      // 转换Markdown为Delta
      final delta = converter.convert(markdown);

      // 创建Document并返回
      return Document.fromDelta(delta);
    } catch (e) {
      logger.e('Markdown转换失败: $e');
      // 转换失败时，创建一个包含原始文本的文档
      final document = Document();
      document.insert(0, markdown);
      return document;
    }
  }

  // 编辑笔记
  void _editNote(Map<String, dynamic> note, int index) {
    Get.to(
        () => FlashNoteEditorPage(
              noteIndex: index,
              initialName: note['name'],
              initialContent: note['content'] ?? '',
            ),
        transition: Transition.cupertino);
  }

  // 构建笔记卡片网格
  Widget _buildNoteGrid() {
    return Obx(() {
      // 获取筛选后的笔记列表
      final notes = _controller.filteredNotesList;

      if (notes.isEmpty) {
        return Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.note_alt_outlined,
                  size: 64, color: Colors.grey.shade400),
              const SizedBox(height: 16),
              Text(
                _controller.searchKeyword.value.isEmpty
                    ? '暂无笔记，点击 + 创建'
                    : '未找到匹配的笔记',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ],
          ),
        );
      }

      // 对笔记按照排序选项排序
      final sortedNotes = List<Map<String, dynamic>>.from(notes);
      final sortOption = _controller.noteSortOption.value;

      sortedNotes.sort((a, b) {
        if (sortOption == 'wordCount') {
          // 按字数排序（从多到少）
          final int countA =
              a.containsKey('content') ? a['content'].toString().length : 0;
          final int countB =
              b.containsKey('content') ? b['content'].toString().length : 0;
          return countB.compareTo(countA);
        } else if (sortOption == 'createTime') {
          // 按创建时间排序（从新到旧）
          return b['createTime'].compareTo(a['createTime']);
        } else if (sortOption == 'name') {
          // 按名称排序（字母顺序）
          return a['name'].toString().compareTo(b['name'].toString());
        } else {
          // 默认按修改时间排序（从新到旧）
          String timeA =
              a.containsKey('modifyTime') ? a['modifyTime'] : a['createTime'];
          String timeB =
              b.containsKey('modifyTime') ? b['modifyTime'] : b['createTime'];
          return timeB.compareTo(timeA);
        }
      });

      // 根据屏幕宽度选择布局方式
      return LayoutBuilder(builder: (context, constraints) {
        // 根据屏幕宽度计算列数
        final double screenWidth = constraints.maxWidth;
        final int columnCount =
            screenWidth < 600 ? 1 : (screenWidth < 900 ? 2 : 3);

        // 使用水平内边距
        final horizontalPadding = const EdgeInsets.symmetric(horizontal: 16.0);

        // 单列模式使用ListView实现自适应高度
        if (columnCount == 1) {
          return Padding(
            padding: horizontalPadding,
            child: ListView.builder(
              padding: const EdgeInsets.only(bottom: 80), // 为FAB留出空间
              itemCount: sortedNotes.length,
              itemBuilder: (context, index) {
                final note = sortedNotes[index];

                // 找到原始索引，用于编辑和删除操作
                final originalIndex = _controller.notesList.indexWhere((n) =>
                    n['createTime'] == note['createTime'] &&
                    n['name'] == note['name']);

                return Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: _buildNoteCard(note, originalIndex, isGridView: false),
                );
              },
            ),
          );
        }
        // 多列模式使用GridView保持固定高宽比
        else {
          return Padding(
            padding: horizontalPadding,
            child: GridView.builder(
              padding: const EdgeInsets.only(bottom: 80), // 为FAB留出空间
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: columnCount,
                childAspectRatio: 1.3,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: sortedNotes.length,
              itemBuilder: (context, index) {
                final note = sortedNotes[index];

                // 找到原始索引，用于编辑和删除操作
                final originalIndex = _controller.notesList.indexWhere((n) =>
                    n['createTime'] == note['createTime'] &&
                    n['name'] == note['name']);

                return _buildNoteCard(note, originalIndex, isGridView: true);
              },
            ),
          );
        }
      });
    });
  }

  // 构建笔记卡片
  Widget _buildNoteCard(Map<String, dynamic> note, int index,
      {bool isGridView = true}) {
    // 计算修改时间
    final String timeString = note.containsKey('modifyTime')
        ? _formatDateTime(note['modifyTime'])
        : _formatDateTime(note['createTime']);

    // 获取预览文本
    final String previewText = note.containsKey('preview')
        ? note['preview']
        : (note['content']?.toString() ?? '');

    // 计算字数
    final int wordCount = note['content']?.toString().length ?? 0;

    // 构建卡片内容
    Widget buildCardContent() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: isGridView ? MainAxisSize.max : MainAxisSize.min,
        children: [
          // 标题栏
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 12, 8, 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题区
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        note['name'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 12,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            timeString,
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // 操作按钮
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.content_copy,
                        size: 18,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      visualDensity: VisualDensity.compact,
                      splashRadius: 18,
                      tooltip: '复制内容',
                      onPressed: () => _copyNoteContent(index),
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.delete_outline,
                        size: 20,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      visualDensity: VisualDensity.compact,
                      splashRadius: 18,
                      tooltip: '删除',
                      onPressed: () => _confirmDelete(index),
                    ),
                    IconButton(
                      icon: Icon(
                        LucideIcons.download,
                        size: 18,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      visualDensity: VisualDensity.compact,
                      splashRadius: 18,
                      tooltip: '导出到Markdown',
                      onPressed: () =>
                          _controller.exportNoteToMarkdown(context, index),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 字数统计
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              children: [
                Icon(
                  Icons.text_fields_outlined,
                  size: 14,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  '$wordCount 字',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),

          // 预览内容
          if (isGridView)
            Expanded(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
                child: Text(
                  previewText,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.8),
                    height: 1.4,
                  ),
                  maxLines: 6,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            )
          else
            Padding(
              padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  // 根据内容长度设置不同的最大高度
                  maxHeight: _calculateMaxHeight(previewText),
                  minHeight: 40, // 最小高度
                ),
                child: Text(
                  previewText,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.8),
                    height: 1.4,
                  ),
                  overflow: TextOverflow.ellipsis,
                  // 最大行数根据内容长度动态设置，但不超过最大高度
                  maxLines: _calculateMaxLines(previewText),
                ),
              ),
            ),
        ],
      );
    }

    return ShadCard(
      padding: EdgeInsets.zero,
      child: InkWell(
        onTap: () => _editNote(note, index),
        borderRadius: BorderRadius.circular(8),
        child: buildCardContent(),
      ),
    );
  }

  // 根据预览文本长度计算最大高度
  double _calculateMaxHeight(String text) {
    final length = text.length;
    // 预设一系列高度值，根据内容长度选择合适的高度
    if (length <= 30) {
      return 60; // 最小高度
    } else if (length <= 100) {
      return 80;
    } else if (length <= 200) {
      return 100;
    } else if (length <= 300) {
      return 120;
    } else {
      return 150; // 最大高度
    }
  }

  // 根据预览文本长度计算最大行数
  int _calculateMaxLines(String text) {
    final length = text.length;
    // 预设一系列行数值，根据内容长度选择合适的行数
    if (length <= 30) {
      return 2;
    } else if (length <= 100) {
      return 3;
    } else if (length <= 200) {
      return 5;
    } else if (length <= 300) {
      return 6;
    } else {
      return 8; // 最大行数
    }
  }

  // 复制笔记内容
  void _copyNoteContent(int index) async {
    final note = _controller.notesList[index];
    final title = note['name'] ?? '';
    final content = note['content']?.toString() ?? '';

    // 组合标题和内容，生成完整的Markdown文本
    final markdownContent = content;

    // 复制到剪贴板
    await Clipboard.setData(ClipboardData(text: markdownContent));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('笔记内容已复制到剪贴板')),
    );
  }

  // 格式化日期时间
  String _formatDateTime(String isoString) {
    try {
      final dateTime = DateTime.parse(isoString).toLocal();
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays == 0) {
        // 今天
        return '今天 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else if (difference.inDays == 1) {
        // 昨天
        return '昨天 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else if (difference.inDays < 30) {
        // n天前
        return '${difference.inDays}天前';
      } else {
        // 完整日期
        return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      return '未知时间';
    }
  }

  // 确认删除对话框
  void _confirmDelete(int index) {
    final note = _controller.notesList[index];
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除笔记"${note['name']}"吗？'),
        actions: [
          ShadButton.outline(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ShadButton.destructive(
            onPressed: () {
              Navigator.pop(context);
              _controller.deleteNote(index);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('笔记已删除')),
              );
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
