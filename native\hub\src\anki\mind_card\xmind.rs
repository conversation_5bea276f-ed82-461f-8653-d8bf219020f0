#![allow(unused)]

use crate::anki::connect::gen_id;
use crate::anki::mind_card::common::{
    mind_node_to_anki_notes, normalize_color, ClozeContext, MindNode, StyleAttribute,
};
use crate::anki::models::{get_kevin_mindmap_card_model, get_mindmap_card_assets, AnkiNote};
use crate::anki::utils::save_to_temp_file;
use serde::{Deserialize, Serialize};
use std::default::Default;
use std::fs;
use std::io;
use std::path::Path;
use std::path::PathBuf;
use tempfile::TempDir;
use ulid::Ulid;
use zip::ZipArchive;

/// 将 .xmind 文件解压到临时目录
///
/// # Arguments
/// * `xmind_path` - .xmind 文件路径
///
/// # Returns
/// * `Result<String, io::Error>` - 成功返回临时目录，失败返回错误
///
pub async fn extract_xmind(xmind_path: impl AsRef<Path>) -> Result<String, io::Error> {
    let temp_dir = crate::anki::utils::get_temp_dir()
        .await
        .map_err(|e| io::Error::new(io::ErrorKind::Other, e.to_string()))?;

    let output_dir = PathBuf::from(temp_dir.clone()).join(Ulid::new().to_string());
    fs::create_dir_all(&output_dir)?;
    // 打开 .xmind 文件
    let file = fs::File::open(&xmind_path)?;
    let mut archive =
        ZipArchive::new(file).map_err(|e| io::Error::new(io::ErrorKind::InvalidData, e))?;

    // 解压所有文件
    for i in 0..archive.len() {
        let mut file = archive
            .by_index(i)
            .map_err(|e| io::Error::new(io::ErrorKind::InvalidData, e))?;

        let outpath = output_dir.join(file.name());

        if file.name().ends_with('/') {
            // 创建目录
            fs::create_dir_all(&outpath)?;
        } else {
            // 创建父目录
            if let Some(parent) = outpath.parent() {
                fs::create_dir_all(parent)?;
            }
            // 写入文件
            let mut outfile = fs::File::create(&outpath)?;
            io::copy(&mut file, &mut outfile)?;
        }
    }

    Ok(output_dir.to_string_lossy().to_string())
}

/// 从JSON文件中读取思维导图数据
///
/// # Arguments
/// * `path` - JSON文件路径
///
/// # Returns
/// * `Result<serde_json::Value, io::Error>` - 成功返回解析后的JSON数据,失败返回错误
pub fn read_mindmap_json(path: impl AsRef<Path>) -> Result<serde_json::Value, io::Error> {
    // 读取文件内容
    let json_str = fs::read_to_string(path)?;

    // 解析JSON
    let json_value = serde_json::from_str(&json_str)
        .map_err(|e| io::Error::new(io::ErrorKind::InvalidData, e))?;

    Ok(json_value)
}

#[derive(Debug, Serialize, Deserialize)]
struct AttributedTitle {
    text: String,
    #[serde(rename = "fo:font-style", default)]
    font_style: Option<String>,
    #[serde(rename = "fo:color", default)]
    color: Option<String>,
    #[serde(rename = "fo:text-decoration", default)]
    text_decoration: Option<String>,
    #[serde(rename = "fo:font-weight", default)]
    font_weight: Option<String>,
}

/// 将attributedTitle数组转换为HTML格式的字符串，支持挖空
///
/// # Arguments
/// * `attributed_title` - attributedTitle JSON数组
/// * `cloze_styles` - 需要被挖空的样式列表，命中任一样式即挖空
/// * `cloze_ctx` - 挖空ID上下文
///
/// # Returns
/// * `String` - 转换后的HTML字符串
pub fn attributed_title_to_html(
    attributed_title: &serde_json::Value,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
) -> String {
    let titles = serde_json::from_value::<Vec<AttributedTitle>>(attributed_title.clone())
        .unwrap_or_default();

    let mut html = String::new();

    for title in titles {
        let mut span = String::new();
        let mut style = Vec::new();
        let mut should_cloze = false;

        if let Some(styles) = cloze_styles {
            // 检查是否满足任一挖空条件
            should_cloze = styles.iter().any(|style| match style {
                StyleAttribute::Colors(colors) => title.color.as_ref().map_or(false, |c| {
                    let normalized_node_color = normalize_color(c);
                    colors
                        .iter()
                        .any(|color| normalize_color(color) == normalized_node_color)
                }),
                StyleAttribute::Highlights(colors) => title.color.as_ref().map_or(false, |c| {
                    let normalized_node_color = normalize_color(c);
                    colors
                        .iter()
                        .any(|color| normalize_color(color) == normalized_node_color)
                }),
                StyleAttribute::Italic => {
                    title.font_style.as_ref().map_or(false, |s| s == "italic")
                }
                StyleAttribute::Bold => title.font_weight.as_ref().map_or(false, |w| {
                    w.parse::<i32>().map_or(w == "bold", |n| n >= 600)
                }),
                StyleAttribute::StrikeThrough => title
                    .text_decoration
                    .as_ref()
                    .map_or(false, |d| d == "line-through"),
                StyleAttribute::Underline => title
                    .text_decoration
                    .as_ref()
                    .map_or(false, |d| d == "underline"),
            });
        }
        dbg!("should_cloze: {}", should_cloze);
        if let Some(font_style) = title.font_style {
            if font_style == "italic" {
                style.push("font-style: italic".to_string());
            }
        }

        if let Some(color) = title.color.as_ref() {
            style.push(format!("color: {}", color));
        }

        if let Some(decoration) = title.text_decoration {
            match decoration.as_str() {
                "line-through" => style.push("text-decoration: line-through".to_string()),
                "underline" => style.push("text-decoration: underline".to_string()),
                _ => {}
            }
        }

        if let Some(weight) = title.font_weight {
            style.push(format!("font-weight: {}", weight));
        }

        // 构建HTML span标签
        let text = if should_cloze {
            let cloze_id = cloze_ctx.next_id();
            format!(
                r#"<span id="{}" class="cloze activated" onclick='this.classList.toggle("activated")'>{}</span>"#,
                cloze_id, title.text
            )
        } else {
            title.text
        };
        // dbg!("text: {}", text.to_string());
        // 替换文本中的换行符为HTML换行标签
        let new_text = text.replace("\n", "<br>");
        if style.is_empty() {
            span = new_text;
        } else {
            span = format!(r#"<span style="{}">{}</span>"#, style.join("; "), new_text);
        }

        html.push_str(&span);
    }

    html
}

#[derive(Debug)]
pub struct ParseResult {
    pub trees: Vec<MindNode>, // 改为存储多个树
    pub images: Vec<String>,
}

/// 解析思维导图 JSON 为节点树列表
pub fn parse_mind_tree(
    json: &serde_json::Value,
    cloze_styles: Option<&[StyleAttribute]>,
    use_tags: bool, // 是否启用标签解析
) -> Option<ParseResult> {
    let mut cloze_ctx = ClozeContext::new();
    let mut images = Vec::new();

    // 获取根节点
    let root_topic = json.as_array()?.first()?.get("rootTopic")?;

    if !use_tags {
        // 原有行为：解析整个树
        let tree = parse_topic(root_topic, cloze_styles, &mut cloze_ctx, &mut images)?;
        return Some(ParseResult {
            trees: vec![tree],
            images,
        });
    }

    // 新行为：根据标签解析
    let mut trees = Vec::new();
    collect_tagged_nodes(
        root_topic,
        cloze_styles,
        &mut cloze_ctx,
        &mut images,
        &mut trees,
    );

    if trees.is_empty() {
        None
    } else {
        Some(ParseResult { trees, images })
    }
}

fn collect_tagged_nodes(
    topic: &serde_json::Value,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
    images: &mut Vec<String>,
    trees: &mut Vec<MindNode>,
) {
    // 检查当前节点的标签
    let has_hash = topic
        .get("labels")
        .and_then(|l| l.as_array())
        .map(|labels| labels.iter().any(|l| l.as_str() == Some("#")))
        .unwrap_or(false);

    let has_exclamation = topic
        .get("labels")
        .and_then(|l| l.as_array())
        .map(|labels| labels.iter().any(|l| l.as_str() == Some("!")))
        .unwrap_or(false);

    if has_hash {
        // 如果有#标签，将整个子树作为一个MindNode
        if let Some(node) = parse_topic(topic, cloze_styles, cloze_ctx, images) {
            trees.push(node);
        }
    } else if has_exclamation {
        // 如果有!标签，只将当前节点作为MindNode（不包含孙子节点）
        if let Some(mut node) = parse_topic(topic, cloze_styles, cloze_ctx, images) {
            // 只保留直接子节点，移除所有孙子节点
            for child in &mut node.children {
                child.children.clear();
            }
            trees.push(node);
        }
    } else {
        // 递归检查子节点
        if let Some(attached) = topic
            .get("children")
            .and_then(|c| c.get("attached"))
            .and_then(|a| a.as_array())
        {
            for child in attached {
                collect_tagged_nodes(child, cloze_styles, cloze_ctx, images, trees);
            }
        }
    }
}

fn parse_topic(
    topic: &serde_json::Value,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
    images: &mut Vec<String>,
) -> Option<MindNode> {
    let id = format!("xmind_{}", topic.get("id")?.as_str()?.to_string());

    let mut content = String::new();

    // 处理图片
    if let Some(image) = topic.get("image") {
        if let Some(src) = image.get("src").and_then(|s| s.as_str()) {
            // 将 xap:resources/ 替换为空，只保留文件名
            let img_src = src.replace("xap:resources/", "");
            content.push_str(&format!(
                r#"<div class="image"><img src="{}" alt="topic image"/></div>"#,
                img_src
            ));
            // 收集图片路径
            images.push(img_src);
        }
    }

    // 处理标签
    // if let Some(labels) = topic.get("labels").and_then(|l| l.as_array()) {
    //     if !labels.is_empty() {
    //         content.push_str("<div class=\"labels\">");
    //         for label in labels {
    //             if let Some(label_text) = label.as_str() {
    //                 content.push_str(&format!(r#"<span class="label">{}</span>"#, label_text));
    //             }
    //         }
    //         content.push_str("</div>");
    //     }
    // }

    // 处理文本内容
    if let Some(attributed_title) = topic.get("attributedTitle") {
        let text_content = attributed_title_to_html(attributed_title, cloze_styles, cloze_ctx);
        content.push_str(&format!(r#"<span>{}</span>"#, text_content));
    } else if let Some(title) = topic.get("title").and_then(|t| t.as_str()) {
        // 创建一个模拟的 AttributedTitle 对象，包含样式信息
        let mut mock_title = AttributedTitle {
            text: title.to_string(),
            font_style: None,
            color: None,
            text_decoration: None,
            font_weight: None,
        };

        // 从 style.properties 中提取样式信息
        if let Some(style) = topic.get("style") {
            if let Some(properties) = style.get("properties") {
                mock_title.font_style = properties
                    .get("fo:font-style")
                    .and_then(|v| v.as_str())
                    .map(String::from);
                mock_title.color = properties
                    .get("fo:color")
                    .and_then(|v| v.as_str())
                    .map(String::from);
                mock_title.text_decoration = properties
                    .get("fo:text-decoration")
                    .and_then(|v| v.as_str())
                    .map(String::from);
                mock_title.font_weight = properties
                    .get("fo:font-weight")
                    .and_then(|v| v.as_str())
                    .map(String::from);
            }
        }

        // 将模拟的 AttributedTitle 转换为 JSON
        let mock_attributed_title = serde_json::json!([mock_title]);
        let text_content =
            attributed_title_to_html(&mock_attributed_title, cloze_styles, cloze_ctx);
        content.push_str(&format!(r#"<span>{}</span>"#, text_content));
    }

    // 如果既没有文本内容也没有图片，则返回 None
    if content.is_empty() {
        return None;
    }

    // 递归处理子节点
    let mut children = Vec::new();
    if let Some(attached) = topic
        .get("children")
        .and_then(|c| c.get("attached"))
        .and_then(|a| a.as_array())
    {
        for child in attached {
            if let Some(child_node) = parse_topic(child, cloze_styles, cloze_ctx, images) {
                children.push(child_node);
            }
        }
    }

    Some(MindNode {
        id,
        content,
        children,
    })
}

#[derive(Debug)]
pub struct XmindCardConfig {
    pub xmind_path: String,
    pub address: String,
    pub deck_name: String,
    pub model_name: String,
    pub cloze_styles: Vec<String>,
    pub text_colors: Vec<String>,
    pub use_tags: bool,
    pub tags: Vec<String>,
    pub is_show_source: bool,
    pub output_path: String,
}

pub async fn make_xmind_card(
    config: XmindCardConfig,
    progress_callback: impl Fn(f64, f64, String),
) -> Result<(), io::Error> {
    // 解压 xmind 文件 (0-20%)
    progress_callback(0.0, 100.0, "正在解压 xmind 文件...".to_string());
    let temp_dir = extract_xmind(&config.xmind_path).await?;
    let path = PathBuf::from(temp_dir.clone()).join("content.json");
    progress_callback(20.0, 100.0, "xmind 文件解压完成".to_string());

    // 解析思维导图 (20-40%)
    progress_callback(20.0, 100.0, "正在解析思维导图...".to_string());
    let json = read_mindmap_json(&path)?;

    let cloze_styles: Vec<StyleAttribute> = config
        .cloze_styles
        .iter()
        .filter_map(|style| match style.as_str() {
            "text_color" => Some(StyleAttribute::Colors(config.text_colors.clone())),
            "italic" => Some(StyleAttribute::Italic),
            "bold" => Some(StyleAttribute::Bold),
            "strikeout" => Some(StyleAttribute::StrikeThrough),
            "underline" => Some(StyleAttribute::Underline),
            _ => None,
        })
        .collect();

    let cloze_styles_ref = if cloze_styles.is_empty() {
        None
    } else {
        Some(cloze_styles.as_slice())
    };

    let result = parse_mind_tree(&json, cloze_styles_ref, config.use_tags)
        .ok_or_else(|| io::Error::new(io::ErrorKind::Other, "Failed to parse mind tree"))?;
    progress_callback(40.0, 100.0, "思维导图解析完成".to_string());

    // 收集图片资源 (40-50%)
    progress_callback(40.0, 100.0, "正在收集图片资源...".to_string());
    let mut media_files = Vec::new();
    for image_name in &result.images {
        let image_path = PathBuf::from(temp_dir.clone())
            .join("resources")
            .join(image_name);
        if image_path.exists() {
            media_files.push(image_path.to_string_lossy().to_string());
        }
    }
    progress_callback(50.0, 100.0, "图片资源收集完成".to_string());

    // 生成 Anki 笔记 (50-80%)
    progress_callback(50.0, 100.0, "正在生成 Anki 笔记...".to_string());
    let mut final_notes = Vec::new();
    let total_trees = result.trees.len() as f64;
    let mut tree_js_path_list = Vec::new();
    for (i, tree) in result.trees.iter().enumerate() {
        let current_progress = 50.0 + (i as f64 / total_trees) * 30.0;
        progress_callback(
            current_progress,
            100.0,
            format!("正在处理第 {} 个思维导图节点...", i + 1),
        );
        let map_id = Ulid::new().to_string();
        let notes = mind_node_to_anki_notes(
            map_id.to_string(),
            tree,
            config.deck_name.clone(),
            config.tags.clone(),
        );
        final_notes.extend(notes);
        let tree_js_path =
            PathBuf::from(temp_dir.clone()).join(format!("__{}.js", map_id.to_string()));
        fs::write(&tree_js_path, &tree.to_js())?;
        tree_js_path_list.push(tree_js_path.to_string_lossy().to_string());
    }
    progress_callback(80.0, 100.0, "Anki 笔记生成完成".to_string());
    dbg!("notes: {:?}", &final_notes);
    // 合并所有媒体文件路径
    let mut media_files = media_files;
    media_files.extend(tree_js_path_list);

    // 使用 gen_apkg 生成 apkg 文件 (80-100%)
    progress_callback(80.0, 100.0, "正在生成 apkg 文件...".to_string());
    crate::anki::models::gen_apkg(
        final_notes,
        Some(media_files),
        &config.output_path,
        None,
        false,
        Some(vec!["mindmap_card".to_string()]),
    )
    .await?;
    progress_callback(100.0, 100.0, "导出完成".to_string());

    Ok(())
}

#[cfg(test)]
mod tests {
    use rinf::debug_print;
    use tokio;

    use super::*;
    use std::fs;
    use std::path::PathBuf;
    // const TEST_XMIND_PATH: &str =
    // "/Users/<USER>/Documents/制卡素材/制卡测试/导图制卡/Shadcn.xmind";
    // const TEST_XMIND_PATH: &str = "/Users/<USER>/Downloads/中心主题.xmind";
    // const TEST_XMIND_PATH: &str = "/Users/<USER>/Downloads/Vocabulary（中高级）.xmind";
    const TEST_XMIND_PATH: &str = "/Users/<USER>/Downloads/中心主题.xmind";
    // #[tokio::test]
    async fn test_parse_shadcn_content() -> Result<(), Box<dyn std::error::Error>> {
        let xmind_path = TEST_XMIND_PATH;
        let parent_dir = Path::new(xmind_path).parent().unwrap();
        let temp_dir = extract_xmind(&xmind_path).await?;
        let path = PathBuf::from(temp_dir.clone()).join("content.json");
        let json = read_mindmap_json(&path)?;

        // 不使用标签解析
        let result =
            parse_mind_tree(&json, None, false).expect("Failed to parse mind tree in normal mode");
        assert!(
            !result.trees.is_empty(),
            "Should have at least one tree in normal mode"
        );
        let tree = &result.trees[0];

        let output_path = parent_dir.join("content_output.json");
        let output_json = tree.to_json();
        fs::write(output_path, serde_json::to_string_pretty(&output_json)?)?;

        // 使用标签解析
        if let Some(tag_result) = parse_mind_tree(&json, None, true) {
            for (i, tag_tree) in tag_result.trees.iter().enumerate() {
                let tag_output_json = tag_tree.to_json();
                let tag_output_path = parent_dir.join(format!("content_tag_output_{}.json", i));
                fs::write(
                    tag_output_path,
                    serde_json::to_string_pretty(&tag_output_json)?,
                )?;
            }
        }

        Ok(())
    }

    #[tokio::test]
    async fn test_make_xmind_card_apkg() {
        let xmind_path = TEST_XMIND_PATH;
        let parent_dir = Path::new(xmind_path).parent().unwrap();
        let output_path = parent_dir.join("output.apkg");

        let config = XmindCardConfig {
            xmind_path: xmind_path.to_string(),
            address: "http://localhost:8765".to_string(),
            deck_name: "Test Deck".to_string(),
            model_name: "Kevin Mindmap Card v3".to_string(),
            cloze_styles: vec!["bold".to_string(), "text_color".to_string()],
            text_colors: vec![
                "#FF9F00".to_string(),
                "#E32C2D".to_string(),
                "#50C3F7".to_string(),
            ],
            use_tags: false,
            tags: vec!["test".to_string()],
            is_show_source: true,
            output_path: output_path.to_string_lossy().to_string(),
        };

        let result = make_xmind_card(config, |progress, total, msg| {
            println!("Progress: {}/{} - {}", progress, total, msg);
        })
        .await;

        assert!(result.is_ok());
        assert!(Path::new(&output_path).exists());
        assert!(false);
    }
}
