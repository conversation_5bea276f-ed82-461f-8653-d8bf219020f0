import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/translations/app_translations.dart';

/// Localization initialization module
/// Handles translation system and locale setup in the correct order
class LocalizationInitializer {
  /// Initialize the translation system and apply saved language settings
  /// Reads language settings directly from StorageBox instead of SettingController
  static Future<void> initialize() async {
    logger.i('Initializing localization system...');

    // Step 1: Initialize translation system
    await AppTranslations.init();

    // Step 2: Apply saved language settings from storage
    await _applySavedLanguageSettings();

    logger.i('Localization system initialized');
  }

  /// Apply saved language settings from storage
  /// Reads directly from StorageBox to avoid SettingController dependency
  static Future<void> _applySavedLanguageSettings() async {
    // Get the locale controller that was created during AppTranslations.init()
    final localeController = Get.find<LocaleController>();

    // Read language setting directly from storage
    final storage = StorageManager();
    final savedLanguage = storage.read(StorageBox.default_, AnkiStorageKeys.language, "zh_CN");

    if (savedLanguage.isNotEmpty) {
      logger.i('从存储中恢复语言设置: $savedLanguage');
      // Apply language setting, ensuring it's completed before app creation
      localeController.changeLocale(savedLanguage);
    }
  }
}
