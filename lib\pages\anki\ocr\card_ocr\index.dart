import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/ocr/card_ocr_controller.dart';
import 'package:anki_guru/pages/anki/ocr/setting.dart';

class CardOCRPage extends StatefulWidget {
  const CardOCRPage({super.key});

  @override
  State<CardOCRPage> createState() => _CardOCRPageState();
}

class _CardOCRPageState extends State<CardOCRPage> {
  final controller = Get.put(CardOCRPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.ocr.card_ocr_title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        // actions: [
        //   IconButton(
        //     icon: const Icon(Icons.settings),
        //     onPressed: () {
        //       showShadDialog(
        //         context: context,
        //         builder: (context) => const SettingsPage(),
        //       );
        //     },
        //   ),
        // ],
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('toolbox.common.functionDescription'.tr,
                    style: defaultPageTitleStyle),
                Text('anki.ocr.card_ocr_description'.tr,
                    style: theme.textTheme.muted),
                const SizedBox(height: 16),
              ],
            ),
            const Expanded(
              child: SingleChildScrollView(
                child: CardOCR(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CardOCR extends GetView<CardOCRPageController> {
  const CardOCR({super.key});

  @override
  Widget build(context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: SingleChildScrollView(
        child: Obx(() => Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              spacing: 8,
              children: [
                ShadInputWithValidate(
                  label: 'anki.ocr.card_id'.tr,
                  placeholder: 'anki.ocr.card_id_placeholder'.tr,
                  maxLines: 3,
                  onChanged: (value) {
                    controller.cardOcrParams.text.value = value;
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return 'anki.ocr.card_id_cannot_empty'.tr;
                    }
                    try {
                      controller.noteList.value = await AnkiConnectController()
                          .findNotes('nid:"${value.trim()}"');
                      if (controller.noteList.isEmpty) {
                        return 'anki.ocr.card_id_illegal'.tr;
                      }
                      logger.i(controller.noteList);
                      controller.roiModelList.value = controller.noteList
                          .map((e) => e['modelName'].toString())
                          .toSet()
                          .toList();
                      controller.cardModel.value = controller.roiModelList[0];
                      controller.fieldList.value = await AnkiConnectController()
                          .getModelFieldNames(controller.cardModel.value);
                    } catch (e) {
                      logger.e("validateCardIds error: $e");
                      return 'anki.ocr.card_id_illegal'.tr;
                    }
                    return "";
                  },
                  onValidateError: (error) {},
                ),
                if (controller.roiModelList.isNotEmpty) ...[
                  ShadSelectWithSearch(
                    label: 'anki.ocr.card_template'.tr,
                    placeholder: 'anki.ocr.select_card_template'.tr,
                    searchPlaceholder:
                        'anki.placeholder.target_deck_search_input'.tr,
                    isMultiple: false,
                    initialValue: [controller.cardModel.value],
                    options: controller.roiModelList
                        .map((e) => {'value': e, 'label': e})
                        .toList(),
                    onChanged: (value) {
                      logger.i(value);
                      controller.cardModel.value = value.single;
                      controller.updateFieldList(controller.cardModel.value);
                    },
                  ),
                  if (controller.fieldList.isNotEmpty) ...[
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      minVerticalPadding: 0,
                      title: Text('anki.ocr.field_config'.tr,
                          style: defaultTitleStyle),
                      subtitle: Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            maxWidth: double.infinity,
                            maxHeight: (1 + controller.fieldList.length) * 48,
                          ),
                          child: ShadTable.list(
                            pinnedRowCount: 1,
                            header: [
                              ShadTableCell.header(
                                  child: Text('anki.ocr.original_field'.tr)),
                              ShadTableCell.header(
                                  child: Text('anki.ocr.table_ocr_col'.tr)),
                              ShadTableCell.header(
                                  child: Text('anki.ocr.fill_field'.tr)),
                              ShadTableCell.header(
                                  child: Text('anki.ocr.table_replace_col'.tr)),
                            ],
                            columnSpanExtent: (index) {
                              return const FractionalTableSpanExtent(0.25);
                            },
                            rowSpanExtent: (index) =>
                                const FixedTableSpanExtent(48),
                            children: [
                              for (var i = 0;
                                  i < controller.fieldList.length;
                                  i++)
                                [
                                  ShadTableCell(
                                      child: Text(controller.fieldList[i])),
                                  ShadTableCell(
                                    child: Obx(() => ShadSwitch(
                                          value: controller.modelFieldConfigs[
                                                          controller
                                                              .cardModel.value]
                                                      ?[controller.fieldList[i]]
                                                  ?['performOcr'] ??
                                              false,
                                          onChanged: (value) {
                                            controller.updateFieldConfig(
                                              controller.cardModel.value,
                                              controller.fieldList[i],
                                              performOcr: value,
                                            );
                                          },
                                        )),
                                  ),
                                  ShadTableCell(
                                    child: ShadSelectCustom(
                                      label: 'anki.ocr.select_field'.tr,
                                      showLabel: false,
                                      placeholder: 'anki.ocr.select_field'.tr,
                                      options: controller.fieldList
                                          .map((e) => {'value': e, 'label': e})
                                          .toList(),
                                      isMultiple: false,
                                      onChanged: (value) {
                                        if (value.isNotEmpty) {
                                          controller.updateFieldConfig(
                                            controller.cardModel.value,
                                            controller.fieldList[i],
                                            targetField:
                                                value.single.toString(),
                                          );
                                        }
                                      },
                                    ),
                                  ),
                                  ShadTableCell(
                                    child: Obx(() => ShadSwitch(
                                          value: controller.modelFieldConfigs[
                                                          controller
                                                              .cardModel.value]
                                                      ?[controller.fieldList[i]]
                                                  ?['replaceContent'] ??
                                              true,
                                          onChanged: (value) {
                                            controller.updateFieldConfig(
                                              controller.cardModel.value,
                                              controller.fieldList[i],
                                              replaceContent: value,
                                            );
                                          },
                                        )),
                                  ),
                                ]
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ],
            )),
      ),
    );
  }
}
