import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class UserCenterPage extends StatelessWidget {
  const UserCenterPage({super.key});

  @override
  Widget build(BuildContext context) {
    final licenseController = Get.find<LicenseController>();
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('userCenter.title'.tr),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: ListView(padding: const EdgeInsets.all(8), children: [
          ShadCard(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            title: Padding(
              padding: const EdgeInsets.fromLTRB(8, 8, 0, 0),
              child: Text(
                'userCenter.memberInfo'.tr,
                style: defaultCardTitleStyle,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Column(
                    children: [
                      Obx(() => licenseController.isActivated.value
                          ? _buildInfoRow(
                              'userCenter.memberType'.tr,
                              _getMemberTypeText(
                                  licenseController.licenseType.value))
                          : _buildInfoRow('userCenter.memberType'.tr,
                              'userCenter.noMember'.tr)),
                      Obx(() => licenseController.isActivated.value
                          ? _buildInfoRow(
                              'userCenter.expiryDate'.tr,
                              licenseController.expiryDate.value.isNotEmpty
                                  ? _formatDateTime(
                                      licenseController.expiryDate.value)
                                  : "N/A")
                          : _buildInfoRow('userCenter.expiryDate'.tr, 'N/A')),
                    ],
                  ),
                ),
                ShadButton(
                  onPressed: () {
                    Get.toNamed("/paywall");
                  },
                  child: Text(
                    'userCenter.buyMembership'.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ]),
      ),
    );
  }

  String _getMemberTypeText(String licenseType) {
    switch (licenseType) {
      case "monthly":
        return 'userCenter.monthly'.tr;
      case "annually":
        return 'userCenter.annually'.tr;
      case "lifetime":
        return 'userCenter.lifetime'.tr;
      default:
        return 'userCenter.unknown'.tr;
    }
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(color: Colors.grey)),
          Text(value),
        ],
      ),
    );
  }

  String _formatDateTime(String isoString) {
    try {
      final dateTime = DateTime.parse(isoString);
      return "${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}";
    } catch (e) {
      return isoString;
    }
  }
}
