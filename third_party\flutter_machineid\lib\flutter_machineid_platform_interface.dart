import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'flutter_machineid_method_channel.dart';

abstract class FlutterMachineidPlatform extends PlatformInterface {
  /// Constructs a FlutterMachineidPlatform.
  FlutterMachineidPlatform() : super(token: _token);

  static final Object _token = Object();

  static FlutterMachineidPlatform _instance = MethodChannelFlutterMachineid();

  /// The default instance of [FlutterMachineidPlatform] to use.
  ///
  /// Defaults to [MethodChannelFlutterMachineid].
  static FlutterMachineidPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [FlutterMachineidPlatform] when
  /// they register themselves.
  static set instance(FlutterMachineidPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  /// Returns the platform specific machine id of the current host OS.
  ///
  /// The returned ID should be considered "confidential" and you should
  /// consider using [getProtectedId] instead for security.
  ///
  /// Returns null if the machine ID cannot be determined.
  Future<String?> getId() {
    throw UnimplementedError('getId() has not been implemented.');
  }

  /// Returns a hashed version of the machine ID in a cryptographically secure way,
  /// using a fixed, application-specific key.
  ///
  /// Internally, this function calculates HMAC-SHA256 of the application ID,
  /// keyed by the machine ID.
  ///
  /// [appId] should be a unique identifier for your application.
  ///
  /// Returns null if the machine ID cannot be determined.
  Future<String?> getProtectedId(String appId) {
    throw UnimplementedError('getProtectedId() has not been implemented.');
  }

  /// Legacy method for backward compatibility.
  @Deprecated('Use getId() instead')
  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
