import argparse
import asyncio
import json
import sys

import aiohttp
from aiolimiter import AsyncLimiter
from loguru import logger
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure

# --- 1. 日志配置 ---
logger.remove()
logger.add(
    sys.stderr,
    level="INFO",
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    colorize=True,
)
logger.add(
    "bing_tool.log",
    level="DEBUG",
    rotation="10 MB",
    retention="7 days",
    backtrace=True,
    diagnose=True,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
)

# --- 2. 必应 API 配置 ---
BING_API_URL = "https://www.bing.com/api/v7/dictionarywords/search"
BING_HEADERS = {
    "Connection": "keep-alive",
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    # 建议将 Cookie 替换为您自己的，以提高请求成功率
    "Cookie": "MUIDB=113308D3F03F609C31981EFBF1596127; SNRHOP=I=&TS=; _EDGE_S=F=1&SID=1A816496BEB56E9724F572BEBFD36FF2; _EDGE_V=1; MUID=113308D3F03F609C31981EFBF1596127; SRCHD=AF=NOFORM; SRCHUID=V=2&GUID=4EB9938088E14CF09E6A57A0F7964023&dmnchg=1; SRCHUSR=DOB=20250713&DS=1; SRCHHPGUSR=SRCHLANG=zh-Hans; _SS=SID=1A816496BEB56E9724F572BEBFD36FF2",
}
BING_PARAMS = {
    "appid": "371E7B2AF0F9B84EC491D731DF90A55719C7D209",
    "mkt": "zh-cn",
    "pname": "bingdict",
    "img": "1",
}


# --- 3. 核心功能函数 ---

def get_db_collection(mongo_uri, db_name, collection_name):
    """连接数据库并返回集合对象 (同步版本)"""
    try:
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        client.admin.command('ping')
        db = client[db_name]
        collection = db[collection_name]
        logger.info(f"成功连接到 MongoDB，使用数据库 '{db_name}' 和集合 '{collection_name}'。")
        return client, collection
    except ConnectionFailure:
        logger.critical(f"无法连接到 MongoDB (URI: {mongo_uri})。请检查服务。")
        return None, None
    except OperationFailure as e:
        logger.critical(f"MongoDB 认证失败: {e.details.get('errmsg', '未知认证错误')}")
        return None, None
    except Exception:
        logger.exception("连接 MongoDB 时发生未知错误。")
        return None, None


def read_words_from_file(file_path):
    """从文本文件读取单词列表，每行一个，并统一转换为小写。"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            words = [line.strip().lower() for line in f if line.strip()]
        unique_words = sorted(list(set(words)))
        logger.success(f"从 '{file_path}' 文件中成功读取到 {len(unique_words)} 个唯一的单词 (已统一为小写)。")
        return unique_words
    except FileNotFoundError:
        logger.error(f"文件 '{file_path}' 未找到。")
        return []
    except Exception:
        logger.exception("读取单词文件时发生错误。")
        return []


def get_existing_words(collection):
    """从数据库中查询所有已存在的单词 (基于 queryWord 字段)。"""
    try:
        cursor = collection.find({}, {"queryWord": 1, "_id": 0})
        existing_words = {item['queryWord'].lower() for item in cursor if 'queryWord' in item}
        logger.info(f"数据库中已存在 {len(existing_words)} 个单词的记录。")
        return existing_words
    except Exception:
        logger.exception("查询数据库时发生错误。")
        return set()


# --- 功能 1: fetch (异步并发改造) ---

async def fetch_one_word(session, word, limiter, collection):
    """
    异步获取单个单词的数据并存入数据库，受限流器控制。
    """
    params = BING_PARAMS.copy()
    params['q'] = word

    async with limiter:
        try:
            logger.info(f"正在请求单词: '{word}'")
            async with session.get(BING_API_URL, headers=BING_HEADERS, params=params, timeout=15) as response:
                response.raise_for_status()
                api_data = await response.json()

                if api_data.get('value') and isinstance(api_data['value'], list) and api_data['value']:
                    query = {"queryWord": word}
                    document_to_store = {"queryWord": word, "apiResponse": api_data}
                    collection.update_one(query, {"$set": document_to_store}, upsert=True)
                    logger.success(f"成功处理并存储了单词 '{word}' 的数据。")
                else:
                    logger.warning(f"API 未返回单词 '{word}' 的有效数据。响应: {api_data}")
        
        except asyncio.TimeoutError:
            logger.error(f"请求单词 '{word}' 超时。")
        except aiohttp.ClientError as e:
            logger.error(f"请求单词 '{word}' 失败: {e.status} {e.message}")
        except Exception:
            logger.exception(f"处理单词 '{word}' 时发生未知错误。")


async def run_fetch_async(args):
    """
    执行 fetch 子命令的异步主逻辑。
    """
    logger.info(f"开始执行 'fetch' 任务 (并发模式)，输入文件: {args.file}")
    
    client, collection = get_db_collection(args.mongo_uri, args.db, args.collection)
    if collection is None:
        if client: client.close()
        return

    words_to_fetch = read_words_from_file(args.file)
    if not args.force:
        existing_words = get_existing_words(collection)
        words_to_fetch = [word for word in words_to_fetch if word not in existing_words]
    
    if not words_to_fetch:
        logger.info("所有单词均已在数据库中或无需获取，任务完成。")
        client.close()
        return
        
    logger.info(f"将为 {len(words_to_fetch)} 个新单词并发请求 API。")
    logger.info(f"并发数: {args.concurrency}, 频率限制: {args.rate_limit}/分钟")

    limiter = AsyncLimiter(args.rate_limit, 60)
    
    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(args.concurrency)
        
        async def run_with_semaphore(task):
            async with semaphore:
                await task

        tasks = [run_with_semaphore(fetch_one_word(session, word, limiter, collection)) for word in words_to_fetch]
        await asyncio.gather(*tasks)

    logger.success("所有并发任务处理完毕！")
    client.close()
    logger.info("MongoDB 连接已关闭。")


# --- 功能 2: query (同步) ---
@logger.catch
def run_query(args):
    """执行 query 子命令的逻辑。"""
    word_to_query = args.word.lower()
    logger.info(f"开始执行 'query' 任务，查询单词: '{word_to_query}'")
    
    client, collection = get_db_collection(args.mongo_uri, args.db, args.collection)
    if collection is None:
        if client: client.close()
        return

    query = {"queryWord": word_to_query}
    logger.debug(f"正在数据库中执行查询: {query}")
    result = collection.find_one(query)
    
    if result:
        logger.success(f"找到了单词 '{args.word}' 的数据：")
        pretty_result = json.dumps(result.get("apiResponse", result), indent=2, ensure_ascii=False, default=str)
        print(pretty_result)
    else:
        logger.warning(f"在数据库中未找到单词 '{args.word}'。")
        
    client.close()
    logger.info("MongoDB 连接已关闭。")


# --- 功能 3: list (同步) ---
@logger.catch
def run_list(args):
    """执行 list 子命令的逻辑。"""
    logger.info("开始执行 'list' 任务，列出数据库中的所有单词。")
    
    client, collection = get_db_collection(args.mongo_uri, args.db, args.collection)
    if collection is None:
        if client: client.close()
        return

    words = []
    try:
        logger.debug("正在查询数据库获取所有单词...")
        cursor = collection.find({}, {"queryWord": 1, "_id": 0})
        for doc in cursor:
            if 'queryWord' in doc:
                words.append(doc['queryWord'])
        words.sort()
        logger.info(f"共找到 {len(words)} 个单词。")

        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                for word in words:
                    f.write(f"{word}\n")
            logger.success(f"已将所有单词保存到文件: {args.output}")
        else:
            logger.info("将在控制台打印所有单词:")
            for word in words:
                print(word)
    except Exception:
        logger.exception("在列出单词过程中发生错误。")
    finally:
        client.close()
        logger.info("MongoDB 连接已关闭。")


# --- 4. 命令行解析 ---
def main():
    parser = argparse.ArgumentParser(
        description="必应词典数据工具 (并发版)，支持获取、查询和列出单词。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    # 通用参数
    parser.add_argument("--mongo-uri", default="******************************************************", help="MongoDB 连接字符串 (URI)。")
    parser.add_argument("--db", default="bing_db", help="MongoDB 数据库名称。")
    parser.add_argument("--collection", default="bing_words", help="MongoDB 集合名称。")

    subparsers = parser.add_subparsers(dest="command", required=True, help="可用的子命令")

    # 定义 'fetch' 子命令
    parser_fetch = subparsers.add_parser("fetch", help="从文本文件并发获取单词数据并存入数据库。")
    parser_fetch.add_argument("file", help="包含单词列表的文本文件路径 (每行一个单词)。")
    parser_fetch.add_argument("--force", action="store_true", help="强制更新已存在于数据库中的单词。")
    parser_fetch.add_argument("--concurrency", type=int, default=10, help="最大并发请求数。(默认: 10)")
    parser_fetch.add_argument("--rate-limit", type=int, default=180, help="每分钟允许的总请求数。(默认: 60)")
    parser_fetch.set_defaults(func=lambda args: asyncio.run(run_fetch_async(args)))

    # 定义 'query' 子命令
    parser_query = subparsers.add_parser("query", help="根据单词在数据库中查询其详细信息。")
    parser_query.add_argument("word", help="要查询的英文单词。")
    parser_query.set_defaults(func=run_query)

    # 定义 'list' 子命令
    parser_list = subparsers.add_parser("list", help="列出数据库中的所有单词。")
    parser_list.add_argument("-o", "--output", help="可选的文件路径，用于保存单词列表。")
    parser_list.set_defaults(func=run_list)
    
    try:
        args = parser.parse_args()
        args.func(args)
    except Exception:
        logger.exception("程序在解析参数或执行主函数时发生严重错误。")


if __name__ == "__main__":
    main()