// --- START OF MODIFIED FILE i18nViewProvider.ts ---

import * as vscode from 'vscode';
import * as path from 'path';
import { getConfiguration, getI18nDirUri, readJsonFile, getValueFromPath } from './utils';
import { I18nTreeItem } from './i18nTreeItem';

// ... (langToFlagMap and getFlagForLang function remain unchanged) ...
const langToFlagMap: Record<string, string> = {
    'en': '🇺🇸', 'en-US': '🇺🇸', 'en-GB': '🇬🇧',
    'zh': '🇨🇳', 'zh-CN': '🇨🇳', 'zh-TW': '🇹🇼', 'zh-HK': '🇭🇰',
    'ja': '🇯🇵', 'jp': '🇯🇵',
    'ko': '🇰🇷', 'kr': '🇰🇷',
    'de': '🇩🇪',
    'fr': '🇫🇷',
    'es': '🇪🇸',
    'it': '🇮🇹',
    'pt': '🇵🇹',
    'ru': '🇷🇺',
    'ar': '🇸🇦',
    'hi': '🇮🇳',
    'th': '🇹🇭',
    'vi': '🇻🇳',
    'id': '🇮🇩',
};

function getFlagForLang(langCode: string): string {
    const lowerLangCode = langCode.toLowerCase();
    if (langToFlagMap[lowerLangCode]) {
        return langToFlagMap[lowerLangCode];
    }
    const baseLang = lowerLangCode.split('-')[0];
    if (langToFlagMap[baseLang]) {
        return langToFlagMap[baseLang];
    }
    return '🌐'; // Default globe icon
}

export class I18nViewProvider implements vscode.TreeDataProvider<I18nTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<I18nTreeItem | undefined | null | void> = new vscode.EventEmitter<I18nTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<I18nTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;
    private allTranslations: Map<string, Record<string, any>> = new Map();
    private allLangs: string[] = [];
    private allFlatKeys: string[] = [];
    private isDataReady: boolean = false;
    public expandedKeys: Set<string> = new Set();
    
    private searchFilter: string = '';

    constructor(private context: vscode.ExtensionContext) {
        this.registerCommands();
        this.refresh();
        // Use a unique context key for the "clear search" button visibility
        vscode.commands.executeCommand('setContext', 'i18n-helper.view.searchVisible', false);
    }

    private registerCommands() {
        this.context.subscriptions.push(vscode.commands.registerCommand('i18n-helper.view.refresh', () => this.refresh()));
        this.context.subscriptions.push(vscode.commands.registerCommand('i18n-helper.view.goToDefinition', (item: I18nTreeItem) => this.goToDefinition(item)));
        
        this.context.subscriptions.push(vscode.commands.registerCommand('i18n-helper.view.search', () => this.searchKeys()));
        this.context.subscriptions.push(vscode.commands.registerCommand('i18n-helper.view.clearSearch', () => this.clearSearch()));
    }
    
    // --- START: MODIFIED FOR LIVE SEARCH ---
    private searchKeys() {
        const inputBox = vscode.window.createInputBox();
        inputBox.placeholder = 'Search translation keys (live results)...';
        inputBox.value = this.searchFilter;

        // Listener for when the user types
        inputBox.onDidChangeValue(text => {
            this.searchFilter = text;
            vscode.commands.executeCommand('setContext', 'i18n-helper.view.searchVisible', !!this.searchFilter);
            this._onDidChangeTreeData.fire(); // Refresh the view in real-time
        });

        // Listener for when the user accepts (e.g., presses Enter) or hides the box
        const disposeAll = () => {
            inputBox.dispose();
        };
        inputBox.onDidAccept(disposeAll);
        inputBox.onDidHide(disposeAll);
        
        inputBox.show();
    }

    private clearSearch() {
        if (this.searchFilter === '') { return; }
        this.searchFilter = '';
        vscode.commands.executeCommand('setContext', 'i18n-helper.view.searchVisible', false);
        this._onDidChangeTreeData.fire();
    }
    // --- END: MODIFIED FOR LIVE SEARCH ---

    public async refresh(): Promise<void> {
        this.isDataReady = false;
        this.clearSearch(); // Also clear search on full refresh
        this._onDidChangeTreeData.fire();
        await this.loadAllData();
        this.isDataReady = true;
        this._onDidChangeTreeData.fire();
    }

    // ... (loadAllData and other methods remain exactly the same as the previous correct version) ...

    private async loadAllData(): Promise<void> {
        this.allTranslations.clear();
        this.allLangs = [];
        this.allFlatKeys = [];
        const i18nDirUri = await getI18nDirUri();
        if (!i18nDirUri) { return; }
        const sourceLang = getConfiguration().get<string>('sourceLanguage', 'en');
        let sourceData: Record<string, any> | null = null;
        try {
            const entries = await vscode.workspace.fs.readDirectory(i18nDirUri);
            for (const [fileName, fileType] of entries) {
                if (fileType === vscode.FileType.File && fileName.endsWith('.json')) {
                    const lang = path.basename(fileName, '.json');
                    const fileUri = vscode.Uri.joinPath(i18nDirUri, fileName);
                    const data = await readJsonFile(fileUri);
                    if (data) {
                        this.allTranslations.set(lang, data);
                        this.allLangs.push(lang);
                        if (lang === sourceLang) {
                            sourceData = data;
                        }
                    }
                }
            }
        } catch (error) { console.error('Error reading i18n directory', error); }

        if (sourceData) {
            const keys = new Set<string>();
            this.collectAllKeysRecursively('', sourceData, keys);
            this.allFlatKeys = Array.from(keys).sort();
        }
        this.allLangs.sort();
    }

    private collectAllKeysRecursively(prefix: string, data: any, keys: Set<string>) {
        for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key)) {
                const newKeyPath = prefix ? `${prefix}.${key}` : key;
                if (typeof data[key] === 'object' && data[key] !== null && !Array.isArray(data[key])) {
                    this.collectAllKeysRecursively(newKeyPath, data[key], keys);
                } else {
                    keys.add(newKeyPath);
                }
            }
        }
    }

    public getTreeItem(element: I18nTreeItem): vscode.TreeItem {
        return element;
    }
    
    getParent(element: I18nTreeItem): vscode.ProviderResult<I18nTreeItem> {
        if (element.lang) {
            return new I18nTreeItem(
                element.keyPath,
                this.expandedKeys.has(element.keyPath)
                    ? vscode.TreeItemCollapsibleState.Expanded
                    : vscode.TreeItemCollapsibleState.Collapsed,
                element.keyPath
            );
        }
        return null;
    }

    public getChildren(element?: I18nTreeItem): Thenable<I18nTreeItem[]> {
        if (!this.isDataReady) {
            return Promise.resolve([new I18nTreeItem("Loading translations...", vscode.TreeItemCollapsibleState.None, 'loading_state')]);
        }

        if (element) {
            const children = this.allLangs.map(lang => {
                const langData = this.allTranslations.get(lang);
                const value = getValueFromPath(langData || {}, element.keyPath);
                const displayValue = (value !== undefined && value !== null && value !== '') ? JSON.stringify(value).replace(/^"|"$/g, '') : '— (missing)';
                const flag = getFlagForLang(lang);
                const label = `${flag} ${lang}: ${displayValue}`;
                return new I18nTreeItem(label, vscode.TreeItemCollapsibleState.None, element.keyPath, lang);
            });
            return Promise.resolve(children);
        } else {
            let keysToShow = this.allFlatKeys;
            const searchTerm = this.searchFilter.trim().toLowerCase();

            if (searchTerm) {
                keysToShow = this.allFlatKeys.filter(key => key.toLowerCase().includes(searchTerm));
            }
            
            if (keysToShow.length === 0) {
                 if (this.searchFilter) {
                     return Promise.resolve([new I18nTreeItem("No matching keys found.", vscode.TreeItemCollapsibleState.None, 'no_results_state')]);
                 }
                 return Promise.resolve([new I18nTreeItem("No translation keys found.", vscode.TreeItemCollapsibleState.None, 'no_keys_state')]);
            }
            
            const items = keysToShow.map(keyPath => {
                const isExpanded = this.expandedKeys.has(keyPath);
                const collapsibleState = isExpanded 
                    ? vscode.TreeItemCollapsibleState.Expanded 
                    : vscode.TreeItemCollapsibleState.Collapsed;

                let label: string | vscode.TreeItemLabel = keyPath;

                if (searchTerm) {
                    const startIndex = keyPath.toLowerCase().indexOf(searchTerm);
                    if (startIndex !== -1) {
                        const endIndex = startIndex + searchTerm.length;
                        const highlights: [number, number][] = [[startIndex, endIndex]];
                        label = { label: keyPath, highlights };
                    }
                }
                
                return new I18nTreeItem(label, collapsibleState, keyPath);
            });
            return Promise.resolve(items);
        }
    }
    
    async goToDefinition(item: I18nTreeItem) {
        if (!item.keyPath) return;
        const lang = item.lang || getConfiguration().get<string>('sourceLanguage', 'en');
        const i18nDirUri = await getI18nDirUri();
        if (!i18nDirUri) return;
        const fileUri = vscode.Uri.joinPath(i18nDirUri, `${lang}.json`);
        try {
            const document = await vscode.workspace.openTextDocument(fileUri);
            const text = document.getText();
            const escapedKey = item.keyPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const regex = new RegExp(`["']${escapedKey}["']\\s*:`);
            const match = regex.exec(text);
            if (match) {
                const position = document.positionAt(match.index);
                const selection = new vscode.Selection(position, position);
                const editor = await vscode.window.showTextDocument(document, { selection });
                editor.revealRange(selection, vscode.TextEditorRevealType.InCenterIfOutsideViewport);
            } else {
                await vscode.window.showTextDocument(document);
                vscode.window.showInformationMessage(`Could not find the exact key '${item.keyPath}' in ${lang}.json.`);
            }
        } catch (error) {
            if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {
                 vscode.window.showErrorMessage(`File not found: ${lang}.json`);
            } else {
                vscode.window.showErrorMessage(`Could not open file: ${path.basename(fileUri.path)}`);
            }
        }
    }
}
// --- END OF MODIFIED FILE i18nViewProvider.ts ---