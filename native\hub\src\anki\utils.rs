use crate::anki::types::PdfError;
use crate::signals::*;
use anyhow::Result;
use base64;
use chardetng::EncodingDetector;
use csv::Writer as CsvWriter;
use futures::channel::oneshot;
use html2text;
use lazy_static::lazy_static;
use notify_rust::Notification;
use regex::Regex;
use rinf::{debug_print, DartSignal, RustSignal};
use rust_xlsxwriter::{Format, FormatAlign, Workbook};
use scraper::{ElementRef, Html, Node, Selector};
use serde_json::Value as JsonValue;
use serde_json::{json, Value};
use std::collections::HashMap;
use std::fs::{create_dir_all, File};
use std::io::Cursor;
use std::io::{self, Read, Write};
use std::path::{Path, PathBuf};
use std::process::Command;
use std::str::FromStr;
use std::thread;
use std::time::Duration;
use tempfile::tempdir;
use std::sync::Mutex;
use ulid::Ulid;
use urlencoding::decode;

// 全局响应通道存储
lazy_static! {
    pub static ref RESPONSE_CHANNELS: Mutex<HashMap<String, oneshot::Sender<DartResponse>>> =
        Mutex::new(HashMap::new());
}

/// 向 Dart 端发送请求并等待响应
///
/// # Arguments
/// * `data` - 请求参数
/// * `msg_type` - 消息类型
///
/// # Returns
/// * `Result<Value>` - JSON 响应数据
pub async fn send_dart_request(data: Value, msg_type: String) -> Result<DartResponse> {
    let interaction_id = Ulid::new().to_string();
    let (sender, receiver) = oneshot::channel::<DartResponse>();

    // 存储响应通道
    RESPONSE_CHANNELS
        .lock()
        .unwrap()
        .insert(interaction_id.clone(), sender);

    // 构造请求消息
    let request = json!({
        "params": data.to_string(),
        "msgType": msg_type.clone(),
        "interactionId": interaction_id.clone()
    });

    // 发送到 Dart
    RustRequest {
        interaction_id: interaction_id.clone(),
        msg_type,
        params: request.to_string(),
    }
    .send_signal_to_dart();

    // 等待响应
    let response = receiver.await?;
    debug_print!("response: {:?}", response);
    Ok(response)
}

pub async fn get_ocr_model_paths() -> Result<(String, String, String)> {
    // 调用Dart接口获取模型路径
    let response = send_dart_request(
        json!({}), // 空参数
        "get_ocr_model_paths".to_string(),
    )
    .await?;

    debug_print!("Model paths response: {:?}", response);
    let status = response.status;
    let data = response.data;

    if status == "success" {
        // 解析返回的JSON数据
        let model_paths: JsonValue = serde_json::from_str(&data)?;

        // 提取各个模型路径
        let det_model = model_paths["det_model"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("Failed to get detection model path"))?
            .to_string();
        let cls_model = model_paths["cls_model"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("Failed to get classification model path"))?
            .to_string();
        let rec_model = model_paths["rec_model"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("Failed to get recognition model path"))?
            .to_string();

        Ok((det_model, cls_model, rec_model))
    } else {
        Err(anyhow::anyhow!(
            "Failed to get OCR model paths: {}",
            response.message
        ))
    }
}

/// 关闭指定端口上运行的进程
///
/// # Arguments
/// * `port` - 要关闭的端口号
/// * `signal` - 可选的信号类型，默认为 SIGKILL
///
/// # Returns
/// * `Result<()>` - 操作结果
pub fn kill_process_on_port(port: u16, signal: Option<&str>) -> Result<()> {
    #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
    {
        use killport::cli::Mode;
        use killport::killport::{Killport, KillportOperations};
        use killport::signal::KillportSignal;

        debug_print!("Killing process on port {} with signal {:?}", port, signal);
        // 创建Killport实例
        let killport = Killport;

        // 解析信号类型
        let signal_type = match signal {
            Some(sig) => KillportSignal::from_str(sig)
                .map_err(|e| anyhow::anyhow!("Invalid signal: {}", e))?,
            None => KillportSignal::from_str("SIGKILL")
                .map_err(|e| anyhow::anyhow!("Failed to parse default signal: {}", e))?,
        };

        // 使用kill_service_by_port函数关闭端口
        let result = killport.kill_service_by_port(
            port,
            signal_type,
            Mode::Auto, // 自动选择进程或容器
            false,      // 非dry-run模式，实际执行关闭操作
        );

        match result {
            Ok(killed) => {
                if killed.is_empty() {
                    debug_print!("No process found on port {}", port);
                } else {
                    for (kill_type, name) in killed {
                        debug_print!("Killed {} '{}' on port {}", kill_type, name, port);
                    }
                }
                Ok(())
            }
            Err(e) => {
                debug_print!("Failed to kill process on port {}: {}", port, e);
                Err(anyhow::anyhow!(
                    "Failed to kill process on port {}: {}",
                    port,
                    e
                ))
            }
        }
    }

    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        debug_print!("kill_process_on_port is only supported on desktop platforms");
        Ok(())
    }
}

/// 关闭多个端口上运行的进程
///
/// # Arguments
/// * `ports` - 要关闭的端口号列表
/// * `signal` - 可选的信号类型，默认为 SIGKILL
///
/// # Returns
/// * `Result<()>` - 操作结果
pub fn kill_processes_on_ports(ports: &[u16], signal: Option<&str>) -> Result<()> {
    #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
    {
        use killport::cli::Mode;
        use killport::killport::{Killport, KillportOperations};
        use killport::signal::KillportSignal;

        debug_print!("Killing processes on ports {:?}", ports);

        // 解析信号类型
        let signal_type = match signal {
            Some(sig) => KillportSignal::from_str(sig)
                .map_err(|e| anyhow::anyhow!("Invalid signal: {}", e))?,
            None => KillportSignal::from_str("SIGKILL")
                .map_err(|e| anyhow::anyhow!("Failed to parse default signal: {}", e))?,
        };

        // 创建Killport实例
        let killport = Killport;

        // 对每个端口执行关闭操作
        for &port in ports {
            match killport.kill_service_by_port(
                port,
                signal_type.clone(),
                Mode::Auto, // 自动选择进程或容器
                false,      // 非dry-run模式，实际执行关闭操作
            ) {
                Ok(killed) => {
                    if killed.is_empty() {
                        debug_print!("No process found on port {}", port);
                    } else {
                        for (kill_type, name) in killed {
                            debug_print!("Killed {} '{}' on port {}", kill_type, name, port);
                        }
                    }
                }
                Err(e) => {
                    debug_print!("Failed to kill process on port {}: {}", port, e);
                    return Err(anyhow::anyhow!(
                        "Failed to kill process on port {}: {}",
                        port,
                        e
                    ));
                }
            }
        }

        Ok(())
    }

    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        debug_print!("kill_processes_on_ports is only supported on desktop platforms");
        Ok(())
    }
}

/// 检查指定端口是否被占用
///
/// # Arguments
/// * `port` - 要检查的端口号
///
/// # Returns
/// * `Result<bool>` - 如果端口被占用则返回 true，否则返回 false
pub fn is_port_in_use(port: u16) -> Result<bool> {
    use std::net::{SocketAddr, TcpListener};

    let addr = SocketAddr::from(([127, 0, 0, 1], port));

    match TcpListener::bind(addr) {
        Ok(_) => {
            // 端口未被占用
            Ok(false)
        }
        Err(_) => {
            // 端口被占用
            Ok(true)
        }
    }
}

/// 查找一个可用的端口
///
/// # Returns
/// * `Result<u16>` - 返回一个可用的端口号
pub fn find_free_port() -> Result<u16> {
    use std::net::TcpListener;

    // 绑定到随机端口
    let listener = TcpListener::bind("127.0.0.1:0")?;
    let port = listener.local_addr()?.port();

    // 释放端口
    drop(listener);

    Ok(port)
}

pub async fn get_temp_dir() -> Result<String> {
    #[cfg(test)]
    {
        // 测试环境下使用 dirs 库
        if let Some(temp_dir) = dirs::cache_dir() {
            return Ok(temp_dir.to_string_lossy().into_owned());
        }
        // 如果获取不到缓存目录，则使用系统临时目录
        Ok(std::env::temp_dir().to_string_lossy().into_owned())
    }

    #[cfg(not(test))]
    {
        // 发送请求到 Dart 端获取临时目录路径
        let response = send_dart_request(
            json!({}), // 空参数
            "get_temp_dir".to_string(),
        )
        .await?;
        debug_print!("response: {:?}", response);
        let status = response.status;
        let data = response.data;
        if status == "success" {
            Ok(data)
        } else {
            Err(anyhow::anyhow!("Failed to get temp directory path"))
        }
    }
}

pub async fn get_page_count(input_path: &str) -> Result<u32> {
    // #[cfg(test)]
    // {
    let pdfium =
        crate::anki::pdf_utils::get_pdfium(None).map_err(|e| PdfError::Other(e.to_string()))?;
    let document = pdfium
        .load_pdf_from_file(&input_path, None)
        .map_err(|e| PdfError::Other(e.to_string()))?;
    let total_pages = document.pages().len() as u32;
    Ok(total_pages)
    // }

    // #[cfg(not(test))]
    // {
    //     // 发送请求到 Dart 端获取临时目录路径
    //     let response = send_dart_request(
    //         json!({
    //            "filePath": input_path,
    //         }),
    //         "get_page_count".to_string(),
    //     )
    //     .await?;
    //     debug_print!("response: {:?}", response);
    //     let status = response.status;
    //     if status == "success" {
    //         let data = response.data.parse::<u32>().map_err(|_| anyhow::anyhow!("Failed to parse page count"))?;
    //         Ok(data)
    //     } else {
    //         Err(anyhow::anyhow!("Failed to get page count"))
    //     }
    // }
}

pub async fn delete_pdf_annotations(
    input_path: &str,
    page_range: &str,
    annotation_ids: &[String],
) -> Result<String> {
    let response = send_dart_request(
        json!({
            "filePath": input_path,
            "pageRange": page_range,
            "annotationIds": annotation_ids,
        }),
        "delete_pdf_annotations".to_string(),
    )
    .await?;
    debug_print!("response: {:?}", response);
    let status = response.status;
    let data = response.data;
    if status == "success" {
        Ok(data)
    } else {
        Err(anyhow::anyhow!("Failed to delete pdf annotations"))
    }
}

pub async fn json_repair(json: &str) -> Result<String> {
    let response = send_dart_request(
        json!({
            "json": json,
        }),
        "json_repair".to_string(),
    )
    .await?;
    debug_print!("response: {:?}", response);
    let status = response.status;
    let data = response.data;
    if status == "success" {
        Ok(data)
    } else {
        Err(anyhow::anyhow!("Failed to json repair"))
    }
}

pub async fn html_to_text(html: &str) -> Result<String> {
    let text = html2text::from_read(html.as_bytes(), 80);
    Ok(text)
}

pub async fn export_pdf_annotations(input_path: &str) -> Result<String> {
    let response = send_dart_request(
        json!({
            "filePath": input_path,
        }),
        "export_pdf_annotations".to_string(),
    )
    .await?;
    debug_print!("response: {:?}", response);
    let status = response.status;
    let data = response.data;
    if status == "success" {
        Ok(data)
    } else {
        Err(anyhow::anyhow!("Failed to export pdf annotations"))
    }
}

pub async fn markdown2json(content: &str) -> Result<String> {
    #[cfg(test)]
    {
        use std::fs;
        let content = fs::read_to_string("/Users/<USER>/Downloads/node.json")?;
        Ok(content)
    }
    #[cfg(not(test))]
    {
        let response = send_dart_request(
            json!({
                "content": content,
            }),
            "markdown2json".to_string(),
        )
        .await?;
        debug_print!("response: {:?}", response);
        let status = response.status;
        let data = response.data;
        if status == "success" {
            Ok(data)
        } else {
            Err(anyhow::anyhow!("Failed to convert markdown to json"))
        }
    }
}

/// 获取PDF文件的页面大小
/// 返回一个Vec，每个元素是一个[f64; 4]，表示页面的左、底、右、上边界
pub fn get_pdf_page_size(path: &str) -> Result<Vec<[f64; 4]>, PdfError> {
    let pdfium =
        crate::anki::pdf_utils::get_pdfium(None).map_err(|e| PdfError::Other(e.to_string()))?;
    let document = pdfium
        .load_pdf_from_file(path, None)
        .map_err(|e| PdfError::Other(e.to_string()))?;
    let pages = document.pages();
    let page_sizes = pages
        .iter()
        .map(|page| {
            let rect = page.page_size();
            [
                rect.left().value as f64,
                rect.bottom().value as f64,
                rect.right().value as f64,
                rect.top().value as f64,
            ]
        })
        .collect();
    Ok(page_sizes)
}

/// 将HTML中的base64图片和本地图片替换为Anki中的图片文件名
pub fn replace_base64_to_local_images(
    html: &str,
    base_path: &str,
    temp_dir: &str,
    html_type: &str,
) -> (String, Vec<String>) {
    let mut fragment: Html;
    if html_type == "document" {
        fragment = Html::parse_document(html);
    } else {
        fragment = Html::parse_fragment(html);
    }
    let selector = Selector::parse("img").unwrap();
    let mut replaced_html = html.to_string();
    let mut replaced_paths = Vec::new();
    let base_path = Path::new(base_path);

    // 遍历所有图片标签
    for element in fragment.select(&selector) {
        if let Some(src) = element.value().attr("src") {
            // 处理URL编码的路径
            let decoded_src = decode(src).unwrap_or_else(|_| src.into()).into_owned();

            // 处理base64编码的图片
            if decoded_src.starts_with("data:") {
                // 处理base64图像
                if let Some(file_path) = save_base64_image(&decoded_src, temp_dir) {
                    // 获取文件名
                    if let Some(file_name) = file_path.file_name() {
                        let file_name_str = file_name.to_string_lossy();

                        // 替换HTML中的base64数据为文件名
                        replaced_html = replaced_html.replace(
                            &format!(r#"src="{}""#, src),
                            &format!(r#"src="{}""#, file_name_str),
                        );

                        // 记录保存的文件路径
                        replaced_paths.push(file_path.to_string_lossy().into_owned());
                    }
                }
            }
            // 排除网络图片，只处理相对路径
            else if !decoded_src.starts_with("http") {
                // 构建绝对路径并标准化
                let absolute_path = base_path.join(&decoded_src).canonicalize().ok();
                // debug_print!("src: {:?}", decoded_src);
                // debug_print!("base_path: {:?}", base_path);
                // debug_print!("absolute_path: {:?}", absolute_path);
                if let Some(path) = absolute_path {
                    // 获取纯文件名
                    if let Some(file_name) = path.file_name() {
                        let file_name_str = file_name.to_string_lossy();

                        // 替换HTML中的路径为纯文件名
                        replaced_html = replaced_html.replace(
                            &format!(r#"src="{}""#, src),
                            &format!(r#"src="{}""#, file_name_str),
                        );

                        // 记录绝对路径（使用字符串格式以保持跨平台兼容性）
                        replaced_paths.push(path.to_string_lossy().into_owned());
                    }
                }
            }
        }
    }

    (replaced_html, replaced_paths)
}

/// 将HTML中的本地图片文件路径替换为base64编码的图片
pub fn replace_local_image_paths_to_base64(
    html: &str,
    base_path: &str,
    temp_dir: &str,
    html_type: &str,
) -> String {
    let mut fragment: Html;
    if html_type == "document" {
        fragment = Html::parse_document(html);
    } else {
        fragment = Html::parse_fragment(html);
    }
    let selector = Selector::parse("img").unwrap();
    let mut replaced_html = html.to_string();
    let base_path = Path::new(base_path);

    // 遍历所有图片标签
    for element in fragment.select(&selector) {
        if let Some(src) = element.value().attr("src") {
            // 处理URL编码的路径
            let decoded_src = decode(src).unwrap_or_else(|_| src.into()).into_owned();

            // 跳过已经是base64或网络图片的情况
            if decoded_src.starts_with("data:") || decoded_src.starts_with("http") {
                continue;
            }

            // 尝试构建绝对路径
            let img_path = if Path::new(&decoded_src).is_absolute() {
                PathBuf::from(&decoded_src)
            } else {
                // 首先尝试从base_path和相对路径构建
                let path = base_path.join(&decoded_src);
                if path.exists() {
                    path
                } else {
                    // 如果不存在，尝试在temp_dir中查找文件名
                    let file_name = Path::new(&decoded_src).file_name();
                    if let Some(name) = file_name {
                        let temp_path = Path::new(temp_dir).join(name);
                        if temp_path.exists() {
                            temp_path
                        } else {
                            // 如果在temp_dir中也找不到，则跳过这个图片
                            debug_print!("Image not found: {:?}", decoded_src);
                            continue;
                        }
                    } else {
                        continue;
                    }
                }
            };

            // 读取图片文件并转换为base64
            if let Ok(content) = std::fs::read(&img_path) {
                if let Some(mime_type) = guess_mime_type(&img_path) {
                    let base64_data = base64::encode(&content);
                    let data_url = format!("data:{};base64,{}", mime_type, base64_data);

                    // 替换HTML中的图片路径为base64数据
                    replaced_html = replaced_html.replace(
                        &format!(r#"src="{}""#, src),
                        &format!(r#"src="{}""#, data_url),
                    );
                }
            }
        }
    }

    replaced_html
}

/// 根据文件扩展名猜测MIME类型
fn guess_mime_type(path: &Path) -> Option<String> {
    if let Some(ext) = path.extension() {
        let ext_str = ext.to_string_lossy().to_lowercase();
        match ext_str.as_str() {
            "jpg" | "jpeg" => Some("image/jpeg".to_string()),
            "png" => Some("image/png".to_string()),
            "gif" => Some("image/gif".to_string()),
            "webp" => Some("image/webp".to_string()),
            "svg" => Some("image/svg+xml".to_string()),
            "bmp" => Some("image/bmp".to_string()),
            "tiff" | "tif" => Some("image/tiff".to_string()),
            _ => Some("application/octet-stream".to_string()), // 默认二进制MIME类型
        }
    } else {
        None
    }
}

/// 将base64编码的图片数据保存为文件
///
/// # 参数
/// * `data_url` - base64编码的图片数据URL
/// * `base_path` - 保存图片的目录路径
///
/// # 返回值
/// * 成功时返回保存的文件路径
/// * 失败时返回None
fn save_base64_image(data_url: &str, temp_dir: &str) -> Option<PathBuf> {
    // 解析data URL格式
    let parts: Vec<&str> = data_url.split(";base64,").collect();
    if parts.len() != 2 {
        debug_print!("Invalid data URL format");
        return None;
    }

    // 获取MIME类型和文件扩展名
    let mime_part = parts[0];
    let base64_data = parts[1];

    // 从MIME类型确定文件扩展名
    let extension = if mime_part.contains("image/jpeg") || mime_part.contains("image/jpg") {
        "jpg"
    } else if mime_part.contains("image/png") {
        "png"
    } else if mime_part.contains("image/gif") {
        "gif"
    } else if mime_part.contains("image/webp") {
        "webp"
    } else if mime_part.contains("image/svg+xml") {
        "svg"
    } else {
        "bin" // 默认二进制扩展名
    };

    // 解码base64数据
    let decoded_data = match base64::decode(base64_data) {
        Ok(data) => data,
        Err(e) => {
            debug_print!("Failed to decode base64 data: {}", e);
            return None;
        }
    };

    // 生成随机文件名
    let file_name = format!("{}__.{}", Ulid::new().to_string(), extension);
    let file_path = PathBuf::from(temp_dir).join(&file_name);

    // 保存文件
    match File::create(&file_path) {
        Ok(mut file) => {
            if let Err(e) = file.write_all(&decoded_data) {
                debug_print!("Failed to write image data: {}", e);
                return None;
            }
        }
        Err(e) => {
            debug_print!("Failed to create image file: {}", e);
            return None;
        }
    };

    Some(file_path)
}

// 移除HTML内容的最外层<p>标签（如果存在且是唯一根节点）
pub fn remove_root_p_tag(html: &str) -> String {
    // 第一步：匹配整个字符串是否被<p>包裹
    let wrapper_re = Regex::new(r"(?is)^\s*<p\b[^>]*>(.*)</p>\s*$").unwrap();
    // 第二步：检查内容是否包含其他根级标签
    let block_tag_re = Regex::new(r"(?i)<p").unwrap();

    if let Some(caps) = wrapper_re.captures(html) {
        let inner = caps.get(1).unwrap().as_str().trim();

        // 检查两个条件：
        // 1. 内部没有其他块级标签
        // 2. 整个字符串只有一个<p>标签对
        if !block_tag_re.is_match(inner) && html.matches("</p>").count() == 1 {
            return inner.to_string();
        }
    }

    html.to_string()
}

/// 将字符串内容保存到临时文件中，并返回生成的临时文件路径
///
/// # 参数
/// * `content` - 要写入临时文件的字符串内容
/// * `extension` - 可选的文件扩展名（例如 "txt", "js", "html" 等）
/// * `filename` - 可选的文件名（不含扩展名）。如果为None，则自动生成唯一文件名
///
/// # 返回值
/// * 成功时返回临时文件的路径字符串
/// * 失败时返回IO错误
pub async fn save_to_temp_file(
    content: &str,
    filename: Option<&str>,
) -> Result<String, std::io::Error> {
    // 创建临时目录
    let temp_dir = get_temp_dir()
        .await
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;

    // 确定文件名
    let file_name = match filename {
        Some(name) => name.to_string(),
        None => Ulid::new().to_string(), // 使用ULID生成唯一ID作为文件名
    };

    // 添加扩展名（如果有）
    let file_path = PathBuf::from(temp_dir).join(file_name);

    // 创建并写入文件
    let mut file = File::create(&file_path)?;
    file.write_all(content.as_bytes())?;
    file.flush()?;

    // 获取文件的绝对路径字符串
    let path_str = file_path.to_string_lossy().into_owned();

    // 返回文件路径
    Ok(path_str)
}

/// 将 JSON 数据转换为 Excel 文件
///
/// # Arguments
/// * `input_path` - 输入 JSON 文件路径
/// * `output_path` - 输出 Excel 文件路径
///
/// # Returns
/// * `Result<()>` - 操作结果
pub fn json2xlsx(input_path: &str, output_path: &str) -> Result<()> {
    // 读取JSON文件
    let json_data = std::fs::read_to_string(input_path)?;

    // 解析 JSON 数据
    let data: Value = serde_json::from_str(&json_data)?;

    // 创建一个新的Excel工作簿
    let mut workbook = Workbook::new();

    // 添加一个工作表
    let worksheet = workbook.add_worksheet();

    // 设置标题行格式（可选）
    let header_format = Format::new().set_bold().set_align(FormatAlign::Center);

    // 获取数据数组
    if let Value::Array(rows) = data {
        let mut row_num = 0;

        // 添加表头
        if let Some(first_row) = rows.first() {
            if let Value::Object(headers) = first_row {
                let header_row: Vec<String> = headers.keys().cloned().collect();

                // 写入表头
                for (col_num, header) in header_row.iter().enumerate() {
                    worksheet.write_with_format(row_num, col_num as u16, header, &header_format)?;
                }
                row_num += 1;
            }
        }

        // 添加数据行
        for row in rows {
            if let Value::Object(cells) = row {
                let row_data: Vec<String> = cells.values().map(|v| v.to_string()).collect();

                // 写入数据行
                for (col_num, value) in row_data.iter().enumerate() {
                    worksheet.write(row_num, col_num as u16, value)?;
                }
                row_num += 1;
            }
        }
    }

    // 将工作簿保存到文件
    workbook.save(output_path)?;

    Ok(())
}

/// 将 JSON 数据转换为 CSV 文件
///
/// # Arguments
/// * `input_path` - 输入 JSON 文件路径
/// * `output_path` - 输出 CSV 文件路径
///
/// # Returns
/// * `Result<()>` - 操作结果
pub fn json2csv(input_path: &str, output_path: &str) -> Result<()> {
    // 读取JSON文件
    let json_data = std::fs::read_to_string(input_path)?;

    // 解析 JSON 数据
    let data: Value = serde_json::from_str(&json_data)?;

    // 创建CSV文件写入器
    let file = File::create(output_path)?;
    let mut writer = CsvWriter::from_writer(file);

    // 获取数据数组
    if let Value::Array(rows) = data {
        // 添加表头
        if let Some(first_row) = rows.first() {
            if let Value::Object(headers) = first_row {
                let header_row: Vec<String> = headers.keys().cloned().collect();
                writer.write_record(&header_row)?;
            }
        }

        // 添加数据行
        for row in rows {
            if let Value::Object(cells) = row {
                let row_data: Vec<String> = cells.values().map(|v| v.to_string()).collect();
                writer.write_record(&row_data)?;
            }
        }
    }

    // 确保所有数据都被写入
    writer.flush()?;

    Ok(())
}

/// Send a system notification
///
/// # Arguments
/// * `title` - The title of the notification
/// * `body` - The body text of the notification
///
/// # Returns
/// * `Result<()>` - Success or error
pub fn send_system_notification(title: &str, body: &str) -> Result<()> {
    Notification::new().summary(title).body(body).show()?;

    Ok(())
}

/// 自动检测文件编码并返回字符串内容
pub fn read_file_auto_decode<P: AsRef<Path>>(path: P) -> Result<String> {
    // 读取文件为字节流
    let mut file = std::fs::File::open(&path)?;
    let mut bytes = Vec::new();
    file.read_to_end(&mut bytes)?;

    // 首先尝试直接作为UTF-8解码
    match std::str::from_utf8(&bytes) {
        Ok(text) => {
            // 成功解码为UTF-8
            return Ok(text.to_string());
        }
        Err(_) => {
            // UTF-8解码失败，使用EncodingDetector尝试检测
            let mut detector = EncodingDetector::new();
            detector.feed(&bytes, true);
            let encoding = detector.guess(None, false);

            // 使用检测到的编码解码
            let (text, _, had_errors) = encoding.decode(&bytes);

            if had_errors {
                debug_print!("Warning: Encoding errors detected when decoding file");
            }

            Ok(text.into_owned())
        }
    }
}

/// 转换 LaTeX 公式为 Anki 格式
///
/// # Arguments
/// * `content` - 包含 LaTeX 公式的内容
///
/// # Returns
/// * `Result<String>` - 转换后的内容
pub async fn transform_formula(content: &str) -> Result<String> {
    let response = send_dart_request(
        json!({
            "content": content,
        }),
        "transform_formula".to_string(),
    )
    .await?;

    let status = response.status;
    let data = response.data;
    if status == "success" {
        Ok(data)
    } else {
        Err(anyhow::anyhow!("转换公式失败: {}", response.message))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::Path;

    // 创建测试用临时文件和目录的辅助函数
    fn create_test_files(base: &Path) {
        let _ = File::create(base.join("cat.png")).unwrap();
        let sub_dir = base.join("subdir");
        create_dir_all(&sub_dir).unwrap();
        let _ = File::create(sub_dir.join("dog.jpg")).unwrap();
    }

    #[test]
    fn test_replace_base64_to_local_images_basic() {
        let temp_dir = tempdir().unwrap();
        let base_path = temp_dir.path();
        create_test_files(base_path);

        let html = format!(r#"<img src="cat.png"><img src="subdir/dog.jpg">"#,);

        let (new_html, paths) =
            replace_base64_to_local_images(&html, base_path.to_str().unwrap(), "", "");

        assert!(new_html.contains(r#"src="cat.png""#));
        assert!(new_html.contains(r#"src="dog.jpg""#));
        assert_eq!(paths.len(), 2);
        assert!(paths[0].ends_with("cat.png"));
        assert!(paths[1].ends_with("dog.jpg"));
    }

    #[test]
    fn test_mixed_local_and_web_images() {
        let temp_dir = tempdir().unwrap();
        let base_path = temp_dir.path();
        let _ = File::create(base_path.join("local.png")).unwrap();

        let html = r#"
            <img src="local.png">
            <img src="https://example.com/web.jpg">
            <img src="data:image/png;base64,iVBOR...">
        "#;

        let (new_html, paths) =
            replace_base64_to_local_images(html, base_path.to_str().unwrap(), "", "");

        assert!(new_html.contains(r#"src="local.png""#));
        assert!(new_html.contains("https://example.com/web.jpg"));
        assert!(new_html.contains("data:image/png;base64"));
        assert_eq!(paths.len(), 1);
    }

    #[test]
    fn test_nested_paths_and_special_chars() {
        let temp_dir = tempdir().unwrap();
        let base_path = temp_dir.path();

        // 创建嵌套目录结构：base/a/b/c
        let nested_dir = base_path.join("a/b/c");
        create_dir_all(&nested_dir).unwrap();

        // 在base/a目录创建测试文件（比当前目录高两级）
        let target_dir = base_path.join("a");
        let test_file = target_dir.join("2023 vacation (1).jpg");
        let _ = File::create(&test_file).unwrap();

        // 从嵌套目录c出发的相对路径（../../指向base/a）
        let html = format!(r#"<img src="../../2023 vacation (1).jpg">"#);

        let (new_html, paths) =
            replace_base64_to_local_images(&html, nested_dir.to_str().unwrap(), "", "");

        assert!(
            new_html.contains(r#"src="2023 vacation (1).jpg""#),
            "Actual HTML: {}",
            new_html
        );
        assert!(
            paths[0].ends_with("2023 vacation (1).jpg"),
            "Actual path: {}",
            paths[0]
        );
    }

    #[test]
    fn test_invalid_paths() {
        let html = r#"<img src="./non_existent.png">"#;
        let (new_html, paths) = replace_base64_to_local_images(html, "/invalid/base/path", "", "");

        // 不修改无效路径
        assert!(new_html.contains(r#"src="./non_existent.png""#));
        assert!(paths.is_empty());
    }

    #[test]
    fn test_remove_root_p_tag() {
        // 嵌套标签现在会保留完整结构
        assert_eq!(
            remove_root_p_tag("<p><div>test</div></p>"),
            "<div>test</div>"
        );
        // 混合内容
        assert_eq!(
            remove_root_p_tag("<p>hello<br><strong>world</strong></p>"),
            "hello<br><strong>world</strong>"
        );
        assert_eq!(remove_root_p_tag("<p>hello</p>"), "hello");
        assert_eq!(remove_root_p_tag("<p class='test'>content</p>"), "content");
        assert_eq!(
            remove_root_p_tag("<p>\n  <div>test</div>\n</p>"),
            "<div>test</div>"
        );
        assert_eq!(remove_root_p_tag("<div>test</div>"), "<div>test</div>"); // 非p根标签保持
        assert_eq!(remove_root_p_tag("<p>a</p><p>b</p>"), "<p>a</p><p>b</p>"); // 保持多个p标签
    }

    #[test]
    fn test_json2xlsx() {
        let temp_dir = tempdir().unwrap();
        let input_path = temp_dir.path().join("test.json");
        let output_path = temp_dir.path().join("test.xlsx");

        let json_data = r#"[
            {"name": "John", "age": 30},
            {"name": "Jane", "age": 25}
        ]"#;

        // 写入测试JSON文件
        std::fs::write(&input_path, json_data).unwrap();

        let result = json2xlsx(input_path.to_str().unwrap(), output_path.to_str().unwrap());
        assert!(result.is_ok());

        // 检查输出文件是否存在
        assert!(output_path.exists());
    }

    #[test]
    fn test_json2csv() {
        let temp_dir = tempdir().unwrap();
        let input_path = temp_dir.path().join("test.json");
        let output_path = temp_dir.path().join("test.csv");

        let json_data = r#"[
            {"name": "John", "age": 30},
            {"name": "Jane", "age": 25}
        ]"#;

        // 写入测试JSON文件
        std::fs::write(&input_path, json_data).unwrap();

        let result = json2csv(input_path.to_str().unwrap(), output_path.to_str().unwrap());
        assert!(result.is_ok());

        // 检查输出文件是否存在
        assert!(output_path.exists());
    }

    #[test]
    #[ignore] // 忽略此测试，因为它会实际关闭正在运行的进程
    fn test_kill_process_on_port() {
        // 这是一个集成测试，需要在实际环境中运行
        // 因此默认被标记为忽略，避免在自动化测试中执行

        // 测试关闭单个端口
        let result = kill_process_on_port(8080, None);
        println!("Kill result: {:?}", result);

        // 测试关闭多个端口
        let ports = vec![8080, 8081, 3000];
        let result = kill_processes_on_ports(&ports, Some("SIGTERM"));
        println!("Kill multiple result: {:?}", result);
    }

    #[test]
    fn test_is_port_in_use() {
        use std::net::TcpListener;

        // 找一个随机可用端口
        let listener = TcpListener::bind("127.0.0.1:0").unwrap();
        let port = listener.local_addr().unwrap().port();

        // 此时端口应该被占用
        assert!(is_port_in_use(port).unwrap());

        // 释放端口
        drop(listener);

        // 此时端口应该可用
        assert!(!is_port_in_use(port).unwrap());
    }

    #[test]
    fn test_find_free_port() {
        // 获取一个可用端口
        let port = find_free_port().unwrap();

        // 端口应该是可用的
        assert!(!is_port_in_use(port).unwrap());

        // 占用这个端口
        let listener = std::net::TcpListener::bind(format!("127.0.0.1:{}", port)).unwrap();

        // 现在端口应该被占用
        assert!(is_port_in_use(port).unwrap());

        // 释放端口
        drop(listener);
    }
}
