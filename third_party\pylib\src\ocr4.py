import cv2
import numpy as np
import re
import string
import math
from pathlib import Path
import time # Import time for performance tracking (optional)

# Import PaddleOCR PPStructure
try:
    from paddleocr import PPStructure, draw_structure_result, save_structure_res
except ImportError:
    print("PaddleOCR PPStructure library not found. Please install paddlepaddle -i https://pypi.baidu.com/simple")
    print("pip install paddlepaddle -i https://pypi.baidu.com/simple")
    exit()

# --- Auxiliary Function ---

def combine_segment_boxes(segment_bboxes_list):
    """
    Given a list of 4-point bounding boxes for segments (words/chars), calculate their combined bounding rectangle.

    Args:
        segment_bboxes_list (list): A list containing 4-point bboxes, e.g., [((x1, y1), ...), [x1, y1], ...].
                                    Handles both tuple and list formats for bboxes and points within.

    Returns:
        list or None: The combined bounding rectangle in [min_x, min_y, max_x, max_y] format, or None if list is empty or invalid.
    """
    if not segment_bboxes_list:
        return None

    all_points = []
    for bbox_4_points in segment_bboxes_list:
        # Handle both list and tuple formats for the bbox container and the points within
        # Convert everything to a list of integer points for consistent processing
        try:
            points_list = []
            # Check if it's a list or tuple of 4 items
            if isinstance(bbox_4_points, (list, tuple)) and len(bbox_4_points) == 4:
                # Check if each item in the 4 is a list or tuple of 2 items (a point)
                if all(isinstance(point, (list, tuple)) and len(point) == 2 for point in bbox_4_points):
                     # Convert points to lists of integers
                     points_list = [[int(p[0]), int(p[1])] for p in bbox_4_points]
                # else: # Too noisy to print malformed point warnings frequently
                    # print(f"Warning: Skipping malformed point format in bbox: {bbox_4_points}")

            if points_list: # If points were successfully extracted
                 all_points.extend(points_list)

        except Exception as e:
            # print(f"Error processing bbox format {bbox_4_points}: {e}. Skipping bbox.")
            continue # Skip this bbox

    if not all_points:
        return None

    # Convert to numpy array for easy min/max calculation
    points_np = np.array(all_points, dtype=np.int32)
    min_x = np.min(points_np[:, 0])
    min_y = np.min(points_np[:, 1])
    max_x = np.max(points_np[:, 0])
    max_y = np.max(points_np[:, 1])

    # Return as [x1, y1, x2, y2] rectangle format
    return [min_x, min_y, max_x, max_y]


# --- Text Cleaning (for matching) ---

# Define punctuation to IGNORE during keyword MATCHing
# Keeping spaces is important if keywords might contain intentional spaces (like "word1 word2")
IGNORABLE_MATCHING_PUNCTUATION = set(string.punctuation + "，。！？【】『』《》（）：；‘’“”·—、—")

def clean_text_for_matching(text):
    """
    Clean text for keyword matching: convert to lowercase, remove specific punctuation.
    Keeps spaces and other characters not in the ignorable set.
    """
    if text is None:
        return ""

    # Convert entire text to lowercase first
    text_lower = text.lower()
    cleaned_chars = []

    # Iterate through the lowercase text to filter punctuation
    for char in text_lower:
        # If the character is NOT in the set of ignorable punctuation, keep it
        # Note: ' ' (space) is not in string.punctuation, so it's kept by default.
        if char not in IGNORABLE_MATCHING_PUNCTUATION:
             cleaned_chars.append(char)

    return "".join(cleaned_chars)

# --- Helper function (kept but not used in main search logic anymore) ---
# This function was part of a different mapping strategy. Keeping it in the file
# just in case, but it's not called by the updated keyword search logic.
def map_cleaned_to_original_indices(original_text, cleaned_text):
    # This function is not used in the current spatial sequence matching logic.
    # Its implementation details are less relevant now.
    pass


# --- Main Function (Utilizing PaddleOCR PPStructure's word boxes) ---

def find_keywords_with_ppstructure_word_boxes(image_path, keywords, ppstructure_engine):
    """
    Uses PaddleOCR PPStructure (with return_word_box=True) to get block-level text and word/char segments with coordinates.
    Pairs segments with their bboxes, sorts them spatially, and then performs
    character-by-character matching of cleaned keywords against the flattened,
    cleaned text of the sorted segments. Collects bboxes of matched segments.

    Args:
        image_path (str): Path to the image file.
        keywords (list): List of keywords to find (strings).
        ppstructure_engine: Initialized PPStructure object (must be initialized with return_word_box=True).

    Returns:
        tuple: (image_with_boxes, found_keyword_info)
            image_with_boxes (numpy.ndarray): A copy of the image with keyword boxes drawn.
            found_keyword_info (list): List of found keywords and their coordinates
                                      [(original_keyword, [x1, y1, x2, y2]), ...]
    """
    img = cv2.imread(image_path)
    if img is None:
        print(f"Error: Could not read image from {image_path}")
        return None, []

    img_height, img_width, _ = img.shape
    image_with_boxes = img.copy()
    found_keyword_info = []

    # Clean keywords for matching
    # Note: clean_text_for_matching preserves original spaces in keywords if they exist.
    cleaned_keywords_map = {kw: clean_text_for_matching(kw) for kw in keywords}
    # Filter out keywords that become empty after cleaning
    cleaned_keywords_map = {orig_kw: cl_kw for orig_kw, cl_kw in cleaned_keywords_map.items() if cl_kw}

    if not cleaned_keywords_map:
        print("No valid keywords to search after cleaning.")
        return image_with_boxes, []

    print("Running PPStructure...")
    start_time = time.time()
    try:
        structure_results = ppstructure_engine(img)
        # print(f"PPStructure raw output (first block sample): {structure_results[0] if structure_results else 'N/A'}") # Debug print raw output if needed
    except Exception as e:
        print(f"Error running PPStructure: {e}")
        return image_with_boxes, []
    end_time = time.time()
    print(f"PPStructure finished in {end_time - start_time:.2f} seconds.")


    if not structure_results:
        print("No structure elements detected by PPStructure.")
        return image_with_boxes, []

    # 2. Process each structure result block that has word-level segments
    processed_blocks_count = 0
    for block_index, block_dict in enumerate(structure_results):
        if isinstance(block_dict, dict) and 'res' in block_dict and isinstance(block_dict['res'], list):
             for res_item_index, res_item in enumerate(block_dict['res']):
                 # Check if this res_item contains the expected word-level data
                 if isinstance(res_item, dict) and \
                    'text_word' in res_item and isinstance(res_item['text_word'], list) and \
                    'text_word_region' in res_item and isinstance(res_item['text_word_region'], list) and \
                    len(res_item['text_word']) > 0 and \
                    len(res_item['text_word']) == len(res_item['text_word_region']):

                    processed_blocks_count += 1

                    block_text_orig = res_item.get('text', '[Overall text not available]')
                    block_word_list_orig = res_item['text_word'] # Segment texts (unordered list, corresponds to text_word_region)
                    block_bbox_list_raw = res_item['text_word_region'] # Segment bboxes (unordered list, corresponds to text_word)

                    print(f"\nProcessing Block {block_index}[{res_item_index}]. Overall text (if available): '{block_text_orig}'")
                    print(f"  Contains {len(block_word_list_orig)} word segments.")

                    # --- Spatial Sorting of Segments ---
                    segment_pairs_unordered = []
                    for i in range(len(block_word_list_orig)):
                        if i < len(block_bbox_list_raw) and isinstance(block_word_list_orig[i], str) and isinstance(block_bbox_list_raw[i], (list, tuple)):
                             segment_pairs_unordered.append((block_word_list_orig[i], block_bbox_list_raw[i]))

                    def get_sort_key(item):
                        bbox = item[1]
                        try:
                            rect = combine_segment_boxes([bbox])
                            if rect:
                                 return (rect[1], rect[0]) # Sort by min_y, then min_x
                            return (float('inf'), float('inf'))
                        except Exception as e:
                            return (float('inf'), float('inf'))

                    try:
                        segment_pairs_valid = [pair for pair in segment_pairs_unordered if get_sort_key(pair) != (float('inf'), float('inf'))]
                        segment_pairs_ordered = sorted(segment_pairs_valid, key=get_sort_key)
                        print(f"  Successfully sorted {len(segment_pairs_ordered)} valid segments spatially.")
                    except Exception as e:
                        print(f"  Warning: Could not sort segments spatially in Block {block_index}[{res_item_index}]. Error: {e}. Skipping this item.")
                        continue

                    if not segment_pairs_ordered:
                         print(f"  No valid segment pairs after sorting for Block {block_index}[{res_item_index}]. Skipping.")
                         continue

                    # --- Prepare Data for Character-by-Character Matching ---
                    flat_cleaned_chars = [] # List of individual cleaned characters from sorted segments
                    # Map from index in flat_cleaned_chars to index in ordered_segments_info
                    char_to_ordered_segment_index_map = []

                    # Create a list of segments with their cleaned text, original text, and original bbox from the sorted list
                    ordered_segments_info = []
                    for word_text_orig, bbox_raw in segment_pairs_ordered:
                         word_cleaned = clean_text_for_matching(word_text_orig)
                         # Append cleaned characters and update map
                         for char_in_cleaned_word in word_cleaned:
                             flat_cleaned_chars.append(char_in_cleaned_word)
                             # The current character corresponds to the segment we are processing in the ordered list
                             char_to_ordered_segment_index_map.append(len(ordered_segments_info)) # Append the *next* segment's index
                         # Add the full segment info *after* appending its characters
                         ordered_segments_info.append((word_cleaned, word_text_orig, bbox_raw))

                    flat_cleaned_text = "".join(flat_cleaned_chars)

                    print(f"  Flattened cleaned text ({len(flat_cleaned_text)} chars): '{flat_cleaned_text}'")
                    # print(f"  Map length: {len(char_to_ordered_segment_index_map)}") # Debug map length


                    # --- 3. Perform Character-by-Character Matching ---
                    for original_keyword, cleaned_keyword in cleaned_keywords_map.items():
                        if not cleaned_keyword: continue

                        # print(f"  Searching for cleaned keyword '{cleaned_keyword}' (original: '{original_keyword}') in flattened text.") # Debug search start

                        start_flat_search_index = 0
                        while start_flat_search_index < len(flat_cleaned_text):
                            # Find the keyword match in the flattened cleaned text string
                            match_start_in_flat = flat_cleaned_text.find(cleaned_keyword, start_flat_search_index)

                            if match_start_in_flat == -1:
                                # print(f"  Keyword '{original_keyword}' not found from index {start_flat_search_index}.") # Debug not found
                                break # No more matches for this keyword in this block

                            match_end_in_flat_inclusive = match_start_in_flat + len(cleaned_keyword) - 1

                            print(f"  FOUND match for '{original_keyword}' (cleaned: '{cleaned_keyword}') in flattened text at indices {match_start_in_flat}-{match_end_in_flat_inclusive}.")

                            # Map back to indices in the ordered_segments_info list using the pre-built map
                            try:
                                # The map provides the index in `ordered_segments_info` for each character index
                                # in the `flat_cleaned_text` string.
                                # The start of the match corresponds to the segment index at map[match_start_in_flat].
                                start_ordered_segment_index = char_to_ordered_segment_index_map[match_start_in_flat]

                                # The end of the match corresponds to the segment index at map[match_end_in_flat_inclusive].
                                # Ensure the end map index is within bounds.
                                end_map_index = min(match_end_in_flat_inclusive, len(char_to_ordered_segment_index_map) - 1)
                                end_ordered_segment_index = char_to_ordered_segment_index_map[end_map_index]

                                # print(f"  Corresponding ordered_segments_info indices range: {start_ordered_segment_index} to {end_ordered_segment_index}.") # Debug range

                                # Collect the original bboxes from the segments in this range (from the ordered list)
                                # Ensure the calculated range of segment indices is valid before slicing.
                                if start_ordered_segment_index >= 0 and end_ordered_segment_index < len(ordered_segments_info) and start_ordered_segment_index <= end_ordered_segment_index:
                                     # Slice the ordered_segments_info list to get the relevant items
                                     # Then extract the original bbox (index 2) from each item in the slice
                                     bboxes_to_combine = [item[2] for item in ordered_segments_info[start_ordered_segment_index : end_ordered_segment_index + 1]]

                                     # print(f"    Collected {len(bboxes_to_combine)} bboxes to combine from ordered segments.") # Debug print

                                     combined_bbox_rect = combine_segment_boxes(bboxes_to_combine)
                                     # print(f"  Combined BBox for '{original_keyword}': {combined_bbox_rect}") # Debug print
                                else:
                                    print(f"  Warning: Calculated segment indices range ({start_ordered_segment_index}-{end_ordered_segment_index}) is invalid or inverted for ordered_segments_info list length {len(ordered_segments_info)}. Skipping match for '{original_keyword}'.")
                                    combined_bbox_rect = None


                            except IndexError as e:
                                # This happens if match indices are out of bounds for the map
                                print(f"  Warning: Index mapping failed during match lookup for '{original_keyword}' at flat indices {match_start_in_flat}-{match_end_in_flat_inclusive}. Flattened text length: {len(flat_cleaned_text)}, Map length: {len(char_to_ordered_segment_index_map)}. Error: {e}. Skipping this match.")
                                combined_bbox_rect = None
                            except Exception as e:
                                print(f"  Unexpected error during bbox combination for '{original_keyword}': {e} in Block {block_index}[{res_item_index}]. Skipping match.")
                                combined_bbox_rect = None


                            if combined_bbox_rect:
                                box_rect = combined_bbox_rect
                                # Add to results (avoiding strict duplicates based on combined box)
                                is_duplicate = False
                                box1 = np.array(box_rect, dtype=np.int32)
                                # Check if this box is very similar to one already found for this keyword
                                for existing_keyword, existing_box in found_keyword_info:
                                     if existing_keyword == original_keyword:
                                          box2 = np.array(existing_box, dtype=np.int32)
                                          # Check if max absolute difference in corner coordinates is small (e.g., 10 pixels tolerance)
                                          if np.max(np.abs(box1 - box2)) < 10:
                                              is_duplicate = True
                                              # print(f"    Detected duplicate box for '{original_keyword}': {box_rect} vs {existing_box}") # Debug duplicates
                                              break

                                if not is_duplicate:
                                     found_keyword_info.append((original_keyword, box_rect))
                                     print(f"    Added match for '{original_keyword}', Box: {box_rect}")
                                # else: print(f"    Skipping duplicate box for '{original_keyword}' based on coordinates.")

                            # Move the search start index in the flattened text past the current match
                            start_flat_search_index = match_end_in_flat_inclusive + 1
                            # Ensure index doesn't go out of bounds for the next iteration's find() call
                            if start_flat_search_index > len(flat_cleaned_text):
                                 start_flat_search_index = len(flat_cleaned_text)

                    # print(f"Finished searching keywords in Block {block_index}[{res_item_index}].") # Debug print

                 # else: # This res_item did NOT contain the expected word-level data structure
                      # print(f"Skipping res_item {res_item_index} in Block {block_index}['res'] due to missing/invalid word data.") # Debug print
                    #   pass # Skip this specific item within 'res'

        # else: # This block_dict did NOT have 'res' list or was not a dict
             # print(f"Skipping Block {block_index} as it does not contain a 'res' list with text segments.") # Debug print
             pass # Skip this specific block

    if processed_blocks_count == 0:
         print("No suitable word-level text blocks found containing 'text_word' and 'text_word_region'.")

    print(f"\nFinished processing all blocks. Total found keywords: {len(found_keyword_info)}")


    # 4. Draw keyword bounding boxes (Same as before)
    colors = {}
    color_palette = [
        (0, 255, 0), (255, 0, 0), (0, 0, 255),
        (255, 255, 0), (255, 0, 255), (0, 255, 255),
        (128, 0, 0), (0, 128, 0), (0, 0, 128),
        (128, 128, 0), (128, 0, 128), (0, 128, 128),
        (192, 192, 192), (128, 128, 128),
        (255, 165, 0), (75, 0, 130), (238, 130, 238),
        (100, 149, 237), (255, 99, 71), (210, 105, 30), (147, 112, 219)
        ]

    color_index = 0

    print("\nDrawing boxes on the image...")
    for keyword, box in found_keyword_info:
        if keyword not in colors:
            colors[keyword] = color_palette[color_index % len(color_palette)]
            color_index += 1
        color = colors[keyword]

        x1, y1, x2, y2 = box
        # Ensure coordinates are integers
        x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)

        # Draw rectangle box (OpenCV uses (x1, y1) and (x2, y2) for diagonal corners)
        cv2.rectangle(image_with_boxes, (x1, y1), (x2, y2), color, 3) # Thicker line

        # Optional: Add keyword text label (Disabled by default)
        # text_label = f"{keyword}"
        # (text_width, text_height), baseline = cv2.getTextSize(text_label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)
        # put_text_x = x1
        # put_text_y = y1 - 5 if y1 - 5 > text_height + 2 else y2 + text_height + 5
        # put_text_y = max(5 + text_height, put_text_y)
        # put_text_x = max(0, put_text_x)
        # if put_text_x + text_width > img_width: put_text_x = img_width - text_width
        # if put_text_y > img_height - baseline: put_text_y = y1 - text_height - 5
        # if put_text_y <= img_height:
        #      cv2.rectangle(image_with_boxes, (put_text_x, put_text_y - text_height - baseline), (put_text_x + text_width, put_text_y), (255, 255, 255), -1)
        #      cv2.putText(image_with_boxes, text_label, (put_text_x, put_text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)


    return image_with_boxes, found_keyword_info

# --- Example Usage ---

if __name__ == '__main__':
    # Replace with your image path
    image_file = '/Users/<USER>/Downloads/Snipaste_2025-04-29_20-52-16.png'

    # Replace with your list of keywords to find
    keywords_to_find = ['视觉语言模型', '使用场景', '使用方式', '多轮会话', '事物', '文本', '图片', '调用', 'OCR', '机器视觉模型', '领域', '接口', 'message', '参数', '关系', 'VLM', 'VLM模型']
    # Added 'VLM' and 'VLM模型' again for testing this new logic

    # Initialize PaddleOCR PPStructure engine (only once)
    print("Initializing PPStructure reader with return_word_box=True...")

    try:
        ppstructure_engine = PPStructure(
            show_log=True,
            lang="ch",
            return_word_box=True, # ESSENTIAL
            recovery=True
        )
    except Exception as e:
        print(f"Error initializing PPStructure: {e}")
        print("Please check your PaddleOCR installation and model availability for PPStructure with return_word_box=True.")
        exit()


    # Find keywords and draw boxes
    print(f"\nProcessing image: {image_file}")

    # Call the main function using the PPStructure engine
    image_output, found_keywords = find_keywords_with_ppstructure_word_boxes(
        image_file,
        keywords_to_find,
        ppstructure_engine
    )

    if image_output is not None:
        # Save result image
        output_image_file = str(Path(image_file).parent / 'output_keywords_boxed_ppstructure_char_match.png') # Changed output name
        cv2.imwrite(output_image_file, image_output)
        print(f"\nResult image saved as: {output_image_file}")

        # Print found keywords and their coordinates
        print("\nFound Keywords and their coordinates (rectangle [x1, y1, x2, y2]):")
        if found_keywords:
            for keyword, box in found_keywords:
                print(f"  Keyword: '{keyword}', Box: {box}")
        else:
            print("  No keywords found.")

        # Display result image (optional)
        # cv2.imshow("Keywords Highlighted (PPStructure Word Boxes)", image_output)
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()
    else:
        print("Processing failed.")