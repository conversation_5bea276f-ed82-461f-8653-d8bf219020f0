
:root{
    --yellow: #fcecab;
    --blue: #2097f3;
}

.card {
    font-family: arial;
    font-size: 20px;
    text-align: left;
    color: black;
    background-color: white;
}

html, body, #content, #qa, .container {
    height: 100%;
    margin: 0;
    padding: 0;
}

span.cloze {
    cursor: pointer;
    position: relative;
    background: rgba(255, 0, 0, 0.1);
}

span.cloze.activated::before {
    cursor: pointer;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: #ec625d;
    z-index: 1;
}

span.cloze2 {
    cursor: pointer;
    position: relative;
    background: rgba(255, 0, 0, 0.1);
}

span.cloze2.activated::before {
    cursor: pointer;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: var(--yellow);
    z-index: 1;
}


table {
    border-collapse: collapse;
}

table,th,td {
    border: 1px solid #ccc;
}

mark.red{
    background-color: red;
}

mark.yellow{
    background-color: yellow;
}

mark.green{
    background-color: green;
}

mark.blue{
    background-color: blue;
}

mark.cyan{
    background-color: cyan;
}

mark.magenta{
    background-color: magenta;
}

.font_red{
    color: red;
}

* {
    margin: 0;
}
#nav{
    display: flex;
    justify-content: center;
}
.nav_btn{
    padding: 4px;
    font-size: 16px;
    min-width: 18%;
    color: #fff;
    background-color: #007bff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    margin: 0.4em;
}

.nav_btn:hover{
    background-color: #0056b3;
}

#q_div, #a_div {
    font-size: 1.2em;
    color: #333;
    background-color: #fbfbfb;
    border: 2px solid #ddd;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 0.4em;
}