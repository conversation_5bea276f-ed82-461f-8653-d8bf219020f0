import 'package:flutter/material.dart';
import 'dart:async';

import 'package:flutter/services.dart';
import 'package:flutter_machineid/flutter_machineid.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _machineId = 'Unknown';
  String _protectedId = 'Unknown';
  String _platformVersion = 'Unknown';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    initPlatformState();
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initPlatformState() async {
    String machineId;
    String protectedId;
    String platformVersion;

    // Platform messages may fail, so we use a try/catch PlatformException.
    // We also handle the message potentially returning null.
    try {
      // Get the raw machine ID
      machineId = await FlutterMachineid.id ?? 'Failed to get machine ID';
      print('Flutter Machine ID: $machineId');

      // Get the protected ID using an app-specific identifier
      protectedId = await FlutterMachineid.protectedID('com.example.flutter_machineid_example') ?? 'Failed to get protected ID';
      print('Flutter Protected ID: $protectedId');

      // Get platform version for legacy compatibility
      platformVersion = await FlutterMachineid().getPlatformVersion() ?? 'Unknown platform version';
      print('Platform Version: $platformVersion');

      // Verify against expected values (for testing)
      const expectedMachineId = 'B54F9400-7FF6-5FE8-B07C-F26BE0226CE1';
      const expectedProtectedId = 'a93ce8ae85e3a7b1a2c3f8203193bb26ece7b0d9a91bdb35ab18bc0faa9bfc2d';

      if (machineId == expectedMachineId) {
        print('✓ Machine ID matches expected value');
      } else {
        print('✗ Machine ID does not match expected value');
        print('  Expected: $expectedMachineId');
        print('  Got:      $machineId');
      }

      if (protectedId == expectedProtectedId) {
        print('✓ Protected ID matches expected value');
      } else {
        print('✗ Protected ID does not match expected value');
        print('  Expected: $expectedProtectedId');
        print('  Got:      $protectedId');
      }

    } on PlatformException catch (e) {
      machineId = 'Failed to get machine ID: ${e.message}';
      protectedId = 'Failed to get protected ID: ${e.message}';
      platformVersion = 'Failed to get platform version: ${e.message}';
      print('Error: ${e.message}');
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    setState(() {
      _machineId = machineId;
      _protectedId = protectedId;
      _platformVersion = platformVersion;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Flutter Machine ID Example'),
          backgroundColor: Colors.blue,
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Machine ID',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Raw machine identifier (should be kept confidential):',
                              style: TextStyle(color: Colors.grey),
                            ),
                            const SizedBox(height: 8),
                            SelectableText(
                              _machineId,
                              style: const TextStyle(fontFamily: 'monospace'),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Protected ID',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'HMAC-SHA256 hash (safe to use in applications):',
                              style: TextStyle(color: Colors.grey),
                            ),
                            const SizedBox(height: 8),
                            SelectableText(
                              _protectedId,
                              style: const TextStyle(fontFamily: 'monospace'),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Platform Info',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(_platformVersion),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Card(
                      color: Colors.orange,
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '⚠️ Security Note',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'The raw Machine ID should be considered confidential. '
                              'Use the Protected ID for application-specific identification.',
                              style: TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }
}
