import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf_pdf;
import 'package:path/path.dart' as path;
import 'package:ulid/ulid.dart';
import 'package:markdown/markdown.dart' hide Text, Element;
import 'package:htmltopdfwidgets/htmltopdfwidgets.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/services.dart';

// 使用映射表获取纸张尺寸
final sizeMap = {
  'a0': sf_pdf.PdfPageSize.a0,
  'a1': sf_pdf.PdfPageSize.a1,
  'a2': sf_pdf.PdfPageSize.a2,
  'a3': sf_pdf.PdfPageSize.a3,
  'a4': sf_pdf.PdfPageSize.a4,
  'a5': sf_pdf.PdfPageSize.a5,
  'a6': sf_pdf.PdfPageSize.a6,
  'a7': sf_pdf.PdfPageSize.a7,
  'a8': sf_pdf.PdfPageSize.a8,
  'a9': sf_pdf.PdfPageSize.a9,
  'a10': sf_pdf.PdfPageSize.a10,
  'b0': sf_pdf.PdfPageSize.b0,
  'b1': sf_pdf.PdfPageSize.b1,
  'b2': sf_pdf.PdfPageSize.b2,
  'b3': sf_pdf.PdfPageSize.b3,
  'b4': sf_pdf.PdfPageSize.b4,
  'b5': sf_pdf.PdfPageSize.b5,
  'letter': sf_pdf.PdfPageSize.letter,
  'legal': sf_pdf.PdfPageSize.legal,
};

class PDFConvertPageController extends GetxController {
  // 已有数据
  final List<Map<String, String>> convertTypeList = [
    {
      'value': 'pdf2img',
      'label': 'home.tools.pdfToImg'.tr,
    },
    {
      'value': 'img2pdf',
      'label': 'home.tools.imgToPdf'.tr,
    },
    {
      'value': 'pdf2img-pdf',
      'label': 'home.tools.pdfToImgPdf'.tr,
    },
    {
      'value': 'html2pdf',
      'label': 'HTML→PDF',
    },
    {
      'value': 'md2pdf',
      'label': 'Markdown→PDF',
    },
  ];
  final List<Map<String, String>> outputFormatList = [
    {
      'value': 'jpg',
      'label': 'JPG',
    },
    {
      'value': 'png',
      'label': 'PNG',
    },
  ];
  final List<Map<String, String>> orientationList = [
    {
      'value': 'auto',
      'label': 'Auto',
    },
    {
      'value': 'portrait',
      'label': 'toolbox.combine.portrait'.tr,
    },
    {
      'value': 'landscape',
      'label': 'toolbox.combine.landscape'.tr,
    },
  ];
  final List<Map<String, String>> paperSizeList = [
    {
      'value': 'sameAsImage',
      'label': 'toolbox.convert.img2pdf.selectPaperSize'.tr,
    },
    // 自动生成A系列和常见纸张尺寸
    for (final size in [
      'a0',
      'a1',
      'a2',
      'a3',
      'a4',
      'a5',
      'a6',
      'a7',
      'a8',
      'a9',
      'a10',
      'b0',
      'b1',
      'b2',
      'b3',
      'b4',
      'b5',
      'letter',
      'legal'
    ])
      {
        'value': size,
        'label': size == 'letter'
            ? 'Letter'
            : size == 'legal'
                ? 'Legal'
                : size.toUpperCase(),
      },
  ];
  final List<Map<String, String>> sortByList = [
    {
      'value': 'selection',
      'label': 'toolbox.convert.img2pdf.sortOptions.bySelection'.tr,
    },
    {
      'value': 'numberPrefix',
      'label': 'toolbox.convert.img2pdf.sortOptions.byNumberPrefix'.tr,
    },
    {
      'value': 'numberSuffix',
      'label': 'toolbox.convert.img2pdf.sortOptions.byNumberSuffix'.tr,
    },
    {
      'value': 'name',
      'label': 'toolbox.convert.img2pdf.sortOptions.byName'.tr,
    },
    {
      'value': 'createDate',
      'label': 'toolbox.convert.img2pdf.sortOptions.byCreateDate'.tr,
    },
    {
      'value': 'modDate',
      'label': 'toolbox.convert.img2pdf.sortOptions.byModDate'.tr,
    },
  ];
  final List<Map<String, String>> directionList = [
    {
      'value': 'ascending',
      'label': 'toolbox.convert.img2pdf.sortDirections.ascending'.tr,
    },
    {
      'value': 'descending',
      'label': 'toolbox.convert.img2pdf.sortDirections.descending'.tr,
    },
  ];

  // 表单参数
  // 公共参数
  final convertType = 'pdf2png'.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // PDF转图片参数
  final isGray = false.obs;
  final dpi = 300.obs;
  final outputFormat = 'jpg'.obs;
  // 自动缩放参数
  final maxWidthPixels = 3000.obs;
  final maxHeightPixels = 4000.obs;
  final autoScaleToA4 = true.obs;
  // 图片转PDF参数
  final mergeToOne = true.obs;
  final sortBy = 'selection'.obs;
  final sortDirection = 'ascending'.obs;
  final paperSize = "sameAsImage".obs;
  final orientation = "auto".obs;
  // HTML/MD转PDF字体参数
  final useCustomFont = false.obs;
  final customFontFilePath = ''.obs;
  final fontName = 'SourceHanSansSC'.obs;
  final fontSize = 12.0.obs;
  // 字体列表
  final List<Map<String, String>> fontFamilyList = [
    {
      'value': 'SourceHanSansSC',
      'label': '思源黑体',
    },
    {
      'value': 'Times New Roman',
      'label': 'Times New Roman',
    },
    {
      'value': 'Courier New',
      'label': 'Courier New',
    },
    {
      'value': 'Helvetica',
      'label': 'Helvetica',
    },
  ];

  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();

  @override
  void onInit() async {
    super.onInit();
    outputDir.value = await PathUtils.downloadDir;
  }

  /// 调用Rust端将PDF转换为图片
  Future<String> _convertPdfToImages(
    String pdfPath,
    String outputPath,
    String pageRange, {
    bool showProgress = true,
  }) async {
    try {
      final data = {
        'path': pdfPath,
        'output_path': outputPath,
        'output_format': outputFormat.value,
        'page_range': pageRange,
        'dpi': dpi.value,
        'is_gray': isGray.value,
        'max_width_pixels': maxWidthPixels.value,
        'max_height_pixels': maxHeightPixels.value,
        'auto_scale_to_a4': autoScaleToA4.value,
        'show_progress': showProgress,
      };

      final resp = await messageController.request(data, 'pdf_to_image');
      if (resp.status == 'success') {
        return resp.data;
      }
      throw Exception(resp.message);
    } catch (e) {
      logger.e("_convertPdfToImages error: $e");
      rethrow;
    }
  }

  Future<String> _convertPdfToImagesPdf(
    String pdfPath,
    String outputPath,
    String pageRange,
  ) async {
    try {
      final imageOutputPath =
          PathUtils.join([await PathUtils.tempDir, Ulid().toString()]);
      final result = await _convertPdfToImages(
        pdfPath,
        imageOutputPath,
        pageRange,
        showProgress: false,
      );
      logger.w("result: $result");
      // 获取输出目录下的所有图片文件
      final files = Directory(imageOutputPath)
          .listSync()
          .whereType<File>()
          .where((file) =>
              file.path.toLowerCase().endsWith('.jpg') ||
              file.path.toLowerCase().endsWith('.png'))
          .map((file) => file.path)
          .toList();
      // 按文件名结尾的页码数字排序
      files.sort((a, b) {
        final aNum = extractNumberSuffix(a);
        final bNum = extractNumberSuffix(b);
        return aNum.compareTo(bNum);
      });
      await _createPdfFromImages(
        files,
        outputPath,
        "sameAsImage",
        "auto",
      );
      return outputPath;
    } catch (e) {
      logger.e("_convertPdfToImagesPDF error: $e");
      rethrow;
    }
  }

  /// 将图片转换为PDF
  Future<String> _convertImagesToPdf(
    List<String> imagePaths,
    String paperSize,
    String orientation,
    bool mergeToOne,
    String sortBy,
    String direction,
    String outputDir,
  ) async {
    try {
      // 创建输出目录
      await Directory(outputDir).create(recursive: true);

      // 对文件进行排序
      final sortedFiles = sortFiles(imagePaths, sortBy, direction);

      if (mergeToOne) {
        // 合并为一个PDF文件
        final outputPath = path.join(
            outputDir, '${'home.tools.imgToPdf'.tr}-${Ulid().toString()}.pdf');
        await _createPdfFromImages(
          sortedFiles,
          outputPath,
          paperSize,
          orientation,
        );
        return outputPath;
      } else {
        // 为每个图片创建单独的PDF
        for (int i = 0; i < sortedFiles.length; i++) {
          final inputPath = sortedFiles[i];
          // 更新进度
          progressController.updateProgress(
            status: "running",
            message: "${'toolbox.background.processingFile'.tr}: ${path.basename(inputPath)}",
            current: i + 1,
            total: sortedFiles.length.toDouble(),
          );
          final fileName = path.basenameWithoutExtension(inputPath);
          final outputPath = path.join(outputDir, '$fileName.pdf');
          await _createPdfFromImages(
            [inputPath],
            outputPath,
            paperSize,
            orientation,
          );
        }
        return outputDir;
      }
    } catch (e) {
      logger.e("_convertImagesToPdf error: $e");
      rethrow;
    }
  }

  /// 创建PDF文档
  Future<void> _createPdfFromImages(
    List<String> imagePaths,
    String outputPath,
    String paperSize,
    String orientation,
  ) async {
    // 创建PDF文档
    final document = sf_pdf.PdfDocument();

    try {
      for (final imagePath in imagePaths) {
        sf_pdf.PdfSection section = document.sections!.add();
        // 读取图片文件
        final imageBytes = await File(imagePath).readAsBytes();
        final image = sf_pdf.PdfBitmap(imageBytes);
        logger.i("image: ${image.width} ${image.height}");
        if (paperSize == 'sameAsImage') {
          // 使用图片原始尺寸
          section.pageSettings.size =
              Size(image.width.toDouble(), image.height.toDouble());
          section.pageSettings.orientation = image.width > image.height
              ? sf_pdf.PdfPageOrientation.landscape
              : sf_pdf.PdfPageOrientation.portrait;
          section.pageSettings.margins.all = 0;
        } else {
          section.pageSettings.size =
              sizeMap[paperSize] ?? sf_pdf.PdfPageSize.a4;
          section.pageSettings.orientation = image.width > image.height
              ? sf_pdf.PdfPageOrientation.landscape
              : sf_pdf.PdfPageOrientation.portrait;
          section.pageSettings.margins.all = 0;
          // 处理页面方向
          if (orientation == 'auto') {
            // 根据图片比例自动选择方向
            section.pageSettings.orientation = image.width > image.height
                ? sf_pdf.PdfPageOrientation.landscape
                : sf_pdf.PdfPageOrientation.portrait;
          } else if (orientation == 'landscape') {
            section.pageSettings.orientation =
                sf_pdf.PdfPageOrientation.landscape;
          } else if (orientation == 'portrait') {
            section.pageSettings.orientation =
                sf_pdf.PdfPageOrientation.portrait;
          }
        }
        sf_pdf.PdfPage page = section.pages.add();
        page.rotation = sf_pdf.PdfPageRotateAngle.rotateAngle0;
        // 计算图片在页面上的位置和大小
        final graphics = page.graphics;
        final pageWidth = page.getClientSize().width;
        final pageHeight = page.getClientSize().height;
        final imageWidth = image.width.toDouble();
        final imageHeight = image.height.toDouble();
        // 计算缩放比例，使图片适应页面
        final widthRatio = pageWidth / imageWidth;
        final heightRatio = pageHeight / imageHeight;
        final scale = widthRatio < heightRatio ? widthRatio : heightRatio;

        final scaledWidth = imageWidth * scale;
        final scaledHeight = imageHeight * scale;
        // 居中绘制图片
        final x = (pageWidth - scaledWidth) / 2;
        final y = (pageHeight - scaledHeight) / 2;

        graphics.drawImage(
          image,
          Rect.fromLTWH(x, y, scaledWidth, scaledHeight),
        );
      }

      // 保存文档
      final bytes = await document.save();
      await File(outputPath).writeAsBytes(bytes);
    } finally {
      // 释放资源
      document.dispose();
    }
  }

  /// 将EPUB转换为PDF
  Future<String> _convertEpubToPdf(
    String epubPath,
    String outputPath,
  ) async {
    try {
      final data = {
        'path': epubPath,
        'output_path': outputPath,
      };
      final resp = await messageController.request(data, 'epub_to_pdf');
      if (resp.status == 'success') {
        return resp.data;
      }
      throw Exception(resp.message);
    } catch (e) {
      logger.e("_convertEpubToPdf error: $e");
      rethrow;
    }
  }

  /// 将MOBI转换为PDF
  Future<String> _convertMobiToPdf(
    String mobiPath,
    String outputPath,
  ) async {
    try {
      final data = {
        'path': mobiPath,
        'output_path': outputPath,
      };
      final resp = await messageController.request(data, 'mobi_to_pdf');
      if (resp.status == 'success') {
        return resp.data;
      }
      throw Exception(resp.message);
    } catch (e) {
      logger.e("_convertMobiToPdf error: $e");
      rethrow;
    }
  }

  /// 将OFD转换为PDF
  Future<String> _convertOFDToPdf(
    String docxPath,
    String outputPath,
  ) async {
    try {
      final data = {
        'path': docxPath,
        'output_path': outputPath,
      };
      final resp = await messageController.request(data, 'ofd_to_pdf');
      if (resp.status == 'success') {
        return resp.data;
      }
      throw Exception(resp.message);
    } catch (e) {
      logger.e("_convertOFDToPdf error: $e");
      rethrow;
    }
  }

  /// 将DOCX转换为PDF
  Future<String> _convertDocxToPdf(
    String docxPath,
    String outputPath,
  ) async {
    try {
      final data = {
        'path': docxPath,
        'output_path': outputPath,
      };
      final resp = await messageController.request(data, 'docx_to_pdf');
      if (resp.status == 'success') {
        return resp.data;
      }
      throw Exception(resp.message);
    } catch (e) {
      logger.e("_convertDOCXToPdf error: $e");
      rethrow;
    }
  }

  /// 将PDF转换为DOCX
  Future<String> _convertPdfToDocx(
    String pdfPath,
    String outputPath,
  ) async {
    try {
      final data = {
        'path': pdfPath,
        'output_path': outputPath,
      };
      final resp = await messageController.request(data, 'pdf_to_docx');
      if (resp.status == 'success') {
        return resp.data;
      }
      throw Exception(resp.message);
    } catch (e) {
      logger.e("_convertPdfToDocx error: $e");
      rethrow;
    }
  }

  /// 将DOCX转换为HTML
  Future<String> _convertDocxToHtml(
    String docxPath,
    String outputPath,
  ) async {
    try {
      final data = {
        'docx_path': docxPath,
        'output_path': outputPath,
        'escape_latex_for_js': false,
      };
      final resp = await messageController.request(data, 'docx_to_html');
      if (resp.status == 'success') {
        return resp.data;
      }
      throw Exception(resp.message);
    } catch (e) {
      logger.e("_convertDocxToHtml error: $e");
      rethrow;
    }
  }

  /// 将HTML转换为PDF
  Future<String> _convertHtmlToPdf(
    String htmlPath,
    String outputPath,
  ) async {
    try {
      // 读取HTML文件内容
      final rawHtmlContent = await File(htmlPath).readAsString();
      final basePath = PathUtils(htmlPath).parent;
      final htmlContent = await replaceHtmlLocalImg(rawHtmlContent, basePath,
          htmlType: "document");

      // 使用HTMLToPdf库将HTML转换为PDF Widgets
      final htmlToPdf = HTMLToPdf();

      // 转换HTML内容到PDF组件
      List<pw.Widget> widgets = await htmlToPdf.convert(
        htmlContent,
        defaultFontFamily: fontName.value,
        defaultFontSize: fontSize.value,
        fontResolver: useCustomFont.value && customFontFilePath.value.isNotEmpty
            ? (fontFamily, bold, italic) async {
                try {
                  // 尝试加载自定义字体
                  final fontFile = File(customFontFilePath.value);
                  if (await fontFile.exists()) {
                    final fontData = await fontFile.readAsBytes();
                    return pw.Font.ttf(fontData.buffer.asByteData());
                  }
                } catch (e) {
                  logger
                      .e("${'toolbox.convert.html2pdf.fontSizeError1'.tr}: $e");
                }
                // 如果自定义字体加载失败，返回系统字体
                return _getFont(fontFamily, bold, italic);
              }
            : null,
      );

      // 创建PDF文档
      final document = pw.Document();

      // 添加到文档中
      document.addPage(
        pw.MultiPage(
          maxPages: 200,
          build: (context) => widgets,
          theme: pw.ThemeData.withFont(
            base: await _getBaseFont(),
            bold: _getBoldFont(),
          ),
        ),
      );

      // 保存PDF文件
      await File(outputPath).writeAsBytes(await document.save());
      return outputPath;
    } catch (e) {
      logger.e("_convertHtmlToPdf error: $e");
      rethrow;
    }
  }

  /// 将MD转换为PDF
  Future<String> _convertMdToPdf(
    String mdPath,
    String outputPath,
  ) async {
    try {
      // 读取Markdown文件内容
      final markdownContent = await File(mdPath).readAsString();
      final rawHtmlContent = markdownToHtml(
        markdownContent,
        extensionSet: ExtensionSet.commonMark,
      );
      final basePath = PathUtils(mdPath).parent;
      final htmlContent = await replaceHtmlLocalImg(rawHtmlContent, basePath,
          htmlType: "document");

      logger.i("htmlContent: $htmlContent");
      // 使用HTMLToPdf库将Markdown转换为PDF Widgets
      final htmlToPdf = HTMLToPdf();

      // 转换Markdown内容到PDF组件
      List<pw.Widget> widgets = await htmlToPdf.convert(
        htmlContent,
        defaultFontFamily: fontName.value,
        defaultFontSize: fontSize.value,
        fontResolver: useCustomFont.value && customFontFilePath.value.isNotEmpty
            ? (fontFamily, bold, italic) async {
                try {
                  // 尝试加载自定义字体
                  final fontFile = File(customFontFilePath.value);
                  if (await fontFile.exists()) {
                    final fontData = await fontFile.readAsBytes();
                    return pw.Font.ttf(fontData.buffer.asByteData());
                  }
                } catch (e) {
                  logger
                      .e("${'toolbox.convert.html2pdf.fontSizeError1'.tr}: $e");
                }
                // 如果自定义字体加载失败，返回系统字体
                return _getFont(fontFamily, bold, italic);
              }
            : null,
      );

      // 创建PDF文档
      final document = pw.Document();

      // 添加到文档中
      document.addPage(
        pw.MultiPage(
          maxPages: 200,
          build: (context) => widgets,
          theme: pw.ThemeData.withFont(
            base: await _getBaseFont(),
            bold: _getBoldFont(),
          ),
        ),
      );

      // 保存PDF文件
      await File(outputPath).writeAsBytes(await document.save());
      return outputPath;
    } catch (e) {
      logger.e("_convertMdToPdf error: $e");
      rethrow;
    }
  }

  /// 将MD转换为HTML
  Future<String> _convertMdToHtml(
    String mdPath,
    String outputPath,
  ) async {
    try {
      final html = markdownToHtml(
        await File(mdPath).readAsString(),
        blockSyntaxes: const [
          FencedCodeBlockSyntax(),
          TableSyntax(),
        ],
        inlineSyntaxes: [
          InlineHtmlSyntax(),
          EmphasisSyntax.asterisk(),
          ImageSyntax(),
          StrikethroughSyntax(),
          EmojiSyntax(),
          ColorSwatchSyntax(),
          AutolinkExtensionSyntax(),
        ],
        extensionSet: ExtensionSet.commonMark,
      );
      await File(outputPath).writeAsString(html);
      return outputPath;
    } catch (e) {
      logger.e("_convertMdToHtml error: $e");
      rethrow;
    }
  }

  // 获取基础字体
  Future<pw.Font> _getBaseFont() async {
    switch (fontName.value) {
      case 'SourceHanSansSC':
        return pw.Font.ttf(
          await rootBundle.load('assets/fonts/SourceHanSansSC-Normal.ttf'),
        );
      case 'Times New Roman':
        return pw.Font.times();
      case 'Courier New':
        return pw.Font.courier();
      case 'Helvetica':
        return pw.Font.helvetica();
      default:
        return pw.Font.helvetica();
    }
  }

  // 获取粗体字体
  pw.Font _getBoldFont() {
    switch (fontName.value) {
      case 'Times New Roman':
        return pw.Font.timesBold();
      case 'Courier New':
        return pw.Font.courierBold();
      case 'Helvetica':
        return pw.Font.helveticaBold();
      default:
        return pw.Font.helveticaBold();
    }
  }

  // 根据字体名称和样式获取字体
  Future<pw.Font> _getFont(String fontFamily, bool bold, bool italic) async {
    if (bold) {
      return _getBoldFont();
    } else {
      return await _getBaseFont();
    }
  }

  void submit(BuildContext context) async {
    logger.i("submit");
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // Validate input files
      if (selectedFilePaths.isEmpty) {
        progressController.updateProgress(
          status: "error", message: 'toolbox.common.selectPdfFiles'.tr,
        );
        return;
      }
      // Process each selected PDF file
      if (convertType.value == 'pdf2img') {
        for (String filePath in selectedFilePaths) {
          // Determine output path
          final pathUtils = PathUtils(filePath);
          String outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_images",
            suffix: "",
            outputDir: outputDir.value,
          );

          // Create output directory
          await Directory(outputPath).create(recursive: true);

          // Convert PDF to images
          final result =
              await _convertPdfToImages(filePath, outputPath, pageRange.value);
          logger.w("result: $result");

          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "running", message: "${'toolbox.background.processingFile'.tr}: ${PathUtils(filePath).name}",
          );
        }
      } else if (convertType.value == 'img2pdf') {
        final outputPath = await _convertImagesToPdf(
          selectedFilePaths,
          paperSize.value,
          orientation.value,
          mergeToOne.value,
          sortBy.value,
          sortDirection.value,
          outputDir.value,
        );
        progressController.outputPath.value = outputPath;
      } else if (convertType.value == 'pdf2img-pdf') {
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "_图片型",
            suffix: ".pdf",
            outputDir: outputDir.value,
          );
          await _convertPdfToImagesPdf(
            filePath,
            outputPath,
            "",
          );
          progressController.outputPath.value = outputPath;
        }
      } else if (convertType.value == 'epub2pdf') {
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "",
            suffix: ".pdf",
            outputDir: outputDir.value,
          );
          await _convertEpubToPdf(
            filePath,
            outputPath,
          );
          progressController.outputPath.value = outputPath;
        }
      } else if (convertType.value == 'mobi2pdf') {
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "",
            suffix: ".pdf",
            outputDir: outputDir.value,
          );
          await _convertMobiToPdf(
            filePath,
            outputPath,
          );
          progressController.outputPath.value = outputPath;
        }
      } else if (convertType.value == 'ofd2pdf') {
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "",
            suffix: ".pdf",
            outputDir: outputDir.value,
          );
          await _convertOFDToPdf(
            filePath,
            outputPath,
          );
          progressController.outputPath.value = outputPath;
        }
      } else if (convertType.value == 'docx2pdf') {
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "",
            suffix: ".pdf",
            outputDir: outputDir.value,
          );
          await _convertDocxToPdf(
            filePath,
            outputPath,
          );
          progressController.outputPath.value = outputPath;
        }
      } else if (convertType.value == 'pdf2docx') {
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "",
            suffix: ".docx",
            outputDir: outputDir.value,
          );
          await _convertPdfToDocx(
            filePath,
            outputPath,
          );
          progressController.outputPath.value = outputPath;
        }
      } else if (convertType.value == 'docx2html') {
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "",
            suffix: ".html",
            outputDir: outputDir.value,
          );
          await _convertDocxToHtml(
            filePath,
            outputPath,
          );
          progressController.outputPath.value = outputPath;
        }
      } else if (convertType.value == 'md2html') {
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "",
            suffix: ".html",
            outputDir: outputDir.value,
          );
          await _convertMdToHtml(
            filePath,
            outputPath,
          );
          progressController.outputPath.value = outputPath;
        }
      } else if (convertType.value == 'html2pdf') {
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "",
            suffix: ".pdf",
            outputDir: outputDir.value,
          );
          await _convertHtmlToPdf(
            filePath,
            outputPath,
          );
          progressController.outputPath.value = outputPath;
        }
      } else if (convertType.value == 'md2pdf') {
        for (String filePath in selectedFilePaths) {
          final outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "",
            suffix: ".pdf",
            outputDir: outputDir.value,
          );
          await _convertMdToPdf(
            filePath,
            outputPath,
          );
          progressController.outputPath.value = outputPath;
        }
      }
      logger.i("completed");
      progressController.updateProgress(
        status: "completed", message: 'toolbox.background.completed'.tr,
      );
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "${'toolbox.common.operationFailed'.tr}: $e",
      );
    }
  }
}
