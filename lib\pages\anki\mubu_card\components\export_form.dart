import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/mubu.dart';

class ExportForm extends GetView<MubuCardPageController> {
  const ExportForm({super.key});

  @override
  Widget build(context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadInputWithValidate(
                  key: ValueKey("cookie-${controller.cookie.value}"),
                  label: 'anki.mubu.cookie'.tr,
                  placeholder: 'anki.mubu.input_cookie'.tr,
                  initialValue: controller.cookie.value,
                  maxLines: 1,
                  onChanged: (value) {},
                  onValidate: (value) async {
                    return await controller.validateCookie(value);
                  }),
              ShadSelectWithSearch(
                key: ValueKey("document-${controller.document.value}"),
                label: 'anki.mubu.target_document'.tr,
                placeholder: 'anki.mubu.select_target_document'.tr,
                searchPlaceholder: 'anki.mubu.input_document'.tr,
                initialValue: controller.document.value.isNotEmpty
                    ? [controller.document.value]
                    : null,
                options: controller.documentList
                    .map((e) => {
                          'value': e['value'] as String,
                          'label': e['label'] as String
                        })
                    .toList(),
                onChanged: (value) {
                  controller.document.value = value.single;
                },
              ),
              ShadRadioGroupCustom(
                key: ValueKey("export-${controller.exportFormat.value}"),
                label: 'anki.common.export_mode'.tr,
                initialValue: controller.exportFormat.value,
                items: controller.exportFormatList,
                onChanged: (value) {
                  controller.exportFormat.value = value;
                },
              ),
              ShadInputWithFileSelect(
                key: ValueKey(controller.outputDir.value),
                title: 'toolbox.common.outputDir'.tr,
                placeholder: Text('toolbox.common.output.error'.tr),
                initialValue: [controller.outputDir.value],
                isFolder: true,
                onFilesSelected: (files) {
                  controller.outputDir.value = files.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {},
              ),
              const SizedBox(height: 8),
            ],
          )),
    );
  }
}
