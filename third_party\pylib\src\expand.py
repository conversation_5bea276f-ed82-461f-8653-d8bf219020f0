import fitz

from .utils import progress_reporter, parse_range


def expand_pdf_by_blank(
    *, 
    doc_path: str = "",
    output_path: str = "",
    top: float = 0,
    bottom: float = 0,
    left: float = 0,
    right: float = 0,
    unit: str = "pt",
    page_range: str = "all",
):
    """
    PDf延展
    :param doc_path: pdf文件路径
    :param top: 上边距
    :param bottom: 下边距
    :param left: 左边距
    :param right: 右边距
    :param unit: 单位，可取值为pt、ratio
    :param page_range: 页码范围
    :param output_path:
    :return:
    """
    doc: fitz.Document = fitz.open(doc_path)
    writer: fitz.Document = fitz.open()
    roi_indices = parse_range(page_range, doc.page_count)
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})

    for idx, page_index in enumerate(roi_indices):
        progress_reporter("processing", f"正在处理第{idx+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
        page = doc[page_index]
        w, h = page.rect.width, page.rect.height
        new_w, new_h = w + left + right, h + top + bottom
        if unit == "ratio":
            new_w, new_h = (1 + left + right)*w, (1 + top + bottom)*h
        new_page = writer.new_page(width=new_w, height=new_h)
        rect = fitz.Rect(left, top, left+w, top+h)
        if unit == "ratio":
            rect = fitz.Rect(left*w, top*h, left*w+w, top*h+h)
        new_page.show_pdf_page(rect, doc, page_index)
    # 恢复书签
    toc = doc.get_toc()
    if toc:
        writer.set_toc(toc)
    writer.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)

def expand_pdf_by_file(
    *, 
    doc_path: str = "",
    output_path: str = "",
    bg_doc_path: str = "",
    direction: str = "right",
    page_range: str = "all",
):
    """
    PDf延展
    :param doc_path: pdf文件路径
    :param output_path: 输出pdf文件路径
    :param bg_doc_path: 背景pdf文件路径
    :param direction: 方向，可取值为right、bottom、left、top
    :param page_range: 页码范围
    :return:
    """
    doc: fitz.Document = fitz.open(doc_path)
    bg_doc: fitz.Document = fitz.open(bg_doc_path)
    bg_page = bg_doc[0]
    bg_w, bg_h = bg_page.rect.width, bg_page.rect.height
    writer: fitz.Document = fitz.open()
    roi_indices = parse_range(page_range, doc.page_count)
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})

    for idx, page_index in enumerate(roi_indices):
        progress_reporter("processing", f"正在处理第{idx+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
        page = doc[page_index]
        w, h = page.rect.width, page.rect.height
        if direction == "right":
            new_w, new_h = w+bg_w, h
            new_page = writer.new_page(width=new_w, height=new_h)
            # 放置原pdf
            rect = fitz.Rect(0, 0, w, h)
            new_page.show_pdf_page(rect, doc, page_index)
            # 放置背景
            rect = fitz.Rect(w, 0, new_w, h)
            new_page.show_pdf_page(rect, bg_doc, 0)
        elif direction == "bottom":
            new_w, new_h = w, h+bg_h
            new_page = writer.new_page(width=new_w, height=new_h)
            # 放置原pdf
            rect = fitz.Rect(0, 0, w, h)
            new_page.show_pdf_page(rect, doc, page_index)
            # 放置背景
            rect = fitz.Rect(0, h, w, new_h)
            new_page.show_pdf_page(rect, bg_doc, 0)
        elif direction == "left":
            new_w, new_h = w+bg_w, h
            new_page = writer.new_page(width=new_w, height=new_h)
            # 放置原pdf
            rect = fitz.Rect(bg_w, 0, w+bg_w, h)
            new_page.show_pdf_page(rect, doc, page_index)
            # 放置背景
            rect = fitz.Rect(0, 0, bg_w, h)
            new_page.show_pdf_page(rect, bg_doc, 0)
        elif direction == "top":
            new_w, new_h = w, h+bg_h
            new_page = writer.new_page(width=new_w, height=new_h)
            # 放置原pdf
            rect = fitz.Rect(0, bg_h, w, h+bg_h)
            new_page.show_pdf_page(rect, doc, page_index)
            # 放置背景
            rect = fitz.Rect(0, 0, w, bg_h)
            new_page.show_pdf_page(rect, bg_doc, 0)
    # 恢复书签
    toc = doc.get_toc()
    if toc:
        writer.set_toc(toc)
    writer.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)