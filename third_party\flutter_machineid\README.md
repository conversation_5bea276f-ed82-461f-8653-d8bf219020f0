# flutter_machineid

A Flutter plugin for getting unique machine identifiers across Windows, macOS, and Linux platforms. This plugin provides the same functionality as the original [machineid](https://github.com/denis<PERSON><PERSON><PERSON>/machineid) library by <PERSON><PERSON><PERSON><PERSON><PERSON>, returning OS native machine UUIDs/GUIDs that are stable across reboots and usually persist through OS updates.

## Features

- **Cross-Platform Support**: Works on Windows, macOS, and Linux
- **Hardware Independent**: No usage of MAC, BIOS, or CPU identifiers (reliable in VM environments)
- **Stable IDs**: Machine IDs are unique to the installed OS and persist across reboots
- **Security**: Provides cryptographically secure HMAC-SHA256 protected IDs
- **No Admin Privileges**: Works without requiring administrator/root privileges

## Platform-Specific Sources

- **Windows**: Uses `MachineGuid` from `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography`
- **macOS**: Uses `IOPlatformUUID` from IOKit
- **Linux**: Uses `/var/lib/dbus/machine-id` or `/etc/machine-id` as fallback

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  flutter_machineid: ^0.0.1
```

## Usage

### Basic Usage

```dart
import 'package:flutter_machineid/flutter_machineid.dart';

// Get the raw machine ID (consider this confidential)
String? machineId = await FlutterMachineid.id;
print('Machine ID: $machineId');

// Get a protected ID (recommended for applications)
String? protectedId = await FlutterMachineid.protectedID('myAppName');
print('Protected ID: $protectedId');
```

### Complete Example

```dart
import 'package:flutter/material.dart';
import 'package:flutter_machineid/flutter_machineid.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _machineId = 'Unknown';
  String _protectedId = 'Unknown';

  @override
  void initState() {
    super.initState();
    _initMachineId();
  }

  Future<void> _initMachineId() async {
    try {
      // Get raw machine ID
      String? id = await FlutterMachineid.id;

      // Get protected ID for your app
      String? protectedId = await FlutterMachineid.protectedID('com.example.myapp');

      setState(() {
        _machineId = id ?? 'Failed to get machine ID';
        _protectedId = protectedId ?? 'Failed to get protected ID';
      });
    } catch (e) {
      setState(() {
        _machineId = 'Error: $e';
        _protectedId = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: Text('Machine ID Example')),
        body: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Machine ID: $_machineId'),
              SizedBox(height: 16),
              Text('Protected ID: $_protectedId'),
            ],
          ),
        ),
      ),
    );
  }
}
```

## API Reference

### FlutterMachineid.id

Returns the platform-specific machine ID of the current host OS.

```dart
static Future<String?> get id
```

**Returns**: The raw machine ID as a string, or `null` if it cannot be determined.

**Security Note**: The returned ID should be considered "confidential" and you should consider using `protectedID` instead for security.

### FlutterMachineid.protectedID(String appId)

Returns a hashed version of the machine ID in a cryptographically secure way, using a fixed, application-specific key.

```dart
static Future<String?> protectedID(String appId)
```

**Parameters**:
- `appId`: A unique identifier for your application (e.g., "com.example.myapp")

**Returns**: A HMAC-SHA256 hash of the machine ID as a hex string, or `null` if the machine ID cannot be determined.

**Security**: This function calculates HMAC-SHA256 of the application ID, keyed by the machine ID. This provides a unique identifier for your application while keeping the original machine ID secure.

## Security Considerations

A machine ID uniquely identifies the host. Therefore it should be considered "confidential", and must not be exposed in untrusted environments.

> **Recommendation**: Use `protectedID()` instead of `id()` for application-specific identification.

The `protectedID()` method provides:
- **Uniqueness**: Properly unique identifier derived from the machine ID
- **Security**: No way to retrieve the original machine ID from the protected version
- **Consistency**: Same protected ID for the same app on the same machine
- **Isolation**: Different apps get different protected IDs on the same machine

## Unique Key Reliability

Machine IDs are usually generated during system installation and stay constant for all subsequent boots. However, note that:

- Machine IDs can be changed by root/admin users (though this may break system services)
- Most IDs won't be regenerated when you clone/image/restore an OS installation
- **Linux**: Users can generate a new ID with `dbus-uuidgen` and update `/var/lib/dbus/machine-id` and `/etc/machine-id`
- **Windows**: Use the `sysprep` toolchain to create proper images for distribution

## Error Handling

The plugin returns `null` when the machine ID cannot be determined. Common scenarios include:

- Insufficient permissions to read system identifiers
- Missing system files or registry keys
- Virtualized environments with incomplete system information

Always check for `null` return values and handle them appropriately in your application.

## Compatibility

- **Flutter**: 3.3.0+
- **Dart**: 3.8.1+
- **Windows**: Windows 7+
- **macOS**: macOS 10.6+
- **Linux**: Any distribution with dbus machine-id support

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Credits

This Flutter plugin is based on the original [machineid](https://github.com/denisbrodbeck/machineid) library by [Denis Brodbeck](https://github.com/denisbrodbeck). The implementation maintains compatibility with the original library's output format and behavior.

