<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <RootNamespace>Converting_Word_to_PDF_document</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CommandLineParser" Version="2.9.1" />
        <PackageReference Include="Syncfusion.DocIORenderer.NET" Version="*" />
        <PackageReference Include="System.Text.Encoding.CodePages" Version="7.0.0" />
    </ItemGroup>

    <ItemGroup>
        <None Update="Data\Template.docx">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Output\.gitkeep">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>
</Project>