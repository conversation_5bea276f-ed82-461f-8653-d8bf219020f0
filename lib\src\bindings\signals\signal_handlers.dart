part of 'signals.dart';

final assignRustSignal = <String, void Function(Uint8List, Uint8List)>{
  'ProgressResponse': (Uint8List messageBytes, Uint8List binary) {
    final message = ProgressResponse.bincodeDeserialize(messageBytes);
    final rustSignal = RustSignalPack(
      message,
      binary,
    );
    _progressResponseStreamController.add(rustSignal);
    ProgressResponse.latestRustSignal = rustSignal;
  },
  'RustRequest': (Uint8List messageBytes, Uint8List binary) {
    final message = RustRequest.bincodeDeserialize(messageBytes);
    final rustSignal = RustSignalPack(
      message,
      binary,
    );
    _rustRequestStreamController.add(rustSignal);
    RustRequest.latestRustSignal = rustSignal;
  },
  'RustResponse': (Uint8List messageBytes, Uint8List binary) {
    final message = RustResponse.bincodeDeserialize(messageBytes);
    final rustSignal = RustSignalPack(
      message,
      binary,
    );
    _rustResponseStreamController.add(rustSignal);
    RustResponse.latestRustSignal = rustSignal;
  },
};
