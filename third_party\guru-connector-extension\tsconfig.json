{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* WXT */
    "types": ["wxt/client-types"],
    "paths": {
      "~/*": ["./*"],
      "@/*": ["./*"]
    }
  },
  "include": [
    "entrypoints/**/*",
    "components/**/*",
    "store/**/*",
    "assets/**/*",
    "public/**/*",
    "*.ts",
    "*.js",
    "*.vue"
  ],
  "exclude": [
    "node_modules",
    "dist",
    ".wxt"
  ]
}
