// ignore_for_file: type=lint, type=warning
part of 'signals.dart';


@immutable
class ProgressResponse {
  /// An async broadcast stream that listens for signals from Rust.
  /// It supports multiple subscriptions.
  /// Make sure to cancel the subscription when it's no longer needed,
  /// such as when a widget is disposed.
  static final rustSignalStream =
      _progressResponseStreamController.stream.asBroadcastStream();
        
  /// The latest signal value received from Rust.
  /// This is updated every time a new signal is received.
  /// It can be null if no signals have been received yet.
  static RustSignalPack<ProgressResponse>? latestRustSignal = null;

  const ProgressResponse({
    required this.status,
    required this.message,
    required this.current,
    required this.total,
  });

  static ProgressResponse deserialize(BinaryDeserializer deserializer) {
    deserializer.increaseContainerDepth();
    final instance = ProgressResponse(
      status: deserializer.deserializeString(),
      message: deserializer.deserializeString(),
      current: deserializer.deserializeFloat64(),
      total: deserializer.deserializeFloat64(),
    );
    deserializer.decreaseContainerDepth();
    return instance;
  }

  static ProgressResponse bincodeDeserialize(Uint8List input) {
    final deserializer = BincodeDeserializer(input);
    final value = ProgressResponse.deserialize(deserializer);
    if (deserializer.offset < input.length) {
      throw Exception('Some input bytes were not read');
    }
    return value;
  }

  final String status;
  final String message;
  final double current;
  final double total;

  ProgressResponse copyWith({
    String? status,
    String? message,
    double? current,
    double? total,
  }) {
    return ProgressResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      current: current ?? this.current,
      total: total ?? this.total,
    );
  }

  void serialize(BinarySerializer serializer) {
    serializer.increaseContainerDepth();
    serializer.serializeString(status);
    serializer.serializeString(message);
    serializer.serializeFloat64(current);
    serializer.serializeFloat64(total);
    serializer.decreaseContainerDepth();
  }

  Uint8List bincodeSerialize() {
      final serializer = BincodeSerializer();
      serialize(serializer);
      return serializer.bytes;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;

    return other is ProgressResponse
      && status == other.status
      && message == other.message
      && current == other.current
      && total == other.total;
  }

  @override
  int get hashCode => Object.hash(
        status,
        message,
        current,
        total,
      );

  @override
  String toString() {
    String? fullString;

    assert(() {
      fullString = '$runtimeType('
        'status: $status, '
        'message: $message, '
        'current: $current, '
        'total: $total'
        ')';
      return true;
    }());

    return fullString ?? 'ProgressResponse';
  }
}

final _progressResponseStreamController =
    StreamController<RustSignalPack<ProgressResponse>>();
