import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';

class PDFExtractPageController extends GetxController {
  // 基本数据
  final extractModeList = [
    {'value': 'page', 'label': 'toolbox.extract.mode.page'.tr},
    {'value': 'text', 'label': 'toolbox.extract.mode.text'.tr},
    {'value': 'image', 'label': 'toolbox.extract.mode.image'.tr},
  ];
  // 表单参数
  final extractMode = "page".obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  final outputDirError = "".obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    // Remove the unsupported check for image extraction since we're implementing it
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      for (String filePath in selectedFilePaths) {
        final pathUtils = PathUtils(filePath);
        String outputPath = "";
        if (extractMode.value == "page") {
          outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_${'toolbox.extract.fileNameAppend'.tr}",
            outputDir: outputDir.value,
          );
          await PDFUtils.extractPages(filePath, pageRange.value, outputPath);
          progressController.outputPath.value = outputPath;
        } else if (extractMode.value == "text") {
          outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_${'toolbox.extract.textAppend'.tr}",
            suffix: ".txt",
            outputDir: outputDir.value,
          );
          // Load the PDF document
          final PdfDocument document = PdfDocument(
            inputBytes: File(filePath).readAsBytesSync(),
          );
          final List<int> pages =
              parsePageRange(pageRange.value, document.pages.count);
          String text = "";
          for (int pageNum in pages) {
            text += PdfTextExtractor(document).extractText(
              startPageIndex: pageNum,
              endPageIndex: pageNum,
            );
            text += "\n\n";
          }
          File(outputPath).writeAsStringSync(text);
          document.dispose();
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'toolbox.common.fileProcessSuccess'.tr,
          );
        } else if (extractMode.value == "image") {
          outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_${'toolbox.extract.imageAppend'.tr}",
            suffix: "",
            outputDir: outputDir.value,
          );

          // Create output directory if it doesn't exist
          final outputDirPath = PathUtils(outputPath);
          await outputDirPath.makeDirs();

          final data = {
            'extract_type': "images",
            'doc_path': filePath,
            'page_range': pageRange.value,
            'output_path': outputPath,
            'image_format': 'png',
            'min_width': 100,
            'min_height': 100,
            'show_progress': true,
          };

          final resp = await messageController.request(data, 'pdf_extract');
          if (resp.status == 'success') {
            progressController.outputPath.value = outputPath;
            progressController.updateProgress(
              status: "completed", message: 'toolbox.common.fileProcessSuccess'.tr,
            );
          } else {
            throw Exception(resp.message);
          }
        }
      }

      progressController.updateProgress(status: "completed", message: "");
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "${'toolbox.common.operationFailed'.tr}: $e",
      );
    }
  }
}
