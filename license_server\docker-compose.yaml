services:
  mongo-anki:
    image: mongo
    container_name: mongo_anki
    network_mode: "host"
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: kevin2li
      MONGO_INITDB_ROOT_PASSWORD: anki_mongo_LK1024
    volumes:
      - ${PWD}/data/db:/data/db
    ports:
      - 9081:27017
  server-anki:
    image: golang:1.22
    container_name: golang_anki
    network_mode: "host"
    restart: always
    environment:
      GO111MODULE: on
      GOPROXY: https://goproxy.cn
    volumes:
      - ${PWD}:/app
    ports:
      - 9082:9082
    working_dir: /app
    command: go mod tidy && go run main.go server
    # command: tail -f /dev/null


# 导出数据库
# docker exec -it mongo_anki bash 
# mongodump -u kevin2li -p anki_mongo_LK1024 --authenticationDatabase admin -d license --archive="all-collections.archive"
# docker cp mongo_anki:/all-collections.archive .

# 导入数据库
# docker exec -it mongo_anki bash
# mongorestore -u kevin2li -p anki_mongo_LK1024 --authenticationDatabase admin -d license --archive="all-collections.archive"
