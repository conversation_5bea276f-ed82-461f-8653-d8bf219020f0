import os
from typing import List, Dict, Any, TypeVar, Generic, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request, HTTPException, Response
from fastapi.responses import JSONResponse
from pymongo import MongoClient
from pydantic import BaseModel, Field
from pydantic.generics import GenericModel
from loguru import logger

# --- 1. 配置 ---
MONGO_URI = os.getenv("MONGO_URI", "******************************************************")
FINAL_DB_NAME = os.getenv("FINAL_DB_NAME", "final_word_db")
FINAL_COLLECTION_NAME = os.getenv("FINAL_COLLECTION_NAME", "words")
MAX_WORDS_LIMIT = 100

# --- 2. 通用 Pydantic 模型和辅助函数 ---

# 定义一个泛型类型变量
T = TypeVar('T')

class StandardResponse(GenericModel, Generic[T]):
    """标准化的 API 响应模型"""
    code: int = Field(0, description="状态码，0 表示成功")
    msg: str = Field("Success", description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")

def create_success_response(data: Any, msg: str = "Success") -> dict:
    """创建成功的 API 响应"""
    return {"code": 0, "msg": msg, "data": data}

# --- 3. FastAPI 应用初始化和生命周期管理 ---

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理器"""
    logger.info("应用启动中... 正在连接数据库...")
    try:
        app.state.mongo_client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
        app.state.mongo_client.admin.command('ping')
        app.state.db_collection = app.state.mongo_client[FINAL_DB_NAME][FINAL_COLLECTION_NAME]
        logger.info(f"成功连接到 MongoDB: {FINAL_DB_NAME}/{FINAL_COLLECTION_NAME}")
    except Exception as e:
        logger.critical(f"无法连接到 MongoDB: {e}")
        raise ConnectionError(f"无法初始化数据库连接: {e}") from e
    yield
    logger.info("应用关闭中... 正在断开数据库连接...")
    if hasattr(app.state, 'mongo_client'):
        app.state.mongo_client.close()
        logger.info("MongoDB 连接已关闭。")

app = FastAPI(
    title="单词词典 API (标准响应版)",
    description=f"一个提供综合词典数据查询的API服务。所有接口均返回 `{'{code, msg, data}'}` 结构。",
    version="1.2.0",
    lifespan=lifespan
)

# --- 4. 自定义异常处理器 ---

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """处理 FastAPI 的 HTTPException，将其格式化为标准响应"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"code": 1, "msg": exc.detail, "data": None},
    )

@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception):
    """处理所有未捕获的服务器内部错误"""
    logger.exception("服务器发生内部错误") # 记录完整的错误堆栈
    return JSONResponse(
        status_code=500,
        content={"code": 500, "msg": "Internal Server Error", "data": None},
    )

# --- 5. Pydantic 请求模型 ---
class WordListRequest(BaseModel):
    words: List[str] = Field(..., example=["analyze", "shape"], description="要查询的单词列表")

# --- 6. API 端点实现 (使用新的响应结构) ---

@app.get(
    "/words",
    response_model=StandardResponse[List[str]], # 使用泛型响应模型
    summary="获取所有单词列表",
)
async def get_all_words(request: Request):
    collection = request.app.state.db_collection
    try:
        cursor = collection.find({}, {"word": 1, "_id": 0})
        word_list = sorted([doc['word'] for doc in cursor if 'word' in doc])
        return create_success_response(data=word_list)
    except Exception as e:
        # 这个异常会被上面的 generic_exception_handler 捕获
        raise e

@app.post(
    "/words/details",
    response_model=StandardResponse[List[Dict[str, Any]]], # 使用泛型响应模型
    summary="获取指定单词的详细信息 (限制数量)",
    description=f"如果列表超过 {MAX_WORDS_LIMIT} 个单词，则只处理前 {MAX_WORDS_LIMIT} 个。",
)
async def get_word_details_with_limit(payload: WordListRequest, request: Request, response: Response):
    collection = request.app.state.db_collection
    original_words = payload.words
    
    if len(original_words) > MAX_WORDS_LIMIT:
        logger.warning(f"请求的单词数量 ({len(original_words)}) 超过了限制 ({MAX_WORDS_LIMIT})。")
        words_to_process = original_words[:MAX_WORDS_LIMIT]
        response.headers["X-Request-Warning"] = f"List truncated. Only the first {MAX_WORDS_LIMIT} words were processed."
    else:
        words_to_process = original_words

    words_to_find = list(set(word.lower() for word in words_to_process))
    query = {"word": {"$in": words_to_find}}
    
    try:
        cursor = collection.find(query, {"_id": 0})
        results = list(cursor)
        return create_success_response(data=results)
    except Exception as e:
        # 这个异常会被上面的 generic_exception_handler 捕获
        raise e

# --- 7. 运行服务器 ---
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api_server:app", host="0.0.0.0", port=8000, reload=True)