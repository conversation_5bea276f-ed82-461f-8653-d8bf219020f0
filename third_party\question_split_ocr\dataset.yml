# =================================================================
# YOLOv8/v5 Dataset Configuration File for Exam Paper Segmentation
# =================================================================

# -----------------------------------------------------------------
# Dataset Paths
# -----------------------------------------------------------------
# 定义数据集的根目录。推荐使用相对路径，这样整个数据集文件夹可以轻松移动。
# '.' 表示当前目录，但更推荐指定一个明确的文件夹名称。
# 例如，如果你的 yaml 文件在 project/ 目录下，数据集在 project/data/ 目录下，
# 可以设置 path: ../data
path: dataset  # TODO: 请修改为你的数据集根目录的相对路径或绝对路径

# 训练集图片所在的文件夹路径 (相对于上面的 path)
train: images/train
# 验证集图片所在的文件夹路径 (相对于上面的 path)
val: images/val
# (可选) 测试集图片所在的文件夹路径 (相对于上面的 path)。强烈建议提供，用于最终的模型评估。
test: images/test


# -----------------------------------------------------------------
# Class Information
# -----------------------------------------------------------------
# 类别数量
nc: 3

# 类别名称列表。顺序非常重要！
# 必须与你标注时生成的 label 文件中的 class_id 对应。
# 0 -> question
# 1 -> answer
names:
  - item
  - question
  - answer

# -----------------------------------------------------------------
# (可选) 下载信息
# 如果你的数据集托管在网上，可以提供下载链接，方便复现。
# download: https://your-dataset-url.zip
# =================================================================