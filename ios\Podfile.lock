PODS:
  - app_links (0.0.2):
    - Flutter
  - audioplayers_da<PERSON>win (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - emoji_picker_flutter (0.0.1):
    - Flutter
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - file_selector_ios (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_js (0.1.0):
    - Flutter
  - flutter_keyboard_visibility_temp_fork (0.0.1):
    - Flutter
  - flutter_localization (0.0.1):
    - Flutter
  - flutter_udid (0.0.1):
    - Flutter
    - SAMKeychain
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - integration_test (0.0.1):
    - Flutter
  - irondash_engine_context (0.0.1):
    - Flutter
  - isar_flutter_libs (1.0.0):
    - Flutter
  - media_kit_libs_ios_video (1.0.4):
    - Flutter
  - media_kit_video (0.0.1):
    - Flutter
  - open_file_ios (0.0.1):
    - Flutter
  - open_file_manager (0.1.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - quill_native_bridge_ios (0.0.1):
    - Flutter
  - rinf (0.1.0):
    - Flutter
  - SAMKeychain (1.5.3)
  - screen_brightness_ios (0.1.0):
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - share_handler_ios (0.0.14):
    - Flutter
    - share_handler_ios/share_handler_ios_models (= 0.0.14)
    - share_handler_ios_models
  - share_handler_ios/share_handler_ios_models (0.0.14):
    - Flutter
    - share_handler_ios_models
  - share_handler_ios_models (0.0.9)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - super_native_extensions (0.0.1):
    - Flutter
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - vibration (1.7.5):
    - Flutter
  - volume_controller (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - emoji_picker_flutter (from `.symlinks/plugins/emoji_picker_flutter/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - file_selector_ios (from `.symlinks/plugins/file_selector_ios/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_js (from `.symlinks/plugins/flutter_js/ios`)
  - flutter_keyboard_visibility_temp_fork (from `.symlinks/plugins/flutter_keyboard_visibility_temp_fork/ios`)
  - flutter_localization (from `.symlinks/plugins/flutter_localization/ios`)
  - flutter_udid (from `.symlinks/plugins/flutter_udid/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - irondash_engine_context (from `.symlinks/plugins/irondash_engine_context/ios`)
  - isar_flutter_libs (from `.symlinks/plugins/isar_flutter_libs/ios`)
  - media_kit_libs_ios_video (from `.symlinks/plugins/media_kit_libs_ios_video/ios`)
  - media_kit_video (from `.symlinks/plugins/media_kit_video/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - open_file_manager (from `.symlinks/plugins/open_file_manager/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - quill_native_bridge_ios (from `.symlinks/plugins/quill_native_bridge_ios/ios`)
  - rinf (from `.symlinks/plugins/rinf/ios`)
  - screen_brightness_ios (from `.symlinks/plugins/screen_brightness_ios/ios`)
  - share_handler_ios (from `.symlinks/plugins/share_handler_ios/ios`)
  - share_handler_ios_models (from `.symlinks/plugins/share_handler_ios/ios/Models`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - super_native_extensions (from `.symlinks/plugins/super_native_extensions/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - volume_controller (from `.symlinks/plugins/volume_controller/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - OrderedSet
    - SAMKeychain
    - SDWebImage
    - SwiftyGif

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  emoji_picker_flutter:
    :path: ".symlinks/plugins/emoji_picker_flutter/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  file_selector_ios:
    :path: ".symlinks/plugins/file_selector_ios/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_js:
    :path: ".symlinks/plugins/flutter_js/ios"
  flutter_keyboard_visibility_temp_fork:
    :path: ".symlinks/plugins/flutter_keyboard_visibility_temp_fork/ios"
  flutter_localization:
    :path: ".symlinks/plugins/flutter_localization/ios"
  flutter_udid:
    :path: ".symlinks/plugins/flutter_udid/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  irondash_engine_context:
    :path: ".symlinks/plugins/irondash_engine_context/ios"
  isar_flutter_libs:
    :path: ".symlinks/plugins/isar_flutter_libs/ios"
  media_kit_libs_ios_video:
    :path: ".symlinks/plugins/media_kit_libs_ios_video/ios"
  media_kit_video:
    :path: ".symlinks/plugins/media_kit_video/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  open_file_manager:
    :path: ".symlinks/plugins/open_file_manager/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  quill_native_bridge_ios:
    :path: ".symlinks/plugins/quill_native_bridge_ios/ios"
  rinf:
    :path: ".symlinks/plugins/rinf/ios"
  screen_brightness_ios:
    :path: ".symlinks/plugins/screen_brightness_ios/ios"
  share_handler_ios:
    :path: ".symlinks/plugins/share_handler_ios/ios"
  share_handler_ios_models:
    :path: ".symlinks/plugins/share_handler_ios/ios/Models"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  super_native_extensions:
    :path: ".symlinks/plugins/super_native_extensions/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  volume_controller:
    :path: ".symlinks/plugins/volume_controller/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  audioplayers_darwin: ccf9c770ee768abb07e26d90af093f7bab1c12ab
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  emoji_picker_flutter: ed468d9746c21711e66b2788880519a9de5de211
  file_picker: 9b3292d7c8bc68c8a7bf8eb78f730e49c8efc517
  file_selector_ios: f92e583d43608aebc2e4a18daac30b8902845502
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_js: 867813c1830e1d9b2c5d547cdb6f61801e2f2145
  flutter_keyboard_visibility_temp_fork: 95b2d534bacf6ac62e7fcbe5c2a9e2c2a17ce06f
  flutter_localization: 72299fb6cb4e51cae587bd953ed0b958040b71e6
  flutter_udid: f7c3884e6ec2951efe4f9de082257fc77c4d15e9
  in_app_purchase_storekit: d1a48cb0f8b29dbf5f85f782f5dd79b21b90a5e6
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  irondash_engine_context: 8e58ca8e0212ee9d1c7dc6a42121849986c88486
  isar_flutter_libs: 9fc2cfb928c539e1b76c481ba5d143d556d94920
  media_kit_libs_ios_video: 5a18affdb97d1f5d466dc79988b13eff6c5e2854
  media_kit_video: 1746e198cb697d1ffb734b1d05ec429d1fcd1474
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  open_file_manager: e18b9214242fd5085928c4e00be3addef2e76419
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  pasteboard: 49088aeb6119d51f976a421db60d8e1ab079b63c
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  quill_native_bridge_ios: f47af4b14e7757968486641656c5d23250cee521
  rinf: 6e8eee5c3ad31f9cee0acaf64518ac3e5266cd0d
  SAMKeychain: 483e1c9f32984d50ca961e26818a534283b4cd5c
  screen_brightness_ios: 28c5fbdb40634de44f86025d84470158ad4df48c
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  share_handler_ios: e2244e990f826b2c8eaa291ac3831569438ba0fb
  share_handler_ios_models: fc638c9b4330dc7f082586c92aee9dfa0b87b871
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  super_native_extensions: b763c02dc3a8fd078389f410bf15149179020cb4
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  vibration: 8e2f50fc35bb736f9eecb7dd9f7047fbb6a6e888
  volume_controller: 3657a1f65bedb98fa41ff7dc5793537919f31b12
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 945c09f847a79982d93c4eb0566eb5df92d7a1c9

COCOAPODS: 1.16.2
