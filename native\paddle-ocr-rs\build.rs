use std::env;
use std::path::PathBuf;

fn main() {
    // println!("cargo:rerun-if-changed=build.rs");

    // let manifest_dir = PathBuf::from(env::var("CARGO_MANIFEST_DIR").unwrap());
    // let libs_dir = manifest_dir.join("libs"); // 假设你的库都在项目内的 libs 文件夹
    // let target = env::var("TARGET").unwrap();

    // println!("cargo:warning=Building for target: {}", target);

    // let lib_path: PathBuf = match target.as_str() {
    //     // --- 自动选择正确的库路径 ---
    //     "aarch64-apple-darwin" => libs_dir.join("onnxruntime-osx-arm64-static_lib-1.22.0/lib"),
    //     "aarch64-linux-android" => {
    //         libs_dir.join("onnxruntime-android-arm64-v8a-static_lib-1.22.0/lib")
    //     }
    //     "aarch64-apple-ios" => libs_dir.join("onnxruntime.xcframework/ios-arm64"),
    //     "x86_64-apple-ios" => libs_dir.join("onnxruntime.xcframework/ios-arm64_x86_64-simulator"),
    //     "aarch64-apple-ios-sim" => libs_dir.join("onnxruntime.xcframework/ios-arm64_x86_64-simulator"),
    //     "x86_64-pc-windows-msvc" => libs_dir.join("onnxruntime-win-x64-static_lib-1.22.0/lib"),
    //     _ => panic!("Unsupported target platform: {}", target),
    // };

    // // --- 只输出 Cargo 允许的 -L 和 -l 标志 ---

    // // 1. 设置库搜索路径 (这替代了 ORT_LIB_LOCATION)
    // if lib_path.exists() {
    //     println!("cargo:rustc-link-search=native={}", lib_path.display());
    // } else {
    //     panic!(
    //         "ONNX Runtime library path does not exist: {}",
    //         lib_path.display()
    //     );
    // }

    // // 2. 链接 onnxruntime 库
   
    // println!("cargo:rustc-link-lib=static=onnxruntime");

    // // 3. 链接平台依赖
    // // 对于 Apple 平台，需要链接 Accelerate 和 CoreFoundation 框架
    // if target.contains("apple") {
    //     println!("cargo:rustc-link-lib=framework=Accelerate");
    //     println!("cargo:rustc-link-lib=framework=CoreFoundation");
    // }

    // // 4. 链接 C++ 标准库和平台特定库
    // if target.contains("windows") && target.contains("msvc") {
    //     // Windows MSVC 需要链接这些系统库
    //     println!("cargo:rustc-link-lib=msvcrt");
    //     println!("cargo:rustc-link-lib=kernel32");
    //     println!("cargo:rustc-link-lib=user32");
    //     println!("cargo:rustc-link-lib=gdi32");
    //     println!("cargo:rustc-link-lib=winspool");
    //     println!("cargo:rustc-link-lib=shell32");
    //     println!("cargo:rustc-link-lib=ole32");
    //     println!("cargo:rustc-link-lib=oleaut32");
    //     println!("cargo:rustc-link-lib=uuid");
    //     println!("cargo:rustc-link-lib=comdlg32");
    //     println!("cargo:rustc-link-lib=advapi32");
    // } else {
    //     // 非 Windows 平台使用 c++
    //     println!("cargo:rustc-link-lib=c++");
    // }
}
