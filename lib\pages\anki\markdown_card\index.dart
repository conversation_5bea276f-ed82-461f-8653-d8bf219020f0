import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/markdown_card.dart';
import 'package:anki_guru/pages/common.dart';
import 'components/cloze_form.dart';
import 'components/qa_form.dart';

class MarkdownCardPage extends StatefulWidget {
  const MarkdownCardPage({super.key});

  @override
  State<MarkdownCardPage> createState() => _MarkdownCardPageState();
}

class _MarkdownCardPageState extends State<MarkdownCardPage> {
  final controller = Get.put(MarkdownCardPageController());
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.markdown_card.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('anki.markdown_card.function_description'.tr, style: defaultPageTitleStyle),
                Text('anki.markdown_card.feature_description'.tr, style: theme.textTheme.muted),
                const SizedBox(height: 16),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 2;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) async {
                        print(controller.tabController.selected);
                        if (controller.tabController.selected == 'cloze') {
                          controller.cardModel.value = "Kevin Text Cloze v3";
                          await controller
                              .updateFieldList(controller.cardModel.value);
                        } else if (controller.tabController.selected == 'qa') {
                          controller.cardModel.value = "Kevin Text QA Card v2";
                          await controller
                              .updateFieldList(controller.cardModel.value);
                        }
                        // 确保更新整个控制器状态
                        controller.update();
                      },
                      tabs: [
                        ShadTab(
                          value: 'cloze',
                          content: const ClozeForm(),
                          width: tabWidth,
                          child: Text('anki.markdown_card.cloze_tab'.tr),
                        ),
                        ShadTab(
                          value: 'qa',
                          content: const QAForm(),
                          width: tabWidth,
                          child: Text('anki.markdown_card.qa_tab'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
