<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON><PERSON>mplates -->
<script>
    // v1.1.8 - https://github.com/SimonLammer/anki-persistence/blob/584396fea9dea0921011671a47a0fdda19265e62/script.js
    if(void 0===window.Persistence){var e="github.com/SimonLammer/anki-persistence/",t="_default";if(window.Persistence_sessionStorage=function(){var i=!1;try{"object"==typeof window.sessionStorage&&(i=!0,this.clear=function(){for(var t=0;t<sessionStorage.length;t++){var i=sessionStorage.key(t);0==i.indexOf(e)&&(sessionStorage.removeItem(i),t--)}},this.setItem=function(i,n){void 0==n&&(n=i,i=t),sessionStorage.setItem(e+i,JSON.stringify(n))},this.getItem=function(i){return void 0==i&&(i=t),JSON.parse(sessionStorage.getItem(e+i))},this.removeItem=function(i){void 0==i&&(i=t),sessionStorage.removeItem(e+i)},this.getAllKeys=function(){for(var t=[],i=Object.keys(sessionStorage),n=0;n<i.length;n++){var s=i[n];0==s.indexOf(e)&&t.push(s.substring(e.length,s.length))}return t.sort()})}catch(n){}this.isAvailable=function(){return i}},window.Persistence_windowKey=function(i){var n=window[i],s=!1;"object"==typeof n&&(s=!0,this.clear=function(){n[e]={}},this.setItem=function(i,s){void 0==s&&(s=i,i=t),n[e][i]=s},this.getItem=function(i){return void 0==i&&(i=t),void 0==n[e][i]?null:n[e][i]},this.removeItem=function(i){void 0==i&&(i=t),delete n[e][i]},this.getAllKeys=function(){return Object.keys(n[e])},void 0==n[e]&&this.clear()),this.isAvailable=function(){return s}},window.Persistence=new Persistence_sessionStorage,Persistence.isAvailable()||(window.Persistence=new Persistence_windowKey("py")),!Persistence.isAvailable()){var i=window.location.toString().indexOf("title"),n=window.location.toString().indexOf("main",i);i>0&&n>0&&n-i<10&&(window.Persistence=new Persistence_windowKey("qt"))}}
  </script>
  <div class="detail-body">
    <div class="question">
      <div class="q-header">
        <span>
          <img src="__bookmark.svg" />
          <b id="q_type"></b>
        </span>
        <a class="submit-btn" onclick="openPopup()">
          <img src="__gear.svg"  style="width:1.5em"/>
        </a>
      </div>
      <hr />
      <div class="q-body">{{Question}}</div>
      <div id="q-clozes"></div>
      <div class="q-answers" id="q-options">{{Options}}</div>
      <div id="show_deck"></div>
      <div id="show_tag"></div>
      <div><a onclick="recover_order()" style="display:none;" id="recover_order">还原顺序</a></div>
    </div>
  </div>
  <hr  />
  <div id="choice_div">
    <div class="answerbox" style="display:none;">
        <div class="zhegnque">
            正确答案<span class="correct">A</span>
        </div>
        <div class="nide">
            你的答案<span class="youranswer">B</span>
        </div>
    </div>
    <div class="detail-body solution" style="margin-top: 16px">
      <div class="q-header">
        <span>
          <img src="__bookmark.svg" />
          <b> 答案解析 </b>
        </span>
      </div>
      <div class="explain">
        <div class="answer" id="answer_div"></div>
        <div class="detail line-feed">{{Remarks}}</div>
      </div>
    </div>
    
    {{#Notes}}
    <div class="detail-body solution" style="margin-top: 16px">
      <div class="q-header">
        <span>
          <img src="__bookmark.svg" />
          <b> 我的笔记 </b>
        </span>
      </div>
      <div class="explain">{{Notes}}</div>
    </div>
    {{/Notes}}
    <div id="statis_div">
        <table id="stats_table">
          <thead>
            <tr>
              <td>本次作答题数</td>
              <td>答对题数</td>
              <td>答错题数</td>
              <td>正确率</td>
            </tr>
          </thead>
          <tbody>
            <tr id="stats_tr">
              <td>5</td>
              <td>2</td>
              <td>3</td>
              <td>33.33%</td>
            </tr>
          </tbody>
        </table>
    </div>
  </div>
  <div id="audio" style="visibility: hidden">>
    <audio controls src="__correct.mp3" id="correct_audio"></audio>
    <audio controls src="__wrong.mp3" id="wrong_audio"></audio>
  </div>
  <div id="settingsModal" class="settings-container" style="display: none">
    <div class="modal-content">
      <span class="close-popup" onclick="closePopup()">&times;</span>
      <div class="settings-block">
        <div class="single-setting">
          <div class="setting-label">显示题型</div>
          <div class="setting-switch">
            <input
              id="show-type"
              class="mui-switch mui-switch-anim"
              type="checkbox"
            />
          </div>
        </div>
        <div class="single-setting">
          <div class="setting-label">显示牌组</div>
          <div class="setting-switch">
            <input
              id="show-deck"
              class="mui-switch mui-switch-anim"
              type="checkbox"
            />
          </div>
        </div>
        <div class="single-setting">
          <div class="setting-label">显示标签</div>
          <div class="setting-switch">
            <input
              id="show-tag"
              class="mui-switch mui-switch-anim"
              type="checkbox"
            />
          </div>
        </div>
        <div class="single-setting">
          <div class="setting-label">随机选项</div>
          <div class="setting-switch">
            <input
              id="random-option"
              class="mui-switch mui-switch-anim"
              type="checkbox"
            />
          </div>
        </div>
        <div class="single-setting">
          <div class="setting-label">自动翻转</div>
          <div class="setting-switch">
            <input
              id="auto-flip"
              class="mui-switch mui-switch-anim"
              type="checkbox"
            />
          </div>
        </div>
        <div class="single-setting">
          <div class="setting-label">做题统计</div>
          <div class="setting-switch">
            <input
              id="statistics"
              class="mui-switch mui-switch-anim"
              type="checkbox"
            />
          </div>
        </div>
        <div class="single-setting">
          <div class="setting-label">播放音效</div>
          <div class="setting-switch">
            <input
              id="play-sound"
              class="mui-switch mui-switch-anim"
              type="checkbox"
            />
          </div>
        </div>
        <div class="single-setting">
          <div class="setting-label">填空模式</div>
          <div class="setting-switch">
            <input
              id="cloze-mode"
              class="mui-switch mui-switch-anim"
              type="checkbox"
            />
          </div>
        </div>
        <div class="single-setting">
          <div class="setting-label">夜间模式</div>
          <div class="setting-switch">
            <input
              id="night-mode"
              class="mui-switch mui-switch-anim"
              type="checkbox"
            />
          </div>
        </div>
    </div>
  </div>
  </div>
  <div id="options_data" style="display:none">{{Options}}</div>
  <script>
    // 打开弹窗的函数
    function openPopup() {
      document.getElementById("settingsModal").style.display = "block";
    }
  
    // 关闭弹窗的函数
    function closePopup() {
      document.getElementById("settingsModal").style.display = "none";
    }
    var show_type     = document.getElementById("show-type");
    var show_deck     = document.getElementById("show-deck");
    var show_tag      = document.getElementById("show-tag");
    var random_option = document.getElementById("random-option");
    var auto_flip     = document.getElementById("auto-flip");
    var statistics    = document.getElementById("statistics");
    var play_sound    = document.getElementById("play-sound");
    var cloze_mode    = document.getElementById("cloze-mode");
    var night_mode    = document.getElementById("night-mode");
    function update_config() {
      show_type.checked     = Persistence.getItem("show_type");
      show_deck.checked     = Persistence.getItem("show_deck");
      show_tag.checked      = Persistence.getItem("show_tag");
      random_option.checked = Persistence.getItem("random_option");
      auto_flip.checked     = Persistence.getItem("auto_flip");
      statistics.checked    = Persistence.getItem("statistics");
      play_sound.checked    = Persistence.getItem("play_sound");
      cloze_mode.checked    = Persistence.getItem("cloze_mode");
      night_mode.checked    = Persistence.getItem("night_mode");
    }
  
    show_type.onchange = function () {
      if (Persistence.isAvailable()) {
        Persistence.setItem("show_type", show_type.checked);
        update_type();
      }
    };
    show_deck.onchange = function () {
      if (Persistence.isAvailable()) {
          Persistence.setItem("show_deck", show_deck.checked);
          update_deck();
      }
    };
    show_tag.onchange = function () {
      if (Persistence.isAvailable()) {
          Persistence.setItem("show_tag", show_tag.checked);
          update_tag();
      }
    };
    random_option.onchange = function () {
      if (Persistence.isAvailable()) {
        Persistence.setItem("random_option", random_option.checked);
        updateOptions();
      }
    };
    auto_flip.onchange = function () {
      if (Persistence.isAvailable()) {
        Persistence.setItem("auto_flip", auto_flip.checked);
      }
    };
    statistics.onchange = function () {
      if (Persistence.isAvailable()) {
        Persistence.setItem("statistics", statistics.checked);
      }
    };
    play_sound.onchange = function () {
      if (Persistence.isAvailable()) {
        Persistence.setItem("play_sound", play_sound.checked);
      }
    }
    cloze_mode.onchange = function () {
      if (Persistence.isAvailable()) {
        Persistence.setItem("cloze_mode", cloze_mode.checked);
        update_config();
        update_type();
        update_tag();
        update_deck();
        if(cloze_mode.checked) {
          updateClozes();
        }else{
          var choice_div = document.getElementById("choice_div");
          choice_div.style.display = "block";
          var q_options_div = document.getElementById("q-options");
          q_options_div.style.display = "block";
          var q_cloze_div = document.getElementById("q-clozes");
          q_cloze_div.style.display = "none";
          show_answers();
          updateOptions();
          updateStats();
          updateOptions();
        }
      }
    }
    night_mode.onchange = function () {
      if (Persistence.isAvailable()) {
        Persistence.setItem("night_mode", night_mode.checked);
      }
      update_nightmode();
    }
    function update_nightmode(){
      var roi_selectors = ["#show_deck",  "#show_tag", "#q_type", "#q-clozes", ".q-header", ".detail-body", ".question", ".q-body", ".q-answer", ".q-answers", ".answer", ".q-deck", ".q-tag", ".explain", "table", "thead tr"];
      for(const name of roi_selectors){
        try{
          [].forEach.call(document.querySelectorAll(name), (ele)=>{
            if(night_mode.checked){
              if(!ele.classList.contains("nightMode")){
                ele.classList.toggle("nightMode");
              }
            }else{
              if(ele.classList.contains("nightMode")){
                ele.classList.toggle("nightMode");
              }
            }
          });
        }catch(err){
          console.log(err)
        }
      }
    }
    function update_type() {
      var q_type = document.getElementById("q_type");
      var answers = `{{Answers}}`.split("||");
      if (Persistence.isAvailable()) {
        var show_type = Persistence.getItem("show_type");
        if(!Persistence.getItem("cloze_mode")){
          if (show_type) {
            if (answers.length > 1) {
              q_type.innerText = "多选题";
            } else {
              q_type.innerText = "单选题";
            }
          } else {
            q_type.innerText = "选择题";
          }
        }else{
          q_type.innerText = "填空题";
        }
      }
    }
    function update_tag() {
      if (Persistence.isAvailable()) {
        var show_tag_div = document.getElementById("show_tag");
        if(Persistence.getItem("show_tag")) {
          show_tag_div.style.display = "block";
          if(`{{Tags}}`.trim() == "") {
            show_tag_div.innerHTML = "<div style='color:#707070'><b>标签:</b> 无</div>";
          }else{
            var tags = `{{Tags}}`.split(" ");
            tag_html = "<b style='color:#707070'>标签:</b>"
            for(var i = 0; i < tags.length; i++) {
              let span = document.createElement("span");
              span.className = "q-tag";
              span.innerText = tags[i];
              tag_html += span.outerHTML;
            }
            show_tag_div.innerHTML = tag_html;
          }
        }else{
          show_tag_div.innerHTML = "";
          show_tag_div.style.display = "none";
        }
        update_nightmode();
      }
    };
    function update_deck() {
        if (Persistence.isAvailable()) {
          var show_deck_div = document.getElementById("show_deck");
          if(Persistence.getItem("show_deck")) {
            show_deck_div.style.display = "block";
            show_deck_div.innerHTML = `<div style='color:#707070'><b>牌组:</b><span class="q-deck">{{Deck}}</span>`;
          }else{
            show_deck_div.innerHTML = "";
            show_deck_div.style.display = "none";
          }
          update_nightmode();
        }
    };
    // 显示答案
    function show_answers(option_recover=false) {
      var options = `{{Options}}`.split("||");
      var correctAnswers = `{{Answers}}`.split("||");
      var selectedOptions = [];
      if (Persistence.isAvailable()) {
        selectedOptions = Persistence.getItem("selectedOptions");
      }
      var answer_div = document.getElementById("answer_div");
      var ans_letters = [];
      var selected_letters = [];
      var seqs = [...Array(options.length).keys()].map((x) => x);
      if (Persistence.isAvailable()) {
        if(!option_recover) {
          seqs = Persistence.getItem("seqs");
        }
      }
      console.log({seqs});
      console.log({selectedOptions});
      for (const [i, val] of seqs.entries()) {
        console.log(i, val);
        if (correctAnswers.includes((val + 1).toString())) {
          if(option_recover){
            ans_letters.push(String.fromCharCode(65 + val));
          }else{
            ans_letters.push(String.fromCharCode(65 + i));
          }
        }
        if (selectedOptions.includes((val + 1).toString())) {
          if(option_recover){
            selected_letters.push(String.fromCharCode(65 + val));
          }else{
            selected_letters.push(String.fromCharCode(65 + i));
          }
        }
      }
      console.log({ ans_letters, selected_letters });
      var ans_letters_str = ans_letters.join("");
      var selected_letters_str = selected_letters.join("");
      var is_correct = false;
      if (ans_letters_str == selected_letters_str) {
        answer_div.innerHTML = `正确答案: <font color="green">${ans_letters_str}</font>&nbsp;&nbsp;你的答案：<font color="green">${selected_letters_str}</font>`;
        is_correct = true;
      } else {
        answer_div.innerHTML = `正确答案: <font color="green">${ans_letters_str}</font>&nbsp;&nbsp;你的答案：<font color="#ff0000">${selected_letters_str}</font>`;
      }
      if(option_recover){
        return;
      }
      if(Persistence.isAvailable()){
        var is_play_sound = Persistence.getItem("play_sound");
        if(is_play_sound){
          if(is_correct){
            var audio = document.getElementById("correct_audio");
            audio.play();
          }else{
            var audio = document.getElementById("wrong_audio");
            audio.play();
          }
        }
      }
      if (Persistence.isAvailable()) {
        if (ans_letters_str == selected_letters_str) {
          if (
            Persistence.getItem("correct") == null ||
            Persistence.getItem("correct") == undefined
          ) {
            Persistence.setItem("correct", 1);
          } else {
            Persistence.setItem("correct", Persistence.getItem("correct") + 1);
          }
        } else {
          if (
            Persistence.getItem("wrong") == null ||
            Persistence.getItem("wrong") == undefined
          ) {
            Persistence.setItem("wrong", 1);
          } else {
            Persistence.setItem("wrong", Persistence.getItem("wrong") + 1);
          }
        }
      }
    }
    // 更新选项
    function updateOptions(option_recover = false) {
      var options = document.getElementById("options_data").innerHTML.split("||");
      var correctAnswers = `{{Answers}}`.split("||");
      var selectedOptions = [];
      if (Persistence.isAvailable()) {
        selectedOptions = Persistence.getItem("selectedOptions");
      }
      var q_options_div = document.getElementById("q-options");
      q_options_div.innerHTML = "";
      var seqs = [...Array(options.length).keys()].map((x) => x);
      if (Persistence.isAvailable()) {
        if(!option_recover){
          seqs = Persistence.getItem("seqs");
        }
      }
      for (const [i, data] of seqs.entries()) {
        var option_div = document.createElement("div");
        let val = (data + 1).toString();
        option_div.setAttribute("data", val);
        option_div.innerHTML = `<span>${String.fromCharCode(65 + i)}. ${options[data]}</span>`;
        if (selectedOptions.includes(val) && correctAnswers.includes(val)) {
          option_div.className = "q-answer correct";
          option_div.innerHTML += `<img src="__correct.svg" class="icon" />`;
        } else if (
          selectedOptions.includes(val) &&
          !correctAnswers.includes(val)
        ) {
          option_div.className = "q-answer false";
          option_div.innerHTML += `<img src="__error.svg" class="icon" />`;
        } else if (
          !selectedOptions.includes(val) &&
          correctAnswers.includes(val)
        ) {
          if (correctAnswers.length > 1) {
            option_div.className = "q-answer should-select";
            option_div.innerHTML += `<img src="__warning.png" class="icon" />`;
          } else {
            option_div.className = "q-answer correct";
            option_div.innerHTML += `<img src="__correct.svg" class="icon" />`;
          }
        } else {
          option_div.className = "q-answer normal";
        }
        if (option_recover) {
          option_div.className += " reorder";
          option_div.style.animation = 'reorder 1s forwards';
        }
        q_options_div.appendChild(option_div);
      }
    }
    //   显示统计结果
    function updateStats() {
      var statis_div = document.getElementById("statis_div");
      if (Persistence.isAvailable()) {
        var is_show_stats = Persistence.getItem("statistics");
        if (!is_show_stats) {
          statis_div.style.display = "none";
          return;
        }
      }
      var correct_count = 0;
      var wrong_count = 0;
      if (Persistence.isAvailable()) {
        correct_count = Persistence.getItem("correct") ?? 0;
        wrong_count = Persistence.getItem("wrong") ?? 0;
      }
      var total_count = correct_count + wrong_count;
      var percent = (correct_count / total_count) * 100;
      var stats_tr = document.getElementById("stats_tr");
      stats_tr.innerHTML = `
      <td>${total_count}</td>
      <td>${correct_count}</td>
      <td>${wrong_count}</td>
      <td>${percent.toFixed(2)}%</td>
      `;
    }
  
  
    function updateClozes() {
      var mask = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
      var correctAnswers = `{{Answers}}`.split("||");
      var choice_div = document.getElementById("choice_div");
      choice_div.style.display = "none";
      var q_options_div = document.getElementById("q-options");
      q_options_div.style.display = "none";
      var q_cloze_div = document.getElementById("q-clozes");
      var options = `{{Options}}`.split("||");
      let final_html_content = "";
      for(let i=0; i<correctAnswers.length; i++){
        let option_content = options[parseInt(correctAnswers[i])-1];
        let span = document.createElement("span");
        span.setAttribute("cloze_id", `c${i}`);
        span.setAttribute("cloze_text", `${option_content}`);
        span.innerHTML = option_content;
        span.className = "cloze-show";
        final_html_content += `${span.outerHTML}`;
        if(i!= correctAnswers.length-1) final_html_content += "，";
      }
      q_cloze_div.innerHTML = final_html_content;
      document.querySelectorAll(".cloze-hide, .cloze-show").forEach(ele => {
          let cloze_id = ele.getAttribute("cloze_id");
          ele.onclick = () => {
              [].forEach.call(q_cloze_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                  if (ele.className === 'cloze-hide') {
                      ele.innerHTML = ele.getAttribute("cloze_text");
                      ele.className = "cloze-show";
                  } else {
                      ele.innerHTML = mask;
                      ele.className = "cloze-hide";
                  }
              })
          }
      });
    }
  
    function recover_order(){
      show_answers(true);
      updateOptions(true);
      updateStats();
      update_nightmode();
    }
    if (Persistence.isAvailable()) {
      update_config();
      update_type();
      update_tag();
      update_deck();
      // 非填空模式
      if(!Persistence.getItem("cloze_mode")){
        show_answers();
        updateOptions();
        updateStats();
        // 选项还原
        console.log("random_option:", Persistence.getItem("random_option"));
        if(Persistence.getItem("random_option")){
          document.getElementById("recover_order").style.display = "block";
        }
      }else{
        // 填空模式
        updateClozes();
      }
      update_nightmode();
    }
  </script>
  