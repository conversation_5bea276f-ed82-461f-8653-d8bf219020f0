package top.kevin2li.guru

import java.io.File

internal class KevinImageClozeModel {
    companion object {
        @JvmField // required for API
        var NAME = "Kevin Image Cloze v5"

        @JvmField // required for API
        var FIELDS =
                arrayOf(
                        "ID",
                        "Header",
                        "Image",
                        "Text",
                        "Masks",
                        "Source",
                        "Notes",
                        "Mode",
                        "Index",
                        "Colors",
                        "Reversed"
                )

        // List of card names that will be used in AnkiDroid (one for each direction of learning)
        @JvmField // required for API
        val CARD_NAMES = arrayOf("Card 1")

        // Template for the question of each card - load from file
        @JvmField // required for API
        val QFMT = arrayOf(readTemplateFile("../../../../../../../third_party/android_templates/kevin_image_cloze/front.html"))

        // Template for the answer of each card - load from file
        @JvmField // required for API
        val AFMT = arrayOf(readTemplateFile("../../../../../../../third_party/android_templates/kevin_image_cloze/back.html"))

        // CSS styling - load from file
        @JvmField // required for API
        var CSS = readTemplateFile("../../../../../../../third_party/android_templates/kevin_image_cloze/style.css")

        private fun readTemplateFile(path: String): String {
            return try {
                val currentDir =
                        File(javaClass.protectionDomain.codeSource.location.toURI()).parentFile
                File(currentDir, path).readText()
            } catch (e: Exception) {
                throw IllegalStateException("Error reading template file: $path", e)
            }
        }
    }
}
