import argparse
import json
import math
import sys
import time
from urllib.parse import quote_plus

import requests
from loguru import logger
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure

# --- 1. 日志配置 (使用 loguru) ---
logger.remove()
logger.add(
    sys.stderr, level="INFO",
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    colorize=True
)
logger.add(
    "word_tool.log", level="DEBUG", rotation="10 MB", retention="7 days",
    backtrace=True, diagnose=True,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
)

# --- 2. 配置区域 ---
# 将配置移入函数或在主逻辑中处理，使其更灵活

# API 和请求头信息
API_URL = "https://resource.baicizhan.com/api/resource/xMode"
HEADERS = {
    "content-type": "application/json;",
    "origin": "https://learn.baicizhan.com",
    "x-requested-with": "com.jiongji.andriod.card",
    "sec-fetch-site": "same-site",
    "sec-fetch-mode": "cors",
    "sec-fetch-dest": "empty",
    "referer": "https://learn.baicizhan.com/",
    "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    "cookie": 'access_token="MeZPpzNmThMOFokECC7KQFIcy3DjJ3yOt3tzPTXJ%2Fj8%3D"; Pay-Support-H5="alipay_mob_client:weixin_app"; device_name="android/HD1910-OnePlus"; bcz_dmid="0f6c285f"; device_version="9"; device_id="95f69a401283fca0"; app_name="7080600"; channel="Rongyao"; client_time="1752377022"'
}

# --- 3. 核心功能函数 ---

def get_db_collection(mongo_uri, db_name, collection_name):
    """连接数据库并返回集合对象，处理连接和认证。"""
    try:
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        # 验证连接和认证是否成功
        client.admin.command('ping')
        db = client[db_name]
        collection = db[collection_name]
        logger.info(f"成功连接到 MongoDB，使用数据库 '{db_name}' 和集合 '{collection_name}'。")
        return client, collection
    except ConnectionFailure:
        logger.critical(f"无法连接到 MongoDB (URI: {mongo_uri})。请检查服务是否正在运行以及网络连接。")
        return None, None
    except OperationFailure as e:
        logger.critical(f"MongoDB 认证失败: {e.details.get('errmsg', '未知认证错误')}")
        logger.critical("请检查您的 MONGO_URI 中的用户名、密码和 authSource 是否正确。")
        return None, None
    except Exception:
        logger.exception("连接 MongoDB 时发生未知错误。")
        return None, None

# --- 功能 1: fetch ---

def extract_unique_topic_ids(file_path):
    """从JSON文件中提取并去重 topic_id。"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        topic_ids = {item['topic_id'] for item in data if 'topic_id' in item}
        logger.success(f"成功从 '{file_path}' 文件中提取到 {len(topic_ids)} 个唯一的 topic_id。")
        return list(topic_ids)
    except FileNotFoundError:
        logger.error(f"文件 '{file_path}' 未找到。")
        return []
    except Exception:
        logger.exception("提取 topic_id 时发生错误")
        return []

def get_existing_topic_ids(collection):
    """从数据库查询已存在的 topic_id。"""
    try:
        cursor = collection.find({}, {"resource.word.topicId": 1, "_id": 0})
        existing_ids = {item['resource']['word']['topicId'] for item in cursor if 'resource' in item and 'word' in item['resource'] and 'topicId' in item['resource']['word']}
        logger.info(f"数据库中已存在 {len(existing_ids)} 条记录。")
        return existing_ids
    except Exception:
        logger.exception("查询数据库时发生错误")
        return set()

def fetch_and_store_data(collection, topic_ids_batch, book_id):
    """请求 API 并存储数据。"""
    payload = {"bookId": book_id, "topicIds": topic_ids_batch}
    logger.info(f"正在请求 {len(topic_ids_batch)} 个 topic_ids: {topic_ids_batch}...")
    try:
        response = requests.post(API_URL, headers=HEADERS, json=payload, timeout=15)
        response.raise_for_status()
        response_data = response.json()

        if response_data.get('code') == 1 and 'data' in response_data and response_data['data']:
            items = response_data['data']
            for item in items:
                if 'resource' in item and 'word' in item['resource'] and 'topicId' in item['resource']['word']:
                    query = {"resource.word.topicId": item['resource']['word']['topicId']}
                    collection.update_one(query, {"$set": item}, upsert=True)
            logger.success(f"成功获取并存储了 {len(items)} 条数据。")
        else:
            logger.warning(f"API请求成功，但未返回有效数据。响应: {response_data.get('message', '无消息')}")
    except requests.exceptions.RequestException as e:
        logger.error(f"API请求失败: {e} | Payload: {payload}")
    except Exception:
        logger.exception(f"处理数据或存储到数据库时发生未知错误 | Payload: {payload}")

@logger.catch
def run_fetch(args):
    """执行 fetch 子命令的逻辑。"""
    logger.info(f"开始执行 'fetch' 任务，文件: {args.file}, book_id: {args.book_id}")
    
    client, collection = get_db_collection(args.mongo_uri, args.db, args.collection)
    if collection is None:
        return

    all_ids = extract_unique_topic_ids(args.file)
    if not all_ids:
        logger.warning("未提取到任何 topic_id，任务终止。")
        client.close()
        return

    existing_ids = get_existing_topic_ids(collection)
    ids_to_fetch = [tid for tid in all_ids if tid not in existing_ids]

    if not ids_to_fetch:
        logger.info("所有 topic_id 均已存在于数据库中，无需请求。")
    else:
        logger.info(f"总计 {len(ids_to_fetch)} 个新的 topic_id 需要请求 API。")
        total_batches = math.ceil(len(ids_to_fetch) / args.batch_size)
        for i in range(total_batches):
            batch = ids_to_fetch[i * args.batch_size:(i + 1) * args.batch_size]
            logger.info(f"--- 处理批次 {i+1}/{total_batches} ---")
            fetch_and_store_data(collection, batch, args.book_id)
            if i < total_batches - 1:
                logger.debug(f"等待 {args.delay} 秒...")
                time.sleep(args.delay)
        logger.success("所有批次处理完毕！")

    client.close()
    logger.info("MongoDB 连接已关闭。")

# --- 功能 2: query ---

@logger.catch
def run_query(args):
    """执行 query 子命令的逻辑。"""
    logger.info(f"开始执行 'query' 任务，查询单词: '{args.word}'")

    client, collection = get_db_collection(args.mongo_uri, args.db, args.collection)
    if collection is None:
        return

    # 查询条件：resource.word.word 字段等于指定的单词
    query = {"resource.word.word": args.word}
    logger.debug(f"正在数据库中执行查询: {query}")
    
    result = collection.find_one(query)

    if result:
        logger.success(f"找到了单词 '{args.word}' 的数据：")
        # 使用 json.dumps 美化输出
        # indent=2 用于缩进，ensure_ascii=False 保证中文正常显示
        pretty_result = json.dumps(result, indent=2, ensure_ascii=False, default=str)
        print(pretty_result)
    else:
        logger.warning(f"在数据库中未找到单词 '{args.word}'。")

    client.close()
    logger.info("MongoDB 连接已关闭。")


# --- 功能 3: list (新功能) ---
@logger.catch
def run_list(args):
    """执行 list 子命令的逻辑。"""
    logger.info("开始执行 'list' 任务，列出数据库中的所有单词。")
    client, collection = get_db_collection(args.mongo_uri, args.db, args.collection)
    if collection is None:
        logger.error("无法获取数据库集合，'list' 任务终止。")
        if client: client.close()
        return

    words = []
    try:
        # 使用 projection 只返回需要的字段，极大提高效率
        # {"resource.word.word": 1, "_id": 0} 表示只返回 word 字段，不返回 _id
        logger.debug("正在查询数据库获取所有单词...")
        cursor = collection.find({}, {"resource.word.word": 1, "_id": 0})
        for doc in cursor:
            # 健壮性检查，防止部分文档结构不一致
            if 'resource' in doc and 'word' in doc.get('resource', {}) and 'word' in doc.get('resource', {}).get('word', {}):
                words.append(doc['resource']['word']['word'])
        
        words.sort() # 按字母顺序排序
        logger.info(f"共找到 {len(words)} 个单词。")

        # 根据 --output 参数决定输出到文件还是控制台
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                for word in words:
                    f.write(f"{word}\n")
            logger.success(f"已将所有单词保存到文件: {args.output}")
        else:
            logger.info("将在控制台打印所有单词:")
            for word in words:
                print(word)

    except Exception:
        logger.exception("在列出单词过程中发生错误。")
    finally:
        client.close()
        logger.info("MongoDB 连接已关闭。")


# --- 4. 命令行解析 ---

def main():
    parser = argparse.ArgumentParser(
        description="百词斩单词数据工具，支持获取、查询和列出单词。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    # 通用参数
    parser.add_argument("--mongo-uri", default="******************************************************", help="MongoDB 连接字符串 (URI)。\n示例: ************************:port/?authSource=admin")
    parser.add_argument("--db", default="baicizhan_db", help="MongoDB 数据库名称。")
    parser.add_argument("--collection", default="baicizhan", help="MongoDB 集合名称。")

    subparsers = parser.add_subparsers(dest="command", required=True, help="可用的子命令")

    # 定义 'fetch' 子命令
    parser_fetch = subparsers.add_parser("fetch", help="从JSON文件获取topic_id，请求API并存入数据库。")
    parser_fetch.add_argument("file", help="包含 topic_id 的 JSON 文件路径。")
    parser_fetch.add_argument("book_id", type=int, help="百词斩的 book_id。")
    parser_fetch.add_argument("--batch-size", type=int, default=5, help="每次API请求的 topic_id 数量。")
    parser_fetch.add_argument("--delay", type=float, default=1.0, help="每次API请求之间的延迟（秒）。")
    parser_fetch.set_defaults(func=run_fetch)

    # 定义 'query' 子命令
    parser_query = subparsers.add_parser("query", help="根据单词在数据库中查询其详细信息。")
    parser_query.add_argument("word", help="要查询的英文单词。")
    parser_query.set_defaults(func=run_query)

    # 定义 'list' 子命令 (新)
    parser_list = subparsers.add_parser("list", help="列出数据库中的所有单词。")
    parser_list.add_argument("-o", "--output", help="可选的文件路径，用于保存单词列表 (例如: words.txt)。")
    parser_list.set_defaults(func=run_list)
    
    try:
        args = parser.parse_args()
        args.func(args)
    except Exception:
        logger.exception("程序在解析参数或执行主函数时发生严重错误。")

if __name__ == "__main__":
    main()