import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'package:anki_guru/controllers/common/setting_controller.dart';
import 'package:anki_guru/controllers/storage.dart';

/// 字符串扩展，强化翻译功能
extension TranslationExtension on String {
  /// 增强版tr方法，确保翻译总是被正确应用
  String get trx {
    // 先尝试常规GetX翻译方法
    String translation = tr;

    // 如果翻译结果与键相同，尝试使用手动翻译
    if (translation == this) {
      // 获取当前语言
      final LocaleController localeController = Get.find<LocaleController>();
      translation = AppTranslations.translate(
          this, localeController.currentLanguage.value);
    }

    // 记录翻译时发现的问题
    if (translation == this) {
      print('翻译失败: 键 "$this" 未找到对应翻译');
    }

    return translation;
  }
}

/// 统一翻译管理类
class AppTranslations extends Translations {
  // 单例模式
  static final AppTranslations _instance = AppTranslations._internal();
  factory AppTranslations() => _instance;
  AppTranslations._internal();

  static final Map<String, Map<String, String>> _translations = {
    'en_US': {},
    'zh_CN': {},
    'zh_TW': {},
    'ja_JP': {},
  };

  @override
  Map<String, Map<String, String>> get keys => _translations;

  /// 初始化翻译系统
  static Future<void> init() async {
    try {
      // 创建语言控制器
      final localeController = Get.put(LocaleController());

      // 获取存储的语言设置 - 直接从StorageBox读取，避免SettingController依赖
      final storage = StorageManager();
      final storedLanguage = storage.read(StorageBox.default_, AnkiStorageKeys.language, "zh_CN");

      // 加载翻译
      await _loadTranslations();

      // 初始化语言
      final languageCode = storedLanguage.isEmpty ? 'zh_CN' : storedLanguage;

      // 确保翻译已加载成功后打印调试信息
      print('翻译系统初始化，语言: $languageCode');
      print('可用的翻译: ${_translations.keys.join(', ')}');

      // 检查关键翻译是否存在
      final testKey = 'settings.language';
      String mappedLocale;
      switch (languageCode) {
        case 'en':
          mappedLocale = 'en_US';
          break;
        case 'zh_CN':
          mappedLocale = 'zh_CN';
          break;
        case 'zh_TW':
          mappedLocale = 'zh_TW';
          break;
        case 'ja':
          mappedLocale = 'ja_JP';
          break;
        default:
          mappedLocale = 'en_US';
      }

      print('测试翻译 $testKey: ${_translations[mappedLocale]?[testKey]}');

      // 使用LocaleController更改语言 - 这会自动处理区域设置和强制刷新
      localeController.changeLocale(languageCode);

      // 输出调试信息，但不使用延迟刷新
      print('翻译检查: ${testKey.tr} / 增强翻译: ${testKey.trx}');
    } catch (e) {
      print('初始化翻译时出错: $e');
    }
  }

  /// 加载所有翻译文件
  static Future<void> _loadTranslations() async {
    // 定义语言文件路径
    final Map<String, String> languageFiles = {
      'en_US': 'assets/i18n/en.json',
      'zh_CN': 'assets/i18n/zh-CN.json',
      'zh_TW': 'assets/i18n/zh-TW.json',
      'ja_JP': 'assets/i18n/ja.json',
    };

    try {
      // 加载每种语言的翻译
      for (final entry in languageFiles.entries) {
        final locale = entry.key;
        final filePath = entry.value;
        try {
          _translations[locale] = await _loadTranslationFile(filePath);
          print(
              'Successfully loaded translations for $locale from $filePath. Keys count: ${_translations[locale]!.length}');
        } catch (e) {
          print('Error loading translations for $locale: $e');
          // 确保至少有一个空的翻译Map，防止空引用
          _translations[locale] = {};
        }
      }

      // 更新GetX翻译
      Get.clearTranslations();
      Get.addTranslations(_translations);
      print('Added translations to GetX: ${_translations.keys.join(', ')}');
    } catch (e) {
      print('Error loading translations: $e');
    }
  }

  /// 加载单个翻译文件
  static Future<Map<String, String>> _loadTranslationFile(String path) async {
    try {
      final String jsonString = await rootBundle.loadString(path);
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      return _flattenJson(jsonMap);
    } catch (e) {
      print('Error loading translation file $path: $e');
      return {};
    }
  }

  /// 扁平化JSON结构
  static Map<String, String> _flattenJson(Map<String, dynamic> json,
      [String prefix = '']) {
    Map<String, String> result = {};

    json.forEach((key, value) {
      String newKey = prefix.isEmpty ? key : '$prefix.$key';

      if (value is Map<String, dynamic>) {
        result.addAll(_flattenJson(value, newKey));
      } else if (value is String) {
        result[newKey] = value;
      } else if (value != null) {
        result[newKey] = value.toString();
      }
    });

    return result;
  }

  // 手动获取翻译，用于测试
  static String translate(String key, String langCode) {
    String translationKey;
    switch (langCode) {
      case 'en':
        translationKey = 'en_US';
        break;
      case 'zh_CN':
        translationKey = 'zh_CN';
        break;
      case 'zh_TW':
        translationKey = 'zh_TW';
        break;
      case 'ja':
        translationKey = 'ja_JP';
        break;
      default:
        translationKey = 'en_US';
    }

    return _translations[translationKey]?[key] ?? key;
  }
}

/// 语言控制器，用于管理应用程序的语言设置
class LocaleController extends GetxController {
  static LocaleController get to => Get.find();

  // 当前语言
  final RxString currentLanguage = 'en'.obs;

  // 可用语言列表
  final List<Map<String, String>> languages = [
    {'name': 'English', 'code': 'en'},
    {'name': '简体中文', 'code': 'zh_CN'},
    {'name': '繁體中文', 'code': 'zh_TW'},
    {'name': '日本語', 'code': 'ja'},
  ];

  // 更改语言
  void changeLocale(String langCode) {
    // 设置当前语言
    currentLanguage.value = langCode;

    // 获取对应的locale和翻译键
    final locale = getLocaleForLanguage(langCode);
    final translationKey = getTranslationKeyForLanguage(langCode);

    print('切换语言到: $langCode (翻译键: $translationKey, Locale: $locale)');

    // 验证翻译是否已加载
    if (AppTranslations._translations[translationKey]?.isNotEmpty != true) {
      print('警告: $translationKey 的翻译未正确加载');
    } else {
      print(
          '已加载 ${AppTranslations._translations[translationKey]?.length} 个 $translationKey 翻译');
    }

    // 应用更新的区域设置
    Get.updateLocale(locale);
  }

  // 根据语言代码获取对应的Locale
  Locale getLocaleForLanguage(String langCode) {
    // 定义映射关系
    Map<String, Locale> localeMapping = {
      'en': const Locale('en', 'US'),
      'zh_CN': const Locale('zh', 'CN'),
      'zh_TW': const Locale('zh', 'TW'),
      'ja': const Locale('ja', 'JP'),
    };

    return localeMapping[langCode] ?? const Locale('en', 'US');
  }

  // 根据语言代码获取翻译键
  String getTranslationKeyForLanguage(String langCode) {
    Map<String, String> translationKeyMapping = {
      'en': 'en_US',
      'zh_CN': 'zh_CN',
      'zh_TW': 'zh_TW',
      'ja': 'ja_JP',
    };

    return translationKeyMapping[langCode] ?? 'en_US';
  }

  // 获取语言选项列表
  List<Map<String, String>> getLanguageOptions() {
    return languages;
  }

  // 获取当前语言名称
  String getCurrentLanguageName() {
    return languages.firstWhere(
      (element) => element['code'] == currentLanguage.value,
      orElse: () => languages[0],
    )['name']!;
  }
}
