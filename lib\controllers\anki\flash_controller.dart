import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

class FlashNoteController extends GetxController {
  // 闪念笔记列表
  RxList<Map<String, dynamic>> notesList = <Map<String, dynamic>>[].obs;

  // 笔记排序选项
  final RxString noteSortOption = 'modifyTime'.obs;

  // 当前选中的笔记索引
  final RxInt selectedNoteIndex = (-1).obs;

  // 新笔记默认标题
  final RxString defaultTitle = 'anki.flash.new_note'.tr.obs;

  // 搜索关键词
  final RxString searchKeyword = ''.obs;

  // 筛选的笔记列表（搜索结果）
  RxList<Map<String, dynamic>> get filteredNotesList {
    if (searchKeyword.value.isEmpty) {
      return notesList;
    }

    final keyword = searchKeyword.value.toLowerCase();
    return notesList
        .where((note) {
          final name = note['name']?.toString().toLowerCase() ?? '';
          final content = note['content']?.toString().toLowerCase() ?? '';
          return name.contains(keyword) || content.contains(keyword);
        })
        .toList()
        .obs;
  }

  // 存储管理器
  final _storage = StorageManager();

  // 存储键名
  static const String storageKey = 'flash_notes';

  @override
  void onInit() {
    super.onInit();
    _loadNotes();

    // 监听排序选项变化，自动保存
    ever(noteSortOption, (_) {
      _saveSortOption();
    });
  }

  // 保存排序选项
  void _saveSortOption() {
    _storage.write(
        StorageBox.default_, 'flash_note_sort_option', noteSortOption.value);
  }

  // 加载所有笔记
  Future<void> _loadNotes() async {
    try {
      // 加载排序选项
      final sortOption =
          _storage.read(StorageBox.default_, 'flash_note_sort_option');
      if (sortOption != null && sortOption is String) {
        noteSortOption.value = sortOption;
      }

      // 加载笔记数据
      final notes = _storage.read(StorageBox.default_, storageKey);

      if (notes != null && notes is List && notes.isNotEmpty) {
        notesList.value = List<Map<String, dynamic>>.from(notes);
      } else {
        notesList.value = [];
        // 创建示例笔记
        if (notesList.isEmpty) {
          final now = DateTime.now().toIso8601String();
          notesList.add({
            'name': 'anki.flash.example_note'.tr,
            'content': 'anki.flash.example_note_content'.tr,
            'createTime': now,
            'modifyTime': now,
            'preview': 'anki.flash.example_note_preview'.tr,
          });
          _saveNotes();
        }
      }

      logger.i('加载了 ${notesList.length} 条闪念笔记');
    } catch (e) {
      logger.e('加载闪念笔记失败: $e');
      notesList.value = [];
    }
  }

  // 保存所有笔记
  Future<void> _saveNotes() async {
    try {
      _storage.write(StorageBox.default_, storageKey, notesList.toList());
      logger.i('保存了 ${notesList.length} 条闪念笔记');
    } catch (e) {
      logger.e('保存闪念笔记失败: $e');
    }
  }

  // 创建新笔记
  Future<int> createNote() async {
    final now = DateTime.now().toIso8601String();
    final newNote = {
      'name': '${defaultTitle.value} ${notesList.length + 1}',
      'content': '',
      'createTime': now,
      'modifyTime': now,
      'preview': '',
    };

    notesList.add(newNote);
    await _saveNotes();

    // 返回新创建笔记的索引
    return notesList.length - 1;
  }

  // 更新笔记
  Future<void> updateNote(int index, {String? name, String? content}) async {
    if (index < 0 || index >= notesList.length) {
      logger.e('更新笔记失败：索引越界 $index');
      return;
    }

    final note = Map<String, dynamic>.from(notesList[index]);
    final now = DateTime.now().toIso8601String();

    if (name != null) {
      note['name'] = name;
    }

    if (content != null) {
      note['content'] = content;

      // 提取前100个字符作为预览
      if (content.isNotEmpty) {
        final plainText =
            content.replaceAll(RegExp(r'<[^>]*>'), ''); // 移除HTML标签
        note['preview'] = plainText.length > 100
            ? '${plainText.substring(0, 100)}...'
            : plainText;
      } else {
        note['preview'] = '';
      }
    }

    note['modifyTime'] = now;
    notesList[index] = note;

    await _saveNotes();
  }

  // 删除笔记
  Future<void> deleteNote(int index) async {
    if (index < 0 || index >= notesList.length) {
      logger.e('删除笔记失败：索引越界 $index');
      return;
    }

    notesList.removeAt(index);
    await _saveNotes();
  }

  // 复制笔记
  Future<int> copyNote(int index) async {
    if (index < 0 || index >= notesList.length) {
      logger.e('复制笔记失败：索引越界 $index');
      return -1;
    }

    final originalNote = notesList[index];
    final now = DateTime.now().toIso8601String();

    final newNote = Map<String, dynamic>.from(originalNote);
    newNote['name'] = '${originalNote['name']} ${'anki.flash.copy_suffix'.tr}';
    newNote['createTime'] = now;
    newNote['modifyTime'] = now;

    notesList.add(newNote);
    await _saveNotes();

    // 返回新复制笔记的索引
    return notesList.length - 1;
  }

  // 导出笔记到Anki
  Future<void> exportNoteToAnki(BuildContext context, int index) async {
    if (index < 0 || index >= notesList.length) {
      logger.e('导出笔记失败：索引越界 $index');
      return;
    }

    try {
      final note = notesList[index];
      final noteContent = note['content'] as String? ?? '';

      // 这里添加导出到Anki的逻辑，可以调用其他控制器的方法
      // 例如调用AnkiConnectController发送数据到Anki

      // 示例代码，实际实现可能不同
      // final ankiController = Get.find<AnkiConnectController>();
      // await ankiController.addNote(note['name'], noteContent);

      // 先用snackbar显示，后续实现真正的导出功能
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('anki.flash.export_feature_under_development'.tr)));

      logger.i('导出笔记到Anki: ${note['name']}');
    } catch (e) {
      logger.e('导出笔记到Anki失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('anki.flash.export_failed'.trParams({'error': e.toString()})), backgroundColor: Colors.red));
    }
  }

  // 导出笔记到Markdown文件
  Future<void> exportNoteToMarkdown(BuildContext context, int index) async {
    if (index < 0 || index >= notesList.length) {
      logger.e('导出笔记失败：索引越界 $index');
      return;
    }

    try {
      final note = notesList[index];
      final noteTitle = note['name'] as String? ?? 'anki.flash.untitled_note'.tr;
      final noteContent = note['content'] as String? ?? '';

      // 构建Markdown内容
      final markdownContent = '''# $noteTitle

$noteContent
''';

      // 处理文件名，去除非法字符
      final sanitizedFileName =
          noteTitle.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');
      final defaultFileName = '$sanitizedFileName.md';

      // 打开保存对话框，让用户选择保存位置和自定义文件名
      final filePath = await FilePicker.platform.saveFile(
        dialogTitle: 'anki.flash.choose_save_location_and_filename'.tr,
        fileName: defaultFileName,
        type: FileType.custom,
        allowedExtensions: ['md'],
      );

      if (filePath != null) {
        // 创建文件并写入内容
        final file = File(filePath);
        await file.writeAsString(markdownContent);

        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('anki.flash.note_exported_to'.trParams({'path': filePath}))));
        logger.i('导出笔记到Markdown: $filePath');
      } else {
        // 用户取消了选择
        logger.i('用户取消了导出操作');
      }
    } catch (e) {
      logger.e('导出笔记到Markdown失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('anki.flash.export_failed'.trParams({'error': e.toString()})), backgroundColor: Colors.red));
    }
  }
}
