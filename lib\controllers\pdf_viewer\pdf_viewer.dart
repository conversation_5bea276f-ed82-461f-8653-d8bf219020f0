import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
// import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class GetxPDFViewerController extends GetxController {
  // final PdfViewerController pdfViewerController = PdfViewerController();
  final path = "".obs;

  void openFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf'],
      compressionQuality: 0
    );

    if (result != null) {
      path.value = result.files.single.path!;
    } else {
      // User canceled the picker
    }
  }
}
