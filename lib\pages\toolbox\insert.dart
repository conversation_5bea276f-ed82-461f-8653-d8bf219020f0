import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/insert.dart';

class PDFInsertPage extends StatefulWidget {
  const PDFInsertPage({super.key});

  @override
  State<PDFInsertPage> createState() => _PDFInsertPageState();
}

class _PDFInsertPageState extends State<PDFInsertPage> {
  final controller = Get.put(PDFInsertPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.insert.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.insert.description'.tr,
                  style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        const SizedBox(height: 4),
                        ShadRadioGroupCustom(
                          label: 'toolbox.insert.insertType'.tr,
                          initialValue: controller.insertMode.value,
                          items: controller.insertModeList,
                          onChanged: (value) {
                            controller.insertMode.value = value;
                          },
                        ),
                        if (controller.insertMode.value == 'file') ...[
                          ShadInputWithFileSelect(
                            key: const ValueKey("insert-file"),
                            title: 'toolbox.insert.insertFile'.tr,
                            placeholder:
                                Text('toolbox.common.inputFilePlaceholder'.tr),
                            allowedExtensions: const ['pdf'],
                            isRequired: true,
                            allowMultiple: false,
                            initialValue: [controller.insertFile.value],
                            onFilesSelected: (files) {
                              controller.insertFile.value = files.single;
                            },
                            onValidate: (value, files) async {
                              return await validateFile(value, files);
                            },
                            onValidateError: (error) {},
                          ),
                          ShadInputWithValidate(
                              key: const ValueKey("split-range"),
                              label: 'toolbox.common.pageRange'.tr,
                              placeholder:
                                  'toolbox.common.pageRangePlaceholder'.tr,
                              initialValue: controller.pageRange.value,
                              onChanged: (value) {
                                controller.pageRange.value = value;
                              },
                              onValidate: (value) async {
                                if (validatePageRange(value)) {
                                  return "";
                                }
                                return 'toolbox.common.enterPageRange'.tr;
                              }),
                        ],
                        if (controller.insertMode.value == 'blank') ...[
                          ShadSelectCustom(
                            label: 'toolbox.insert.paperSize'.tr,
                            placeholder: 'toolbox.insert.paperSize'.tr,
                            initialValue: [controller.paperSize.value],
                            isMultiple: false,
                            options: paperSizeList,
                            onChanged: (value) {
                              logger.i(value);
                              controller.paperSize.value = value.single;
                            },
                          ),
                          ShadSelectCustom(
                            label: 'toolbox.insert.orientation'.tr,
                            placeholder: 'toolbox.insert.orientation'.tr,
                            initialValue: [controller.orientation.value],
                            options: controller.orientationList,
                            onChanged: (value) {
                              controller.orientation.value = value.single;
                            },
                          ),
                          ShadInputWithValidate(
                            key: const ValueKey("insert-count"),
                            label: 'toolbox.insert.insertCount'.tr,
                            placeholder: 'toolbox.insert.insertCount'.tr,
                            initialValue:
                                controller.insertCount.value.toString(),
                            onChanged: (value) {
                              controller.insertCount.value = int.parse(value);
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return '请输入插入数量';
                              }
                              final reg = RegExp(r'^\d+$');
                              if (!reg.hasMatch(value)) {
                                return '请输入整数';
                              }
                              return null;
                            },
                          ),
                        ],
                        ShadSelectCustom(
                          label: 'toolbox.insert.insertPosition'.tr,
                          placeholder: 'toolbox.insert.insertPosition'.tr,
                          initialValue: [controller.insertPosition.value],
                          options: controller.positionList,
                          onChanged: (value) {
                            controller.insertPosition.value = value.single;
                          },
                        ),
                        if (controller.insertPosition.value == 'before_page' ||
                            controller.insertPosition.value ==
                                'after_page') ...[
                          ShadInputWithValidate(
                            key: const ValueKey("insert-pos"),
                            label: 'toolbox.insert.insertPage'.tr,
                            placeholder: 'toolbox.insert.insertPage'.tr,
                            initialValue:
                                controller.insertPage.value.toString(),
                            onChanged: (value) {
                              controller.insertPage.value = int.parse(value);
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return '请输入插入页码';
                              }
                              final reg = RegExp(r'^\d+$');
                              if (!reg.hasMatch(value)) {
                                return '请输入整数';
                              }
                              return null;
                            },
                          ),
                        ],
                        // ShadSwitchCustom(
                        //   label: '替换模式',
                        //   initialValue: controller.isReplaceMode.value,
                        //   onChanged: (v) {
                        //     controller.isReplaceMode.value = v;
                        //   },
                        // ),
                        if (PathUtils.isDesktop)
                          ShadSelectCustom(
                            key: const ValueKey("output-position"),
                            label: 'toolbox.common.outputLocation'.tr,
                            placeholder:
                                'toolbox.common.selectOutputLocation'.tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.common.outputDirectory'.tr,
                            placeholder:
                                Text('toolbox.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.common.inputFile'.tr,
                          placeholder:
                              Text('toolbox.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['pdf'],
                          isRequired: true,
                          allowMultiple: true,
                          initialValue: controller.selectedFilePaths,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
