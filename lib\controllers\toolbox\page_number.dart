import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';

class PDFPageNumberController extends GetxController {
  // 基础数据
  final alignList = [
    {"value": "left", "label": 'toolbox.pageNumber.selectAlignment'.tr},
    {"value": "center", "label": 'toolbox.pageNumber.selectAlignment'.tr},
    {"value": "right", "label": 'toolbox.pageNumber.selectAlignment'.tr},
  ];
  final numberPosList = [
    {"value": "top", "label": 'toolbox.pageNumber.numberPosition'.tr},
    {"value": "bottom", "label": 'toolbox.pageNumber.numberPosition'.tr},
  ];
  final styleList = [
    {"value": "digit", "label": "1"},
    {"value": "roman", "label": "I"},
    {"value": "1/x", "label": "1/x"},
    {"value": "第x页", "label": "第x页"},
    {"value": "第1页,共x页", "label": "第1页,共x页"},
    {"value": "custom", "label": 'toolbox.pageNumber.customStyle'.tr},
  ];
  final fontFamilyList = [
    {'value': 'SourceHanSansSC', 'label': 'toolbox.pageNumber.fontFamily'.tr},
    {'value': 'helvetica', 'label': 'Helvetica'},
    {'value': 'courier', 'label': 'Courier'},
    {'value': 'times', 'label': 'Times New Roman'},
    {'value': 'symbol', 'label': 'Symbol'},
    {'value': 'zapfdingbats', 'label': 'ZapfDingbats'},
  ];
  // 表单参数
  final numberPos = "bottom".obs;
  final align = "center".obs;
  final startNumber = 1.obs;
  final useCustomFont = false.obs;
  final customFontFilePath = ''.obs;
  final fontSize = 12.0.obs;
  final fontColor = const Color(0xFF000000).obs;
  final opacity = 0.5.obs;
  final fontFamily = "SourceHanSansSC".obs;
  final style = "digit".obs;
  final customStyleTemplate = "".obs;
  final unit = "pt".obs;
  final top = 10.0.obs;
  final bottom = 10.0.obs;
  final left = 10.0.obs;
  final right = 10.0.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'annotate');

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      for (String filePath in selectedFilePaths) {
        progressController.updateProgress(
          status: "running",
          message: "${'toolbox.common.processing'.tr} ${PathUtils(filePath).name}",
        );
        final outputPath = await PathUtils(filePath).convertPath(
          outputMode.value,
          stem_append: "_${'toolbox.pageNumber.title'.tr}",
          suffix: ".pdf",
          outputDir: outputDir.value,
        );

        final result = await _addPageNumbers(
          filePath,
          outputPath,
          pageRange.value,
        );

        if (result.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'toolbox.common.process.completed'.tr,
          );
        } else {
          progressController.updateProgress(
            status: "error", message: "${'toolbox.common.operationFailed'.tr}: ${result.message}",
          );
          return;
        }
      }
      progressController.updateProgress(status: "completed", message: "");
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "${'toolbox.common.operationFailed'.tr}: $e",
      );
    }
  }

  /// 获取PDF字体系列
  Future<dynamic> getPdfFontFamily(String fontName) async {
    switch (fontName.toLowerCase()) {
      case 'sourcehansanssc':
        final ByteData fontData =
            await rootBundle.load('assets/fonts/SourceHanSansSC-Normal.ttf');
        return PdfTrueTypeFont(fontData.buffer.asUint8List(), fontSize.value);
      case 'helvetica':
        return PdfFontFamily.helvetica;
      case 'courier':
        return PdfFontFamily.courier;
      case 'times':
        return PdfFontFamily.timesRoman;
      case 'symbol':
        return PdfFontFamily.symbol;
      case 'zapfdingbats':
        return PdfFontFamily.zapfDingbats;
      default:
        return PdfFontFamily.helvetica;
    }
  }

  Future<CustomResponse> _addPageNumbers(
    String inputPath,
    String outputPath,
    String pageRangeStr,
  ) async {
    try {
      // 加载PDF文档
      final File inputFile = File(inputPath);
      if (!inputFile.existsSync()) {
        logger.e("文件不存在: $inputPath");
        return CustomResponse(
          status: "error",
          message: 'toolbox.background.fileNotExist'.tr,
          data: "",
        );
      }

      final PdfDocument document = PdfDocument(
        inputBytes: inputFile.readAsBytesSync(),
      );

      // 解析页码范围
      List<int> pageIndices =
          parsePageRange(pageRangeStr, document.pages.count);

      // 创建字体
      PdfFont font;
      if (useCustomFont.value && customFontFilePath.value.isNotEmpty) {
        // 使用自定义字体
        final fontData = File(customFontFilePath.value).readAsBytesSync();
        font = PdfTrueTypeFont(fontData, fontSize.value);
      } else {
        // 使用标准字体
        final fontName = await getPdfFontFamily(fontFamily.value);
        if (fontName is PdfCjkFontFamily) {
          font = PdfCjkStandardFont(fontName, fontSize.value);
        } else if (fontName is PdfTrueTypeFont) {
          font = fontName;
        } else {
          font = PdfStandardFont(fontName as PdfFontFamily, fontSize.value);
        }
      }

      // 创建画笔
      PdfBrush brush = PdfSolidBrush(
        PdfColor(
          fontColor.value.red,
          fontColor.value.green,
          fontColor.value.blue,
        ),
      );

      // 为每个选定的页面添加页码
      for (int i = 0; i < pageIndices.length; i++) {
        final pageIndex = pageIndices[i];
        final pageNumber = startNumber.value + i;
        final page = document.pages[pageIndex];
        final pageSize = page.getClientSize();

        // 生成页码文本
        String pageText = '';
        switch (style.value) {
          case 'digit':
            pageText = pageNumber.toString();
            break;
          case 'roman':
            pageText = convertToRoman(pageNumber);
            break;
          case '1/x':
            pageText = '$pageNumber/${pageIndices.length}';
            break;
          case '第x页':
            pageText = '第$pageNumber页';
            break;
          case '第1页,共x页':
            pageText = '第$pageNumber页,共${pageIndices.length}页';
            break;
          case 'custom':
            // 替换自定义格式中的占位符
            pageText = customStyleTemplate.value
                .replaceAll('%p', pageNumber.toString())
                .replaceAll('%P', document.pages.count.toString());
            break;
          default:
            pageText = pageNumber.toString();
        }

        // 测量文本大小
        Size textSize = font.measureString(pageText);

        // 计算文本位置
        double x = 0;
        double y = 0;

        // 根据对齐方式设置水平位置
        switch (align.value) {
          case 'left':
            x = left.value;
            break;
          case 'center':
            x = (pageSize.width - textSize.width) / 2;
            break;
          case 'right':
            x = pageSize.width - textSize.width - right.value;
            break;
          default:
            x = (pageSize.width - textSize.width) / 2;
        }

        // 根据位置设置垂直位置
        switch (numberPos.value) {
          case 'top':
            y = top.value;
            break;
          case 'bottom':
            y = pageSize.height - textSize.height - bottom.value;
            break;
          default:
            y = pageSize.height - textSize.height - bottom.value;
        }

        // 添加页码
        page.graphics.save();
        page.graphics.setTransparency(opacity.value);
        page.graphics.drawString(
          pageText,
          font,
          brush: brush,
          bounds: Rect.fromLTWH(x, y, textSize.width, textSize.height),
        );
        page.graphics.restore();
      }

      // 保存修改后的文档
      File(outputPath).writeAsBytes(await document.save());

      // 释放文档资源
      document.dispose();

      return CustomResponse(
        status: "success",
        message: 'toolbox.common.process.completed'.tr,
        data: outputPath,
      );
    } catch (e) {
      logger.e("添加页码错误: $e");
      return CustomResponse(
        status: "error",
        message: "${'toolbox.common.operationFailed'.tr}: $e",
        data: "",
      );
    }
  }
}
