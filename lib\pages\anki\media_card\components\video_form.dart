import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/media_card.dart';
import 'dart:io';

class VideoForm extends GetView<MediaCardPageController> {
  const VideoForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadSelectWithInput(
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
                onAddNew: (newDeckName) {
                  // Add to the deck list if not already present
                  if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                    ankiConnectController.parentDeckList.add(newDeckName);
                  }

                  // Set as selected deck
                  controller.parentDeck.value = newDeckName;
                },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              // ShadSelectCustom(
              //   label: "卡片模版",
              //   placeholder: "选择卡片模版",
              //   initialValue: [controller.cardModel.value],
              //   options: controller.modelList
              //       .map((e) => {"value": e, "label": e})
              //       .toList(),
              //   onChanged: (value) {
              //     controller.cardModel.value = value.single;
              //     controller.updateFieldList(controller.cardModel.value);
              //   },
              // ),
              // if (controller.fieldList.isNotEmpty) ...[
              //   ListTile(
              //     contentPadding: EdgeInsets.zero,
              //     minVerticalPadding: 0,
              //     title: Text('字段映射', style: defaultTitleStyle),
              //     subtitle: Padding(
              //       padding: const EdgeInsets.only(top: 8),
              //       child: ConstrainedBox(
              //         constraints: BoxConstraints(
              //           maxWidth: double.infinity,
              //           maxHeight: (1 + controller.fieldList.length) * 48,
              //         ),
              //         child: ShadTable(
              //           columnCount: 3,
              //           rowCount: controller.fieldList.length,
              //           pinnedRowCount: 1,
              //           header: (context, column) {
              //             if (column == 0) {
              //               return const ShadTableCell.header(
              //                 child: Text('模版字段'),
              //               );
              //             } else {
              //               return const ShadTableCell.header(
              //                 child: Text('匹配类型'),
              //               );
              //             }
              //           },
              //           columnSpanExtent: (index) {
              //             if (Platform.isWindows ||
              //                 Platform.isLinux ||
              //                 Platform.isMacOS) {
              //               final splitMap = {
              //                 0: 0.3,
              //                 1: 0.7,
              //               };
              //               return FractionalTableSpanExtent(
              //                   splitMap[index] ?? 0.2);
              //             } else {
              //               if (index == 1) {
              //                 return const FractionalTableSpanExtent(0.5);
              //               }
              //             }
              //           },
              //           rowSpanExtent: (index) =>
              //               const FixedTableSpanExtent(48),
              //           builder: (context, index) {
              //             final field = controller.fieldList[index.row];
              //             if (index.column == 0) {
              //               return ShadTableCell(child: Text(field));
              //             } else {
              //               return ShadTableCell(
              //                 child: GetBuilder<MediaCardPageController>(
              //                     id: 'field_mappings_$field',
              //                     builder: (controller) {
              //                       return SizedBox(
              //                         width: double.infinity,
              //                         child: ShadSelectCustom(
              //                           key: ValueKey(
              //                               'field_mapping_${field}_${controller.cardModel.value}'),
              //                           label: '匹配类型',
              //                           placeholder: '选择匹配类型',
              //                           isMultiple: false,
              //                           showLabel: false,
              //                           initialValue: controller
              //                                       .fieldMappings[field] !=
              //                                   null
              //                               ? [controller.fieldMappings[field]!]
              //                               : [],
              //                           options: controller.matchTypeList,
              //                           onChanged: (value) {
              //                             logger.i(value);
              //                             if (value.isNotEmpty) {
              //                               controller.fieldMappings[field] =
              //                                   value.single;
              //                             } else {
              //                               controller.fieldMappings
              //                                   .remove(field);
              //                             }
              //                           },
              //                         ),
              //                       );
              //                     }),
              //               );
              //             }
              //           },
              //         ),
              //       ),
              //     ),
              //   )
              // ],
              ShadInputWithFileSelect(
                key: const ValueKey("subtitle-file"),
                title: 'anki.media_card.subtitle_file'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const ['srt', 'vtt'],
                isRequired: true,
                allowMultiple: false,
                initialValue: [controller.subtitleFile.value],
                onFilesSelected: (files) {
                  controller.subtitleFile.value = files.single;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
              ShadInputWithFileSelect(
                key: const ValueKey("media-file"),
                title: 'anki.media_card.media_file'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const [
                  'mp4',
                  'm4a',
                  'wav',
                  'avi',
                  'mov',
                  'mp3'
                ],
                isRequired: true,
                allowMultiple: false,
                initialValue: [controller.mediaFile.value],
                onFilesSelected: (files) {
                  controller.mediaFile.value = files.single;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
              ShadSwitchCustom(
                label: 'anki.common.advanced_options'.tr,
                initialValue: controller.isAdvanced.value,
                onChanged: (v) {
                  controller.isAdvanced.value = v;
                },
              ),
              if (controller.isAdvanced.value) ...[
                ShadRadioGroupCustom(
                  label: 'anki.media_card.video_output_format'.tr,
                  initialValue: controller.videoOutputFormat.value,
                  items: controller.videoOutputFormatList,
                  onChanged: (value) {
                    controller.videoOutputFormat.value = value;
                  },
                ),
                ShadInputWithValidate(
                    label: 'anki.media_card.subtitle_offset_ms'.tr,
                    placeholder: 'anki.media_card.subtitle_offset_placeholder'.tr,
                    initialValue: controller.timeDelta.value.toString(),
                    onChanged: (value) {
                      final regex = RegExp(r'^-?\d+$');
                      if (value.isNotEmpty && regex.hasMatch(value)) {
                        controller.timeDelta.value = int.parse(value);
                      }
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return null;
                      }
                      final regex = RegExp(r'^-?\d+$');
                      if (!regex.hasMatch(value)) {
                        return "anki.media_card.subtitle_offset_error".tr;
                      }
                      return null;
                    }),
                ShadInputWithValidate(
                  label: 'anki.media_card.left_padding_ms'.tr,
                  placeholder: 'anki.media_card.left_padding_placeholder'.tr,
                  initialValue: controller.padLeft.value.toString(),
                  onChanged: (value) {
                    final regex = RegExp(r'^-?\d+$');
                    if (value.isNotEmpty && regex.hasMatch(value)) {
                      controller.padLeft.value = int.parse(value);
                    }
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return null;
                    }
                    final regex = RegExp(r'^-?\d+$');
                    if (!regex.hasMatch(value)) {
                      return "anki.media_card.left_padding_error".tr;
                    }
                    return null;
                  },
                ),
                ShadInputWithValidate(
                  label: 'anki.media_card.right_padding_ms'.tr,
                  placeholder: 'anki.media_card.right_padding_placeholder'.tr,
                  initialValue: controller.padRight.value.toString(),
                  onChanged: (value) {
                    final regex = RegExp(r'^-?\d+$');
                    if (value.isNotEmpty && regex.hasMatch(value)) {
                      controller.padRight.value = int.parse(value);
                    }
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return null;
                    }
                    final regex = RegExp(r'^-?\d+$');
                    if (!regex.hasMatch(value)) {
                      return "anki.media_card.right_padding_error".tr;
                    }
                    return null;
                  },
                ),
                ShadInputWithValidate(
                  label: 'anki.media_card.start_time'.tr,
                  placeholder: 'anki.media_card.start_time_placeholder'.tr,
                  initialValue: controller.startAt.value.toString(),
                  onChanged: (value) {
                    controller.startAt.value = value;
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return null;
                    }
                    final regex = RegExp(r'^\d{2}:\d{2}(:\d{2})?$');
                    if (!regex.hasMatch(value)) {
                      return "anki.media_card.start_time_error".tr;
                    }
                    return null;
                  },
                ),
                ShadInputWithValidate(
                  label: 'anki.media_card.end_time'.tr,
                  placeholder: 'anki.media_card.end_time_placeholder'.tr,
                  initialValue: controller.endAt.value.toString(),
                  onChanged: (value) {
                    controller.endAt.value = value;
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return null;
                    }
                    final regex = RegExp(r'^\d{2}:\d{2}(:\d{2})?$');
                    if (!regex.hasMatch(value)) {
                      return "anki.media_card.end_time_error".tr;
                    }
                    return null;
                  },
                ),
              ],

              ShadSelectWithInput(
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.media_card.select_tags'.tr,
                searchPlaceholder: 'anki.media_card.input_tags'.tr,
                isMultiple: true,
                initialValue: controller.tags.toList(),
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the global tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tags.contains(newTag)) {
                    controller.tags.add(newTag);
                  }
                },
              ),
              const SizedBox(height: 8),
            ],
          )),
    );
  }
}
