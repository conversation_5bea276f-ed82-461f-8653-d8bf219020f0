import os
from pathlib import Path
import fitz

def set_metadata(
    *, 
    doc_path: str = "",
    output_path: str = "",
    title: str = "",
    author: str = "",
    subject: str = "",
    keywords: str = "",
    creator: str = "",
    producer: str = "",
    creationDate: str = "",
    modDate: str = "",
):
    """
    设置PDF元数据
    :param doc_path: PDF文件路径
    :param metadata: 元数据字典，可设置的key有: format, encryption, title, author, subject, keywords, creator, producer, creationDate, modDate, trapped
    """
    doc: fitz.Document = fitz.open(doc_path)
    metadata = {
        "title": title,
        "author": author,
        "subject": subject,
        "keywords": keywords,
        "creator": creator,
        "producer": producer,
        "creationDate": creationDate,
        "modDate": modDate,
    }
    doc.set_metadata(metadata)
    doc.ez_save(output_path, garbage=4)
