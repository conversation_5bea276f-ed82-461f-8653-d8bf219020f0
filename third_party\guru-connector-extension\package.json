{"name": "guru-connector-extension", "description": "<PERSON><PERSON> 浏览器连接器 - 视频笔记工具", "private": true, "version": "1.0.8", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "vue-tsc --noEmit", "postinstall": "wxt prepare", "build:mv2": "wxt build --mv2", "build:mv3": "wxt build --mv3", "zip:mv2": "wxt zip --mv2", "zip:mv3": "wxt zip --mv3", "build:all": "bash scripts/build-all.sh"}, "dependencies": {"html2canvas": "^1.4.1", "pinia": "^2.1.7", "vue": "^3.5.12", "webextension-polyfill": "^0.10.0"}, "devDependencies": {"@types/webextension-polyfill": "^0.10.0", "@wxt-dev/module-vue": "^1.0.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vue-tsc": "^1.8.25", "wxt": "^0.19.29"}}