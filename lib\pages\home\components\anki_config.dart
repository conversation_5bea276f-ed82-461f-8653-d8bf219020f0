import 'package:flutter/material.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:shadcn_ui/shadcn_ui.dart';

class AnkiConfig extends GetView<SettingController> {
  const AnkiConfig({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('ankiConfig.title'.tr),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () {
              controller.saveSettings();
            },
          ),
        ],
      ),
      body: Obx(() => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: [
                  ShadCard(
                    padding: const EdgeInsets.only(
                        left: 16, right: 16, top: 16, bottom: 16),
                    child: Column(
                      spacing: 4,
                      children: [
                        // 制卡模式选择
                        if (Platform.isAndroid)
                          ShadSelectCustom(
                            label: 'ankiConfig.cardMode'.tr,
                            placeholder: 'ankiConfig.selectCardMode'.tr,
                            initialValue: [controller.cardMode.value],
                            options: controller.androidCardModeList,
                            isMultiple: false,
                            onChanged: (value) {
                              controller.cardMode.value = value.single;
                              controller.saveSettings(showToast: false);
                            },
                          ),
                        if (Platform.isWindows ||
                            Platform.isMacOS ||
                            Platform.isLinux)
                          ShadSelectCustom(
                            label: 'ankiConfig.cardMode'.tr,
                            placeholder: 'ankiConfig.selectCardMode'.tr,
                            initialValue: [controller.cardMode.value],
                            options: controller.desktopCardModeList,
                            isMultiple: false,
                            onChanged: (value) {
                              controller.cardMode.value = value.single;
                              controller.saveSettings(showToast: false);
                            },
                          ),
                        if (Platform.isIOS)
                          ShadSelectCustom(
                            label: 'ankiConfig.cardMode'.tr,
                            placeholder: 'ankiConfig.selectCardMode'.tr,
                            initialValue: [controller.cardMode.value],
                            options: controller.iosCardModeList,
                            isMultiple: false,
                            onChanged: (value) {
                              controller.cardMode.value = value.single;
                              controller.saveSettings(showToast: false);
                            },
                          ),
                        if (controller.cardMode.value == 'ankiconnect')
                          ShadInputWithValidate(
                            label: 'ankiConfig.ankiConnectAddress'.tr,
                            placeholder: 'http://localhost:8765',
                            initialValue: controller.ankiConnectUrl.value,
                            onValidate: (value) async {
                              return "";
                            },
                            onChanged: (value) {
                              controller.ankiConnectUrl.value = value;
                            },
                          ),
                        if (controller.cardMode.value == 'apkg')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                'output-dir-${controller.outputDir.value}'),
                            title: 'ankiConfig.outputDirectory'.tr,
                            placeholder: Text(
                                'ankiConfig.outputDirectoryPlaceholder'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        if (Platform.isWindows ||
                            Platform.isMacOS ||
                            Platform.isLinux) ...[
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                'pdf_reader_path-${controller.pdfReaderPath.value}'),
                            title: 'ankiConfig.pdfReaderPath'.tr,
                            placeholder:
                                Text('ankiConfig.selectPdfReaderPath'.tr),
                            initialValue: [controller.pdfReaderPath.value],
                            isRequired: true,
                            onFilesSelected: (files) {
                              controller.pdfReaderPath.value = files.single;
                            },
                            onValidate: (value, files) async {
                              return await validateFile(value, files);
                            },
                            onValidateError: (error) {
                              controller.pdfReaderPathError.value = error;
                            },
                          ),
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                'ffmpeg_path-${controller.ffmpegPath.value}'),
                            title: 'ankiConfig.ffmpegPath'.tr,
                            placeholder: Text('ankiConfig.selectFfmpegPath'.tr),
                            initialValue: [controller.ffmpegPath.value],
                            isRequired: true,
                            onFilesSelected: (files) {
                              controller.ffmpegPath.value = files.single;
                            },
                            onValidate: (value, files) async {
                              return await validateFile(value, files);
                            },
                            onValidateError: (error) {
                              controller.ffmpegPathError.value = error;
                            },
                          ),
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                'anki_path-${controller.ankiPath.value}'),
                            title: 'ankiConfig.ankiPath'.tr,
                            placeholder: Text('ankiConfig.selectAnkiPath'.tr),
                            initialValue: [controller.ankiPath.value],
                            isRequired: true,
                            onFilesSelected: (files) {
                              controller.ankiPath.value = files.single;
                            },
                            onValidate: (value, files) async {
                              return await validateFile(value, files);
                            },
                            onValidateError: (error) {},
                          ),
                          ShadSwitchCustom(
                            label: 'ankiConfig.autoStartAnki'.tr,
                            initialValue: controller.autoStartAnki.value,
                            onChanged: (v) {
                              controller.autoStartAnki.value = v;
                              controller.saveSettings(showToast: false);
                            },
                          ),
                        ],
                      ],
                    ),
                  )
                ]),
          )),
    );
  }
}
