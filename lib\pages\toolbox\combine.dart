import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/combine.dart';

class PDFCombinePage extends StatefulWidget {
  const PDFCombinePage({super.key});

  @override
  State<PDFCombinePage> createState() => _PDFCombinePageState();
}

class _PDFCombinePageState extends State<PDFCombinePage> {
  final controller = Get.put(PDFCombinePageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.combine.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.combine.description'.tr, style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        const SizedBox(height: 4),
                        ShadInputWithValidate(
                            key: const ValueKey("num-rows"),
                            label: 'toolbox.combine.numRows'.tr,
                            placeholder: 'toolbox.combine.numRowsPlaceholder'.tr,
                            initialValue: controller.numRows.value.toString(),
                            onChanged: (value) {
                              controller.numRows.value = int.tryParse(value) ??
                                  controller.numRows.value;
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return 'toolbox.combine.numRowsPlaceholder'.tr;
                              }
                              final reg = RegExp(r'^\d+$');
                              if (!reg.hasMatch(value)) {
                                return "toolbox.validation.enterInteger".tr;
                              }
                              final size = int.parse(value);
                              if (size <= 0) {
                                return "toolbox.validation.mustBeGreaterThanZero".tr;
                              }
                              return "";
                            }),
                        ShadInputWithValidate(
                            key: const ValueKey("num-cols"),
                            label: 'toolbox.combine.numCols'.tr,
                            placeholder: 'toolbox.combine.numColsPlaceholder'.tr,
                            initialValue: controller.numCols.value.toString(),
                            onChanged: (value) {
                              controller.numCols.value = int.tryParse(value) ??
                                  controller.numCols.value;
                            },
                            onValidate: (value) async {
                              if (value.isEmpty) {
                                return 'toolbox.combine.numColsPlaceholder'.tr;
                              }
                              final reg = RegExp(r'^\d+$');
                              if (!reg.hasMatch(value)) {
                                return "toolbox.validation.enterInteger".tr;
                              }
                              final size = int.parse(value);
                              if (size <= 0) {
                                return "toolbox.validation.mustBeGreaterThanZero".tr;
                              }
                              return "";
                            }),
                        
                        ShadSelectCustom(
                          label: 'toolbox.combine.layout_order'.tr,
                          placeholder: 'toolbox.combine.layout_order'.tr,
                          initialValue: [controller.layoutOrder.value],
                          options: controller.layoutOrderList,
                          onChanged: (value) {
                            controller.layoutOrder.value = value.single;
                          },
                        ),
                        ShadInputWithValidate(
                            key: const ValueKey("split-range"),
                            label: 'toolbox.common.pageRange'.tr,
                            placeholder: 'toolbox.common.pageRangePlaceholder'.tr,
                            initialValue: controller.pageRange.value,
                            onChanged: (value) {
                              controller.pageRange.value = value;
                            },
                            onValidate: (value) async {
                              if (validatePageRange(value)) {
                                return "";
                              }
                              return 'toolbox.common.enterPageRange'.tr;
                            }),
                        if (PathUtils.isDesktop)
                          ShadSelectCustom(
                            key: const ValueKey("output-position"),
                            label: 'toolbox.common.outputLocation'.tr,
                            placeholder: 'toolbox.common.selectOutputLocation'.tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.common.outputDirectory'.tr,
                            placeholder: Text('toolbox.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.common.inputFile'.tr,
                          placeholder:
                              Text('toolbox.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['pdf'],
                          isRequired: true,
                          allowMultiple: true,
                          initialValue: controller.selectedFilePaths,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
