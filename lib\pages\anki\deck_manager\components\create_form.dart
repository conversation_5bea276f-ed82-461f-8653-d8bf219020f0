import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/deck_manager.dart';

class CreateDeckForm extends GetView<DeckManagerController> {
  const CreateDeckForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadRadioGroupCustom(
                label: 'anki.deck_manager.create_mode'.tr,
                initialValue: controller.createMode.value,
                items: controller.createModeList,
                onChanged: (value) {
                  controller.createMode.value = value;
                },
              ),
              if (controller.createMode.value == "manual") ...[
                ShadInputWithValidate(
                  label: 'anki.deck_manager.deck_list'.tr,
                  placeholder: 'anki.deck_manager.deck_list_placeholder'.tr,
                  maxLines: 8,
                  onChanged: (value) {
                    controller.deckNames.value = value.split('\n');
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return 'anki.deck_manager.deck_list_required'.tr;
                    }
                    return "";
                  },
                  onValidateError: (error) {},
                ),
              ],
              if (controller.createMode.value == "file") ...[
                ShadInputWithFileSelect(
                  key: const ValueKey("deck-names-file"),
                  title: 'anki.deck_manager.deck_names_file'.tr,
                  placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                  allowedExtensions: const ['txt', 'md'],
                  isRequired: true,
                  allowMultiple: false,
                  initialValue: controller.selectedFilePaths,
                  onFilesSelected: (files) {
                    controller.deckNamesFile.value = files.single;
                  },
                  onValidate: (value, files) async {
                    return await validateFile(value, files);
                  },
                  onValidateError: (error) {},
                ),
              ],
              const SizedBox(height: 4),
            ],
          )),
    );
  }
}
