
package top.kevin2li.guru

internal class KevinTextQACardModel {
    companion object {
        @JvmField // required for API
        var NAME = "Kevin Text QA Card v2"

        @JvmField // required for API
        var FIELDS = arrayOf("Front", "Back")

        // List of card names that will be used in AnkiDroid (one for each direction of learning)
        @JvmField // required for API
        val CARD_NAMES = arrayOf("Card 1")

        // Template for the question of each card
        @JvmField // required for API
        val QFMT = arrayOf("{{Front}}")

        @JvmField // required for API
        val AFMT = arrayOf(
            """{{FrontSide}}
        
        |<hr id="answer">
        
        |{{Back}}
            """.trimMargin()
        )

        @JvmField // required for API
        var CSS = ""

    }
}