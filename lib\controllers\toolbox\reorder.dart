import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFReorderPageController extends GetxController {
  // 表单参数
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;

  // 错误
  final outputDirError = ''.obs;

  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();

  /// 调用Rust端将PDF转换为图片
  Future<String> _reorderPdfPages(
    String pdfPath,
    String outputPath,
    String pageRange,
  ) async {
    try {
      final data = {
        'input_path': pdfPath,
        'page_range': pageRange,
        'output_path': outputPath,
        'show_progress': true,
      };

      final resp = await messageController.request(data, 'pdf/reorder');
      if (resp.status == 'success') {
        return resp.data;
      }
      throw Exception(resp.message);
    } catch (e) {
      logger.e("_convertPdfToImages error: $e");
      rethrow;
    }
  }

  void submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.fileSelect.error'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // Process each selected PDF file
      for (String filePath in selectedFilePaths) {
        // Determine output path
        final pathUtils = PathUtils(filePath);
        String outputPath = await pathUtils.convertPath(
          outputMode.value,
          stem_append: "_${'toolbox.reorder.reorder'.tr}",
          outputDir: outputDir.value,
        );
        final data = {
          'input_path': filePath,
          'page_range': pageRange.value,
          'output_path': outputPath,
          'show_progress': true,
        };

        final resp = await messageController.request(data, 'pdf/reorder');
        logger.w("resp: $resp");
        if (resp.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'common.completed'.tr,
          );
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
          return;
        }
      }
      progressController.updateProgress(status: "completed", message: "");
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "toolbox.common.error_with_msg".trParams({'error': e.toString()}),
      );
    }
  }
}
