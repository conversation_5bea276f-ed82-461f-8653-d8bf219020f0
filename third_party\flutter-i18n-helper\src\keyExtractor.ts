import * as vscode from 'vscode';
import { getConfiguration, getI18nDirUri, readJsonFile, setValueByPath, getValueFromPath } from './utils';

function toSnakeCase(str: string): string {
    return str.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_.]/g, '').toLowerCase();
}

async function updateSourceJsonFile(key: string, value: string): Promise<boolean> {
    const config = getConfiguration();
    const sourceLang = config.get<string>('sourceLanguage');
    const keyStructure = config.get<string>('keyStructure', 'flat'); // Read key structure config
    const i18nDirUri = await getI18nDirUri();

    if (!sourceLang || !i18nDirUri) {
        vscode.window.showErrorMessage('Please configure sourceLanguage and i18nDir.');
        return false;
    }

    const sourceFileUri = vscode.Uri.joinPath(i18nDirUri, `${sourceLang}.json`);
    
    try {
        let sourceData = await readJsonFile(sourceFileUri) || {};
        const existingValue = getValueFromPath(sourceData, key);

        if (existingValue !== undefined && existingValue !== value) {
            const overwrite = await vscode.window.showWarningMessage(
                `Key '${key}' already exists with a different value. Overwrite?`,
                { modal: true },
                'Yes'
            );
            if (overwrite !== 'Yes') return false;
        }
        
        if (keyStructure === 'nested') {
            setValueByPath(sourceData, key, value);
        } else {
            sourceData[key] = value;
        }

        const sortedKeys = Object.keys(sourceData).sort();
        const orderedData = Object.fromEntries(
            sortedKeys.map(k => [k, sourceData[k]])
        );
        
        const newContent = JSON.stringify(orderedData, null, 2);
        await vscode.workspace.fs.writeFile(sourceFileUri, Buffer.from(newContent, 'utf8'));

        vscode.window.showInformationMessage(`Updated '${key}' in ${sourceLang}.json`);
        vscode.commands.executeCommand('i18n-helper._internal.refreshAll');
        return true;

    } catch (error) {
        vscode.window.showErrorMessage(`Failed to update ${sourceLang}.json: ${error}`);
        return false;
    }
}

export async function extractAsKey() {
    const editor = vscode.window.activeTextEditor;
    if (!editor || editor.selection.isEmpty) {
        vscode.window.showWarningMessage('Please select text to extract.');
        return;
    }
    
    const selection = editor.document.getText(editor.selection);
    const key = selection.replace(/['"]/g, '');

    const value = await vscode.window.showInputBox({
        prompt: `Enter translation value for the key '${key}'`,
        ignoreFocusOut: true
    });

    if (value === undefined) { return; }

    await updateSourceJsonFile(key, value);
}

// --- MODIFIED FUNCTION SIGNATURE AND LOGIC ---
export async function extractAsValue(context?: { range: vscode.Range }) {
    const editor = vscode.window.activeTextEditor;
    if (!editor) { return; }

    let selectionRange: vscode.Range | vscode.Selection;
    
    // Determine the range: either from the Quick Fix context or the user's selection.
    if (context?.range) {
        selectionRange = context.range;
    } else if (!editor.selection.isEmpty) {
        selectionRange = editor.selection;
    } else {
        vscode.window.showWarningMessage('Please select text to extract.');
        return;
    }

    const selection = editor.document.getText(selectionRange);
    const value = selection.replace(/['"]/g, '');

    const config = getConfiguration();
    const sourceLang = config.get<string>('sourceLanguage');
    const i18nDirUri = await getI18nDirUri();
    let existingKeys: string[] = [];
    if (sourceLang && i18nDirUri) {
        const sourceFileUri = vscode.Uri.joinPath(i18nDirUri, `${sourceLang}.json`);
        const sourceData = await readJsonFile(sourceFileUri);
        if (sourceData) {
            const allKeys = Object.keys(sourceData);
            existingKeys = allKeys.filter(k => {
                const val = getValueFromPath(sourceData, k);
                return val === value;
            });
        }
    }

    let finalKey: string | undefined;

    if (existingKeys.length > 0) {
        const quickPickItems: vscode.QuickPickItem[] = existingKeys.map(k => ({
            label: k,
            description: `Use existing key for value: "${value}"`
        }));
        
        quickPickItems.push({ 
            label: 'Create a new key...', 
            description: 'Define a new key for this value' 
        });

        const choice = await vscode.window.showQuickPick(quickPickItems, {
            placeHolder: `The value "${value}" already exists. Choose an existing key or create a new one.`
        });

        if (!choice) { return; }

        if (choice.label === 'Create a new key...') {
            finalKey = await vscode.window.showInputBox({
                prompt: `Enter a new translation key for the value '${value}'`,
                value: toSnakeCase(value),
                ignoreFocusOut: true
            });
        } else {
            finalKey = choice.label;
        }
    } else {
        const suggestedKey = toSnakeCase(value);
        finalKey = await vscode.window.showInputBox({
            prompt: `Enter translation key for the value '${value}'`,
            value: suggestedKey,
            valueSelection: [0, suggestedKey.length],
            ignoreFocusOut: true
        });
    }

    if (!finalKey) { return; }

    const performReplacement = () => {
        const replacementPattern = config.get<string>('replacementPattern', "'{key}'.tr");
        const replacementString = replacementPattern.replace('{key}', finalKey);
        editor.edit(editBuilder => {
            // Use the determined range for replacement
            editBuilder.replace(selectionRange, replacementString);
        });
    };

    if (existingKeys.includes(finalKey)) {
        performReplacement();
        vscode.commands.executeCommand('i18n-helper._internal.refreshAll');
        return;
    }
    
    const success = await updateSourceJsonFile(finalKey, value);
    
    if (success) {
        performReplacement();
    }
}