import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/deck_manager.dart';

class CloneDeckForm extends GetView<DeckManagerController> {
  const CloneDeckForm({super.key});

  @override
  Widget build(BuildContext context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadSelectWithSearch(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.value}"),
                label: 'anki.deck_manager.source_deck'.tr,
                placeholder: 'anki.deck_manager.source_deck_placeholder'.tr,
                searchPlaceholder: 'anki.deck_manager.source_deck_search_placeholder'.tr,
                isMultiple: false,
                initialValue: [controller.srcDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  controller.srcDeck.value = value.single;
                },
              ),
              ShadInputWithValidate(
                  label: 'anki.common.target_deck'.tr,
                  placeholder: 'anki.deck_manager.target_deck_placeholder'.tr,
                  initialValue: controller.destDeck.value,
                  onChanged: (value) {
                    controller.destDeck.value = value;
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return 'anki.deck_manager.target_deck_required'.tr;
                    }
                    return null;
                  }),
              const SizedBox(height: 4),
            ],
          )),
    );
  }
}
