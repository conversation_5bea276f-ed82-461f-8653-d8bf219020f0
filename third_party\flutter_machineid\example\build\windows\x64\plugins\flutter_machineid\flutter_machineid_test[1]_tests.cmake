add_test([=[FlutterMachineidPlugin.GetPlatformVersion]=]  C:/Users/<USER>/code/anki_guru/third_party/flutter_machineid/example/build/windows/x64/plugins/flutter_machineid/Debug/flutter_machineid_test.exe [==[--gtest_filter=FlutterMachineidPlugin.GetPlatformVersion]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FlutterMachineidPlugin.GetPlatformVersion]=]  PROPERTIES WORKING_DIRECTORY C:/Users/<USER>/code/anki_guru/third_party/flutter_machineid/example/build/windows/x64/plugins/flutter_machineid SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==])
set(  flutter_machineid_test_TESTS FlutterMachineidPlugin.GetPlatformVersion)
