import 'package:flutter/material.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/ocr/ocr_controller.dart';

class SettingsPage extends GetView<OCRController> {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('anki.ocr.settings'.tr),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () async {
              // await controller.saveSettings();
            },
          ),
        ],
      ),
      body: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          children: [
            ShadCard(
              padding: const EdgeInsets.only(
                  left: 16, right: 16, top: 16, bottom: 16),
              child: Column(
                children: [
                  ShadSelectCustom(
                    key: ValueKey("ocr-${controller.ocrProvider.value}"),
                    label: 'anki.ocr.defaultOcrProvider'.tr,
                    placeholder: 'anki.ocr.selectOcrProvider'.tr,
                    options: controller.ocrProviderList,
                    initialValue: [controller.ocrProvider.value],
                    onChanged: (value) {
                      controller.ocrProvider.value = value.single;
                    },
                  ),
                ],
              ),
            ),
          ]),
    );
  }
}
