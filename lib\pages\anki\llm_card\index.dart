import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/anki/llm_card/components/llm_card.dart';
import 'package:anki_guru/pages/anki/llm_card/components/setting.dart';
import 'package:anki_guru/controllers/anki/llm_controller.dart';
import 'package:anki_guru/pages/anki/llm_card/components/prompt_form.dart';

class LLMCardPage extends StatefulWidget {
  const LLMCardPage({super.key});

  @override
  State<LLMCardPage> createState() => _LLMCardPageState();
}

class _LLMCardPageState extends State<LLMCardPage> {
  final controller = Get.put(LLMCardPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.llm_card.ai_card_title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Get.to(() => const SettingsPage(),
              //     transition: Transition.cupertino);
              showShadDialog(
                context: context,
                builder: (context) => const LLMCardSetting(),
              );
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('anki.llm_card.function_description'.tr, style: defaultPageTitleStyle),
            Text('anki.llm_card.feature_description'.tr, style: theme.textTheme.muted),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 2;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;
                
                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        print(controller.tabController.selected);
                        controller.update();
                      },
                      tabs: [
                        ShadTab(
                          value: 'card',
                          content: const LLMCard(),
                          width: tabWidth,
                          child: Text('anki.llm_card.smart_card_tab'.tr),
                        ),
                        ShadTab(
                          value: 'prompt',
                          content: const LLMPromptForm(),
                          width: tabWidth,
                          child: Text('anki.llm_card.prompt_management_tab'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
