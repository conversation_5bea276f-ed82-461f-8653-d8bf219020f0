[package]
name = "llm_rs"
version = "0.1.0"
edition = "2024"

[dependencies]
llm = { path = "../llm-main", features = [
    "openai",
    "anthropic",
    "ollama",
    "deepseek",
    "google",
] }
tokio = { version = "1", features = ["rt-multi-thread", "macros"] }
serde_json = "1.0"
futures = "0.3.31"
futures-util = "0.3.31"
reqwest = { version = "0.12.15", features = ["json", "stream"] }
repair_json = "0.1.0"
