import 'package:get/get.dart';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:anki_guru/controllers/video_notes/bilibili_api.dart';

class BiliLoginPage extends StatefulWidget {
  const BiliLoginPage({super.key});

  @override
  State<BiliLoginPage> createState() => _BiliLoginPageState();
}

class _BiliLoginPageState extends State<BiliLoginPage> {
  final _biliService = Get.find<BilibiliService>();
  String? _qrUrl;
  String? _qrcodeKey;
  bool _isReturning = false;

  @override
  void initState() {
    super.initState();
    _generateQRCode();
  }

  Future<void> _generateQRCode() async {
    try {
      setState(() {
        _qrUrl = null; // 清除旧的二维码
        _qrcodeKey = null;
      });

      final result = await _biliService.generateQRCode();

      // 确保组件仍然挂载
      if (!mounted) return;

      setState(() {
        // 这里的 url 是二维码的内容，不是图片URL
        _qrUrl = result['url']; // 这个URL是用来生成二维码的内容
        _qrcodeKey = result['qrcode_key'];
      });

      // 当二维码显示后，开始轮询登录状态
      if (_qrcodeKey != null) {
        _startPolling();
      }
    } catch (e) {
      setState(() {
        _qrUrl = null;
        _qrcodeKey = null;
      });
    }
  }

  Future<void> _startPolling() async {
    if (_qrcodeKey == null) return;

    try {
      // 开始登录流程
      await _biliService.startLogin(_qrcodeKey!);

      // 如果登录成功，自动返回
      if (_biliService.isLoggedIn.value && !_isReturning) {
        _isReturning = true;
        Get.back(result: true);
      }
    } catch (e) {
      // 错误处理已经在 BilibiliService 中完成
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('哔哩哔哩登录'),
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_qrUrl != null)
              // 使用 QrImageView 将 URL 渲染成二维码
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 2,
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: QrImageView(
                  data: _qrUrl!, // 使用 URL 生成二维码
                  version: QrVersions.auto,
                  size: 200.0,
                ),
              ),
            const SizedBox(height: 24),
            Obx(() => Text(
                  _biliService.loginStatus.value,
                  style: const TextStyle(fontSize: 16),
                )),
            const SizedBox(height: 16),
            if (_biliService.loginStatus.value == '二维码已过期，请重试')
              ElevatedButton.icon(
                onPressed: _generateQRCode,
                icon: const Icon(Icons.refresh),
                label: const Text('刷新二维码'),
              ),
          ],
        ),
      ),
    );
  }
}
