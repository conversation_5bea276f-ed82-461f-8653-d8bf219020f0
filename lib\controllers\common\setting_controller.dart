import 'dart:io';
import 'package:anki_guru/controllers/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:launch_at_startup/launch_at_startup.dart';

class SettingController extends GetxController {
  // 已有数据
  final desktopCardModeList = [
    {
      'value': 'apkg',
      'label': 'settings.cardMode.exportApkg'.tr,
    },
    {
      'value': 'ankiconnect',
      'label': 'settings.cardMode.directAnki'.tr,
    }
  ];
  final iosCardModeList = [
    {
      'value': 'apkg',
      'label': 'settings.cardMode.exportApkg'.tr,
    }
  ];
  final androidCardModeList = [
    {
      'value': 'apkg',
      'label': 'settings.cardMode.exportApkg'.tr,
    },
    {
      'value': 'ankidroid',
      'label': 'settings.cardMode.directAnkidroid'.tr,
    },
  ];
  // 配置
  var theme = 'light'.obs;
  final themeData = ThemeData().obs;
  final currentIndex = 0.obs;
  final systemTheme = "light".obs;
  final exitMode = "exit".obs;
  final isLaunchAtStartup = false.obs;
  final isAlwaysOnTop = false.obs;
  final version = "".obs;
  final buildNumber = "".obs;
  final serverPort = 52025.obs;
  final pyServerPort = 52026.obs;
  // 首选项
  final cardMode = "apkg".obs;
  final outputDir = "".obs;
  final ankiConnectUrl = "http://localhost:8765".obs;
  final pdfReaderPath = "".obs;
  final ankiPath = "".obs;
  final autoStartAnki = false.obs;
  final ffmpegPath = "".obs;
  final language = "".obs;
  final _storage = StorageManager();

  // 错误
  final outputDirError = "".obs;
  final pdfReaderPathError = "".obs;
  final ffmpegPathError = "".obs;
  @override
  void onInit() async {
    super.onInit();
    systemTheme.value = getSystemTheme();
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    version.value = packageInfo.version;
    buildNumber.value = packageInfo.buildNumber;
    loadSettings();
    if (outputDir.value.isEmpty) {
      outputDir.value = await PathUtils.downloadDir;
    }
  }

  Future<void> loadSettings() async {
    theme.value =
        _storage.read(StorageBox.default_, AnkiStorageKeys.themeMode, "light");
    String defaultCardMode = "apkg";
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      defaultCardMode = "ankiconnect";
    }
    cardMode.value = _storage.read(
        StorageBox.default_, AnkiStorageKeys.cardMode, defaultCardMode);
    outputDir.value =
        _storage.read(StorageBox.default_, AnkiStorageKeys.outputDir, "");
    ankiConnectUrl.value = _storage.read(StorageBox.default_,
        AnkiStorageKeys.ankiConnectUrl, "http://localhost:8765");
    ffmpegPath.value =
        _storage.read(StorageBox.default_, AnkiStorageKeys.ffmpegPath, "");
    pdfReaderPath.value =
        _storage.read(StorageBox.default_, AnkiStorageKeys.pdfReaderPath, "");
    ankiPath.value =
        _storage.read(StorageBox.default_, AnkiStorageKeys.ankiPath, "");
    autoStartAnki.value = _storage.read(
        StorageBox.default_, AnkiStorageKeys.autoStartAnki, false);
    language.value =
        _storage.read(StorageBox.default_, AnkiStorageKeys.language, "");

    if (ffmpegPath.value.isEmpty) {
      ffmpegPath.value = await PathUtils.which("ffmpeg");
    }
    changeTheme(theme.value);
  }

  void saveSettings({bool showToast = true}) {
    _storage.write(StorageBox.default_, AnkiStorageKeys.themeMode, theme.value);
    _storage.write(
        StorageBox.default_, AnkiStorageKeys.cardMode, cardMode.value);
    _storage.write(
        StorageBox.default_, AnkiStorageKeys.outputDir, outputDir.value);
    _storage.write(StorageBox.default_, AnkiStorageKeys.ankiConnectUrl,
        ankiConnectUrl.value);
    _storage.write(
        StorageBox.default_, AnkiStorageKeys.ffmpegPath, ffmpegPath.value);
    _storage.write(StorageBox.default_, AnkiStorageKeys.pdfReaderPath,
        pdfReaderPath.value);
    _storage.write(
        StorageBox.default_, AnkiStorageKeys.ankiPath, ankiPath.value);
    _storage.write(StorageBox.default_, AnkiStorageKeys.autoStartAnki,
        autoStartAnki.value);
    _storage.write(
        StorageBox.default_, AnkiStorageKeys.language, language.value);
    if (showToast) {
      showToastNotification(null, 'settings.saved'.tr, '');
    }
  }

  String getSystemTheme() {
    return SchedulerBinding.instance.platformDispatcher.platformBrightness ==
            Brightness.light
        ? "light"
        : "dark";
  }

  Future<bool> getIsLaunchAtStartup() async {
    return await launchAtStartup.isEnabled();
  }

  Future<void> setLaunchAtStartup(bool enabled) async {
    if (enabled) {
      await launchAtStartup.enable();
    } else {
      await launchAtStartup.disable();
    }
    _storage.write(StorageBox.default_, AnkiStorageKeys.isLaunchAtStartup,
        enabled.toString());
  }

  void changeTheme(String mode) {
    theme.value = mode;
    // if (mode == 'light') {
    //   Get.changeThemeMode(ThemeMode.light);
    // } else if (mode == 'dark') {
    //   Get.changeThemeMode(ThemeMode.dark);
    // } else {
    //   Get.changeThemeMode(ThemeMode.system);
    // }
  }

  void changeCardMode(String mode) {
    cardMode.value = mode;
    _storage.write(StorageBox.default_, AnkiStorageKeys.cardMode, mode);
  }

  Future<void> setLanguage(String languageCode) async {
    language.value = languageCode;

    // 只保存语言设置到存储，不立即应用
    // 下次应用启动时会读取并应用该设置
    _storage.write(StorageBox.default_, AnkiStorageKeys.language, languageCode);

    // 注释掉立即应用语言的代码
    // switch (languageCode) {
    //   case 'en':
    //     await LocaleSettings.setLocale(AppLocale.en);
    //     break;
    //   case 'ja':
    //     await LocaleSettings.setLocale(AppLocale.ja);
    //     break;
    //   case 'zh':
    //     await LocaleSettings.setLocale(AppLocale.zh);
    //     break;
    //   case 'zh_Hant':
    //     await LocaleSettings.setLocale(AppLocale.zhHant);
    //     break;
    //   default:
    //     // 如果没有指定语言或者语言不支持，使用设备语言
    //     await LocaleSettings.useDeviceLocale();
    //     break;
    // }
  }

  String getTheme() {
    if (theme.value == 'system') {
      return getSystemTheme();
    }
    return theme.value;
  }

  String getExportCardMode() {
    String exportCardMode = "apkg";
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      exportCardMode = _storage.read(
          StorageBox.default_, AnkiStorageKeys.cardMode, "ankiconnect");
    }
    if (Platform.isAndroid) {
      exportCardMode = _storage.read(
          StorageBox.default_, AnkiStorageKeys.cardMode, "ankidroid");
    }
    if (Platform.isIOS) {
      exportCardMode = "apkg";
    }
    return exportCardMode;
  }
}
