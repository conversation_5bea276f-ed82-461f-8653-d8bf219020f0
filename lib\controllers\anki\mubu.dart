import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:flutter/material.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:ulid/ulid.dart';

/// 幕布API错误
class MubuApiException implements Exception {
  final String message;
  final int? code;
  final dynamic data;

  MubuApiException(this.message, {this.code, this.data});

  @override
  String toString() => 'MubuApiException: $message (code: $code)';
}

/// 幕布文档模型类
class MubuDocument {
  final String id;
  final String name;
  final String? folderId;
  final int? createTime;
  final int? updateTime;
  final int? itemCount;
  final bool? encrypted;
  final Map<String, dynamic>? author;
  final String? definition;
  final List<dynamic>? directory;
  final List<MubuNode>? nodes;

  MubuDocument({
    required this.id,
    required this.name,
    this.folderId,
    this.createTime,
    this.updateTime,
    this.itemCount,
    this.encrypted,
    this.author,
    this.definition,
    this.directory,
    this.nodes,
  });

  /// 从JSON映射创建文档对象
  factory MubuDocument.fromJson(Map<String, dynamic> json) {
    String docId = json['id'] as String? ?? '';
    // 如果提供了docId但在json中为空，尝试从其他可能的字段名获取
    if (docId.isEmpty) {
      docId = json['docId'] as String? ?? json['documentId'] as String? ?? '';
    }

    return MubuDocument(
      id: docId,
      name: json['name'] ?? '',
      folderId: json['folderId'],
      createTime: json['createTime'],
      updateTime: json['updateTime'],
      itemCount: json['itemCount'],
      encrypted: json['encrypted'] == 1,
      author: json['author'] as Map<String, dynamic>?,
      definition: json['definition'] as String?,
      directory: json['directory'] as List<dynamic>?,
      nodes: null, // 节点结构需要单独解析
    );
  }

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'id': id.isNotEmpty ? id : '', // 确保id字段存在且不为null
      'name': name,
    };

    // 只添加非null的字段
    if (folderId != null) json['folderId'] = folderId;
    if (createTime != null) json['createTime'] = createTime;
    if (updateTime != null) json['updateTime'] = updateTime;
    if (itemCount != null) json['itemCount'] = itemCount;
    if (encrypted != null) json['encrypted'] = encrypted;
    if (author != null) json['author'] = author;
    if (definition != null) json['definition'] = definition;
    if (directory != null) json['directory'] = directory;

    // 添加节点信息
    if (nodes != null && nodes!.isNotEmpty) {
      json['nodes'] = nodes!.map((node) => node.toJson()).toList();
    }

    return json;
  }

  @override
  String toString() {
    return 'MubuDocument{id: $id, name: $name, itemCount: $itemCount}';
  }
}

/// 幕布文档节点模型类
class MubuNode {
  final String id;
  final String text;
  final int level;
  final String? color; // 节点文本颜色
  final String? note; // 节点备注
  final int? modified; // 修改时间戳
  final List<dynamic>? images;
  final List<dynamic>? refs;
  final List<dynamic>? links;
  final Map<String, dynamic>? style;
  final List<MubuNode> children;

  MubuNode({
    required this.id,
    required this.text,
    required this.level,
    this.color,
    this.note,
    this.modified,
    this.images,
    this.refs,
    this.links,
    this.style,
    this.children = const [],
  });

  /// 从JSON映射创建节点对象
  factory MubuNode.fromJson(Map<String, dynamic> json, {int level = 0}) {
    final List<MubuNode> children = [];

    if (json.containsKey('children') && json['children'] is List) {
      for (final child in json['children']) {
        children.add(MubuNode.fromJson(child, level: level + 1));
      }
    }

    return MubuNode(
      id: json['id'] ?? '',
      text: json['text'] ?? '',
      level: level,
      color: json['color'] as String?,
      note: json['note'] as String?,
      modified: json['modified'] as int?,
      images: json['images'] as List<dynamic>?,
      refs: json['refs'] as List<dynamic>?,
      links: json['links'] as List<dynamic>?,
      style: json['style'] as Map<String, dynamic>?,
      children: children,
    );
  }

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'id': id,
      'text': text,
      'level': level,
    };

    // 只添加非null字段
    if (color != null) json['color'] = color;
    if (note != null) json['note'] = note;
    if (modified != null) json['modified'] = modified;
    if (images != null && images!.isNotEmpty) json['images'] = images;
    if (refs != null && refs!.isNotEmpty) json['refs'] = refs;
    if (links != null && links!.isNotEmpty) json['links'] = links;
    if (style != null && style!.isNotEmpty) json['style'] = style;

    // 添加子节点
    if (children.isNotEmpty) {
      json['children'] = children.map((child) => child.toJson()).toList();
    }

    return json;
  }

  @override
  String toString() {
    return 'MubuNode{id: $id, text: $text, level: $level, childrenCount: ${children.length}}';
  }

  /// 搜索节点及其子节点中包含指定文本的节点
  List<MubuNode> searchNodes(String query) {
    final results = <MubuNode>[];
    final lowerQuery = query.toLowerCase();

    if (text.toLowerCase().contains(lowerQuery)) {
      results.add(this);
    }

    for (final child in children) {
      results.addAll(child.searchNodes(query));
    }

    return results;
  }
}

/// 幕布API客户端工具类
///
/// 提供对幕布API的封装，方便程序调用幕布文档服务。
/// 本工具类实现了以下功能：
/// 1. 获取文档列表和文件夹列表
/// 2. 获取指定文档的详细内容
/// 3. 获取文档条目数量统计
/// 4. 解析文档结构并支持导出为纯文本格式
///
/// 使用说明：
/// 1. 需要提供有效的cookie才能访问幕布API
/// 2. cookie可以通过浏览器登录幕布网站后，从浏览器开发者工具的网络请求中获取
/// 3. jwt-token会自动从cookie中提取，不需要手动设置
class MubuCardPageController extends GetxController {
  static MubuCardPageController get to => Get.find<MubuCardPageController>();

  final Logger logger = Logger();
  final Dio _dio = Dio();
  final String baseUrl = 'https://api2.mubu.com';
  // 已有数据
  final documentList = <Map<String, dynamic>>[].obs;
  final tabController = ShadTabsController(value: 'card');
  final exportFormatList = [
    {"label": "Markdown", "value": "md"},
    {"label": "TXT", "value": "txt"},
    {"label": "HTML", "value": "html"},
    {"label": "JSON", "value": "json"},
    {"label": "OPML", "value": "opml"},
  ].obs;
  final sepList = [
    {"label": "---", "value": "---"},
  ].obs;
  final clozeModeList = [
    {"label": 'anki.mubu.mask_one_guess_one'.tr, "value": "mask_one_guess_one"},
    {"label": 'anki.mubu.mask_all_guess_one'.tr, "value": "mask_all_guess_one"},
    {"label": 'anki.mubu.mask_all_guess_all'.tr, "value": "mask_all_guess_all"},
    {"label": "'anki.mubu.free_guess'.tr", "value": "free_guess"},
  ].obs;
  final clozeGrammarList = [
    {"label": "[[c1::xx]]", "value": "[[c1::xx]]"},
    {"label": "{{c1::xx}}", "value": "{{c1::xx}}"},
    {"label": "[[xx]]", "value": "[[xx]]"},
    {"label": "{{xx}}", "value": "{{xx}}"},
    {"label": "==xx==", "value": "==xx=="},
    {"label": "**xx**", "value": "**xx**"},
    {"label": "***xx***", "value": "***xx***"},
    {"label": "*xx*", "value": "*xx*"},
  ].obs;
  final cardModeList = [
    {"label": 'anki.mubu.hierarchy'.tr, "value": "hierarchy"},
    {"label": 'anki.mubu.node_desc'.tr, "value": "node_desc"},
    {"label": 'anki.mubu.mindmap'.tr, "value": "mindmap"},
  ];
  final cardTypeList = [
    {"label": 'anki.mubu.qa'.tr, "value": "qa"},
    {"label": 'anki.mubu.cloze'.tr, "value": "cloze"},
  ];
  final clozeStyleList = [
    {"label": 'anki.mubu.bold'.tr, "value": "bold"},
    {"label": 'anki.mubu.italic'.tr, "value": "italic"},
    {"label": 'anki.mubu.underline'.tr, "value": "underline"},
    {"label": 'anki.mubu.strikeout'.tr, "value": "strikeout"},
    {"label": 'anki.mubu.text_color'.tr, "value": "text_color"},
    {"label": 'anki.mubu.text_highlight'.tr, "value": "text_highlight"},
  ];
  final mubuColorList = [
    {"label": 'anki.mubu.yellow'.tr, "value": "yellow"},
    {"label": 'anki.mubu.red'.tr, "value": "red"},
    {"label": 'anki.mubu.green'.tr, "value": "green"},
    {"label": 'anki.mubu.blue'.tr, "value": "blue"},
    {"label": 'anki.mubu.purple'.tr, "value": "purple"},
  ].obs;
  final mubuHighlightColorList = [
    {"label": 'anki.mubu.yellow'.tr, "value": "yellow"},
    {"label": 'anki.mubu.red'.tr, "value": "red"},
    {"label": 'anki.mubu.grey'.tr, "value": "grey"},
    {"label": 'anki.mubu.olive'.tr, "value": "olive"},
    {"label": 'anki.mubu.blue'.tr, "value": "blue"},
    {"label": 'anki.mubu.pink'.tr, "value": "pink"},
    {"label": 'anki.mubu.cyan'.tr, "value": "cyan"},
  ].obs;
  final questionNodeLevelList = [
    {"label": 'anki.mubu.level_1'.tr, "value": "1"},
    {"label": 'anki.mubu.level_2'.tr, "value": "2"},
    {"label": 'anki.mubu.level_3'.tr, "value": "3"},
    {"label": 'anki.mubu.level_4'.tr, "value": "4"},
    {"label": 'anki.mubu.level_5'.tr, "value": "5"},
    {"label": 'anki.mubu.level_6'.tr, "value": "6"},
  ];
  final commonRegexList = [
    {"label": 'anki.mubu.front'.tr, "value": "__reserved_front__"},
    {"label": 'anki.mubu.back'.tr, "value": "__reserved_back__"},
    {"label": 'anki.mubu.none'.tr, "value": ""},
    {"label": 'anki.mubu.sep'.tr, "value": "---"},
    {"label": "1[.．、] xx", "value": "^\\s*\\d+\\s*[.．、]"},
    {"label": "1 xx", "value": "^\\s*\\d+"},
    {"label": "一[、.．] xx", "value": "^\\s*[一二三四五六七八九十]+[.．、]"},
    {"label": "[a-zA-Z]+", "value": "^\\s*[a-zA-Z]+"},
    {"label": "中文字符集", "value": "^\\s*[\u4e00-\u9fff]+"},
    {"label": "问: xx", "value": "^\\s*问[:：]"},
    {"label": "问题: xx", "value": "^\\s*问题[:：]"},
    {"label": "答: xx", "value": "^\\s*答[:：]"},
    {"label": "答案: xx", "value": "^\\s*答案[:：]"},
    {"label": "【答案】 xx", "value": "^\\s*【答案】"},
    {"label": "【答案解析】 xx", "value": "^\\s*【答案解析"},
    {"label": "【正确答案】 xx", "value": "^\\s*【正确答案"},
    {"label": "解析: xx", "value": "^\\s*解析[:：]"},
    {"label": "提示: xx", "value": "^\\s*提示[:：]"},
    {"label": "Q: xx", "value": "^\\s*Q[:：]"},
    {"label": "Q1: xx", "value": "^\\s*Q\\d+[:：]"},
    {"label": "A: xx", "value": "^\\s*A[:：]"},
    {"label": "A1: xx", "value": "^\\s*A\\d+[:：]"},
    {"label": "h: xx", "value": "^\\s*h[:：]"},
    {"label": "* xx", "value": "^\\s*\\*"},
    {"label": "- xx", "value": "^\\s*-"},
    {"label": "+ xx", "value": "^\\s*\\+"},
    {"label": "> xx", "value": "^\\s*>"},
    {"label": "# xx", "value": "^\\s*#\\s"},
    {"label": "## xx", "value": "^\\s*##\\s"},
    {"label": "### xx", "value": "^\\s*###\\s"},
    {"label": "#### xx", "value": "^\\s*####\\s"},
    {"label": "(1) xx", "value": "^\\s*[\\(（]\\d+[\\)）]"},
    {"label": "1) xx", "value": "^\\s*\\d+[\\)）])"},
    {"label": "A[.．、] xx", "value": "^\\s*[A-Z]+[.．、]"},
    {"label": "(a) xx", "value": "^\\s*[\\(（][a-z]+[\\)）]"},
    {"label": "a) xx", "value": "^\\s*[a-z]+[\\)）]"},
  ];

  // 表单参数
  /// 制卡相关
  final parentDeck = ''.obs;
  final document = ''.obs;
  final cookie = ''.obs;
  final mapID = ''.obs;
  final isCreateSubDeck = false.obs;
  final cardModel = "Kevin Text QA Card v2".obs;
  final cardMode = "hierarchy".obs;
  final cardType = "qa".obs;
  final clozeMode = "free_guess".obs;
  final oneClozePeCard = false.obs;
  final questionNodeLevel = "1".obs;
  final isShowSource = true.obs;
  final isAnswerCloze = false.obs;
  final isPartCard = false.obs; // 是否部分制卡
  final tags = <String>[].obs;
  final fieldMappings = <String, PatternMatch>{}.obs;
  final maskTypes = <String>['bold', 'text_highlight'].obs;
  final textColorList = <String>[].obs;
  final highlightColorList = <String>[].obs;

  /// 导出相关
  final outputDir = "".obs;
  final exportFormat = "md".obs;

  // 响应式状态
  final RxString _token = ''.obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isAuthenticated = false.obs;
  final RxList<MubuDocument> _recentDocuments = <MubuDocument>[].obs;
  final RxString _errorMessage = ''.obs;

  // Getters
  String get token => _token.value;
  bool get isLoading => _isLoading.value;
  bool get isAuthenticated => _isAuthenticated.value;
  List<MubuDocument> get recentDocuments => _recentDocuments.toList();
  String get errorMessage => _errorMessage.value;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final ankiConnectController = Get.find<AnkiConnectController>();
  final _storage = StorageManager();

  @override
  void onInit() async {
    super.onInit();
    // 初始化设置
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'User-Agent':
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
    };

    // 添加请求拦截器，处理公共请求头
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // 如果有Cookie和jwt-token，都设置到请求头中
          if (cookie.isNotEmpty) {
            options.headers['Cookie'] = cookie.value;
          }
          if (_token.isNotEmpty) {
            options.headers['jwt-token'] = _token.value;
          }
          return handler.next(options);
        },
        onError: (DioException e, handler) {
          _handleError(e);
          return handler.next(e);
        },
      ),
    );

    // Now set initial values with null checks
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }

    await updateFieldList(cardModel.value);
    outputDir.value = await PathUtils.downloadDir;

    // 尝试从存储中加载保存的Cookie
    final savedCookie = _storage.read(StorageBox.default_, 'mubu_cookie', '');
    if (savedCookie.isNotEmpty) {
      logger.i('Found saved Mubu cookie, attempting to use it');
      // 使用保存的Cookie自动登录
      final validationResult = await validateCookie(savedCookie);
      if (validationResult.isEmpty) {
        logger.i('Saved cookie validation successful');
        cookie.value = savedCookie;
      } else {
        logger.w('Saved cookie validation failed: $validationResult');
      }
    }
  }

  Future<void> updateFieldList(String modelName) async {
    try {
      // 调用updateFieldList并等待它完成，但不使用返回值
      await ankiConnectController.updateFieldList(modelName);
      fieldMappings.clear();

      if (cardType.value == "qa") {
        for (var field in ankiConnectController.fieldList) {
          if (field == "Front") {
            fieldMappings[field] =
                PatternMatch(field, "__reserved_front__", null, true);
          } else if (field == "Back") {
            fieldMappings[field] =
                PatternMatch(field, "__reserved_back__", null, true);
          } else {
            fieldMappings[field] = PatternMatch(field, "", null, true);
          }
          // 更新字段映射相关的UI
          final updateId = 'field_mappings_${field}_$modelName';
          update([updateId]);
        }
      }

      // 更新主控制器状态
      update();
    } catch (e, stack) {
      logger.e("updateFieldList error", error: e, stackTrace: stack);
    }
  }

  // 获取字段映射值
  PatternMatch? getFieldMappingValue(String field) {
    if (fieldMappings.containsKey(field)) {
      return fieldMappings[field];
    }
    return null;
  }

  // 更新字段映射
  void updateFieldMapping(String field, PatternMatch value) {
    fieldMappings[field] = value;
    fieldMappings.refresh();
    update();
  }

  /// 处理错误
  void _handleError(dynamic error) {
    String errorMessage = 'anki.mubu.unknow_error'.tr;

    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
          errorMessage = 'anki.mubu.connection_timeout'.tr;
          break;
        case DioExceptionType.sendTimeout:
          errorMessage = 'anki.mubu.send_timeout'.tr;
          break;
        case DioExceptionType.receiveTimeout:
          errorMessage = 'anki.mubu.receive_timeout'.tr;
          break;
        case DioExceptionType.badResponse:
          errorMessage = 'anki.mubu.server_error'.tr;
          break;
        case DioExceptionType.cancel:
          errorMessage = 'anki.mubu.request_canceled'.tr;
          break;
        default:
          errorMessage = 'anki.mubu.network_error'.tr;
          break;
      }
    } else if (error is MubuApiException) {
      errorMessage = error.message;
    }

    _errorMessage.value = errorMessage;
    logger.e('Mubu API Error: $errorMessage');
  }

  /// 清除错误消息
  void clearError() {
    _errorMessage.value = '';
  }

  /// 从Cookie中提取jwt-token
  ///
  /// 幕布的Cookie中包含jwt-token字段，格式为"jwt-token=xxxxx;"
  String _extractTokenFromCookie(String cookieStr) {
    if (cookieStr.isEmpty) return '';

    // 查找jwt-token字段
    final RegExp regex = RegExp(r'[Jj]wt-[Tt]oken=([^;]+)');
    final match = regex.firstMatch(cookieStr);

    if (match != null && match.groupCount >= 1) {
      return match.group(1) ?? '';
    }

    return '';
  }

  /// 设置Cookie并提取jwt-token
  ///
  /// 这是API的主要入口点，设置身份验证信息
  /// [cookieStr] 完整的cookie字符串，包含jwt-token
  /// [autoRefresh] 是否自动刷新文档列表
  void setCookie(String cookieStr, {bool autoRefresh = true}) {
    if (cookieStr.isEmpty) {
      _errorMessage.value = 'anki.mubu.cookie_cannot_be_empty'.tr;
      return;
    }

    cookie.value = cookieStr;

    // 从Cookie中提取jwt-token
    final token = _extractTokenFromCookie(cookieStr);
    if (token.isNotEmpty) {
      _token.value = token;
      _isAuthenticated.value = true;

      // 获取最近文档
      if (autoRefresh) {
        _fetchDocumentsAndUpdateList();
      }
    } else {
      _errorMessage.value = 'anki.mubu.cannot_extract_token'.tr;
    }
  }

  /// 内部方法：获取文档并更新列表
  Future<void> _fetchDocumentsAndUpdateList() async {
    try {
      _isLoading.value = true;

      // 获取文档列表
      final data = await getAllDocumentsRaw();
      final documents = (data['documents'] as List<dynamic>? ?? [])
          .map((doc) => MubuDocument.fromJson(doc))
          .toList();

      // 更新文档列表
      _recentDocuments.value = documents;

      // 更新UI选择列表
      documentList.clear();
      for (final doc in documents) {
        documentList.add({
          'value': doc.id,
          'label': doc.name,
        });
      }
    } catch (e) {
      _handleError(e);
    } finally {
      _isLoading.value = false;
    }
  }

  /// 验证Cookie并提取jwt-token
  Future<String> validateCookie(String cookieStr) async {
    if (cookieStr.isEmpty) {
      return "anki.mubu.cookie_cannot_be_empty".tr;
    }

    // 从Cookie中提取jwt-token
    final token = _extractTokenFromCookie(cookieStr);
    if (token.isEmpty) {
      return "anki.mubu.cookie_validation_failed".tr;
    }

    try {
      setCookie(cookieStr);
      // 尝试获取文档列表验证token有效性
      final data = await getAllDocumentsRaw();

      // 验证成功后立即更新文档列表
      _isAuthenticated.value = true;
      // 临时设置token和cookie进行验证
      cookie.value = cookieStr;
      _token.value = token;
      final documents = (data['documents'] as List<dynamic>? ?? [])
          .map((doc) => MubuDocument.fromJson(doc))
          .toList();

      _recentDocuments.value = documents;

      // 更新文档列表，用于UI选择
      documentList.clear();
      for (final doc in documents) {
        documentList.add({
          'value': doc.id,
          'label': doc.name,
        });
      }

      if (documents.isNotEmpty) {
        document.value = documents.first.id;
      }

      // 验证成功后，将cookie保存到_storage
      _storage.write(StorageBox.default_, 'mubu_cookie', cookieStr);
      logger.i('Mubu cookie saved to storage successfully');

      return ""; // 验证成功，返回空字符串
    } catch (e) {
      _token.value = '';
      cookie.value = '';

      // 清除可能存在的无效Cookie
      clearSavedCookie();

      logger.e("Cookie验证失败: $e");
      return "anki.mubu.cookie_validation_failed".tr;
    }
  }

  /// 检查是否已设置认证信息
  bool get hasAuth => _token.isNotEmpty;

  /// 刷新最近文档列表
  Future<void> refreshRecentDocuments() async {
    if (!hasAuth) {
      _errorMessage.value = 'anki.mubu.no_auth_info'.tr;
      return;
    }

    await _fetchDocumentsAndUpdateList();
  }

  /// 发送请求到幕布API
  Future<Map<String, dynamic>> _request(
      String endpoint, Map<String, dynamic> payload) async {
    try {
      if (!hasAuth) {
        throw MubuApiException('anki.mubu.no_auth_info'.tr);
      }

      _isLoading.value = true;
      final response = await _dio.post(
        '$baseUrl$endpoint',
        data: payload,
      );

      final data = response.data;

      if (data['code'] != 0) {
        final errorMsg = data['msg'] ?? 'Unknown error';
        throw MubuApiException('Mubu API error: $errorMsg',
            code: data['code'], data: data);
      }

      return data;
    } on DioException catch (e) {
      _handleError(e);
      rethrow;
    } catch (e) {
      if (e is! MubuApiException) {
        _handleError(e);
      }
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// 获取文档内容
  ///
  /// [docId] 文档ID
  /// [password] 文档密码，如果有的话
  /// [isFromDocDir] 是否从文档目录获取，默认为true
  Future<Map<String, dynamic>> getDocumentRaw(String docId,
      {String password = '', bool isFromDocDir = true}) async {
    final endpoint = '/v3/api/document/edit/get';
    final payload = {
      'docId': docId,
      'password': password,
      'isFromDocDir': isFromDocDir,
    };

    final response = await _request(endpoint, payload);
    return response['data'] ?? {};
  }

  /// 获取文档内容并转换为MubuDocument对象
  ///
  /// [docId] 文档ID
  /// [password] 文档密码，如果有的话
  /// [isFromDocDir] 是否从文档目录获取，默认为true
  /// [parseNodes] 是否解析文档节点结构
  Future<MubuDocument> getDocument(
    String docId, {
    String password = '',
    bool isFromDocDir = true,
    bool parseNodes = false,
    bool fetchFullInfo = false,
  }) async {
    final data = await getDocumentRaw(docId,
        password: password, isFromDocDir: isFromDocDir);

    // 尝试获取更详细的文档信息
    Map<String, dynamic> docInfo = {...data};

    // 确保文档ID被正确设置，这是最重要的
    docInfo['id'] = docId;

    // 如果需要获取完整信息，从文档列表获取额外的元数据
    if (fetchFullInfo) {
      try {
        final allDocsData = await getAllDocumentsRaw();
        final docs = allDocsData['documents'] as List<dynamic>? ?? [];
        final matchDoc = docs.firstWhere(
          (doc) => doc['id'] == docId,
          orElse: () => <String, dynamic>{},
        );

        if (matchDoc.isNotEmpty) {
          // 合并来自文档列表的元数据
          docInfo.addAll({
            'id': matchDoc['id'] ?? docId, // 确保ID不丢失
            'createTime': matchDoc['createTime'],
            'updateTime': matchDoc['updateTime'],
            'itemCount': matchDoc['itemCount'],
            'folderId': matchDoc['folderId'],
            'encrypted': matchDoc['encrypted'],
          });
        }
      } catch (e) {
        logger.w('获取完整文档信息时出错: $e');
        // 继续使用已有的数据
      }
    }

    final document = MubuDocument.fromJson(docInfo);

    if (parseNodes) {
      // 解析文档结构
      final nodes = await parseDocumentStructure(docId);
      return MubuDocument(
        id: docId, // 直接使用传入的docId而非document.id
        name: document.name,
        folderId: document.folderId,
        createTime: document.createTime,
        updateTime: document.updateTime,
        itemCount: document.itemCount,
        encrypted: document.encrypted,
        author: document.author,
        definition: document.definition,
        directory: document.directory,
        nodes: nodes,
      );
    }

    // 如果document.id为空，使用传入的docId
    if (document.id.isEmpty) {
      return MubuDocument(
        id: docId,
        name: document.name,
        folderId: document.folderId,
        createTime: document.createTime,
        updateTime: document.updateTime,
        itemCount: document.itemCount,
        encrypted: document.encrypted,
        author: document.author,
        definition: document.definition,
        directory: document.directory,
        nodes: document.nodes,
      );
    }

    return document;
  }

  /// 获取所有文档列表
  ///
  /// [start] 起始位置，分页获取时使用
  Future<Map<String, dynamic>> getAllDocumentsRaw({String start = ''}) async {
    final endpoint = '/v3/api/list/get_all_documents_page';
    final payload = {
      'start': start,
    };

    final response = await _request(endpoint, payload);
    return response['data'] ?? {};
  }

  /// 获取所有文档列表并转换为MubuDocument对象列表
  ///
  /// [start] 起始位置，分页获取时使用
  Future<List<MubuDocument>> getAllDocuments({String start = ''}) async {
    final data = await getAllDocumentsRaw(start: start);
    final documents = data['documents'] as List<dynamic>? ?? [];

    return documents.map((doc) => MubuDocument.fromJson(doc)).toList();
  }

  /// 获取所有文件夹列表
  ///
  /// [start] 起始位置，分页获取时使用
  Future<List<Map<String, dynamic>>> getAllFolders({String start = ''}) async {
    final data = await getAllDocumentsRaw(start: start);
    final folders = data['folders'] as List<dynamic>? ?? [];

    return folders.map((folder) => folder as Map<String, dynamic>).toList();
  }

  /// 获取文档条目数
  ///
  /// [folderId] 文件夹ID，默认为0（根目录）
  /// [source] 来源，默认为"home"
  Future<Map<String, dynamic>> getItemCount(
      {dynamic folderId = 0, String source = 'home'}) async {
    final endpoint = '/v3/api/list/item_count';
    final payload = {
      'folderId': folderId,
      'source': source,
    };

    final response = await _request(endpoint, payload);
    return response['data'] ?? {};
  }

  /// 获取所有文档ID
  Future<List<String>> getAllDocumentIds() async {
    final documents = await getAllDocuments();
    return documents.map((doc) => doc.id).toList();
  }

  /// 通过文档名称获取文档内容
  Future<MubuDocument?> getDocumentByName(String name,
      {bool parseNodes = false}) async {
    // 首先尝试从缓存的文档列表中查找
    if (documentList.isNotEmpty) {
      final docInfo =
          documentList.firstWhereOrNull((doc) => doc['label'] == name);
      if (docInfo != null) {
        return await getDocument(docInfo['value'], parseNodes: parseNodes);
      }
    }

    // 如果在缓存中没有找到，则获取所有文档并查找
    final documents = await getAllDocuments();
    for (final doc in documents) {
      if (doc.name == name) {
        return await getDocument(doc.id, parseNodes: parseNodes);
      }
    }

    logger.w('Document with name "$name" not found');
    return null;
  }

  /// 搜索文档
  ///
  /// 在所有文档名称中搜索包含指定关键词的文档
  Future<List<MubuDocument>> searchDocuments(String query) async {
    if (query.isEmpty) return [];

    final documents = await getAllDocuments();
    final lowerQuery = query.toLowerCase();

    return documents
        .where((doc) => doc.name.toLowerCase().contains(lowerQuery))
        .toList();
  }

  /// 在指定文档中搜索内容
  ///
  /// 在文档的所有节点中搜索包含指定关键词的节点
  Future<List<MubuNode>> searchInDocument(String docId, String query) async {
    if (query.isEmpty) return [];

    final nodes = await parseDocumentStructure(docId);
    final results = <MubuNode>[];

    for (final node in nodes) {
      results.addAll(node.searchNodes(query));
    }

    return results;
  }

  /// 解析文档结构，提取出层级关系和内容
  Future<List<MubuNode>> parseDocumentStructure(String docId) async {
    final docData = await getDocumentRaw(docId);
    if (docData.isEmpty) {
      logger.w('anki.mubu.document_data_empty'.tr);
      return [];
    }

    final definition = docData['definition'];
    if (definition == null || definition.toString().isEmpty) {
      logger.w('anki.mubu.document_definition_empty'.tr);
      return [];
    }

    try {
      // 幕布文档定义通常是JSON字符串
      dynamic content;
      if (definition is String) {
        try {
          content = jsonDecode(definition);
        } catch (e) {
          logger.e('anki.mubu.parse_document_definition_json_failed'.tr);
          return [];
        }
      } else {
        content = definition;
      }

      // 处理不同格式的根节点结构
      if (content is Map<String, dynamic>) {
        if (content.containsKey('nodes') && content['nodes'] is List) {
          // 标准格式: {"nodes": [...]}
          final result = <MubuNode>[];
          for (final node in content['nodes']) {
            result.add(MubuNode.fromJson(node));
          }
          return result;
        } else if (content.containsKey('root') && content['root'] is Map) {
          // 另一种格式: {"root": {...}}
          return [MubuNode.fromJson(content['root'])];
        } else if (content.containsKey('children') &&
            content['children'] is List) {
          // 第三种格式: 顶层有children属性
          final result = <MubuNode>[];
          for (final child in content['children']) {
            result.add(MubuNode.fromJson(child));
          }
          return result;
        } else {
          // 可能自身就是一个节点
          if (content.containsKey('id') && content.containsKey('text')) {
            return [MubuNode.fromJson(content)];
          }

          // 记录未知结构以便调试
          logger.w('未知的文档结构格式: ${content.keys.join(", ")}');
          return [];
        }
      } else if (content is List) {
        // 有些文档可能直接是节点列表
        final result = <MubuNode>[];
        for (final node in content) {
          if (node is Map<String, dynamic>) {
            result.add(MubuNode.fromJson(node));
          }
        }
        return result;
      } else {
        logger.w('不支持的文档内容类型: ${content.runtimeType}');
        return [];
      }
    } catch (e) {
      logger.e('处理文档结构时出错: $e');
      return [];
    }
  }

  /// 将文档导出为纯文本格式
  Future<String> exportDocumentToPlainText(String docId) async {
    try {
      // 首先获取HTML内容
      final htmlContent = await exportDocumentToHtml(docId);

      // 使用WebviewController将HTML转换为纯文本
      final webviewController = Get.find<WebviewController>();
      final plainText = await webviewController.htmlToText(htmlContent);

      return plainText;
    } catch (e) {
      logger.e('导出文档为纯文本时出错: $e');
      return '${'anki.mubu.export_failed'.tr}: ${e.toString()}';
    }
  }

  /// 将文档导出为Markdown格式
  Future<String> exportDocumentToMarkdown(String docId) async {
    final nodes = await parseDocumentStructure(docId);
    if (nodes.isEmpty) {
      return '';
    }

    final textLines = <String>[];

    // 添加CSS样式片段，使用_getAllStyles方法
    textLines.add(_getAllStyles());
    textLines.add('');

    void processNode(MubuNode node, {int level = 1}) {
      // 添加当前节点文本，使用#号表示标题层级
      final prefix = '#' * Math.min(level, 6);

      // 如果有颜色信息，使用HTML语法显示颜色，确保颜色不为空
      if (node.color != null &&
          node.color!.isNotEmpty &&
          node.color != '#333333') {
        textLines.add(
            '$prefix <span style="color: ${node.color};">${node.text}</span>');
      } else {
        textLines.add('$prefix ${node.text}');
      }

      // 如果有备注，添加为引用块，不加"备注："前缀
      if (node.note != null && node.note!.isNotEmpty) {
        textLines.add('');
        textLines.add('> ${node.note}');
      }

      textLines.add(''); // 空行

      // 处理图片
      if (node.images != null && node.images!.isNotEmpty) {
        for (final image in node.images!) {
          final imageUrl = getFullImageUrl(image);
          if (imageUrl.isNotEmpty) {
            String altText = "image";
            String sizeInfo = "";

            if (image is Map) {
              // 添加图片ID作为alt文本
              if (image.containsKey('id')) {
                altText = "image-${image['id']}";
              }

              // 使用辅助方法处理图片尺寸
              final sizeData = processImageSize(image);

              // 添加尺寸信息
              if (sizeData['width'] != null && sizeData['height'] != null) {
                sizeInfo = " (${sizeData['width']}x${sizeData['height']})";
              } else if (sizeData['width'] != null) {
                sizeInfo = " (${'anki.mubu.width'.tr}: ${sizeData['width']})";
              } else if (sizeData['height'] != null) {
                sizeInfo = " (${'anki.mubu.height'.tr}: ${sizeData['height']})";
              }
            }

            textLines.add('![${altText}${sizeInfo}](${imageUrl})');
            textLines.add(''); // 空行
          }
        }
      }

      // 处理子节点
      for (final child in node.children) {
        processNode(child, level: level + 1);
      }
    }

    // 处理所有根节点
    for (final node in nodes) {
      processNode(node);
    }

    return textLines.join('\n');
  }

  /// 将文档导出为HTML格式
  Future<String> exportDocumentToHtml(String docId) async {
    final nodes = await parseDocumentStructure(docId);
    if (nodes.isEmpty) {
      return '';
    }

    final doc = await getDocument(docId, fetchFullInfo: true);
    final buffer = StringBuffer();

    // 添加HTML头部
    buffer.writeln('<!DOCTYPE html>');
    buffer.writeln('<html>');
    buffer.writeln('<head>');
    buffer.writeln('  <meta charset="utf-8">');
    buffer.writeln('  <title>${doc.name}</title>');
    buffer.writeln('  <style>');
    buffer.writeln(
        '    body { font-family: Arial, sans-serif; line-height: 1.6; }');
    buffer.writeln('    .indent { margin-left: 20px; }');
    buffer.writeln(
        '    .note { background-color: #f8f9fa; border-left: 4px solid #ccc; padding: 10px; margin: 10px 0; color: #666; }');
    buffer.writeln('    .image-container { margin: 10px 0; }');
    buffer.writeln(
        '    .image-container img { max-width: 100%; border: 1px solid #eee; }');

    // 添加指定的CSS样式片段
    buffer.writeln('    .text-color-dark { color: #51565d; }');
    buffer.writeln('    .text-color-red { color: #dc2d1e; }');
    buffer.writeln('    .text-color-yellow { color: #ffaf38; }');
    buffer.writeln('    .text-color-green { color: #75c940; }');
    buffer.writeln('    .text-color-blue { color: #3da8f5; }');
    buffer.writeln('    .text-color-purple { color: #797ec9; }');
    buffer.writeln('    .bold { font-weight: bold; }');
    buffer.writeln('    .underline { text-decoration: underline; }');
    buffer.writeln('    .strikethrough { text-decoration: line-through; }');
    buffer.writeln('    .italic { font-style: italic; }');
    buffer.writeln('    .highlight-yellow { background-color: #f9f989; }');
    buffer.writeln('    .highlight-red { background-color: #f5bdbc; }');
    buffer.writeln('    .highlight-blue { background-color: #c5edfd; }');
    buffer.writeln('    .highlight-grey { background-color: #d1d3d8; }');
    buffer.writeln('    .highlight-olive { background-color: #e4fec1; }');
    buffer.writeln('    .highlight-pink { background-color: #f1ccfc; }');
    buffer.writeln('    .highlight-cyan { background-color: #cffdde; }');

    buffer.writeln('  </style>');
    buffer.writeln('</head>');
    buffer.writeln('<body>');
    buffer.writeln('<h1>${doc.name}</h1>');

    // 添加元数据信息
    buffer.writeln('<div class="metadata">');
    buffer.writeln(
        '  <p>${'anki.mubu.document_id'.tr}: ${doc.id.isEmpty ? docId : doc.id}</p>');
    if (doc.createTime != null) {
      final createTime = DateTime.fromMillisecondsSinceEpoch(doc.createTime!);
      buffer.writeln(
          '  <p>${'anki.mubu.create_time'.tr}: ${createTime.toLocal()}</p>');
    }
    if (doc.updateTime != null) {
      final updateTime = DateTime.fromMillisecondsSinceEpoch(doc.updateTime!);
      buffer.writeln(
          '  <p>${'anki.mubu.update_time'.tr}: ${updateTime.toLocal()}</p>');
    }
    if (doc.itemCount != null) {
      buffer.writeln('  <p>${'anki.mubu.item_count'.tr}: ${doc.itemCount}</p>');
    }
    buffer.writeln('</div>');
    buffer.writeln('<hr>');

    void processNode(MubuNode node) {
      // 根据节点层级添加HTML标签
      String tagName;
      String styleAttr = '';

      // 添加颜色样式，确保color不为空值
      if (node.color != null &&
          node.color!.isNotEmpty &&
          node.color != '#333333') {
        styleAttr = ' style="color: ${node.color};"';
      }

      if (node.level < 5) {
        tagName = 'h${node.level + 2}';
        buffer.writeln('<$tagName$styleAttr>${node.text}</$tagName>');
      } else {
        // 超过6级标题的使用缩进段落
        tagName = 'p';
        buffer.writeln(
            '<$tagName class="indent"$styleAttr style="margin-left: ${(node.level - 4) * 20}px">${node.text}</$tagName>');
      }

      // 添加备注信息，不加"备注："前缀
      if (node.note != null && node.note!.isNotEmpty) {
        buffer.writeln('<div class="note">${node.note}</div>');
      }

      // 处理图片
      if (node.images != null && node.images!.isNotEmpty) {
        for (final image in node.images!) {
          final imageUrl = getFullImageUrl(image);
          if (imageUrl.isNotEmpty) {
            String imageId = "";

            if (image is Map) {
              imageId = image['id'] as String? ?? '';

              // 使用辅助方法处理图片尺寸
              final sizeInfo = processImageSize(image);

              buffer.writeln('<div class="image-container">');
              buffer.write('<img src="${imageUrl}" alt="image-$imageId"');

              // 根据新规则设置宽高属性
              if (sizeInfo['width'] != null) {
                buffer.write(' width="${sizeInfo['width']}"');
              }
              if (sizeInfo['height'] != null) {
                buffer.write(' height="${sizeInfo['height']}"');
              }

              buffer.writeln('>');
              buffer.writeln('</div>');
            }
          }
        }
      }

      // 处理子节点
      for (final child in node.children) {
        processNode(child);
      }
    }

    // 处理所有根节点
    for (final node in nodes) {
      processNode(node);
    }

    buffer.writeln('</body>');
    buffer.writeln('</html>');

    return buffer.toString();
  }

  /// 将文档导出为JSON格式
  Future<String> exportDocumentToJson(String docId) async {
    try {
      // 获取完整的文档信息
      final doc =
          await getDocument(docId, parseNodes: false, fetchFullInfo: true);

      // 转换为JSON格式对象
      final jsonMap = doc.toJson();

      // 确保ID正确
      if (jsonMap['id'] == null || jsonMap['id'].isEmpty) {
        jsonMap['id'] = docId;
      }

      // 移除nodes属性
      jsonMap.remove('nodes');

      // 如果definition是字符串，尝试将其解析为JSON对象
      if (jsonMap.containsKey('definition') &&
          jsonMap['definition'] is String) {
        try {
          final String definitionStr = jsonMap['definition'] as String;
          if (definitionStr.isNotEmpty) {
            final definitionJson = jsonDecode(definitionStr);
            jsonMap['definition'] = definitionJson;
            logger.i('成功将definition从字符串转换为JSON对象');
          }
        } catch (e) {
          logger.e('将definition转换为JSON对象时出错: $e');
          // 保持原样，不做更改
        }
      }

      logger.i('导出JSON文档，ID: ${jsonMap['id']}');
      return const JsonEncoder.withIndent('  ').convert(jsonMap);
    } catch (e) {
      logger.e('导出JSON文档时出错: $e');
      // 返回最小化JSON数据
      return '{"id": "$docId", "error": "${e.toString().replaceAll('"', '\\"')}"}';
    }
  }

  /// 将文档导出为OPML格式
  Future<String> exportDocumentToOpml(String docId) async {
    final nodes = await parseDocumentStructure(docId);
    if (nodes.isEmpty) {
      return '';
    }

    final doc = await getDocument(docId, fetchFullInfo: true);
    final buffer = StringBuffer();

    // 添加OPML头部
    buffer.writeln('<?xml version="1.0" encoding="UTF-8"?>');
    buffer.writeln('<opml version="2.0">');
    buffer.writeln('  <head>');
    buffer.writeln('    <title>${doc.name}</title>');
    buffer.writeln('    <docId>${doc.id.isEmpty ? docId : doc.id}</docId>');

    // 如果有创建时间和修改时间，添加到OPML头部
    if (doc.createTime != null) {
      final createTime = DateTime.fromMillisecondsSinceEpoch(doc.createTime!);
      buffer.writeln(
          '    <dateCreated>${createTime.toIso8601String()}</dateCreated>');
    }

    if (doc.updateTime != null) {
      final updateTime = DateTime.fromMillisecondsSinceEpoch(doc.updateTime!);
      buffer.writeln(
          '    <dateModified>${updateTime.toIso8601String()}</dateModified>');
    }

    buffer.writeln('  </head>');
    buffer.writeln('  <body>');

    void processNode(MubuNode node, int indent) {
      final spaces = ' ' * indent;
      // 安全处理特殊字符
      final String safeText = node.text
          .replaceAll('"', '&quot;')
          .replaceAll('<', '&lt;')
          .replaceAll('>', '&gt;');

      buffer.writeln('$spaces<outline text="$safeText"');

      // 添加节点ID和其他属性
      buffer.writeln('$spaces         _id="${node.id}"');

      // 添加颜色属性，确保不为空
      if (node.color != null &&
          node.color!.isNotEmpty &&
          node.color != '#333333') {
        buffer.writeln('$spaces         _color="${node.color}"');
      }

      // 添加备注属性
      if (node.note != null && node.note!.isNotEmpty) {
        final String safeNote = node.note!
            .replaceAll('"', '&quot;')
            .replaceAll('<', '&lt;')
            .replaceAll('>', '&gt;');
        buffer.writeln('$spaces         _note="$safeNote"');
      }

      // 添加修改时间属性
      if (node.modified != null) {
        buffer.writeln('$spaces         _modified="${node.modified}"');
      }

      // 添加图片属性
      if (node.images != null && node.images!.isNotEmpty) {
        buffer.writeln('$spaces         _hasImages="true"');
        buffer.writeln('$spaces         _imageCount="${node.images!.length}"');

        // 为每张图片添加单独的属性
        for (int i = 0; i < node.images!.length; i++) {
          final image = node.images![i];
          final imageUrl = getFullImageUrl(image);

          if (imageUrl.isNotEmpty) {
            final String safeUrl = imageUrl.replaceAll('"', '&quot;');
            buffer.writeln('$spaces         _image${i + 1}="$safeUrl"');

            // 添加图片的额外信息
            if (image is Map) {
              // 添加图片ID
              if (image.containsKey('id')) {
                buffer.writeln(
                    '$spaces         _imageId${i + 1}="${image['id']}"');
              }

              // 使用辅助方法处理图片尺寸
              final sizeData = processImageSize(image);

              // 添加宽高信息，根据新规则
              if (sizeData['width'] != null && sizeData['height'] != null) {
                // 同时添加宽高
                buffer.writeln(
                    '$spaces         _imageSize${i + 1}="${sizeData['width']}x${sizeData['height']}"');
                buffer.writeln(
                    '$spaces         _imageWidth${i + 1}="${sizeData['width']}"');
                buffer.writeln(
                    '$spaces         _imageHeight${i + 1}="${sizeData['height']}"');
              } else if (sizeData['width'] != null) {
                // 只添加宽度
                buffer.writeln(
                    '$spaces         _imageWidth${i + 1}="${sizeData['width']}"');
              } else if (sizeData['height'] != null) {
                // 只添加高度
                buffer.writeln(
                    '$spaces         _imageHeight${i + 1}="${sizeData['height']}"');
              }

              // 添加图片URL的来源信息
              if (image.containsKey('uri')) {
                final String uri = image['uri'] as String? ?? '';
                if (uri.isNotEmpty) {
                  final String safeUri = uri.replaceAll('"', '&quot;');
                  buffer
                      .writeln('$spaces         _imageUri${i + 1}="$safeUri"');
                }
              }
            }
          }
        }
      }

      if (node.children.isEmpty) {
        buffer.writeln('$spaces/>');
      } else {
        buffer.writeln('$spaces>');
        // 递归处理子节点
        for (final child in node.children) {
          processNode(child, indent + 2);
        }
        buffer.writeln('$spaces</outline>');
      }
    }

    // 处理所有根节点
    for (final node in nodes) {
      processNode(node, 4);
    }

    buffer.writeln('  </body>');
    buffer.writeln('</opml>');

    return buffer.toString();
  }

  /// 清除保存的Cookie
  void clearSavedCookie() {
    try {
      _storage.remove(StorageBox.default_, 'mubu_cookie');
      logger.i('Cleared saved Mubu cookie');
    } catch (e) {
      logger.e('Error clearing saved Mubu cookie: $e');
    }
  }

  /// 清除会话数据
  void clearSession() {
    _token.value = '';
    cookie.value = '';
    _isAuthenticated.value = false;
    _recentDocuments.clear();
    _errorMessage.value = '';
    documentList.clear();

    // 同时清除存储的Cookie
    clearSavedCookie();
  }

  Future<void> submit(BuildContext context) async {
    try {
      if (tabController.selected == "card") {
        // 笔记制卡
        // 获取导出模式
        final exportCardMode = settingController.getExportCardMode();
        final ankiConnectUrl = settingController.ankiConnectUrl.value;
        // 显示进度对话框
        progressController.reset(
          showOutputHint: exportCardMode == "apkg",
          numberButtons: exportCardMode == "apkg" ? 2 : 0,
        );
        progressController.showProgressDialog(context);

        if (cardMode.value == "node_desc") {
          // 获取文档内容
          final mubuDoc = await getDocument(document.value, parseNodes: true);
          if (mubuDoc.nodes == null || mubuDoc.nodes!.isEmpty) {
            progressController.updateProgress(
              status: "error",
              message: "anki.mubu.document_no_content".tr,
              current: 0.0,
              total: 100.0,
            );
            return;
          }

          // 创建卡片列表
          final notes = <AnkiNote>[];
          final totalNodes = _countAllNodes(mubuDoc.nodes!);
          var processedNodes = 0;

          // 根据cardType判断是问答卡还是挖空卡
          if (cardType.value == "qa") {
            logger.i(fieldMappings);
            // 检查是否已设置了前端字段映射
            if (!fieldMappings.entries
                .any((entry) => entry.value.regex == "__reserved_front__")) {
              // 如果没有设置，尝试寻找名为"Front"的字段作为默认前端字段
              if (ankiConnectController.fieldList.contains("Front")) {
                logger.i("未找到前端字段映射，使用'Front'字段作为默认前端字段");
                fieldMappings["Front"] =
                    PatternMatch("Front", "__reserved_front__", null, true);
              } else {
                // 如果没有找到名为"Front"的字段，显示错误信息
                progressController.updateProgress(
                  status: "error",
                  message: "anki.mubu.front_field_mapping_error".tr,
                  current: 0.0,
                  total: 100.0,
                );
                return;
              }
            }

            // 检查是否已设置了后端字段映射
            if (!fieldMappings.entries
                .any((entry) => entry.value.regex == "__reserved_back__")) {
              // 如果没有设置，尝试寻找名为"Back"的字段作为默认后端字段
              if (ankiConnectController.fieldList.contains("Back")) {
                logger.i("未找到后端字段映射，使用'Back'字段作为默认后端字段");
                fieldMappings["Back"] =
                    PatternMatch("Back", "__reserved_back__", null, true);
              }
              // 对于后端字段，不报错，因为可以没有
            }

            // 遍历所有节点生成问答卡片
            for (final node in mubuDoc.nodes!) {
              await _processNodeForNodeDesc(
                  node, mubuDoc.name, notes, processedNodes, totalNodes);
              processedNodes = _countProcessedNodes(node);
            }
          } else if (cardType.value == "cloze") {
            cardModel.value = "Kevin Text Cloze v3";
            fieldMappings['Front'] =
                PatternMatch('Front', '__reserved_front__', null, true);
            fieldMappings['Back'] =
                PatternMatch('Back', '__reserved_back__', null, true);
            fieldMappings['Mode'] =
                PatternMatch('Mode', clozeMode.value, null, true);
            // 遍历所有节点生成挖空卡片
            for (final node in mubuDoc.nodes!) {
              await _processNodeForCloze(
                  node, mubuDoc.name, notes, processedNodes, totalNodes);
              processedNodes = _countProcessedNodes(node);
            }
          }
          // 导入卡片
          progressController.updateProgress(
            status: "running",
            message: "正在导入卡片...",
            current: 90,
            total: 100,
          );
          await AnkiConnectController().generateAndImportCards(notes);
          progressController.updateProgress(
            status: "completed",
            message: "卡片生成完成",
            current: 100.0,
            total: 100.0,
          );
        } else if (cardMode.value == "hierarchy") {
          // 获取文档内容
          final mubuDoc = await getDocument(document.value, parseNodes: true);
          if (mubuDoc.nodes == null || mubuDoc.nodes!.isEmpty) {
            progressController.updateProgress(
              status: "error",
              message: "文档没有内容或解析失败",
              current: 0.0,
              total: 100.0,
            );
            return;
          }
          // 创建卡片列表
          final notes = <AnkiNote>[];
          final totalNodes = _countAllNodes(mubuDoc.nodes!);
          var processedNodes = 0;

          // 获取问题节点级别
          final qNodeLevel = int.tryParse(questionNodeLevel.value) ?? 1;

          // 根据cardType判断是问答卡还是挖空卡
          if (cardType.value == "qa") {
            // 检查是否已设置了前端字段映射
            if (!fieldMappings.entries
                .any((entry) => entry.value.regex == "__reserved_front__")) {
              // 如果没有设置，尝试寻找名为"Front"的字段作为默认前端字段
              if (ankiConnectController.fieldList.contains("Front")) {
                logger.i("未找到前端字段映射，使用'Front'字段作为默认前端字段");
                fieldMappings["Front"] =
                    PatternMatch("Front", "__reserved_front__", null, true);
              } else {
                // 如果没有找到名为"Front"的字段，显示错误信息
                progressController.updateProgress(
                  status: "error",
                  message: "请设置一个字段为正面内容 (Front)",
                  current: 0.0,
                  total: 100.0,
                );
                return;
              }
            }

            // 检查是否已设置了后端字段映射
            if (!fieldMappings.entries
                .any((entry) => entry.value.regex == "__reserved_back__")) {
              // 如果没有设置，尝试寻找名为"Back"的字段作为默认后端字段
              if (ankiConnectController.fieldList.contains("Back")) {
                logger.i("未找到后端字段映射，使用'Back'字段作为默认后端字段");
                fieldMappings["Back"] =
                    PatternMatch("Back", "__reserved_back__", null, true);
              }
              // 对于后端字段，不报错，因为可以没有
            }

            // 遍历所有节点生成问答卡片
            for (final node in mubuDoc.nodes!) {
              await _processNodeForHierarchy(node, mubuDoc.name, notes,
                  processedNodes, totalNodes, qNodeLevel, [], 1);
              processedNodes = _countProcessedNodes(node);
            }
          } else if (cardType.value == "cloze") {
            // 挖空题类型
            // 设置特殊的挖空卡片模板
            cardModel.value = "Kevin Text Cloze v3";
            fieldMappings['Front'] =
                PatternMatch('Front', '__reserved_front__', null, true);
            fieldMappings['Back'] =
                PatternMatch('Back', '__reserved_back__', null, true);
            fieldMappings['Mode'] =
                PatternMatch('Mode', clozeMode.value, null, true);

            // 遍历所有节点生成挖空卡片，但需要遵循questionNodeLevel筛选节点
            for (final node in mubuDoc.nodes!) {
              // 在hierarchy模式下，对于cloze类型，我们需要使用_processNodeForHierarchyCloze方法来处理
              // 这样可以确保只对特定层级的节点生成挖空卡片
              await _processNodeForHierarchyCloze(node, mubuDoc.name, notes,
                  processedNodes, totalNodes, qNodeLevel, [], 1);
              processedNodes = _countProcessedNodes(node);
            }
          }
          // 导入卡片
          progressController.updateProgress(
            status: "running",
            message: "anki.mubu.importing_cards".tr,
            current: 90,
            total: 100,
          );
          await AnkiConnectController().generateAndImportCards(notes);
          progressController.updateProgress(
            status: "completed",
            message: "anki.mubu.card_generation_complete".tr,
            current: 100.0,
            total: 100.0,
          );
        } else if (cardMode.value == "mindmap") {
          final mubuDoc = await getDocument(document.value, parseNodes: true);
          final finalMaskTypes =
              cardType.value == "cloze" ? maskTypes.value : [];
          final content = await exportDocumentToJson(document.value);
          final tempDir = await PathUtils.tempDir;
          final jsonFilePath =
              PathUtils.join([tempDir, "${Ulid().toString()}.json"]);
          final jsonFile = File(jsonFilePath);
          await jsonFile.writeAsString(content);
          // 牌组名称
          final docName = mubuDoc.name;
          String deckName = parentDeck.value;
          if (isCreateSubDeck.value) {
            // 获取文档完整路径
            final folderPath = await _getDocumentFolderPath(document.value);
            if (folderPath.isNotEmpty) {
              // 如果有完整路径，则添加到牌组名称中：根牌组::文件夹路径::文档名
              deckName = "$deckName::$folderPath::$docName";
            } else {
              // 如果没有路径，则直接使用文档名
              deckName = "$deckName::$docName";
            }
          }

          final data = {
            "mind_source": "mubu",
            "file_path": jsonFilePath,
            "parent_deck": deckName,
            "mask_types": finalMaskTypes,
            "text_colors": textColorList.value,
            "highlight_colors": highlightColorList.value,
            "tags": tags,
            "is_show_source": isShowSource.value,
            "output_path": await PathUtils.getOutputApkgPath(),
            "map_id": mapID.value,
            "address": ankiConnectUrl,
          };
          logger.d("data: $data");
          // 发送请求
          final resp = await messageController.request(data, "mindmap_card");
          logger.d("resp: $resp");
          if (resp.status == "success") {
            if (exportCardMode == "ankiconnect") {
              await AnkiConnectController()
                  .importApkg(resp.data, isDelete: true);
            }
            progressController.outputPath.value = resp.data;
            progressController.updateProgress(
              status: "completed",
              message: "",
              current: 100.0,
              total: 100.0,
            );
          } else {
            progressController.updateProgress(
              status: "error",
              message: resp.message,
              current: 100.0,
              total: 100.0,
            );
          }
        } else {
          progressController.updateProgress(
            status: "error",
            message: "不支持的制卡模式",
            current: 0.0,
            total: 100.0,
          );
        }
      } else {
        // 导出笔记
        progressController.reset(
          showOutputHint: true,
          numberButtons: 0,
        );
        progressController.showProgressDialog(context);
        progressController.updateProgress(
          status: "running",
          message: "处理中...",
          current: 3,
          total: 100,
        );

        // 导出文档
        await _exportDocument(document.value, exportFormat.value);
      }
      // 实现提交逻辑
    } catch (e) {
      _handleError(e);
      logger.e("Error submitting: $e");
      progressController.updateProgress(
        status: "error",
        message: e.toString(),
        current: 0.0,
        total: 100.0,
      );
    }
  }

  /// 处理挖空题模式
  Future<void> _processNodeForCloze(MubuNode node, String docName,
      List<AnkiNote> notes, int processedNodes, int totalNodes) async {
    // 更新进度
    progressController.updateProgress(
      status: "running",
      message: "正在处理节点 ${processedNodes + 1}/${totalNodes}",
      current: (processedNodes + 1).toDouble(),
      total: totalNodes.toDouble(),
    );

    // 首先处理节点的标签，判断是否需要跳过
    List<String> nodeTags = [];
    String cleanedNote = "";
    bool skipCard = false;

    if (node.note != null && node.note!.isNotEmpty) {
      // 处理笔记中的标签
      final processedNote = _processNodeTags(node.note);
      cleanedNote = processedNote['cleanedContent'] as String;
      nodeTags = processedNote['extractedTags'] as List<String>;

      // 根据isPartCard和特殊标签决定是否跳过
      if (!isPartCard.value) {
        // 如果不是部分制卡模式，只有带有精确的"ignore"标签的节点需要跳过
        skipCard = nodeTags.any((tag) => tag.toLowerCase() == 'ignore');
      } else {
        // 如果是部分制卡模式，默认跳过所有节点，只处理带有精确的"keep"标签的节点
        skipCard = !nodeTags.any((tag) => tag.toLowerCase() == 'keep');
      }
    } else if (isPartCard.value) {
      // 部分制卡模式下，没有note或标签的节点跳过
      skipCard = true;
    }

    // 如果需要跳过该节点，直接处理子节点
    if (skipCard) {
      // 递归处理子节点
      for (final child in node.children) {
        await _processNodeForCloze(
            child, docName, notes, processedNodes + 1, totalNodes);
      }
      return;
    }

    // 创建卡片内容
    Map<String, String> content = {};

    // 设置正面内容 - 包含文本和图片
    StringBuffer frontContent = StringBuffer();

    // 不再此处添加样式，移到最后添加
    // frontContent.writeln(_getAllStyles());

    // 处理节点文本，移除可能存在的标签
    String nodeText = node.text;
    final processedText = _processNodeTags(nodeText);
    nodeText = processedText['cleanedContent'] as String;
    // 进一步清理，移除任何剩余的标签显示
    nodeText =
        nodeText.replaceAll(RegExp(r'<span class="tag">#[^<]*</span>'), '');

    // 添加节点文本
    frontContent.writeln('<div class="node-text">');
    if (node.color != null &&
        node.color!.isNotEmpty &&
        node.color != '#333333') {
      frontContent
          .writeln('<span style="color: ${node.color};">${nodeText}</span>');
    } else {
      frontContent.writeln(nodeText);
    }
    frontContent.writeln('</div>');

    // 添加图片到正面
    final imageHtml = _processNodeImages(node);
    if (imageHtml.isNotEmpty) {
      frontContent.writeln(imageHtml);
    }

    // 设置卡片背面内容 - 使用节点的note内容或子节点内容
    StringBuffer backContent = StringBuffer();

    // 如果有note，使用note作为背面内容
    if (cleanedNote.isNotEmpty) {
      backContent.writeln('<div class="node-note">');
      backContent.writeln(cleanedNote);
      backContent.writeln('</div>');
    }
    // 如果有子节点，使用子节点作为背面内容
    else if (node.children.isNotEmpty) {
      // 递归处理子节点
      await _buildNestedContent(node.children, backContent, 0);
    }

    // 处理挖空: 应用挖空标记到正面内容
    String frontContentStr = frontContent.toString();

    // 获取WebView控制器应用挖空处理
    final webviewController = Get.find<WebviewController>();

    // 定义挖空样式和颜色，这里使用所有可能的样式类型
    final List<String> clozeStyles = maskTypes.toList();
    logger.i("使用的挖空样式: $clozeStyles");

    // 创建用于JS函数的颜色列表 - 直接使用已有的列表
    final List<String> textColors = textColorList.isEmpty
        ? ['red', 'yellow', 'green', 'blue', 'purple', 'dark']
        : textColorList.toList();
    logger.i("使用的文本颜色: $textColors");

    final List<String> highlightColors = highlightColorList.isEmpty
        ? ['yellow', 'red', 'blue', 'grey', 'olive', 'pink', 'cyan']
        : highlightColorList.toList();
    logger.i("使用的高亮颜色: $highlightColors");

    // 处理前端内容挖空
    try {
      logger.i("开始处理挖空内容，原始内容长度: ${frontContentStr.length}");
      frontContentStr = await webviewController.convertClozeForMubu(
          frontContentStr, clozeStyles, textColors, highlightColors);
      logger.i("挖空内容处理完成，处理后内容长度: ${frontContentStr.length}");

      // 检查是否成功转换（简单检查是否包含挖空标记）
      if (frontContentStr.contains("[[c") && frontContentStr.contains("::")) {
        logger.i("成功找到挖空标记");
      } else {
        logger.w("未找到挖空标记，可能转换失败");
      }
    } catch (e) {
      logger.e("处理挖空内容时出错: $e");
      // 发生错误时使用原始内容
    }

    // 设置字段内容 - 确保设置Mode字段
    content["Front"] = frontContentStr;
    content["Back"] = backContent.toString();

    // 确保Mode字段设置正确，值为clozeMode.value
    content["Mode"] = clozeMode.value;
    logger.i("设置Mode字段值: ${clozeMode.value}");

    // 设置ID字段为节点ID
    content["ID"] = "${document.value}-${node.id}";
    logger.i("设置ID字段值: ${document.value}-${node.id}");

    // 验证字段映射是否存在必要的字段
    if (!fieldMappings.containsKey('Front') ||
        !fieldMappings.containsKey('Back') ||
        !fieldMappings.containsKey('Mode')) {
      logger.e("字段映射缺少必要字段，自动补充");
      // 确保字段映射中包含必要的字段
      fieldMappings['Front'] =
          PatternMatch('Front', '__reserved_front__', null, true);
      fieldMappings['Back'] =
          PatternMatch('Back', '__reserved_back__', null, true);
      fieldMappings['Mode'] = PatternMatch('Mode', clozeMode.value, null, true);
    }

    // 确保ID字段映射存在
    if (!fieldMappings.containsKey('ID')) {
      fieldMappings['ID'] = PatternMatch('ID', '', null, true);
    }

    // 只有在isShowSource为true时才添加回链
    if (isShowSource.value) {
      content["Front"] = content["Front"] ?? "";
      if (!content["Front"]!.contains("source-link")) {
        // 确保不重复添加
        content["Front"] = content["Front"]! +
            '''
<div class="source-link">
<a href="https://mubu.com/app/edit/home/<USER>">查看出处</a>
</div>''';
      }
    }

    // 在最后添加样式
    content["Front"] = _getAllStyles() + (content["Front"] ?? "");

    // 牌组名称
    String deckName = parentDeck.value;
    if (isCreateSubDeck.value) {
      // 获取文档完整路径
      final folderPath = await _getDocumentFolderPath(document.value);
      if (folderPath.isNotEmpty) {
        // 如果有完整路径，则添加到牌组名称中：根牌组::文件夹路径::文档名
        deckName = "$deckName::$folderPath::$docName";
      } else {
        // 如果没有路径，则直接使用文档名
        deckName = "$deckName::$docName";
      }
    }

    // 创建笔记 - 使用节点ID作为guid以便后续同步或更新
    // 添加一空一卡的功能
    if (oneClozePeCard.value &&
        (clozeMode.value == "mask_one_guess_one" ||
            clozeMode.value == "mask_all_guess_one")) {
      // 修改Mode字段，添加_multi后缀
      content["Mode"] = "${clozeMode.value}_multi";

      // 提取所有cloze编号
      final clozeNumbers = <String>[];
      final frontContent = content["Front"];
      if (frontContent != null) {
        final regex = RegExp(r'\[\[(c\d+)::.*?\]\]');
        final matches = regex.allMatches(frontContent);
        for (final match in matches) {
          final clozeNum = match.group(1);
          if (clozeNum != null && !clozeNumbers.contains(clozeNum)) {
            clozeNumbers.add(clozeNum);
          }
        }
      }

      // 为每个挖空编号创建单独的卡片
      if (clozeNumbers.isNotEmpty) {
        for (final clozeNum in clozeNumbers) {
          final guid =
              "${document.value}-${node.id}-${clozeMode.value}-${clozeNum}";
          content['Index'] = clozeNum;
          content['ID'] = guid;
          final note = _createNote(deckName, content, cardModel.value,
              guid: guid, cardTags: nodeTags);

          // 记录字段值
          logger.i("创建挖空卡片(一空一卡), 字段: ${note.fields.join(' | ')}");

          notes.add(note);
        }
      } else {
        // 如果没有找到挖空，仍然创建一张卡片
        final note = _createNote(deckName, content, cardModel.value,
            guid: "${document.value}-${node.id}-${clozeMode.value}",
            cardTags: nodeTags);

        // 记录字段值
        logger.i("创建挖空卡片(未找到挖空), 字段: ${note.fields.join(' | ')}");

        notes.add(note);
      }
    } else {
      final note = _createNote(deckName, content, cardModel.value,
          guid: "${document.value}-${node.id}-${cardModel.value}",
          cardTags: nodeTags);

      // 记录字段值
      logger.i("创建挖空卡片，字段: ${note.fields.join(' | ')}");

      notes.add(note);
    }

    // 递归处理子节点
    for (final child in node.children) {
      await _processNodeForCloze(
          child, docName, notes, processedNodes + 1, totalNodes);
    }
  }

  /// 根据选择的格式导出文档内容到文件
  Future<void> _exportDocument(String docId, String format) async {
    String content = '';
    String extension = '';

    // 获取文档信息以获取名称
    final doc = await getDocument(docId, fetchFullInfo: true);
    String fileName = doc.name;

    // 净化文件名，去除不合法字符
    fileName = fileName.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');

    // 根据不同格式生成内容
    switch (format) {
      case "md":
        content = await exportDocumentToMarkdown(docId);
        extension = "md";
        break;
      case "txt":
        content = await exportDocumentToPlainText(docId);
        extension = "txt";
        break;
      case "html":
        content = await exportDocumentToHtml(docId);
        extension = "html";
        break;
      case "json":
        content = await exportDocumentToJson(docId);
        extension = "json";
        break;
      case "opml":
        content = await exportDocumentToOpml(docId);
        extension = "opml";
        break;
      default:
        progressController.updateProgress(
          status: "error",
          message: "不支持的导出格式: $format",
          current: 0.0,
          total: 100.0,
        );
        return;
    }

    // 生成输出文件路径
    final outputPath =
        PathUtils.join([outputDir.value, "$fileName.$extension"]);

    try {
      // 创建输出目录（如果不存在）
      final directory = Directory(outputDir.value);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // 创建文件并写入内容
      final file = File(outputPath);
      await file.writeAsString(content);

      // 更新进度并显示成功消息
      progressController.outputPath.value = outputPath;
      progressController.updateProgress(
        status: "completed",
        message: "导出成功",
        current: 100.0,
        total: 100.0,
      );
    } catch (e) {
      progressController.updateProgress(
        status: "error",
        message: "写入文件失败: $e",
        current: 0.0,
        total: 100.0,
      );
    }
  }

  /// 通过文档名称导出文档
  Future<void> exportDocumentByName(
      String docName, String format, BuildContext context) async {
    try {
      progressController.reset(
        showOutputHint: true,
        numberButtons: 0,
      );
      progressController.showProgressDialog(context);
      progressController.updateProgress(
        status: "running",
        message: "查找文档中...",
        current: 1,
        total: 100,
      );

      // 查找文档
      final doc = await getDocumentByName(docName);
      if (doc == null) {
        progressController.updateProgress(
          status: "error",
          message: "未找到名为 '$docName' 的文档",
          current: 0.0,
          total: 100.0,
        );
        return;
      }

      progressController.updateProgress(
        status: "running",
        message: "正在导出文档...",
        current: 30,
        total: 100,
      );

      // 导出文档
      await _exportDocument(doc.id, format);
    } catch (e) {
      _handleError(e);
      logger.e("Error exporting document by name: $e");
      progressController.updateProgress(
        status: "error",
        message: e.toString(),
        current: 0.0,
        total: 100.0,
      );
    }
  }

  /// 构建完整的图片URL
  String getFullImageUrl(dynamic imageData) {
    if (imageData == null) return '';

    // 检查是否有uri字段
    if (imageData is Map && imageData.containsKey('uri')) {
      final String uri = imageData['uri'] as String? ?? '';

      if (uri.isEmpty) return '';

      // 如果已经是完整URL，直接返回
      if (uri.startsWith('http://') || uri.startsWith('https://')) {
        return uri;
      }

      // 如果是base64格式图片
      if (uri.startsWith('data:image')) {
        return uri;
      }

      // 对于普通的uri，添加前缀构成完整URL
      return 'https://document-image.mubu.com/$uri';
    }

    // 如果只是一个字符串URL
    if (imageData is String) {
      if (imageData.isEmpty) return '';

      if (imageData.startsWith('http://') ||
          imageData.startsWith('https://') ||
          imageData.startsWith('data:image')) {
        return imageData;
      }
      return 'https://document-image.mubu.com/$imageData';
    }

    // 处理url字段
    if (imageData is Map && imageData.containsKey('url')) {
      final String url = imageData['url'] as String? ?? '';
      if (url.isNotEmpty) {
        return url;
      }
    }

    logger.d('无法从图片数据获取URL: $imageData');
    return '';
  }

  /// 处理图片尺寸信息，返回计算后的宽高
  Map<String, dynamic> processImageSize(Map<dynamic, dynamic> image) {
    final result = <String, dynamic>{
      'width': null,
      'height': null,
      'originalWidth': null,
      'originalHeight': null,
      'useOriginal': false,
      'aspectRatio': null,
    };

    // 获取原始宽高
    final int? originalWidth = image['ow'] as int?;
    final int? originalHeight = image['oh'] as int?;
    result['originalWidth'] = originalWidth;
    result['originalHeight'] = originalHeight;

    // 获取缩放宽高
    final int? scaledWidth = image['w'] as int?;
    final int? scaledHeight = image['h'] as int?;

    // 根据新规则设置宽高
    if (scaledWidth != null && scaledHeight != null) {
      // 如果两个缩放属性都存在，同时使用
      result['width'] = scaledWidth;
      result['height'] = scaledHeight;
    } else if (scaledWidth != null) {
      // 只有缩放宽度
      result['width'] = scaledWidth;
    } else if (scaledHeight != null) {
      // 只有缩放高度
      result['height'] = scaledHeight;
    } else {
      // 都没有缩放属性，使用原始宽高
      result['width'] = originalWidth;
      result['height'] = originalHeight;
      result['useOriginal'] = true;
    }

    // 计算原始宽高比，可用于调试或信息显示
    if (originalWidth != null && originalHeight != null && originalHeight > 0) {
      result['aspectRatio'] = originalWidth / originalHeight;
    }

    return result;
  }

  // 节点递归处理辅助方法

  /// 计算文档中所有节点的数量
  int _countAllNodes(List<MubuNode> nodes) {
    int count = 0;
    for (final node in nodes) {
      count++; // 当前节点
      count += _countAllNodes(node.children); // 子节点
    }
    return count;
  }

  /// 计算节点及其所有子节点的数量
  int _countProcessedNodes(MubuNode node) {
    int count = 1; // 当前节点
    for (final child in node.children) {
      count += _countProcessedNodes(child); // 递归计算子节点
    }
    return count;
  }

  /// 获取指定字段名
  String _getFieldNameByValue(String value) {
    for (final entry in fieldMappings.entries) {
      if (entry.value.regex == value) {
        return entry.key;
      }
    }
    return "";
  }

  /// 处理节点图片，返回HTML格式
  String _processNodeImages(MubuNode node) {
    if (node.images == null || node.images!.isEmpty) {
      return '';
    }

    StringBuffer imageHtml = StringBuffer();
    for (final image in node.images!) {
      final imageUrl = getFullImageUrl(image);
      if (imageUrl.isNotEmpty) {
        imageHtml.writeln('<div class="image-container">');
        imageHtml.writeln('<img src="${imageUrl}" alt="image">');
        imageHtml.writeln('</div>');
      }
    }
    return imageHtml.toString();
  }

  /// 处理节点内容，返回节点的文本和备注的HTML
  String _processNodeContent(MubuNode node, {bool includeImages = true}) {
    StringBuffer content = StringBuffer();

    // 清理文本中的标签
    String cleanedText = node.text;
    cleanedText =
        cleanedText.replaceAll(RegExp(r'<span class="tag">#[^<]*</span>'), '');
    // 尝试提取其他形式的标签
    final processedText = _processNodeTags(cleanedText);
    cleanedText = processedText['cleanedContent'] as String;

    // 添加节点文本 - 使用直接的内联样式
    content.write('<span class="node-text">');
    if (node.color != null &&
        node.color!.isNotEmpty &&
        node.color != '#333333') {
      content
          .write('<span style="color: ${node.color};">${cleanedText}</span>');
    } else {
      content.write(cleanedText);
    }
    content.write('</span>');

    // 添加节点备注（先清理标签）
    if (node.note != null && node.note!.isNotEmpty) {
      String cleanedNote = node.note!;
      // 移除HTML标签格式的标签
      cleanedNote = cleanedNote.replaceAll(
          RegExp(r'<span class="tag">#[^<]*</span>'), '');
      // 处理其他格式的标签
      final processedNote = _processNodeTags(cleanedNote);
      cleanedNote = processedNote['cleanedContent'] as String;

      content.write('<div class="node-note">');
      content.write(cleanedNote);
      content.write('</div>');
    }

    // 添加图片
    if (includeImages) {
      final imageHtml = _processNodeImages(node);
      if (imageHtml.isNotEmpty) {
        content.write(imageHtml);
      }
    }

    return content.toString();
  }

  /// 处理答案中的内容，根据maskTypes应用挖空标记
  Future<String> _processContentWithCloze(String htmlContent) async {
    if (!isAnswerCloze.value || maskTypes.isEmpty) {
      return htmlContent;
    }

    try {
      // 获取WebView控制器
      final webviewController = Get.find<WebviewController>();

      // 获取样式列表
      final List<String> selectedMaskTypes = maskTypes.toList();

      // 创建用于JS函数的颜色列表
      final List<String> selectedTextColors = [];
      if (maskTypes.contains('text_color')) {
        // 如果用户选择了特定颜色，则使用这些颜色
        if (textColorList.isNotEmpty) {
          selectedTextColors.addAll(textColorList);
        } else {
          // 否则使用所有可用颜色
          selectedTextColors
              .addAll(['red', 'yellow', 'green', 'blue', 'purple', 'dark']);
        }
      }

      final List<String> selectedHighlightColors = [];
      if (maskTypes.contains('text_highlight')) {
        // 如果用户选择了特定高亮颜色，则使用这些颜色
        if (highlightColorList.isNotEmpty) {
          selectedHighlightColors.addAll(highlightColorList);
        } else {
          // 否则使用所有可用高亮颜色
          selectedHighlightColors.addAll(
              ['yellow', 'red', 'blue', 'grey', 'olive', 'pink', 'cyan']);
        }
      }

      // 调用JS函数处理内容
      final result = await webviewController.convertClozeForMubu(htmlContent,
          selectedMaskTypes, selectedTextColors, selectedHighlightColors);

      logger.i("Processed HTML with cloze: ${result.length} chars");
      return result;
    } catch (e) {
      logger.e("Error processing cloze content: $e");
      // 发生错误时回退到原始内容
      return htmlContent;
    }
  }

  /// 获取幕布样式的CSS代码以及详细表格样式
  String _getAllStyles() {
    return '';
  }

  /// 处理节点描述模式 (node_desc)
  Future<void> _processNodeForNodeDesc(MubuNode node, String docName,
      List<AnkiNote> notes, int processedNodes, int totalNodes) async {
    // 更新进度
    progressController.updateProgress(
      status: "running",
      message:
          "${'anki.mubu.processing_node'.tr} ${processedNodes + 1}/${totalNodes}",
      current: (processedNodes + 1).toDouble(),
      total: totalNodes.toDouble(),
    );

    // 首先处理节点的标签，判断是否需要跳过
    List<String> nodeTags = [];
    String cleanedNote = "";
    bool skipCard = false;
    bool hasKeepTag = false;
    bool hasIgnoreTag = false;

    if (node.note != null && node.note!.isNotEmpty) {
      // 处理笔记中的标签
      final processedNote = _processNodeTags(node.note);
      cleanedNote = processedNote['cleanedContent'] as String;
      nodeTags = processedNote['extractedTags'] as List<String>;

      // 检查是否有keep或ignore标签
      hasKeepTag = nodeTags.any((tag) => tag.toLowerCase() == 'keep');
      hasIgnoreTag = nodeTags.any((tag) => tag.toLowerCase() == 'ignore');
    }

    // 根据isPartCard和特殊标签决定是否跳过
    if (!isPartCard.value) {
      // 如果不是部分制卡模式，只有带有精确的"ignore"标签的节点需要跳过
      skipCard = hasIgnoreTag;
    } else {
      // 如果是部分制卡模式，默认跳过所有节点，只处理带有精确的"keep"标签的节点
      skipCard = !hasKeepTag;
    }

    // 如果需要跳过该节点，直接处理子节点
    if (skipCard) {
      // 递归处理子节点
      for (final child in node.children) {
        await _processNodeForNodeDesc(
            child, docName, notes, processedNodes + 1, totalNodes);
      }
      return;
    }

    // 获取前端字段名和后端字段名
    String frontFieldName = _getFieldNameByValue("__reserved_front__");
    String backFieldName = _getFieldNameByValue("__reserved_back__");

    // 如果没有前端字段映射，尝试使用名为"Front"的字段
    if (frontFieldName.isEmpty) {
      if (ankiConnectController.fieldList.contains("Front")) {
        logger.i("在_processNodeForNodeDesc中未找到前端字段映射，使用'Front'字段作为默认前端字段");
        fieldMappings["Front"] =
            PatternMatch("Front", "__reserved_front__", null, true);
        // 更新前端字段名
        frontFieldName = "Front";
      }
    }

    // 如果没有后端字段映射，尝试使用名为"Back"的字段
    if (backFieldName.isEmpty) {
      if (ankiConnectController.fieldList.contains("Back")) {
        logger.i("在_processNodeForNodeDesc中未找到后端字段映射，使用'Back'字段作为默认后端字段");
        fieldMappings["Back"] =
            PatternMatch("Back", "__reserved_back__", null, true);
        // 更新后端字段名
        backFieldName = "Back";
      } else {
        // 如果没有找到"Back"字段，尝试获取下一个字段
        final fieldIndex =
            ankiConnectController.fieldList.indexOf(frontFieldName);
        if (fieldIndex >= 0 &&
            fieldIndex < ankiConnectController.fieldList.length - 1) {
          backFieldName = ankiConnectController.fieldList[fieldIndex + 1];
        } else {
          backFieldName = frontFieldName; // 实在没有就用相同字段
        }
      }
    }

    // 创建卡片内容
    Map<String, String> content = {};

    // 设置正面内容 - 包含文本和图片
    StringBuffer frontContent = StringBuffer();

    // 不再此处添加样式，移到最后添加
    // frontContent.writeln(_getAllStyles());

    // 处理节点文本，移除可能存在的标签
    String nodeText = node.text;
    final processedText = _processNodeTags(nodeText);
    nodeText = processedText['cleanedContent'] as String;
    // 进一步清理，移除任何剩余的标签显示
    nodeText =
        nodeText.replaceAll(RegExp(r'<span class="tag">#[^<]*</span>'), '');

    // 添加节点文本
    frontContent.writeln('<div class="node-text">');
    if (node.color != null &&
        node.color!.isNotEmpty &&
        node.color != '#333333') {
      frontContent
          .writeln('<span style="color: ${node.color};">${nodeText}</span>');
    } else {
      frontContent.writeln(nodeText);
    }
    frontContent.writeln('</div>');

    // 添加图片到正面
    final imageHtml = _processNodeImages(node);
    if (imageHtml.isNotEmpty) {
      frontContent.writeln(imageHtml);
    }

    // 只有在isShowSource为true时才添加回链
    if (isShowSource.value) {
      frontContent.writeln('<div class="source-link">');
      frontContent.writeln(
          '<a href="https://mubu.com/app/edit/home/<USER>">${'anki.mubu.view_source'.tr}</a>');
      frontContent.writeln('</div>');
    }

    // 在最后添加样式
    content[frontFieldName] = _getAllStyles() + frontContent.toString();

    // 设置背面内容 - 确保用已清除标签的note内容
    if (cleanedNote.isNotEmpty) {
      // 移除任何剩余的标签显示（如<span class="tag">#标签</span>）
      String finalCleanedNote = cleanedNote.replaceAll(
          RegExp(r'<span class="tag">#[^<]*</span>'), '');

      // 如果启用了答案挖空功能，处理背面内容
      if (isAnswerCloze.value) {
        finalCleanedNote = await _processContentWithCloze(
            '<div class="node-note">$finalCleanedNote</div>');
      } else {
        finalCleanedNote = '<div class="node-note">$finalCleanedNote</div>';
      }

      content[backFieldName] = finalCleanedNote;
    } else {
      content[backFieldName] = "";
    }

    // 设置ID字段为节点ID
    content["ID"] = "${document.value}-${node.id}";
    logger.i("设置ID字段值: ${document.value}-${node.id}");

    // 确保ID字段映射存在
    if (!fieldMappings.containsKey('ID')) {
      fieldMappings['ID'] = PatternMatch('ID', '', null, true);
    }

    // 过滤掉keep和ignore标签
    List<String> cardTags = nodeTags
        .where((tag) =>
            tag.toLowerCase() != 'keep' && tag.toLowerCase() != 'ignore')
        .toList();

    // 牌组名称
    String deckName = parentDeck.value;
    if (isCreateSubDeck.value) {
      // 获取文档完整路径
      final folderPath = await _getDocumentFolderPath(document.value);
      if (folderPath.isNotEmpty) {
        // 如果有完整路径，则添加到牌组名称中：根牌组::文件夹路径::文档名
        deckName = "$deckName::$folderPath::$docName";
      } else {
        // 如果没有路径，则直接使用文档名
        deckName = "$deckName::$docName";
      }
    }
    // 创建笔记 - 使用节点ID作为guid以便后续同步或更新
    final note = _createNote(deckName, content, cardModel.value,
        guid: "${document.value}-${node.id}-${cardModel.value}",
        cardTags: cardTags);
    notes.add(note);

    // 递归处理子节点
    for (final child in node.children) {
      await _processNodeForNodeDesc(
          child, docName, notes, processedNodes + 1, totalNodes);
    }
  }

  /// 处理层次模式 (hierarchy)
  Future<void> _processNodeForHierarchy(
      MubuNode node,
      String docName,
      List<AnkiNote> notes,
      int processedNodes,
      int totalNodes,
      int questionNodeLevel,
      List<MubuNode> ancestors,
      int currentLevel) async {
    // 更新进度
    progressController.updateProgress(
      status: "running",
      message:
          "${'anki.mubu.processing_node'.tr} ${processedNodes + 1}/${totalNodes}",
      current: (processedNodes + 1).toDouble(),
      total: totalNodes.toDouble(),
    );

    // 跳过完全没有内容的节点（文本、备注和图片都为空）
    if (node.text.trim().isEmpty &&
        (node.note == null || node.note!.trim().isEmpty) &&
        (node.images == null || node.images!.isEmpty)) {
      return;
    }

    // 处理节点的标签，判断是否需要跳过
    List<String> nodeTags = [];
    bool skipCard = false;
    bool hasKeepTag = false;
    bool hasIgnoreTag = false;

    // 检查当前节点的标签
    if (node.note != null && node.note!.isNotEmpty) {
      // 处理笔记中的标签
      final processedNote = _processNodeTags(node.note);
      nodeTags = processedNote['extractedTags'] as List<String>;

      // 检查是否有keep或ignore标签
      hasKeepTag = nodeTags.any((tag) => tag.toLowerCase() == 'keep');
      hasIgnoreTag = nodeTags.any((tag) => tag.toLowerCase() == 'ignore');
    }

    // 检查祖先节点的标签（继承标签）
    bool ancestorHasKeepTag = false;
    bool ancestorHasIgnoreTag = false;

    for (final ancestor in ancestors) {
      if (ancestor.note != null && ancestor.note!.isNotEmpty) {
        final processedNote = _processNodeTags(ancestor.note);
        final ancestorTags = processedNote['extractedTags'] as List<String>;

        if (ancestorTags.any((tag) => tag.toLowerCase() == 'keep')) {
          ancestorHasKeepTag = true;
        }
        if (ancestorTags.any((tag) => tag.toLowerCase() == 'ignore')) {
          ancestorHasIgnoreTag = true;
        }
      }
    }

    // 根据isPartCard和特殊标签决定是否跳过
    if (!isPartCard.value) {
      // 如果不是部分制卡模式，只有带有精确的"ignore"标签的节点或其祖先有ignore标签需要跳过
      skipCard = hasIgnoreTag || ancestorHasIgnoreTag;
    } else {
      // 如果是部分制卡模式，默认跳过所有节点，只处理带有精确的"keep"标签的节点或其祖先有keep标签
      skipCard = !(hasKeepTag || ancestorHasKeepTag);
    }

    // 如果需要跳过该节点，将skipCard的值传递给子节点
    if (skipCard) {
      // 如果当前节点需要跳过，不要处理其子节点的卡片，但需要遍历子节点以保持进度更新
      for (final child in node.children) {
        // 创建新的祖先列表，确保包含当前跳过的节点
        List<MubuNode> skipAncestors = List.from(ancestors);
        skipAncestors.add(node); // 添加当前节点作为祖先

        await _processNodeForHierarchy(
            child,
            docName,
            notes,
            processedNodes + 1,
            totalNodes,
            questionNodeLevel,
            skipAncestors,
            currentLevel + 1);
      }
      return;
    }

    // 当前节点是问题节点的情况
    if (currentLevel == questionNodeLevel) {
      // 获取前端字段名
      String frontFieldName = _getFieldNameByValue("__reserved_front__");
      String backFieldName = _getFieldNameByValue("__reserved_back__");

      // 如果没有前端字段映射，尝试使用名为"Front"的字段
      if (frontFieldName.isEmpty) {
        if (ankiConnectController.fieldList.contains("Front")) {
          logger.i("在_processNodeForHierarchy中未找到前端字段映射，使用'Front'字段作为默认前端字段");
          fieldMappings["Front"] =
              PatternMatch("Front", "__reserved_front__", null, true);
          frontFieldName = "Front";
        }
      }

      // 如果没有后端字段映射，尝试使用名为"Back"的字段
      if (backFieldName.isEmpty) {
        if (ankiConnectController.fieldList.contains("Back")) {
          logger.i("在_processNodeForHierarchy中未找到后端字段映射，使用'Back'字段作为默认后端字段");
          fieldMappings["Back"] =
              PatternMatch("Back", "__reserved_back__", null, true);
          backFieldName = "Back";
        } else {
          // 如果没有找到"Back"字段，尝试获取下一个字段
          final fieldIndex =
              ankiConnectController.fieldList.indexOf(frontFieldName);
          if (fieldIndex >= 0 &&
              fieldIndex < ankiConnectController.fieldList.length - 1) {
            backFieldName = ankiConnectController.fieldList[fieldIndex + 1];
          } else {
            backFieldName = frontFieldName; // 实在没有就用相同字段
          }
        }
      }

      // 创建卡片内容
      Map<String, String> content = {};

      // 构建正面内容，包括所有祖先节点和当前节点
      StringBuffer frontContent = StringBuffer();

      // 添加幕布样式
      frontContent.writeln(_getAllStyles());

      // 添加祖先节点内容
      if (questionNodeLevel > 1) {
        for (final ancestor in ancestors) {
          final nodeContent =
              _processNodeContent(ancestor, includeImages: true);
          frontContent.writeln("""
                <article>
                <details style="margin-left: 1rem;">
                    <summary>
                    ${nodeContent}
                    </summary>
""");
        }
        for (final ancestor in ancestors) {
          frontContent.writeln("</details></article>");
        }
      }

      // 添加当前节点内容（作为问题）
      frontContent.writeln(_processNodeContent(node, includeImages: true));

      // 只有在isShowSource为true时才添加回链
      if (isShowSource.value) {
        frontContent.writeln('<div class="source-link">');
        frontContent.writeln(
            '<a href="https://mubu.com/app/edit/home/<USER>">${'anki.mubu.view_source'.tr}</a>');
        frontContent.writeln('</div>');
      }

      // 在最后添加样式
      content[frontFieldName] = _getAllStyles() + frontContent.toString();

      // 构建背面内容，使用所有子孙节点并通过details标签嵌套
      StringBuffer backContent = StringBuffer();
      if (node.children.isNotEmpty) {
        // 递归处理所有子孙节点
        await _buildNestedContent(node.children, backContent, 0);
      }

      String backContentStr = backContent.toString();

      // 如果启用了答案挖空功能，处理背面内容
      if (isAnswerCloze.value) {
        backContentStr = await _processContentWithCloze(backContentStr);
      }

      content[backFieldName] = backContentStr;

      // 收集当前节点和子节点的所有标签，但仅用于当前卡片
      // 只使用当前节点的标签，过滤掉keep和ignore标签
      List<String> cardTags = nodeTags
          .where((tag) =>
              tag.toLowerCase() != 'keep' && tag.toLowerCase() != 'ignore')
          .toList();

      // 添加祖先节点的标签（继承标签，但不包含keep和ignore控制标签）
      for (final ancestor in ancestors) {
        if (ancestor.note != null && ancestor.note!.isNotEmpty) {
          final processedNote = _processNodeTags(ancestor.note);
          final ancestorTags = processedNote['extractedTags'] as List<String>;

          // 添加除了keep和ignore之外的所有标签
          cardTags.addAll(ancestorTags.where((tag) =>
              tag.toLowerCase() != 'keep' && tag.toLowerCase() != 'ignore'));
        }
      }

      // 去重处理标签
      cardTags = cardTags.toSet().toList();

      // 处理特殊字段映射
      for (final entry in fieldMappings.entries) {
        final field = entry.key;
        final pattern = entry.value;

        // 跳过已处理的前后端字段
        if (field == frontFieldName || field == backFieldName) {
          continue;
        }

        // 其他字段处理，根据正则匹配节点的子内容
        if (pattern.regex.isNotEmpty &&
            pattern.regex != "__reserved_front__" &&
            pattern.regex != "__reserved_back__") {
          for (final child in node.children) {
            final regExp = RegExp(pattern.regex, multiLine: true);
            if (regExp.hasMatch(child.text)) {
              // 提取内容时是否保留前缀
              if (pattern.keepPrefix) {
                content[field] =
                    _processNodeContent(child, includeImages: true);
              } else {
                // 去除匹配的前缀
                final match = regExp.firstMatch(child.text);
                if (match != null) {
                  // 复制节点并修改文本
                  final modifiedNode = MubuNode(
                    id: child.id,
                    text: child.text.substring(match.end),
                    level: child.level,
                    color: child.color,
                    note: child.note,
                    images: child.images,
                    children: child.children,
                  );
                  content[field] =
                      _processNodeContent(modifiedNode, includeImages: true);
                } else {
                  content[field] =
                      _processNodeContent(child, includeImages: true);
                }
              }
              break; // 找到匹配的第一个子节点就停止
            }
          }
        }
      }

      // 牌组名称
      String deckName = parentDeck.value;
      if (isCreateSubDeck.value) {
        // 获取文档完整路径
        final folderPath = await _getDocumentFolderPath(document.value);
        if (folderPath.isNotEmpty) {
          // 如果有完整路径，则添加到牌组名称中：根牌组::文件夹路径::文档名
          deckName = "$deckName::$folderPath::$docName";
        } else {
          // 如果没有路径，则直接使用文档名
          deckName = "$deckName::$docName";
        }
      }

      // 创建笔记 - 使用节点ID作为guid以便后续同步或更新
      // 传递节点的标签，会与全局标签合并
      final note = _createNote(deckName, content, cardModel.value,
          guid: "${document.value}-${node.id}-${cardModel.value}",
          cardTags: cardTags);
      notes.add(note);
    }

    // 构建新的祖先列表，用于递归
    List<MubuNode> newAncestors = List.from(ancestors);
    if (currentLevel < questionNodeLevel) {
      newAncestors.add(node);
    } else {
      // 当前级别是问题节点级别或更深，也把当前节点作为祖先传递
      // 这样可以确保标签的继承性
      newAncestors.add(node);
    }

    // 递归处理子节点
    for (final child in node.children) {
      await _processNodeForHierarchy(child, docName, notes, processedNodes + 1,
          totalNodes, questionNodeLevel, newAncestors, currentLevel + 1);
    }
  }

  /// 创建Anki笔记
  AnkiNote _createNote(
      String deckName, Map<String, String> content, String modelName,
      {String? guid, List<String>? cardTags}) {
    final fields =
        List.generate(ankiConnectController.fieldList.length, (index) {
      final field = ankiConnectController.fieldList[index];
      return content[field] ?? '';
    });

    // 合并全局标签和节点特定标签
    final List<String> noteTags = [...tags]; // 先加入全局标签
    if (cardTags != null && cardTags.isNotEmpty) {
      noteTags.addAll(cardTags); // 再加入节点特定标签
    }

    // 去重处理
    final uniqueTags = noteTags.toSet().toList();

    return AnkiNote(
      deckName: deckName,
      modelName: modelName,
      fields: fields,
      tags: uniqueTags,
      guid: guid,
    );
  }

  /// 处理节点中的标签，提取#标签并添加到卡片标签中
  /// 返回一个包含清理后的note内容和提取的标签列表的Map
  Map<String, dynamic> _processNodeTags(String? noteContent) {
    if (noteContent == null || noteContent.isEmpty) {
      return {'cleanedContent': '', 'extractedTags': <String>[]};
    }

    // 首先检查是否有HTML格式的标签
    bool hasHtmlTags = noteContent.contains('<span class="tag">');

    // 创建临时纯文本用于提取标签
    String plainText = noteContent.replaceAll(RegExp(r'<[^>]*>'), '');

    // 匹配标签模式 #tag 或 #tag::subtag
    final tagPattern = RegExp(
        r'#([a-zA-Z0-9_\u4e00-\u9fa5]+(?:::[a-zA-Z0-9_\u4e00-\u9fa5]+)*)');
    final matches = tagPattern.allMatches(plainText);

    // 提取标签
    final extractedTags = <String>[];
    for (final match in matches) {
      final tag = match.group(1);
      if (tag != null && tag.isNotEmpty) {
        // 确保标签不包含HTML或其他不需要的内容
        final cleanTag = tag.trim();
        if (cleanTag.isNotEmpty) {
          extractedTags.add(cleanTag);
        }
      }
    }

    // 移除标签内容
    String cleanedContent = noteContent;
    if (extractedTags.isNotEmpty) {
      // 对于HTML格式的标签
      if (hasHtmlTags) {
        // 直接移除所有带有tag类的span元素
        cleanedContent = cleanedContent.replaceAll(
            RegExp(r'<span class="tag">#[^<]*</span>'), '');
      }

      // 同时也处理纯文本标签
      for (final tag in extractedTags) {
        // 使用正则表达式找到并替换标签（包括可能嵌套在HTML中的情况）
        cleanedContent = cleanedContent.replaceAll(
            RegExp('#$tag(?![a-zA-Z0-9_\u4e00-\u9fa5])'), '');
      }

      // 清理HTML标签之间的空格和空HTML标签
      cleanedContent = cleanedContent
          .replaceAll(RegExp(r'<span>\s*</span>'), '')
          .replaceAll(RegExp(r'<div>\s*</div>'), '')
          .replaceAll(RegExp(r'\n\s*\n'), '\n')
          .trim();
    }

    return {
      'cleanedContent': cleanedContent,
      'extractedTags': extractedTags,
    };
  }

  /// 递归构建嵌套的details标签结构，显示所有子孙节点
  Future<void> _buildNestedContent(
      List<MubuNode> nodes, StringBuffer buffer, int depth) async {
    for (int i = 0; i < nodes.length; i++) {
      final node = nodes[i];

      // 处理当前节点的note内容，移除标签
      MubuNode processedNode = node;
      if (node.note != null && node.note!.isNotEmpty) {
        final processedNote = _processNodeTags(node.note);

        // 进一步清理，移除任何剩余的标签显示
        String cleanedNote = processedNote['cleanedContent'] as String;
        cleanedNote = cleanedNote.replaceAll(
            RegExp(r'<span class="tag">#[^<]*</span>'), '');

        // 创建一个新节点，使用清理后的note内容
        processedNode = MubuNode(
          id: node.id,
          text: node.text,
          level: node.level,
          color: node.color,
          note: cleanedNote,
          images: node.images,
          children: node.children,
        );
      }

      // 如果有子节点，使用表格布局和自定义折叠功能
      if (node.children.isNotEmpty) {
        String nodeContent =
            _processNodeContent(processedNode, includeImages: true)
                .replaceAll('\n', '<br>'); // 将换行符替换为HTML的br标签
        buffer.writeln("""
                <article>
                <details style="margin-left: 1rem;">
                    <summary>
                    ${nodeContent}
                    </summary>
""");
        // 递归处理子节点
        await _buildNestedContent(node.children, buffer, depth + 1);

        buffer.writeln('</details>');
        buffer.writeln('</article>');
      } else {
        // 如果没有子节点，直接显示内容，但要将换行符替换为br标签
        String content = _processNodeContent(processedNode, includeImages: true)
            .replaceAll('\n', '<br>');
        buffer.writeln("<li>$content</li>");
      }
    }
  }

  /// 处理层次模式下的挖空题 (hierarchy + cloze)
  Future<void> _processNodeForHierarchyCloze(
      MubuNode node,
      String docName,
      List<AnkiNote> notes,
      int processedNodes,
      int totalNodes,
      int questionNodeLevel,
      List<MubuNode> ancestors,
      int currentLevel) async {
    // 更新进度
    progressController.updateProgress(
      status: "running",
      message:
          "${'anki.mubu.processing_node'.tr} ${processedNodes + 1}/${totalNodes}",
      current: (processedNodes + 1).toDouble(),
      total: totalNodes.toDouble(),
    );

    // 跳过完全没有内容的节点（文本、备注和图片都为空）
    if (node.text.trim().isEmpty &&
        (node.note == null || node.note!.trim().isEmpty) &&
        (node.images == null || node.images!.isEmpty)) {
      return;
    }

    // 处理节点的标签，判断是否需要跳过
    List<String> nodeTags = [];
    String cleanedNote = "";
    bool skipCard = false;
    bool hasKeepTag = false;
    bool hasIgnoreTag = false;

    // 检查当前节点的标签
    if (node.note != null && node.note!.isNotEmpty) {
      // 处理笔记中的标签
      final processedNote = _processNodeTags(node.note);
      cleanedNote = processedNote['cleanedContent'] as String;
      nodeTags = processedNote['extractedTags'] as List<String>;

      // 检查是否有keep或ignore标签
      hasKeepTag = nodeTags.any((tag) => tag.toLowerCase() == 'keep');
      hasIgnoreTag = nodeTags.any((tag) => tag.toLowerCase() == 'ignore');
    }

    // 检查祖先节点的标签（继承标签）
    bool ancestorHasKeepTag = false;
    bool ancestorHasIgnoreTag = false;

    for (final ancestor in ancestors) {
      if (ancestor.note != null && ancestor.note!.isNotEmpty) {
        final processedNote = _processNodeTags(ancestor.note);
        final ancestorTags = processedNote['extractedTags'] as List<String>;

        if (ancestorTags.any((tag) => tag.toLowerCase() == 'keep')) {
          ancestorHasKeepTag = true;
        }
        if (ancestorTags.any((tag) => tag.toLowerCase() == 'ignore')) {
          ancestorHasIgnoreTag = true;
        }
      }
    }

    // 根据isPartCard和特殊标签决定是否跳过
    if (!isPartCard.value) {
      // 如果不是部分制卡模式，只有带有精确的"ignore"标签的节点或其祖先有ignore标签需要跳过
      skipCard = hasIgnoreTag || ancestorHasIgnoreTag;
    } else {
      // 如果是部分制卡模式，默认跳过所有节点，只处理带有精确的"keep"标签的节点或其祖先有keep标签
      skipCard = !(hasKeepTag || ancestorHasKeepTag);
    }

    // 如果需要跳过该节点，将skipCard的值传递给子节点
    if (skipCard) {
      // 如果当前节点需要跳过，不要处理其子节点的卡片，但需要遍历子节点以保持进度更新
      for (final child in node.children) {
        // 创建新的祖先列表，确保包含当前跳过的节点
        List<MubuNode> skipAncestors = List.from(ancestors);
        skipAncestors.add(node); // 添加当前节点作为祖先

        await _processNodeForHierarchyCloze(
            child,
            docName,
            notes,
            processedNodes + 1,
            totalNodes,
            questionNodeLevel,
            skipAncestors,
            currentLevel + 1);
      }
      return;
    }

    // 只有当当前节点级别与问题节点级别匹配时才生成卡片
    if (currentLevel == questionNodeLevel) {
      // 验证字段映射是否存在必要的字段
      if (!fieldMappings.containsKey('Front') ||
          !fieldMappings.containsKey('Back')) {
        logger.i("挖空题模式检查字段映射");
        // 确保字段映射中包含必要的字段

        // 检查Front字段
        if (!fieldMappings.containsKey('Front')) {
          if (ankiConnectController.fieldList.contains("Front")) {
            logger.i("在_processNodeForHierarchyCloze中未找到Front字段映射，使用默认值");
            fieldMappings['Front'] =
                PatternMatch('Front', '__reserved_front__', null, true);
          }
        }

        // 检查Back字段
        if (!fieldMappings.containsKey('Back')) {
          if (ankiConnectController.fieldList.contains("Back")) {
            logger.i("在_processNodeForHierarchyCloze中未找到Back字段映射，使用默认值");
            fieldMappings['Back'] =
                PatternMatch('Back', '__reserved_back__', null, true);
          }
        }
      }

      // 创建卡片内容
      Map<String, String> content = {};

      // 设置正面内容 - 包含文本和图片
      StringBuffer frontContent = StringBuffer();

      // 添加祖先节点内容
      if (questionNodeLevel > 1) {
        for (final ancestor in ancestors) {
          frontContent
              .writeln(_processNodeContent(ancestor, includeImages: true));
        }
      }

      // 处理节点文本，移除可能存在的标签
      String nodeText = node.text;
      final processedText = _processNodeTags(nodeText);
      nodeText = processedText['cleanedContent'] as String;
      // 进一步清理，移除任何剩余的标签显示
      nodeText =
          nodeText.replaceAll(RegExp(r'<span class="tag">#[^<]*</span>'), '');

      // 添加节点文本
      frontContent.writeln('<div class="node-text">');
      if (node.color != null &&
          node.color!.isNotEmpty &&
          node.color != '#333333') {
        frontContent
            .writeln('<span style="color: ${node.color};">${nodeText}</span>');
      } else {
        frontContent.writeln(nodeText);
      }
      frontContent.writeln('</div>');

      // 添加图片到正面
      final imageHtml = _processNodeImages(node);
      if (imageHtml.isNotEmpty) {
        frontContent.writeln(imageHtml);
      }

      // 设置卡片背面内容 - 使用节点的note内容或子节点内容
      StringBuffer backContent = StringBuffer();

      // 如果有note，使用note作为背面内容
      if (cleanedNote.isNotEmpty) {
        backContent.writeln('<div class="node-note">');
        backContent.writeln(cleanedNote);
        backContent.writeln('</div>');
      }
      // 如果有子节点，使用子节点作为背面内容
      else if (node.children.isNotEmpty) {
        // 递归处理子节点
        await _buildNestedContent(node.children, backContent, 0);
      }

      // 处理挖空: 应用挖空标记到正面内容
      String frontContentStr = frontContent.toString();

      // 获取WebView控制器应用挖空处理
      final webviewController = Get.find<WebviewController>();

      // 定义挖空样式和颜色，这里使用所有可能的样式类型
      final List<String> clozeStyles = maskTypes.toList();
      logger.i("使用的挖空样式: $clozeStyles");

      // 创建用于JS函数的颜色列表 - 直接使用已有的列表
      final List<String> textColors = textColorList.isEmpty
          ? ['red', 'yellow', 'green', 'blue', 'purple', 'dark']
          : textColorList.toList();
      logger.i("使用的文本颜色: $textColors");

      final List<String> highlightColors = highlightColorList.isEmpty
          ? ['yellow', 'red', 'blue', 'grey', 'olive', 'pink', 'cyan']
          : highlightColorList.toList();
      logger.i("使用的高亮颜色: $highlightColors");

      // 处理前端内容挖空
      try {
        logger.i("开始处理挖空内容，原始内容长度: ${frontContentStr.length}");
        frontContentStr = await webviewController.convertClozeForMubu(
            frontContentStr, clozeStyles, textColors, highlightColors);
        logger.i("挖空内容处理完成，处理后内容长度: ${frontContentStr.length}");

        // 检查是否成功转换（简单检查是否包含挖空标记）
        if (frontContentStr.contains("[[c") && frontContentStr.contains("::")) {
          logger.i("成功找到挖空标记");
        } else {
          logger.w("未找到挖空标记，可能转换失败");
        }
      } catch (e) {
        logger.e("处理挖空内容时出错: $e");
        // 发生错误时使用原始内容
      }

      // 只有在isShowSource为true时才添加回链
      if (isShowSource.value) {
        frontContentStr = frontContentStr +
            '''
<div class="source-link">
<a href="https://mubu.com/app/edit/home/<USER>">${'anki.mubu.view_source'.tr}</a>
</div>''';
      }

      // 在最后添加样式
      frontContentStr = _getAllStyles() + frontContentStr;

      // 设置字段内容 - 确保设置正确的字段值
      content["Front"] = frontContentStr;
      content["Back"] = backContent.toString();
      content["Mode"] = clozeMode.value;
      content["ID"] = "${document.value}-${node.id}"; // 设置ID字段为文档ID拼接上节点ID
      logger.i("设置Mode字段值: ${clozeMode.value}");
      logger.i("设置ID字段值: ${document.value}-${node.id}");
      // 收集当前节点的所有标签，过滤掉keep和ignore标签
      List<String> cardTags = nodeTags
          .where((tag) =>
              tag.toLowerCase() != 'keep' && tag.toLowerCase() != 'ignore')
          .toList();

      // 添加祖先节点的标签（继承标签，但不包含keep和ignore控制标签）
      for (final ancestor in ancestors) {
        if (ancestor.note != null && ancestor.note!.isNotEmpty) {
          final processedNote = _processNodeTags(ancestor.note);
          final ancestorTags = processedNote['extractedTags'] as List<String>;

          cardTags.addAll(ancestorTags.where((tag) =>
              tag.toLowerCase() != 'keep' && tag.toLowerCase() != 'ignore'));
        }
      }

      // 去重处理标签
      cardTags = cardTags.toSet().toList();

      // 牌组名称
      String deckName = parentDeck.value;
      if (isCreateSubDeck.value) {
        // 获取文档完整路径
        final folderPath = await _getDocumentFolderPath(document.value);
        if (folderPath.isNotEmpty) {
          // 如果有完整路径，则添加到牌组名称中：根牌组::文件夹路径::文档名
          deckName = "$deckName::$folderPath::$docName";
        } else {
          // 如果没有路径，则直接使用文档名
          deckName = "$deckName::$docName";
        }
      }

      // 创建笔记 - 使用节点ID作为guid以便后续同步或更新
      if (oneClozePeCard.value &&
          (clozeMode.value == "mask_one_guess_one" ||
              clozeMode.value == "mask_all_guess_one")) {
        // 修改Mode字段，添加_multi后缀
        content["Mode"] = "${clozeMode.value}_multi";

        // 提取所有cloze编号
        final clozeNumbers = <String>[];
        final frontContent = content["Front"];
        if (frontContent != null) {
          final regex = RegExp(r'\[\[(c\d+)::.*?\]\]');
          final matches = regex.allMatches(frontContent);
          for (final match in matches) {
            final clozeNum = match.group(1);
            if (clozeNum != null && !clozeNumbers.contains(clozeNum)) {
              clozeNumbers.add(clozeNum);
            }
          }
        }

        // 为每个挖空编号创建单独的卡片
        if (clozeNumbers.isNotEmpty) {
          for (final clozeNum in clozeNumbers) {
            final guid =
                "${document.value}-${node.id}-${clozeMode.value}-${clozeNum}";
            content['Index'] = clozeNum;
            content['ID'] = guid;
            final note = _createNote(deckName, content, cardModel.value,
                guid: guid, cardTags: cardTags);

            // 记录字段值
            logger.i("创建挖空卡片(一空一卡), 字段: ${note.fields.join(' | ')}");

            notes.add(note);
          }
        } else {
          // 如果没有找到挖空，仍然创建一张卡片
          final note = _createNote(deckName, content, cardModel.value,
              guid: "${document.value}-${node.id}-${clozeMode.value}",
              cardTags: cardTags);

          // 记录字段值
          logger.i("创建挖空卡片(未找到挖空), 字段: ${note.fields.join(' | ')}");

          notes.add(note);
        }
      } else {
        final note = _createNote(deckName, content, cardModel.value,
            guid: "${document.value}-${node.id}-${clozeMode.value}",
            cardTags: cardTags);

        // 记录字段值
        logger.i("创建挖空卡片, 字段: ${note.fields.join(' | ')}");

        notes.add(note);
      }
    }

    // 构建新的祖先列表，用于递归
    List<MubuNode> newAncestors = List.from(ancestors);
    if (currentLevel < questionNodeLevel) {
      newAncestors.add(node);
    } else {
      // 当前级别是问题节点级别或更深，也把当前节点作为祖先传递
      // 这样可以确保标签的继承性
      newAncestors.add(node);
    }

    // 递归处理子节点
    for (final child in node.children) {
      await _processNodeForHierarchyCloze(
          child,
          docName,
          notes,
          processedNodes + 1,
          totalNodes,
          questionNodeLevel,
          newAncestors,
          currentLevel + 1);
    }
  }

  /// 获取文档所在文件夹的完整路径（从根目录到当前文件夹）
  /// 返回格式为 "根目录::子目录1::子目录2"，用于创建Anki子牌组
  Future<String> _getDocumentFolderPath(String docId) async {
    try {
      // 获取文档信息，包含directory字段
      final docData = await getDocument(docId, fetchFullInfo: true);

      // 检查文档是否有directory字段，并且不为空
      if (docData.directory == null || docData.directory!.isEmpty) {
        // 文档在根目录下，无需添加路径
        return "";
      }

      // directory字段是一个数组，包含从根目录到当前文件夹的完整路径
      // 每个元素包含name和id，我们只需要提取name字段并拼接
      final List<String> pathParts = [];
      for (final folder in docData.directory!) {
        if (folder is Map<String, dynamic> && folder.containsKey('name')) {
          pathParts.add(folder['name'] as String);
        }
      }

      // 返回完整路径，用::分隔（Anki牌组层级分隔符）
      return pathParts.join('::');
    } catch (e) {
      logger.e('获取文档文件夹路径时出错: $e');
      return "";
    }
  }
}

/// 数学工具类
class Math {
  static int min(int a, int b) => a < b ? a : b;
  static int max(int a, int b) => a > b ? a : b;
}

/// 使用示例
/// ```dart
/// // 初始化
/// final mubuController = Get.put(MubuCardPageController());
/// 
/// // 设置Cookie，会自动提取jwt-token
/// mubuController.setCookie('your_cookie_string_including_jwt_token');
/// 
/// // 获取所有文档
/// final docs = await mubuController.getAllDocuments();
/// print('找到 ${docs.length} 个文档');
/// 
/// // 获取第一个文档的内容
/// if (docs.isNotEmpty) {
///   final firstDoc = docs.first;
///   print('获取文档: ${firstDoc.name} (ID: ${firstDoc.id})');
///   
///   final docContent = await mubuController.getDocument(firstDoc.id, parseNodes: true);
///   print('文档名称: ${docContent.name}');
///   print('作者: ${docContent.author?['name']}');
///   
///   // 导出为纯文本
///   print('\n导出文档为纯文本:');
///   final plainText = await mubuController.exportDocumentToPlainText(firstDoc.id);
///   print(plainText.length > 500 ? '${plainText.substring(0, 500)}...' : plainText);
///   
///   // 或者导出为Markdown
///   print('\n导出文档为Markdown:');
///   final markdown = await mubuController.exportDocumentToMarkdown(firstDoc.id);
///   print(markdown.length > 500 ? '${markdown.substring(0, 500)}...' : markdown);
///   
///   // 搜索文档内容
///   final searchResults = await mubuController.searchInDocument(firstDoc.id, '关键词');
///   print('\n搜索结果:');
///   for (final node in searchResults) {
///     print('- ${node.text}');
///   }
///   
///   // 或者直接使用解析后的节点结构
///   if (docContent.nodes != null) {
///     print('\n文档结构:');
///     for (final node in docContent.nodes!) {
///       print('- ${node.text} (${node.children.length} 个子节点)');
///     }
///   }
/// }
/// 
/// // 获取文档条目数
/// final itemCounts = await mubuController.getItemCount();
/// print('\n文档条目数:');
/// (itemCounts['doc'] as Map<String, dynamic>?)?.forEach((docId, count) {
///   print('  文档 $docId: $count 条目');
/// });
/// ```
