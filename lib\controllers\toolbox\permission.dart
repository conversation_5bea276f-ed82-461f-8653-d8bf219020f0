import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class PDFPermissionController extends GetxController {
  // 表单参数
  final outputMode = "same".obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'custom');

  void submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      if (selectedFilePaths.isEmpty) {
        progressController.updateProgress(
          status: "error", message: 'toolbox.common.fileSelect.error'.tr,
        );
        return;
      }
      for (String filePath in selectedFilePaths) {
        progressController.updateProgress(
          status: "running",
          message: "${'toolbox.common.processing'.tr} ${PathUtils(filePath).name}",
        );
        // Determine output path
        String outputPath = await PathUtils(filePath).convertPath(
          outputMode.value,
          stem_append: "_${'toolbox.permission.title'.tr}",
          suffix: ".pdf",
          outputDir: outputDir.value,
        );

        final data = {
          'input_path': filePath,
          'output_path': outputPath,
          'show_progress': true,
        };

        final resp =
            await messageController.request(data, 'pdf/recover_permission');
        logger.w("resp: $resp");
        if (resp.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'toolbox.common.process.completed'.tr,
          );
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
          return;
        }
      }
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "${'toolbox.common.operationFailed'.tr}: $e",
      );
    }
  }
}
