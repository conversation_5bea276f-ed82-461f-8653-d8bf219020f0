PODS:
  - app_links (1.0.0):
    - FlutterMacOS
  - audioplayers_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - desktop_drop (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - file_picker (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_inappwebview_macos (0.0.1):
    - FlutterMacOS
    - OrderedSet (~> 6.0.3)
  - flutter_js (0.0.1):
    - FlutterMacOS
  - flutter_localization (0.0.1):
    - FlutterMacOS
  - flutter_machineid (0.0.1):
    - FlutterMacOS
  - flutter_udid (0.0.1):
    - FlutterMacOS
    - SAMKeychain
  - FlutterMacOS (1.0.0)
  - HotKey (0.2.1)
  - hotkey_manager_macos (0.0.1):
    - FlutterMacOS
    - HotKey
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - irondash_engine_context (0.0.1):
    - FlutterMacOS
  - isar_flutter_libs (1.0.0):
    - FlutterMacOS
  - local_notifier (0.1.0):
    - FlutterMacOS
  - media_kit_libs_macos_video (1.0.4):
    - FlutterMacOS
  - media_kit_video (0.0.1):
    - FlutterMacOS
  - open_file_mac (0.0.1):
    - FlutterMacOS
  - OrderedSet (6.0.3)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - pasteboard (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - quill_native_bridge_macos (0.0.1):
    - FlutterMacOS
  - rinf (0.1.0):
    - FlutterMacOS
  - SAMKeychain (1.5.3)
  - screen_brightness_macos (0.1.0):
    - FlutterMacOS
  - screen_capturer_macos (0.0.1):
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - super_native_extensions (0.0.1):
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - volume_controller (0.0.1):
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - window_size (0.0.2):
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `Flutter/ephemeral/.symlinks/plugins/app_links/macos`)
  - audioplayers_darwin (from `Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/darwin`)
  - desktop_drop (from `Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - file_picker (from `Flutter/ephemeral/.symlinks/plugins/file_picker/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_inappwebview_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos`)
  - flutter_js (from `Flutter/ephemeral/.symlinks/plugins/flutter_js/macos`)
  - flutter_localization (from `Flutter/ephemeral/.symlinks/plugins/flutter_localization/macos`)
  - flutter_machineid (from `Flutter/ephemeral/.symlinks/plugins/flutter_machineid/macos`)
  - flutter_udid (from `Flutter/ephemeral/.symlinks/plugins/flutter_udid/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - hotkey_manager_macos (from `Flutter/ephemeral/.symlinks/plugins/hotkey_manager_macos/macos`)
  - in_app_purchase_storekit (from `Flutter/ephemeral/.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - irondash_engine_context (from `Flutter/ephemeral/.symlinks/plugins/irondash_engine_context/macos`)
  - isar_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/isar_flutter_libs/macos`)
  - local_notifier (from `Flutter/ephemeral/.symlinks/plugins/local_notifier/macos`)
  - media_kit_libs_macos_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos`)
  - media_kit_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos`)
  - open_file_mac (from `Flutter/ephemeral/.symlinks/plugins/open_file_mac/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - pasteboard (from `Flutter/ephemeral/.symlinks/plugins/pasteboard/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - quill_native_bridge_macos (from `Flutter/ephemeral/.symlinks/plugins/quill_native_bridge_macos/macos`)
  - rinf (from `Flutter/ephemeral/.symlinks/plugins/rinf/macos`)
  - screen_brightness_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos`)
  - screen_capturer_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_capturer_macos/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - super_native_extensions (from `Flutter/ephemeral/.symlinks/plugins/super_native_extensions/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - volume_controller (from `Flutter/ephemeral/.symlinks/plugins/volume_controller/macos`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - webview_flutter_wkwebview (from `Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - window_size (from `Flutter/ephemeral/.symlinks/plugins/window_size/macos`)

SPEC REPOS:
  trunk:
    - HotKey
    - OrderedSet
    - SAMKeychain

EXTERNAL SOURCES:
  app_links:
    :path: Flutter/ephemeral/.symlinks/plugins/app_links/macos
  audioplayers_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/darwin
  desktop_drop:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  file_picker:
    :path: Flutter/ephemeral/.symlinks/plugins/file_picker/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_inappwebview_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos
  flutter_js:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_js/macos
  flutter_localization:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_localization/macos
  flutter_machineid:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_machineid/macos
  flutter_udid:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_udid/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  hotkey_manager_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/hotkey_manager_macos/macos
  in_app_purchase_storekit:
    :path: Flutter/ephemeral/.symlinks/plugins/in_app_purchase_storekit/darwin
  irondash_engine_context:
    :path: Flutter/ephemeral/.symlinks/plugins/irondash_engine_context/macos
  isar_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/isar_flutter_libs/macos
  local_notifier:
    :path: Flutter/ephemeral/.symlinks/plugins/local_notifier/macos
  media_kit_libs_macos_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos
  media_kit_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos
  open_file_mac:
    :path: Flutter/ephemeral/.symlinks/plugins/open_file_mac/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  pasteboard:
    :path: Flutter/ephemeral/.symlinks/plugins/pasteboard/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  quill_native_bridge_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/quill_native_bridge_macos/macos
  rinf:
    :path: Flutter/ephemeral/.symlinks/plugins/rinf/macos
  screen_brightness_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos
  screen_capturer_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_capturer_macos/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  super_native_extensions:
    :path: Flutter/ephemeral/.symlinks/plugins/super_native_extensions/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  volume_controller:
    :path: Flutter/ephemeral/.symlinks/plugins/volume_controller/macos
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  webview_flutter_wkwebview:
    :path: Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin
  window_size:
    :path: Flutter/ephemeral/.symlinks/plugins/window_size/macos

SPEC CHECKSUMS:
  app_links: afe860c55c7ef176cea7fb630a2b7d7736de591d
  audioplayers_darwin: 4f9ca89d92d3d21cec7ec580e78ca888e5fb68bd
  desktop_drop: 248706031734554504f939cab1ad4c5fbc9c9c72
  device_info_plus: 4fb280989f669696856f8b129e4a5e3cd6c48f76
  file_picker: 7584aae6fa07a041af2b36a2655122d42f578c1a
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  flutter_inappwebview_macos: c2d68649f9f8f1831bfcd98d73fd6256366d9d1d
  flutter_js: 79c3c70d33ba464c67d8eb88639a61aa718cd8b8
  flutter_localization: 3cda759884c7dab4466fe963d97fba723a492666
  flutter_machineid: 80aab92e41ac026ddb2ad35127212fbed33929f9
  flutter_udid: d26e455e8c06174e6aff476e147defc6cae38495
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  HotKey: 400beb7caa29054ea8d864c96f5ba7e5b4852277
  hotkey_manager_macos: a4317849af96d2430fa89944d3c58977ca089fbe
  in_app_purchase_storekit: d1a48cb0f8b29dbf5f85f782f5dd79b21b90a5e6
  irondash_engine_context: 893c7d96d20ce361d7e996f39d360c4c2f9869ba
  isar_flutter_libs: a65381780401f81ad6bf3f2e7cd0de5698fb98c4
  local_notifier: ebf072651e35ae5e47280ad52e2707375cb2ae4e
  media_kit_libs_macos_video: 85a23e549b5f480e72cae3e5634b5514bc692f65
  media_kit_video: fa6564e3799a0a28bff39442334817088b7ca758
  open_file_mac: 01874b6d6a2c1485ac9b126d7105b99102dea2cf
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: f0052d280d17aa382b932f399edf32507174e870
  pasteboard: 278d8100149f940fb795d6b3a74f0720c890ecb7
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  quill_native_bridge_macos: 2b005cb56902bb740e0cd9620aa399dfac6b4882
  rinf: 508cb5436e14f383f8d7d510f18e1c8c95a322b5
  SAMKeychain: 483e1c9f32984d50ca961e26818a534283b4cd5c
  screen_brightness_macos: 2a3ee243f8051c340381e8e51bcedced8360f421
  screen_capturer_macos: 229306903c56767a7c7d3a48167ba303e95c6d29
  share_plus: 510bf0af1a42cd602274b4629920c9649c52f4cc
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  super_native_extensions: c2795d6d9aedf4a79fae25cb6160b71b50549189
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  volume_controller: 5c068e6d085c80dadd33fc2c918d2114b775b3dd
  wakelock_plus: 21ddc249ac4b8d018838dbdabd65c5976c308497
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  window_size: 4bd15034e6e3d0720fd77928a7c42e5492cfece9

PODFILE CHECKSUM: 0ab5b6a0a98b764eab982f84457addb7ebb12b60

COCOAPODS: 1.16.2
