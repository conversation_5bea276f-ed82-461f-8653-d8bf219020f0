# PDF Guru API 服务

这是PDF Guru的HTTP API服务，用于替代原来的命令行接口，提供更灵活的PDF处理功能和实时进度反馈。

## 功能特点

- 使用FastAPI构建的RESTful API
- 支持Server-Sent Events (SSE)进行实时进度更新
- 异步任务处理，避免阻塞主线程
- 完整的OpenAPI文档支持

## 如何启动服务

### 安装依赖

```bash
pip install -r requirements.txt
```

### 直接启动

```bash
python main.py
```

或者指定主机和端口：

```bash
python main.py --host 127.0.0.1 --port 8080
```

### 作为服务启动

```bash
python service.py
```

## API文档

启动服务后，访问以下地址查看API文档：

- Swagger UI: http://127.0.0.1:8000/docs
- ReDoc: http://127.0.0.1:8000/redoc

## API使用示例

### 创建PDF转图片任务

```bash
curl -X POST "http://127.0.0.1:8000/task" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "operation=pdf2img&doc_path=/path/to/document.pdf&output_dir=/path/to/output&dpi=300&is_gray=false&format=png"
```

### 获取任务状态

```bash
curl -X GET "http://127.0.0.1:8000/task/{task_id}"
```

### 监听任务进度更新（SSE）

可以使用浏览器或支持SSE的HTTP客户端：

```javascript
// 浏览器中的JavaScript代码
const eventSource = new EventSource('http://127.0.0.1:8000/task/{task_id}/events');

eventSource.addEventListener('processing', function(event) {
  const data = JSON.parse(event.data);
  console.log('处理进度:', data.progress.current, '/', data.progress.total);
  console.log('消息:', data.progress.message);
});

eventSource.addEventListener('completed', function(event) {
  const data = JSON.parse(event.data);
  console.log('任务完成:', data.result);
  eventSource.close();
});

eventSource.addEventListener('error', function(event) {
  const data = JSON.parse(event.data);
  console.error('任务错误:', data.error);
  eventSource.close();
});
```

## 与Rust集成

Rust端通过HTTP客户端与API服务交互，实现了异步的PDF处理操作和实时进度反馈。相关代码位于：

- `native/hub/src/anki/api_manager.rs` - API服务管理
- `native/hub/src/anki/cmd.rs` - PDF处理操作接口