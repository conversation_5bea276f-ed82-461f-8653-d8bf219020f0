import * as vscode from 'vscode';
import { getConfiguration } from './utils';
import { setupInlineDecorator, updateDecorations } from './inlineDecorator';
import { syncJsonKeys } from './jsonSynchronizer';
import { batchTranslate } from './batchTranslator';
// --- Note the import changes ---
import { setupDiagnostics, scanWorkspace, updateInstantDiagnosticsForDocument } from './diagnostics';
import { extractAsKey, extractAsValue } from './keyExtractor';
import { I18nViewProvider } from './i18nViewProvider';
import { I18nTreeItem } from './i18nTreeItem';
import { renameKeyProcess } from './keyRenamer';
import { diffWithPrevious, diffWithNext } from './gitDiffer';
import { Logger } from './logger';
import { I18nCodeActionProvider } from './codeActions';

export function activate(context: vscode.ExtensionContext) {
    try {
        Logger.initialize(context);
        Logger.log('Activating "flutter-i18n-helper"...');

        const i18nViewProvider = new I18nViewProvider(context);

        context.subscriptions.push(
            vscode.commands.registerCommand('i18n-helper._internal.refreshAll', async () => {
                Logger.log('Internal refresh command triggered.');
                await i18nViewProvider.refresh();
                if (vscode.window.activeTextEditor) {
                    await updateDecorations(vscode.window.activeTextEditor);
                }
                // The main refresh command should trigger a full scan
                await scanWorkspace();
            })
        );

        setupInlineDecorator(context);
        
        // This setup now handles both instant and workspace-level diagnostics
        setupDiagnostics(context);

        context.subscriptions.push(
            vscode.languages.registerCodeActionsProvider(
                { language: 'dart' },
                new I18nCodeActionProvider(),
                { providedCodeActionKinds: I18nCodeActionProvider.providedCodeActionKinds }
            )
        );

        context.subscriptions.push(
            // ... all other command registrations remain the same ...
            vscode.commands.registerCommand('i18n-helper.showCommands', async () => {
                const extension = vscode.extensions.getExtension(context.extension.id);
                if (!extension) {
                    vscode.window.showErrorMessage('Could not find the I18n Helper extension details.');
                    return;
                }
                const allContributedCommands = extension.packageJSON?.contributes?.commands;
                if (!Array.isArray(allContributedCommands)) {
                    Logger.log('No commands found in package.json to display.');
                    return;
                }
                const config = getConfiguration();
                const customCommandIds = config.get<string[]>('quickPick.commands');
                let commandsToDisplay: any[];
                if (customCommandIds && customCommandIds.length > 0) {
                    const customCommandSet = new Set(customCommandIds);
                    commandsToDisplay = allContributedCommands.filter(cmd => customCommandSet.has(cmd.command));
                    commandsToDisplay.sort((a, b) => customCommandIds.indexOf(a.command) - customCommandIds.indexOf(b.command));
                } else {
                    commandsToDisplay = allContributedCommands.filter(
                        (cmd: any) => cmd.category === 'I18n Helper' && cmd.command !== 'i18n-helper.showCommands'
                    );
                }
                if (commandsToDisplay.length === 0) {
                    Logger.log('No commands found to display in the quick pick based on current settings.');
                    vscode.window.showInformationMessage('No I18n Helper commands are configured to be shown.');
                    return;
                }
                const quickPickItems: (vscode.QuickPickItem & { commandId: string })[] = commandsToDisplay.map((cmd: any) => ({
                    label: cmd.title,
                    description: `(${cmd.command})`,
                    commandId: cmd.command
                }));
                const selectedItem = await vscode.window.showQuickPick(quickPickItems, {
                    placeHolder: 'Select an I18n Helper command to run',
                    title: 'I18n Helper Commands'
                });
                if (selectedItem) {
                    vscode.commands.executeCommand(selectedItem.commandId);
                }
            }),
            vscode.commands.registerCommand('i18n-helper.syncKeys', syncJsonKeys),
            vscode.commands.registerCommand('i18n-helper.batchTranslate', batchTranslate),
            vscode.commands.registerCommand('i18n-helper.extractKey', extractAsKey),
            vscode.commands.registerCommand('i18n-helper.extractValue', extractAsValue),
            vscode.commands.registerCommand('i18n-helper.findMissingTranslations', scanWorkspace),
            vscode.commands.registerCommand('i18n-helper.findUnusedKeys', scanWorkspace),
            vscode.commands.registerCommand('i18n-helper.view.renameKey', async (item: I18nTreeItem) => {
                if (!item || !item.keyPath) {
                    vscode.window.showErrorMessage('Invalid item selected for rename.');
                    return;
                }
                const success = await renameKeyProcess(item.keyPath);
                if (success) {
                    vscode.commands.executeCommand('i18n-helper._internal.refreshAll');
                }
            }),
            vscode.commands.registerCommand('i18n-helper.git.diffWithPrevious', diffWithPrevious),
            vscode.commands.registerCommand('i18n-helper.git.diffWithNext', diffWithNext)
        );

        const treeView = vscode.window.createTreeView('i18n-key-view', {
            treeDataProvider: i18nViewProvider,
            canSelectMany: true
        });
        context.subscriptions.push(treeView);

        context.subscriptions.push(
            treeView.onDidExpandElement(e => {
                if (e.element.keyPath) {
                    i18nViewProvider.expandedKeys.add(e.element.keyPath);
                }
            }),
            treeView.onDidCollapseElement(e => {
                if (e.element.keyPath) {
                    i18nViewProvider.expandedKeys.delete(e.element.keyPath);
                }
            })
        );
        
        context.subscriptions.push(
            vscode.workspace.onDidChangeConfiguration(e => {
                const affectedConfig = 'i18n-helper';
                if (
                    e.affectsConfiguration(`${affectedConfig}.i18nDir`) ||
                    e.affectsConfiguration(`${affectedConfig}.sourceLanguage`)
                ) {
                    vscode.commands.executeCommand('i18n-helper._internal.refreshAll');
                }
            })
        );

        Logger.log('"flutter-i18n-helper" has been activated successfully!');

    } catch (error) {
        Logger.error('Failed to activate "flutter-i18n-helper"', error);
        let errorMessage = 'An unknown error occurred.';
        if (error instanceof Error) {
            errorMessage = error.message;
        } else {
            errorMessage = String(error);
        }
        vscode.window.showErrorMessage(`Flutter I18n Helper failed to activate. Reason: ${errorMessage}`);
    }
}

export function deactivate() { }