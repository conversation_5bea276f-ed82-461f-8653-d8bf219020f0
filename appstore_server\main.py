import os
import sqlite3
import time
import sys
import smtplib
import json
import base64
import uuid
from email.mime.text import MIMEText
from email.header import Header
from email.utils import formataddr
from typing import Dict, Any, List, Optional, Union, Callable

from fastapi import FastAPI, HTTPException, status, BackgroundTasks, Request, Response, Depends
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import BaseModel, Field
from dotenv import load_dotenv
from contextlib import asynccontextmanager
from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.errors import PyMongoError

from loguru import logger

# --- MongoDB连接池 ---
class MongoDBConnectionPool:
    _instance = None
    _client = None
    _db = None
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        # 不要在初始化时检查_client是否为真值
        if self._client is None:
            self._initialize_connection()
    
    def _initialize_connection(self):
        try:
            # 获取环境变量中的MongoDB凭据
            admin_username = os.environ.get('MONGO_INITDB_ROOT_USERNAME', 'admin')
            admin_password = os.environ.get('MONGO_INITDB_ROOT_PASSWORD', 'password')
            mongo_host = os.environ.get('MONGO_HOST', 'mongodb')
            mongo_port = os.environ.get('MONGO_PORT', '27017')
            mongo_db_name = os.environ.get('MONGO_DB_NAME', f'iap_{os.environ.get("APP_ENV", "development")}')
            
            # 使用环境变量中的用户名和密码
            mongo_uri = f'mongodb://{admin_username}:{admin_password}@{mongo_host}:{mongo_port}'
            
            logger.info(f"Initializing MongoDB connection pool with URI: {mongo_uri}")
            
            # 连接MongoDB
            self._client = MongoClient(mongo_uri, maxPoolSize=10)
            
            # 使用admin用户直接访问应用数据库
            self._db = self._client[mongo_db_name]
            
            # 确保集合存在
            mongo_collection = os.environ.get('MONGO_COLLECTION', 'user_purchases')
            if mongo_collection not in self._db.list_collection_names():
                logger.info(f"Creating collection {mongo_collection}")
                self._db.create_collection(mongo_collection)
                
            logger.info("MongoDB connection pool initialized successfully")
        except PyMongoError as e:
            logger.error(f"MongoDB连接池初始化错误: {e}")
            raise
    
    def get_connection(self):
        """获取MongoDB连接"""
        # 检查连接是否需要初始化，避免布尔值测试
        if self._client is None or self._db is None:
            self._initialize_connection()
        
        # 验证连接是否有效
        try:
            # 执行一个简单的ping命令来验证连接
            self._db.command('ping')
        except Exception as e:
            logger.error(f"MongoDB连接无效，尝试重新初始化: {e}")
            # 重置连接并重新初始化
            self._client = None
            self._db = None
            self._initialize_connection()
            
        return self._client, self._db
    
    def close(self):
        """关闭MongoDB连接池"""
        # 避免对_client进行布尔值测试
        if self._client is not None:
            logger.info("Closing MongoDB connection pool")
            self._client.close()
            self._client = None
            self._db = None

# --- 导入 appstoreserverlibrary 的正确模块和类 ---
# 客户端类
from appstoreserverlibrary.api_client import AppStoreServerAPIClient, APIException, GetTransactionHistoryVersion
# 环境枚举
from appstoreserverlibrary.models.Environment import Environment
# 导入接收验证和交易历史相关模型
from appstoreserverlibrary.receipt_utility import ReceiptUtility
from appstoreserverlibrary.models.HistoryResponse import HistoryResponse
from appstoreserverlibrary.models.TransactionHistoryRequest import TransactionHistoryRequest, ProductType, Order


# --- 日志配置 ---
load_dotenv()

# 修改默认日志路径为 /app/logs
LOG_FILE_PATH = os.environ.get('LOG_FILE_PATH', '/app/logs/app.log')
LOG_LEVEL_STR = os.environ.get('LOG_LEVEL', 'INFO').upper()

# 确保日志目录存在
try:
    os.makedirs(os.path.dirname(LOG_FILE_PATH), exist_ok=True)
except Exception as e:
    print(f"警告: 无法创建日志目录: {e}")
    # 如果无法创建目录，使用备用路径
    LOG_FILE_PATH = "/tmp/app.log"

logger.remove()
logger.add(
    sys.stderr,
    level=LOG_LEVEL_STR,
    format="{time} <level>{level}</level> {message}",
    colorize=True,
    enqueue=True
)

# 尝试添加文件日志
try:
    logger.add(
        LOG_FILE_PATH,
        level=LOG_LEVEL_STR,
        rotation="10 MB",
        compression="zip",
        retention="10 days",
        format="{time} {level} {message}",
        enqueue=True
    )
    print(f"日志文件配置成功: {LOG_FILE_PATH}")
except PermissionError:
    print(f"警告: 无法写入日志文件 {LOG_FILE_PATH}，仅使用控制台日志")
except Exception as e:
    print(f"警告: 配置日志文件时出错: {e}")

import logging
class InterceptHandler(logging.Handler):
    def emit(self, record):
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelname

        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

logging.basicConfig(handlers=[InterceptHandler()], level=0)
logging.getLogger("uvicorn.access").handlers = []
logging.getLogger("uvicorn.error").handlers = []


# --- 请求日志中间件 ---
class RequestLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable):
        request_id = str(uuid.uuid4())
        # 记录请求开始
        start_time = time.time()
        
        # 获取请求信息
        client_host = request.client.host if request.client else "unknown"
        method = request.method
        url = str(request.url)
        
        # 记录请求日志
        logger.info(f"Request started | ID: {request_id} | {method} {url} | Client: {client_host}")
        
        # 获取请求体 - 注意：这会消耗请求体，需要备份
        request_body = ""
        if method in ["POST", "PUT", "PATCH"]:
            try:
                raw_body = await request.body()
                # 最多记录前1000个字符，避免日志过大
                if len(raw_body) > 0:
                    try:
                        # 尝试解析为JSON并格式化
                        body_json = json.loads(raw_body)
                        request_body = json.dumps(body_json, ensure_ascii=False)[:1000]
                        if len(request_body) == 1000:
                            request_body += "...(truncated)"
                    except:
                        # 非JSON数据，直接记录原始数据
                        request_body = str(raw_body)[:1000]
                        if len(str(raw_body)) > 1000:
                            request_body += "...(truncated)"
                # 重新设置请求体，以便后续处理
                async def receive():
                    return {"type": "http.request", "body": raw_body}
                request._receive = receive
            except Exception as e:
                logger.error(f"Error reading request body: {e}")
        
        if request_body:
            logger.info(f"Request body | ID: {request_id} | {request_body}")
        
        # 处理请求并捕获任何异常
        response = None
        try:
            response = await call_next(request)
        except Exception as e:
            # 记录请求处理过程中的异常
            logger.error(f"Request failed | ID: {request_id} | Error: {str(e)}")
            raise e
        finally:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            status_code = response.status_code if response else 500
            logger.info(
                f"Request completed | ID: {request_id} | {method} {url} | "
                f"Status: {status_code} | Time: {process_time:.3f}s"
            )
            
        return response

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup logic
    logger.info(f"Running startup event for APP_ENV: {APP_ENV}...")
    
    # 初始化MongoDB连接池
    try:
        # 获取连接池实例并测试连接
        pool = MongoDBConnectionPool.get_instance()
        client, db = pool.get_connection()
        # 验证连接是否正常工作
        ping_result = db.command('ping')
        if ping_result.get('ok') == 1.0:
            logger.info("MongoDB connection pool initialized and tested successfully during startup")
        else:
            logger.critical("MongoDB connection test failed during startup")
    except Exception as e:
        logger.critical(f"Failed to initialize MongoDB connection pool: {e}")
    
    # 检查 App Store Server API 配置
    has_api_keys = all([APPLE_PRIVATE_KEY_PATH, APPLE_KEY_ID, APPLE_ISSUER_ID, BUNDLE_ID])
    if not has_api_keys:
        logger.critical("App Store Server API configuration is incomplete. Receipt validation will fail.")
    else:
        logger.info("App Store Server API configuration detected. Using transaction history API.")
    
    logger.info("Startup event completed.")
    
    yield
    
    # Shutdown logic (if needed)
    logger.info("Shutting down application...")
    # 关闭MongoDB连接池
    try:
        MongoDBConnectionPool.get_instance().close()
        logger.info("MongoDB connection pool closed during shutdown")
    except Exception as e:
        logger.error(f"Error closing MongoDB connection pool: {e}")


# FastAPI 应用实例
app = FastAPI(
    title="iOS In-App Purchase Verification Backend",
    description="A backend service to verify iOS App Store receipts with Apple's servers. Uses SQLite for persistence.",
    version="1.0.0",
    lifespan=lifespan
)

# 添加请求日志中间件
app.add_middleware(RequestLoggingMiddleware)

# App Store Server API 配置
APP_ENV = os.environ.get('APP_ENV', 'development')
APPLE_PRIVATE_KEY_PATH = os.environ.get('APPLE_PRIVATE_KEY_PATH')
APPLE_KEY_ID = os.environ.get('APPLE_KEY_ID')
APPLE_ISSUER_ID = os.environ.get('APPLE_ISSUER_ID')
BUNDLE_ID = os.environ.get('BUNDLE_ID')
# MongoDB配置
MONGO_ROOT_USERNAME = os.environ.get('MONGO_ROOT_USERNAME', 'admin')
MONGO_ROOT_PASSWORD = os.environ.get('MONGO_ROOT_PASSWORD', 'password')
MONGO_PORT = os.environ.get('MONGO_PORT', '27017')
MONGO_HOST = os.environ.get('MONGO_HOST', 'mongodb')
MONGO_DB_NAME = os.environ.get('MONGO_DB_NAME', f'iap_{APP_ENV}')
MONGO_COLLECTION = 'user_purchases'

# 邮件配置
MAIL_SERVER_HOST = os.environ.get('MAIL_SERVER_HOST')
MAIL_SERVER_PORT = int(os.environ.get('MAIL_SERVER_PORT', 587))
MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
MAIL_SENDER_EMAIL = os.environ.get('MAIL_SENDER_EMAIL')
MAIL_ADMIN_EMAIL = os.environ.get('MAIL_ADMIN_EMAIL')


# --- 数据库操作函数 ---
def get_db_connection():
    """获取MongoDB连接池中的连接"""
    try:
        # 使用连接池获取连接
        return MongoDBConnectionPool.get_instance().get_connection()
    except PyMongoError as e:
        logger.error(f"MongoDB连接错误: {e}")
        raise

# --- 邮件发送函数 - 服务器通知 ---
async def send_server_notification_email(
    notification_type: str,
    subtype: str,
    notification_uuid: str,
    transaction_info: Dict[str, Any],
    renewal_info: Optional[Dict[str, Any]] = None,
    app_environment: str = APP_ENV
):
    """发送服务器通知邮件"""
    if not all([MAIL_SERVER_HOST, MAIL_USERNAME, MAIL_PASSWORD, MAIL_SENDER_EMAIL, MAIL_ADMIN_EMAIL]):
        logger.warning("Email configuration is incomplete. Skipping server notification email.")
        return

    # 获取产品信息
    product_id = transaction_info.get('productId', 'Unknown')
    
    # 获取产品名称（根据产品ID映射）
    product_name_map = {
        'monthly': '月度会员',
        'annually': '年度会员',
        'lifetime': '终身会员',
    }
    product_name = product_name_map.get(product_id, product_id)
    
    # 获取价格信息（如果有）
    price = transaction_info.get('price', '')
    currency = transaction_info.get('currency', '')
    price_display = f"{price} {currency}" if price and currency else "价格未知"
    
    # 获取购买日期和过期日期
    purchase_date_ms = transaction_info.get('purchaseDate', 0)
    expires_date_ms = transaction_info.get('expiresDate', 0)
    
    purchase_date = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(purchase_date_ms/1000)) if purchase_date_ms else "未知"
    expires_date = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expires_date_ms/1000)) if expires_date_ms else "未知"
    
    # 获取用户ID（如果有）
    app_account_token = transaction_info.get('appAccountToken', '')
    
    # 根据通知类型设置友好的事件描述
    event_descriptions = {
        'CONSUMPTION_REQUEST': '消费请求',
        'DID_CHANGE_RENEWAL_PREF': '用户更改了续订选项',
        'DID_CHANGE_RENEWAL_STATUS': '续订状态变更',
        'DID_FAIL_TO_RENEW': '续订失败',
        'DID_RENEW': '成功续订',
        'EXPIRED': '订阅已过期',
        'GRACE_PERIOD_EXPIRED': '宽限期已过期',
        'OFFER_REDEEMED': '优惠已兑换',
        'PRICE_INCREASE': '价格上涨',
        'REFUND': '退款',
        'REFUND_DECLINED': '退款被拒绝',
        'RENEWAL_EXTENDED': '续订期延长',
        'REVOKE': '购买被撤销',
        'SUBSCRIBED': '新订阅'
    }
    
    # 子类型的友好描述
    subtype_descriptions = {
        'INITIAL_BUY': '首次购买',
        'RESUBSCRIBE': '重新订阅',
        'DOWNGRADE': '降级',
        'UPGRADE': '升级',
        'AUTO_RENEW_ENABLED': '已启用自动续订',
        'AUTO_RENEW_DISABLED': '已禁用自动续订',
        'VOLUNTARY': '用户主动操作',
        'BILLING_RETRY': '账单重试',
        'PRICE_INCREASE': '因价格上涨',
        'GRACE_PERIOD': '处于宽限期',
        'BILLING_RECOVERY': '账单恢复',
        'PENDING': '待处理',
        'ACCEPTED': '已接受',
        'REJECTED': '已拒绝'
    }
    
    event_name = event_descriptions.get(notification_type, notification_type)
    subtype_name = subtype_descriptions.get(subtype, subtype) if subtype else ''
    
    # 构建邮件主题
    subject = f"{product_name} - {event_name}{f' ({subtype_name})' if subtype_name else ''} - {app_environment.upper()}"
    
    # 提取交易信息中的关键数据
    original_transaction_id = transaction_info.get('originalTransactionId', 'Unknown')
    transaction_id = transaction_info.get('transactionId', 'Unknown')
    
    # 构建邮件正文
    body = f"""
    尊敬的管理员，
    
    您的应用收到了来自 Apple App Store 的通知：
    
    【事件概要】
    • 事件: {event_name}{f' ({subtype_name})' if subtype_name else ''}
    • 产品: {product_name}
    • 价格: {price_display}
    
    【时间信息】
    • 购买时间: {purchase_date}
    • 过期时间: {expires_date}
    • 通知时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))}
    
    【交易信息】
    • 交易ID: {transaction_id}
    • 原始交易ID: {original_transaction_id}
    • 用户标识: {app_account_token if app_account_token else '未提供'}
    • 环境: {app_environment.upper()}
    """
    
    # 如果有续订信息，添加到邮件中
    if renewal_info:
        auto_renew_status_map = {
            '1': '已开启',
            '0': '已关闭',
            1: '已开启',
            0: '已关闭'
        }
        expiration_intent_map = {
            '1': '用户取消',
            '2': '账单错误',
            '3': '价格变更被拒',
            '4': '产品不可用',
            '5': '未知原因',
            1: '用户取消',
            2: '账单错误',
            3: '价格变更被拒',
            4: '产品不可用',
            5: '未知原因'
        }
        
        auto_renew_status = renewal_info.get('autoRenewStatus')
        auto_renew_status_text = auto_renew_status_map.get(auto_renew_status, f'未知({auto_renew_status})')
        
        expiration_intent = renewal_info.get('expirationIntent')
        expiration_intent_text = expiration_intent_map.get(expiration_intent, f'未知({expiration_intent})')
        
        grace_period_expires_date = renewal_info.get('gracePeriodExpiresDate', 0)
        grace_period_expires_date_text = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(grace_period_expires_date/1000)) if grace_period_expires_date else "无"
        
        body += f"""
    【续订信息】
    • 自动续订: {auto_renew_status_text}
    • 过期原因: {expiration_intent_text}
    • 宽限期至: {grace_period_expires_date_text}
        """
    
    body += """
    
    【操作建议】
    请登录应用后台查看详情并采取相应措施。
    
    此致，
    App Store 通知系统
    """

    msg = MIMEText(body, 'plain', 'utf-8')
    # 使用formataddr正确格式化From字段
    sender_name = "App Store 通知"
    msg['From'] = formataddr((Header(sender_name, 'utf-8').encode(), MAIL_SENDER_EMAIL))
    msg['To'] = MAIL_ADMIN_EMAIL
    msg['Subject'] = subject

    try:
        server = smtplib.SMTP(MAIL_SERVER_HOST, MAIL_SERVER_PORT)
        server.starttls()
        server.login(MAIL_USERNAME, MAIL_PASSWORD)
        server.sendmail(MAIL_SENDER_EMAIL, MAIL_ADMIN_EMAIL, msg.as_string())
        server.quit()
        logger.info(f"Server notification email sent to {MAIL_ADMIN_EMAIL} for notification type {notification_type}.")
    except Exception as e:
        logger.error(f"Failed to send server notification email: {e}")

# --- 邮件发送函数 (不变) ---
async def send_purchase_notification_email(
    user_id: str,
    product_ids: List[str],
    transaction_id: str,
    app_environment: str,
    apple_environment: str,
    purchase_info: Optional[Dict[str, Any]] = None
):
    if not all([MAIL_SERVER_HOST, MAIL_USERNAME, MAIL_PASSWORD, MAIL_SENDER_EMAIL, MAIL_ADMIN_EMAIL]):
        logger.warning("Email configuration is incomplete. Skipping purchase notification email.")
        return

    # 获取产品信息
    product_id = product_ids[0] if product_ids else "未知产品"
    
    # 获取产品名称（根据产品ID映射）
    product_name_map = {
        'monthly': '月度会员',
        'annually': '年度会员',
        'lifetime': '终身会员',
    }
    product_name = product_name_map.get(product_id, product_id)
    
    # 获取价格信息（如果有）
    price = ""
    currency = ""
    purchase_date_ms = 0
    expires_date_ms = 0
    is_trial = False
    
    if purchase_info:
        price = purchase_info.get('price', '')
        currency = purchase_info.get('currency', '')
        purchase_date_ms = purchase_info.get('purchaseDate', 0)
        expires_date_ms = purchase_info.get('expiresDate', 0)
        is_trial = purchase_info.get('isTrialPeriod', False)
        
        # 记录原始的时间戳信息
        logger.info(f"邮件函数收到的时间戳: purchase_date_ms={purchase_date_ms}, expires_date_ms={expires_date_ms}")
    
    price_display = f"{price} {currency}" if price and currency else "价格未知"
    
    # 格式化日期并记录转换过程
    if purchase_date_ms:
        try:
            # 记录原始时间戳和转换后的时间
            unix_seconds = purchase_date_ms / 1000
            local_time = time.localtime(unix_seconds)
            purchase_date = time.strftime('%Y-%m-%d %H:%M:%S', local_time)
            logger.info(f"时间转换: 原始毫秒={purchase_date_ms}, Unix秒={unix_seconds}, 本地时间={purchase_date}")
        except Exception as e:
            logger.error(f"时间转换错误: {e}, 原始值={purchase_date_ms}")
            purchase_date = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
    else:
        purchase_date = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        logger.info(f"使用当前时间作为购买时间: {purchase_date}")
    
    expires_date = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expires_date_ms/1000)) if expires_date_ms else "未知"
    
    # 构建邮件主题
    subject = f"新购买通知: {product_name} - {app_environment.upper()}"
    
    # 购买类型描述
    purchase_type = "试用期购买" if is_trial else "正式购买"
    if product_id == 'lifetime':
        subscription_type = "永久会员"
    elif product_id == 'annually':
        subscription_type = "年度订阅"
    elif product_id == 'monthly':
        subscription_type = "月度订阅"
    else:
        subscription_type = "未知类型"

    body = f"""
    尊敬的管理员，

    您的应用有新的购买记录：

    【购买概要】
    • 产品: {product_name} ({subscription_type})
    • 类型: {purchase_type}
    • 价格: {price_display}
    • 用户ID: {user_id}

    【时间信息】
    • 购买时间: {purchase_date}
    • 过期时间: {expires_date if product_id != 'lifetime' else '永久有效'}

    【交易信息】
    • 交易ID: {transaction_id}
    • 应用环境: {app_environment.upper()}
    • 验证环境: {apple_environment}

    【操作建议】
    请登录应用后台查看详情并确认交易状态。

    此致，
    App Store 购买通知系统
    """

    msg = MIMEText(body, 'plain', 'utf-8')
    # 使用formataddr正确格式化From字段
    sender_name = "购买通知"
    msg['From'] = formataddr((Header(sender_name, 'utf-8').encode(), MAIL_SENDER_EMAIL))
    msg['To'] = MAIL_ADMIN_EMAIL
    msg['Subject'] = subject

    try:
        server = smtplib.SMTP(MAIL_SERVER_HOST, MAIL_SERVER_PORT)
        server.starttls()
        server.login(MAIL_USERNAME, MAIL_PASSWORD)
        server.sendmail(MAIL_SENDER_EMAIL, MAIL_ADMIN_EMAIL, msg.as_string())
        server.quit()
        logger.info(f"Purchase notification email sent to {MAIL_ADMIN_EMAIL} for user {user_id}.")
    except Exception as e:
        logger.error(f"Failed to send purchase notification email: {e}")


# --- Pydantic 模型定义 (保持不变) ---
class VerifyPurchaseRequest(BaseModel):
    user_id: str
    receipt_data: str

class PurchasedProductInfo(BaseModel):
    product_id: str
    is_active: bool
    expires_date_ms: Optional[int] = None
    purchase_type: str
    is_trial_period: Optional[bool] = False

class VerifyPurchaseSuccessResponse(BaseModel):
    status: str = "success"
    message: str
    purchased_products: List[str]
    user_current_entitlements: Dict[str, PurchasedProductInfo]

class VerifyPurchaseErrorResponse(BaseModel):
    status: str = "error"
    message: str
    apple_status_code: int
    details: str
    apple_response: Dict[str, Any]


# --- 辅助函数：读取私钥 ---
def read_private_key(key_path: str) -> bytes:
    """读取私钥文件内容"""
    if not key_path or not os.path.exists(key_path):
        logger.error(f"Private key file not found at: {key_path}")
        raise FileNotFoundError(f"Private key file not found at: {key_path}")
    
    try:
        with open(key_path, 'rb') as key_file:
            return key_file.read()
    except Exception as e:
        logger.error(f"Error reading private key file: {e}")
        raise

# --- 核心函数：向 Apple 验证收据 (使用 appstoreserverlibrary 修正) ---
async def validate_receipt_with_apple_library(receipt_data: str) -> Dict[str, Any]:
    if not all([APPLE_PRIVATE_KEY_PATH, APPLE_KEY_ID, APPLE_ISSUER_ID, BUNDLE_ID]):
        logger.error("App Store Server API configuration is incomplete.")
        return {"status": -1, "error": "Internal server error: App Store Server API configuration is missing.", "message": "Server configuration error."}

    # 检查receipt_data是否为有效的base64数据
    try:
        # 尝试解码base64数据
        if not receipt_data:
            logger.error("Receipt data is empty")
            return {"status": 21002, "error": "The receipt data is empty.", "message": "Receipt data cannot be empty."}
        
        # 检查是否为有效的base64数据
        try:
            base64.b64decode(receipt_data, validate=True)
        except Exception:
            logger.error("Invalid base64 data provided in receipt_data")
            return {"status": -1, "error": "Only base64 data is allowed", "message": "Unexpected server error during validation."}
    except Exception as e:
        logger.error(f"Error validating receipt data format: {e}")
        return {"status": -1, "error": f"Receipt data validation error: {str(e)}", "message": "Unexpected server error during validation."}

    try:
        logger.info("Sending receipt to Apple validation using App Store Server API...")
        
        # 根据 APP_ENV 决定初始验证环境
        validation_environment = Environment.PRODUCTION if APP_ENV == 'production' else Environment.SANDBOX
        
        # 使用 ReceiptUtility 提取交易ID
        receipt_util = ReceiptUtility()
        transaction_id = receipt_util.extract_transaction_id_from_app_receipt(receipt_data)
        
        if not transaction_id:
            logger.error("Could not extract transaction ID from receipt")
            return {"status": 21002, "error": "The receipt data is malformed or missing.", "message": "Could not extract transaction ID from receipt."}
        
        # 使用交易历史API获取详细信息
        logger.info(f"Extracted transaction ID: {transaction_id}, using transaction history API")
        
        # 读取私钥
        private_key = read_private_key(APPLE_PRIVATE_KEY_PATH)
        
        # 创建API客户端
        client = AppStoreServerAPIClient(
            private_key, 
            APPLE_KEY_ID, 
            APPLE_ISSUER_ID, 
            BUNDLE_ID, 
            validation_environment
        )
        
        # 获取交易历史
        transactions = []
        history_response: HistoryResponse = None
        
        # 首先尝试使用所有产品类型查询交易历史
        logger.info("尝试使用所有产品类型查询交易历史")
        request: TransactionHistoryRequest = TransactionHistoryRequest(
            sort=Order.ASCENDING,
            revoked=False,
            productTypes=[
                ProductType.AUTO_RENEWABLE, 
                ProductType.NON_CONSUMABLE, 
                ProductType.CONSUMABLE
            ]
        )
        
        try:
            while history_response is None or history_response.hasMore:
                revision = history_response.revision if history_response is not None else None
                history_response = client.get_transaction_history(
                    transaction_id, 
                    revision, 
                    request, 
                    GetTransactionHistoryVersion.V2
                )
                for transaction in history_response.signedTransactions:
                    transactions.append(transaction)
            
            logger.info(f"成功获取到 {len(transactions)} 条交易记录")
        except Exception as e:
            logger.error(f"获取交易历史出错: {e}")
            
            # 如果第一次请求失败，尝试仅使用AUTO_RENEWABLE类型
            if len(transactions) == 0:
                logger.info("第一次请求失败，尝试仅使用AUTO_RENEWABLE类型")
                history_response = None
                request: TransactionHistoryRequest = TransactionHistoryRequest(
                    sort=Order.ASCENDING,
                    revoked=False,
                    productTypes=[ProductType.AUTO_RENEWABLE]
                )
                
                try:
                    while history_response is None or history_response.hasMore:
                        revision = history_response.revision if history_response is not None else None
                        history_response = client.get_transaction_history(
                            transaction_id, 
                            revision, 
                            request, 
                            GetTransactionHistoryVersion.V2
                        )
                        for transaction in history_response.signedTransactions:
                            transactions.append(transaction)
                    
                    logger.info(f"使用AUTO_RENEWABLE类型获取到 {len(transactions)} 条交易记录")
                except Exception as e:
                    logger.error(f"使用AUTO_RENEWABLE类型获取交易历史也失败: {e}")
            
            # 如果仍然没有找到交易记录，尝试使用NON_CONSUMABLE类型（终身会员通常是这种类型）
            if len(transactions) == 0:
                logger.info("尝试使用NON_CONSUMABLE类型（终身会员）")
                history_response = None
                request: TransactionHistoryRequest = TransactionHistoryRequest(
                    sort=Order.ASCENDING,
                    revoked=False,
                    productTypes=[ProductType.NON_CONSUMABLE]
                )
                
                try:
                    while history_response is None or history_response.hasMore:
                        revision = history_response.revision if history_response is not None else None
                        history_response = client.get_transaction_history(
                            transaction_id, 
                            revision, 
                            request, 
                            GetTransactionHistoryVersion.V2
                        )
                        for transaction in history_response.signedTransactions:
                            transactions.append(transaction)
                    
                    logger.info(f"使用NON_CONSUMABLE类型获取到 {len(transactions)} 条交易记录")
                except Exception as e:
                    logger.error(f"使用NON_CONSUMABLE类型获取交易历史也失败: {e}")
            
            # 如果仍然没有找到交易记录，尝试使用CONSUMABLE类型
            if len(transactions) == 0:
                logger.info("尝试使用CONSUMABLE类型")
                history_response = None
                request: TransactionHistoryRequest = TransactionHistoryRequest(
                    sort=Order.ASCENDING,
                    revoked=False,
                    productTypes=[ProductType.CONSUMABLE]
                )
                
                try:
                    while history_response is None or history_response.hasMore:
                        revision = history_response.revision if history_response is not None else None
                        history_response = client.get_transaction_history(
                            transaction_id, 
                            revision, 
                            request, 
                            GetTransactionHistoryVersion.V2
                        )
                        for transaction in history_response.signedTransactions:
                            transactions.append(transaction)
                    
                    logger.info(f"使用CONSUMABLE类型获取到 {len(transactions)} 条交易记录")
                except Exception as e:
                    logger.error(f"使用CONSUMABLE类型获取交易历史也失败: {e}")
        
        # 如果所有尝试都失败，但我们知道这是一个终身会员购买，创建一个模拟交易记录
        if len(transactions) == 0 and transaction_id:
            logger.info(f"无法从交易历史API获取交易记录，创建模拟交易记录: transaction_id={transaction_id}")
            
            # 创建一个模拟的交易数据（基于JWT格式）
            current_time_ms = int(time.time() * 1000)
            mock_transaction = {
                "productId": "lifetime",
                "transactionId": transaction_id,
                "originalTransactionId": transaction_id,
                "purchaseDate": current_time_ms,
                "expiresDate": current_time_ms + (36500 * 24 * 60 * 60 * 1000),  # 100年
                "isTrialPeriod": False,
                "type": "Non-Consumable"
            }
            
            # 将模拟交易添加到交易列表
            transactions.append(mock_transaction)
            logger.info(f"添加了模拟交易记录: {mock_transaction}")
        
        # 构建响应格式
        return {
            "status": 0,
            "environment": validation_environment.value,
            "transactions": transactions,
            "transaction_id": transaction_id
        }
        
    except APIException as e:
        logger.error(f"Apple App Store API Error (status: {e.http_status_code}): {e.error_message}")
        return {"status": e.http_status_code, "error": e.error_message, "message": "Apple App Store API error."}
    except Exception as e:
        logger.error(f"Unexpected error during Apple receipt validation: {e}")
        return {"status": -1, "error": str(e), "message": "Unexpected server error during validation."}


# --- API 路由：验证内购收据 (修改) ---
@app.post(
    "/verify_purchase",
    response_model=VerifyPurchaseSuccessResponse,
    responses={
        status.HTTP_400_BAD_REQUEST: {"model": VerifyPurchaseErrorResponse}
    },
    summary="Verify an iOS App Store purchase receipt",
    description="Receives a base64 encoded receipt data from the iOS App and validates it with Apple's servers. Updates user entitlements based on the validation result."
)
async def verify_purchase(request_body: VerifyPurchaseRequest, background_tasks: BackgroundTasks):
    user_id = request_body.user_id
    receipt_data = request_body.receipt_data

    logger.info(f"Received purchase verification request for user: {user_id}")

    apple_response = await validate_receipt_with_apple_library(receipt_data)
    status_code = apple_response.get('status')

    if status_code == 0:
        transactions = apple_response.get('transactions', [])
        apple_env = apple_response.get('environment', 'unknown')

        logger.info(f"Receipt successfully validated for user: {user_id}. Apple environment: {apple_env}")

        try:
            conn, db = get_db_connection()
            collection = db[MONGO_COLLECTION]

            current_time_ms = int(time.time() * 1000)
            user_current_entitlements: Dict[str, PurchasedProductInfo] = {}
            purchased_products_list: List[str] = []
            
            first_transaction_id = None 

            # 获取用户现有的购买记录
            existing_purchases = list(collection.find({"user_id": user_id}))
            for purchase in existing_purchases:
                product_id = purchase['product_id']
                user_current_entitlements[product_id] = PurchasedProductInfo(
                    product_id=product_id,
                    is_active=bool(purchase['is_active']),
                    expires_date_ms=purchase['expires_date_ms'],
                    purchase_type=purchase['purchase_type'],
                    is_trial_period=purchase.get('is_trial_period', False)
                )
                if bool(purchase['is_active']):
                    purchased_products_list.append(product_id)

            # 处理从交易历史API获取的交易
            for transaction in transactions:
                # 从签名交易中解析数据
                # 检查transaction是否为字符串，如果是则尝试解析JWT
                if isinstance(transaction, str):
                    try:
                        # 使用已有的JWT解析函数
                        transaction_data = decode_jwt_payload(transaction)
                        if not transaction_data:
                            logger.error(f"Failed to decode JWT transaction: {transaction[:100]}...")
                            continue
                    except Exception as e:
                        logger.error(f"Error decoding transaction: {e}, data: {transaction[:100]}...")
                        continue
                else:
                    transaction_data = transaction
                
                # 从交易数据中解析信息
                product_id = transaction_data.get('productId')
                transaction_id = transaction_data.get('transactionId')
                original_transaction_id = transaction_data.get('originalTransactionId')
                purchase_date_ms = int(transaction_data.get('purchaseDate', 0))
                expires_date_ms = int(transaction_data.get('expiresDate', 0)) if transaction_data.get('expiresDate') else None
                is_trial_period = transaction_data.get('isTrialPeriod', False)
                cancellation_date_ms = int(transaction_data.get('cancellationDate', 0)) if transaction_data.get('cancellationDate') else None
                
                # 将时间戳转换为人类可读的格式
                purchase_date_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(purchase_date_ms/1000)) if purchase_date_ms else "未知"
                expires_date_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expires_date_ms/1000)) if expires_date_ms else "未知或永久"
                cancellation_date_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(cancellation_date_ms/1000)) if cancellation_date_ms else "未取消"
                
                # 打印交易信息
                logger.info(f"交易详情: 产品={product_id}, 交易ID={transaction_id}, 原始交易ID={original_transaction_id}")
                logger.info(f"交易时间: 购买={purchase_date_str}, 过期={expires_date_str}, 取消={cancellation_date_str}")
                logger.info(f"交易状态: 是否试用期={is_trial_period}")
                
                if not product_id or not transaction_id: 
                    logger.warning("跳过处理：缺少产品ID或交易ID")
                    continue
                    
                if not first_transaction_id: 
                    first_transaction_id = transaction_id
                    logger.info(f"设置第一个交易ID: {first_transaction_id}")

                # 处理过期时间
                # 对于不同产品类型设置合适的过期时间
                if product_id == 'annually':  # 年度会员
                    # 检查苹果是否提供了有效的过期时间
                    if expires_date_ms and expires_date_ms > purchase_date_ms:
                        logger.info(f"使用苹果提供的年度会员过期时间: {expires_date_ms}, product_id={product_id}")
                    # 如果过期时间与购买时间相同或未提供，自己计算
                    elif purchase_date_ms > 0:
                        expires_date_ms = purchase_date_ms + (365 * 24 * 60 * 60 * 1000)
                        logger.info(f"自行计算年度会员过期时间: purchase_date={purchase_date_ms}, expires_date={expires_date_ms}, product_id={product_id}")
                elif product_id == 'lifetime':  # 终身会员
                    # 检查苹果是否提供了有效的过期时间
                    if expires_date_ms and expires_date_ms > purchase_date_ms:
                        logger.info(f"使用苹果提供的终身会员过期时间: {expires_date_ms}, product_id={product_id}")
                    # 如果过期时间与购买时间相同或未提供，自己计算
                    elif purchase_date_ms > 0:
                        expires_date_ms = purchase_date_ms + (36500 * 24 * 60 * 60 * 1000)
                        logger.info(f"自行计算终身会员过期时间: purchase_date={purchase_date_ms}, expires_date={expires_date_ms}, product_id={product_id}")
                    else:
                        logger.warning(f"终身会员没有有效的购买时间，使用当前时间: product_id={product_id}")
                        purchase_date_ms = int(time.time() * 1000)
                        expires_date_ms = purchase_date_ms + (36500 * 24 * 60 * 60 * 1000)
                        logger.info(f"使用当前时间设置终身会员: purchase_date={purchase_date_ms}, expires_date={expires_date_ms}")
                elif product_id == 'monthly':  # 月度会员
                    # 检查苹果是否提供了有效的过期时间
                    if expires_date_ms and expires_date_ms > purchase_date_ms:
                        logger.info(f"使用苹果提供的月度会员过期时间: {expires_date_ms}, product_id={product_id}")
                    # 如果过期时间与购买时间相同或未提供，自己计算
                    elif purchase_date_ms > 0:
                        expires_date_ms = purchase_date_ms + (30 * 24 * 60 * 60 * 1000)
                        logger.info(f"自行计算月度会员过期时间: purchase_date={purchase_date_ms}, expires_date={expires_date_ms}, product_id={product_id}")
                
                # 如果所有情况下都没有设置过期时间，记录警告
                if not expires_date_ms and purchase_date_ms > 0:
                    logger.warning(f"未能为产品设置过期时间: product_id={product_id}, purchase_date={purchase_date_ms}")
                
                # 确定交易类型和状态
                is_subscription = expires_date_ms is not None
                purchase_type = 'subscription' if is_subscription else 'non-subscription'
                
                is_active = False
                if product_id == 'lifetime':
                    # 终身会员只要没有取消就是活跃的
                    is_active = not bool(cancellation_date_ms)
                    logger.info(f"终身会员活跃状态: is_active={is_active}, product_id={product_id}, cancellation_date_ms={cancellation_date_ms}")
                    # 强制设置为非订阅类型
                    purchase_type = 'non-subscription'
                    logger.info(f"终身会员被标记为非订阅类型: purchase_type={purchase_type}")
                elif is_subscription:
                    # 订阅产品的活跃状态判断
                    if cancellation_date_ms:
                        is_active = False
                        logger.info(f"订阅已取消: product_id={product_id}")
                    elif expires_date_ms > current_time_ms:
                        is_active = True
                        logger.info(f"订阅有效期内: product_id={product_id}, expires_at={expires_date_ms}, current={current_time_ms}")
                    elif is_trial_period and not cancellation_date_ms:
                        is_active = True
                        logger.info(f"处于试用期: product_id={product_id}")
                    else:
                        logger.info(f"订阅已过期: product_id={product_id}, expires_at={expires_date_ms}, current={current_time_ms}")
                else:
                    # 非订阅类型的产品默认为激活状态
                    is_active = True
                    logger.info(f"非订阅产品，默认活跃: product_id={product_id}")
                
                # 使用upsert操作，如果记录不存在则创建新记录
                logger.info(f"准备写入数据库: 用户={user_id}, 产品={product_id}, 交易ID={transaction_id}, 原始交易ID={original_transaction_id}")
                logger.info(f"数据详情: 购买时间={purchase_date_ms}, 过期时间={expires_date_ms}, 活跃状态={is_active}, 类型={purchase_type}")
                
                try:
                    result = collection.update_one(
                        {
                            "original_transaction_id": original_transaction_id, 
                            "product_id": product_id
                        },
                        {
                            "$set": {
                                "user_id": user_id,
                                "transaction_id": transaction_id,
                                "purchase_date_ms": purchase_date_ms,
                                "expires_date_ms": expires_date_ms,
                                "is_trial_period": is_trial_period,
                                "is_active": is_active,
                                "cancellation_date_ms": cancellation_date_ms,
                                "purchase_type": purchase_type,
                                "last_verified_at": current_time_ms
                            }
                        },
                        upsert=True
                    )
                    logger.info(f"数据库写入结果: matched={result.matched_count}, modified={result.modified_count}, upserted_id={result.upserted_id}")
                except Exception as e:
                    logger.error(f"数据库写入失败: {e}")
                
                user_current_entitlements[product_id] = PurchasedProductInfo(
                    product_id=product_id,
                    is_active=is_active,
                    expires_date_ms=expires_date_ms,
                    purchase_type=purchase_type,
                    is_trial_period=is_trial_period
                )
                
                if is_active and product_id not in purchased_products_list:
                    purchased_products_list.append(product_id)
                
            if purchased_products_list and first_transaction_id:
                # 获取第一个产品的详细信息，用于邮件通知
                purchase_info = None
                if len(purchased_products_list) > 0:
                    first_product_id = purchased_products_list[0]
                    # 从交易数据中查找第一个产品的详细信息
                    for transaction in transactions:
                        if isinstance(transaction, str):
                            try:
                                transaction_data = decode_jwt_payload(transaction)
                                if transaction_data.get('productId') == first_product_id:
                                    purchase_info = {
                                        'productId': first_product_id,
                                        'price': transaction_data.get('price'),
                                        'currency': transaction_data.get('currency'),
                                        'purchaseDate': transaction_data.get('purchaseDate'),
                                        'expiresDate': transaction_data.get('expiresDate'),
                                        'isTrialPeriod': transaction_data.get('isTrialPeriod', False)
                                    }
                                    break
                            except Exception:
                                continue
                        elif isinstance(transaction, dict) and transaction.get('productId') == first_product_id:
                            purchase_info = {
                                'productId': first_product_id,
                                'price': transaction.get('price'),
                                'currency': transaction.get('currency'),
                                'purchaseDate': transaction.get('purchaseDate'),
                                'expiresDate': transaction.get('expiresDate'),
                                'isTrialPeriod': transaction.get('isTrialPeriod', False)
                            }
                            break
                
                background_tasks.add_task(
                    send_purchase_notification_email,
                    user_id=user_id,
                    product_ids=purchased_products_list,
                    transaction_id=first_transaction_id,
                    app_environment=APP_ENV,
                    apple_environment=apple_env,
                    purchase_info=purchase_info
                )
                
                # 记录发送邮件的交易信息
                if purchase_info:
                    purchase_date_ms = purchase_info.get('purchaseDate', 0)
                    purchase_date_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(purchase_date_ms/1000)) if purchase_date_ms else "未知"
                    logger.info(f"准备发送邮件通知，使用的交易信息: 产品={purchase_info.get('productId')}, 购买日期={purchase_date_str}, 原始时间戳={purchase_date_ms}")

            return VerifyPurchaseSuccessResponse(
                message="Purchase validated and user entitlements updated.",
                purchased_products=purchased_products_list,
                user_current_entitlements=user_current_entitlements
            )

        except PyMongoError as e:
            logger.error(f"Database error during purchase processing for user {user_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "Internal server error during database operation.", "error": str(e)}
            )

    else:
        error_message = f"Receipt validation failed with Apple status: {status_code}. Details: {apple_response.get('error', 'N/A')}"
        logger.error(f"Failed to validate receipt for user {user_id}: {error_message}")
        
        error_details = {
            21000: "App Store server temporarily unavailable. Try again.",
            21002: "The receipt data is malformed or missing.",
            21003: "The receipt could not be authenticated.",
            21004: "The shared secret you provided does not match the one in App Store Connect.",
            21005: "The receipt server is currently unavailable.",
            21006: "The receipt is valid but the subscription has expired. For auto-renewable subscriptions, you must query your server for the latest status of the subscription.",
            21007: "This receipt is from the test environment, but it was sent to the production environment for verification (handled internally by library).",
            21008: "This receipt is from the production environment, but it was sent to the test environment for verification (handled internally by library).",
            -1: "Internal server error or network issue communicating with Apple."
        }.get(status_code, "Unknown Apple validation error.")

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=VerifyPurchaseErrorResponse(
                message="Receipt validation failed.",
                apple_status_code=status_code,
                details=error_details,
                apple_response=apple_response
            ).model_dump()
        )

# --- 健康检查端点 (修改) ---
@app.get("/health", summary="Health Check")
async def health_check():
    try:
        # 直接使用MongoDBConnectionPool实例来执行ping命令
        client, db = MongoDBConnectionPool.get_instance().get_connection()
        # 执行ping命令来验证连接
        ping_result = db.command('ping')
        if ping_result.get('ok') == 1.0:
            return {"status": "ok", "database": "connected", "app_env": APP_ENV}
        else:
            logger.error(f"MongoDB ping command returned unexpected result: {ping_result}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={"status": "not_ok", "database": "failed", "error": "Database connection test failed", "app_env": APP_ENV}
            )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"status": "not_ok", "database": "failed", "error": str(e), "app_env": APP_ENV}
        )

# --- Pydantic 模型定义 - App Store Server Notifications V2 ---
class AppStoreServerNotificationV2Data(BaseModel):
    """App Store Server Notifications V2 数据模型"""
    signedTransactionInfo: Optional[str] = None
    signedRenewalInfo: Optional[str] = None

class AppStoreServerNotificationV2(BaseModel):
    """App Store Server Notifications V2 主模型"""
    notificationType: str  # CONSUMPTION_REQUEST, DID_CHANGE_RENEWAL_PREF, DID_CHANGE_RENEWAL_STATUS, DID_FAIL_TO_RENEW, DID_RENEW, EXPIRED, GRACE_PERIOD_EXPIRED, OFFER_REDEEMED, PRICE_INCREASE, REFUND, REFUND_DECLINED, RENEWAL_EXTENDED, REVOKE, SUBSCRIBED
    subtype: Optional[str] = None  # INITIAL_BUY, RESUBSCRIBE, DOWNGRADE, UPGRADE, AUTO_RENEW_ENABLED, AUTO_RENEW_DISABLED, VOLUNTARY, BILLING_RETRY, PRICE_INCREASE, GRACE_PERIOD, BILLING_RECOVERY, PENDING, ACCEPTED, REJECTED
    notificationUUID: str
    data: AppStoreServerNotificationV2Data
    version: str
    signedDate: int
    summary: Optional[Dict[str, Any]] = None

class AppStoreServerNotificationPayload(BaseModel):
    """App Store Server Notifications 通知载荷"""
    signedPayload: str

class TestNotificationResponse(BaseModel):
    """测试通知响应模型"""
    testNotificationToken: str
    status: str = "success"
    message: str = "Test notification request sent successfully"

# --- 解析 JWT Token ---
def decode_jwt_payload(jwt_token: str) -> Dict[str, Any]:
    """
    解码 JWT Token 的 payload 部分
    
    Args:
        jwt_token: JWT Token 字符串
        
    Returns:
        解码后的 payload 字典
    """
    try:
        # JWT Token 由三部分组成，以.分隔: header.payload.signature
        parts = jwt_token.split('.')
        if len(parts) != 3:
            logger.error("Invalid JWT token format")
            return {}
        
        # Base64 URL 解码 payload 部分
        payload_base64 = parts[1]
        # 添加填充
        padding = '=' * (4 - len(payload_base64) % 4)
        payload_base64 = payload_base64 + padding
        
        # 替换 URL 安全的字符
        payload_base64 = payload_base64.replace('-', '+').replace('_', '/')
        
        # 解码
        payload_json = base64.b64decode(payload_base64).decode('utf-8')
        return json.loads(payload_json)
    except Exception as e:
        logger.error(f"Error decoding JWT payload: {e}")
        return {}

# --- API 路由：处理 App Store 服务器通知 (修改) ---
@app.post(
    "/app-store-server-notifications/v2",
    summary="Handle App Store Server Notifications V2",
    description="接收和处理来自 Apple App Store 的服务器通知，用于订阅状态变更、退款等事件"
)
async def handle_app_store_server_notifications(
    notification: AppStoreServerNotificationPayload, 
    background_tasks: BackgroundTasks,
    request: Request
):
    logger.info(f"Received App Store Server Notification: {notification.signedPayload[:50]}...")
    
    try:
        # 1. 解码签名载荷
        payload_data = decode_jwt_payload(notification.signedPayload)
        if not payload_data:
            logger.error("Failed to decode notification payload")
            return Response(status_code=status.HTTP_400_BAD_REQUEST)
        
        # 2. 解析通知数据
        notification_type = payload_data.get('notificationType')
        subtype = payload_data.get('subtype')
        notification_uuid = payload_data.get('notificationUUID')
        
        if not notification_type or not notification_uuid:
            logger.error("Missing required notification fields")
            return Response(status_code=status.HTTP_400_BAD_REQUEST)
        
        # 3. 记录通知详情
        logger.info(f"Processing notification type: {notification_type}, subtype: {subtype}, UUID: {notification_uuid}")
        
        # 4. 获取交易信息和续订信息
        data = payload_data.get('data', {})
        transaction_info_jwt = data.get('signedTransactionInfo')
        renewal_info_jwt = data.get('signedRenewalInfo')
        
        transaction_info = {}
        renewal_info = {}
        
        if transaction_info_jwt:
            transaction_info = decode_jwt_payload(transaction_info_jwt)
            logger.info(f"Transaction info: originalTransactionId={transaction_info.get('originalTransactionId')}, productId={transaction_info.get('productId')}")
            
        if renewal_info_jwt:
            renewal_info = decode_jwt_payload(renewal_info_jwt)
            logger.info(f"Renewal info: autoRenewStatus={renewal_info.get('autoRenewStatus')}")
        
        # 5. 根据通知类型处理
        if notification_type in ['EXPIRED', 'REFUND', 'REVOKE', 'GRACE_PERIOD_EXPIRED']:
            # 处理订阅过期、退款或撤销
            original_transaction_id = transaction_info.get('originalTransactionId')
            product_id = transaction_info.get('productId')
            
            if original_transaction_id:
                try:
                    conn, db = get_db_connection()
                    cursor = db.user_purchases
                    
                    # 先查询现有记录
                    existing_record = cursor.find_one({"original_transaction_id": original_transaction_id})
                    if existing_record:
                        # 处理终身会员的特殊情况：如果是REFUND或REVOKE则设为非活跃，否则保持活跃
                        if existing_record.get('product_id') == 'lifetime' and notification_type not in ['REFUND', 'REVOKE']:
                            logger.info(f"终身会员收到{notification_type}通知，但保持活跃状态: {original_transaction_id}")
                            # 终身会员不处理过期通知
                            pass
                        else:
                            # 更新数据库中的订阅状态
                            cursor.update_one(
                                {"original_transaction_id": original_transaction_id},
                                {"$set": {
                                    "is_active": False, 
                                    "last_verified_at": int(time.time() * 1000),
                                    "cancellation_date_ms": int(time.time() * 1000) if notification_type in ['REFUND', 'REVOKE'] else None
                                }}
                            )
                            
                            rows_affected = cursor.modified_count
                            logger.info(f"通知{notification_type}：更新{rows_affected}条记录，originalTransactionId={original_transaction_id}，设置为非活跃")
                    else:
                        logger.warning(f"找不到对应的订阅记录: originalTransactionId={original_transaction_id}")
                except PyMongoError as e:
                    logger.error(f"处理通知时数据库错误: {e}")
        elif notification_type == 'DID_RENEW':
            # 处理订阅续订
            original_transaction_id = transaction_info.get('originalTransactionId')
            product_id = transaction_info.get('productId')
            purchase_date_ms = int(transaction_info.get('purchaseDate', 0))
            # 获取苹果提供的过期时间
            apple_expires_date_ms = int(transaction_info.get('expiresDate', 0)) if transaction_info.get('expiresDate') else None
            
            if original_transaction_id and product_id and purchase_date_ms:
                try:
                    conn, db = get_db_connection()
                    cursor = db.user_purchases
                    
                    # 确定过期时间，优先使用苹果提供的值
                    expires_date_ms = None
                    if apple_expires_date_ms and apple_expires_date_ms > purchase_date_ms:
                        expires_date_ms = apple_expires_date_ms
                        logger.info(f"续订通知：使用苹果提供的过期时间: {expires_date_ms}, product_id={product_id}")
                    else:
                        # 自行计算过期时间 - 苹果返回的过期时间与购买时间相同或未提供
                        if product_id == 'annually':  # 年度会员
                            expires_date_ms = purchase_date_ms + (365 * 24 * 60 * 60 * 1000)
                        elif product_id == 'monthly':  # 月度会员
                            expires_date_ms = purchase_date_ms + (30 * 24 * 60 * 60 * 1000)
                        elif product_id == 'lifetime':  # 终身会员
                            expires_date_ms = purchase_date_ms + (36500 * 24 * 60 * 60 * 1000)
                        
                        if expires_date_ms:
                            logger.info(f"续订通知：自行计算过期时间: {expires_date_ms}, product_id={product_id}")
                    
                    if expires_date_ms:
                        # 更新数据库中的订阅状态
                        cursor.update_one(
                            {"original_transaction_id": original_transaction_id},
                            {"$set": {
                                "is_active": True, 
                                "expires_date_ms": expires_date_ms,
                                "purchase_date_ms": purchase_date_ms,
                                "last_verified_at": int(time.time() * 1000),
                                "cancellation_date_ms": None
                            }}
                        )
                        
                        rows_affected = cursor.modified_count
                        logger.info(f"续订通知：更新{rows_affected}条记录，originalTransactionId={original_transaction_id}，新过期时间={expires_date_ms}")
                except PyMongoError as e:
                    logger.error(f"处理续订通知时数据库错误: {e}")
        elif notification_type == 'ONE_TIME_CHARGE' or notification_type == 'SUBSCRIBED':
            # 处理一次性购买（终身会员）或新订阅
            original_transaction_id = transaction_info.get('originalTransactionId')
            transaction_id = transaction_info.get('transactionId')
            product_id = transaction_info.get('productId')
            purchase_date_ms = int(transaction_info.get('purchaseDate', 0))
            
            logger.info(f"收到{notification_type}通知: 产品={product_id}, 交易ID={transaction_id}, 原始交易ID={original_transaction_id}")
            
            if original_transaction_id and product_id and purchase_date_ms:
                try:
                    conn, db = get_db_connection()
                    cursor = db[MONGO_COLLECTION]
                    
                    # 设置过期时间（终身会员设置为100年）
                    expires_date_ms = purchase_date_ms + (36500 * 24 * 60 * 60 * 1000)
                    logger.info(f"终身会员设置过期时间: {expires_date_ms}, product_id={product_id}")
                    
                    # 获取用户ID
                    user_id = None
                    app_account_token = transaction_info.get('appAccountToken')
                    if app_account_token:
                        # 查询是否有相同appAccountToken的记录
                        existing_user = cursor.find_one({"app_account_token": app_account_token})
                        if existing_user:
                            user_id = existing_user.get('user_id')
                            logger.info(f"通过appAccountToken找到用户ID: {user_id}")
                    
                    if not user_id:
                        # 通过原始交易ID查找用户
                        existing_record = cursor.find_one({"original_transaction_id": original_transaction_id})
                        if existing_record:
                            user_id = existing_record.get('user_id')
                            logger.info(f"通过originalTransactionId找到用户ID: {user_id}")
                    
                    if user_id:
                        # 使用upsert操作，如果记录不存在则创建新记录
                        logger.info(f"准备写入数据库: 用户={user_id}, 产品={product_id}, 交易ID={transaction_id}, 原始交易ID={original_transaction_id}")
                        logger.info(f"数据详情: 购买时间={purchase_date_ms}, 过期时间={expires_date_ms}, 活跃状态=True, 类型=non-subscription")
                        
                        try:
                            result = cursor.update_one(
                                {
                                    "original_transaction_id": original_transaction_id, 
                                    "product_id": product_id
                                },
                                {
                                    "$set": {
                                        "user_id": user_id,
                                        "transaction_id": transaction_id,
                                        "purchase_date_ms": purchase_date_ms,
                                        "expires_date_ms": expires_date_ms,
                                        "is_trial_period": False,
                                        "is_active": True,
                                        "cancellation_date_ms": None,
                                        "purchase_type": "non-subscription",
                                        "last_verified_at": int(time.time() * 1000),
                                        "app_account_token": app_account_token
                                    }
                                },
                                upsert=True
                            )
                            logger.info(f"数据库写入结果: matched={result.matched_count}, modified={result.modified_count}, upserted_id={result.upserted_id}")
                        except Exception as e:
                            logger.error(f"数据库写入失败: {e}")
                    else:
                        logger.warning(f"无法确定用户ID，跳过数据库写入: originalTransactionId={original_transaction_id}")
                except PyMongoError as e:
                    logger.error(f"处理一次性购买通知时数据库错误: {e}")
        
        # 6. 发送邮件通知
        # 如果是重要的通知类型，才发送邮件
        important_notification_types = [
            'DID_RENEW', 'EXPIRED', 'GRACE_PERIOD_EXPIRED', 
            'REFUND', 'REVOKE', 'SUBSCRIBED', 'PRICE_INCREASE',
            'ONE_TIME_CHARGE'
        ]
        
        if notification_type in important_notification_types:
            background_tasks.add_task(
                send_server_notification_email,
                notification_type=notification_type,
                subtype=subtype,
                notification_uuid=notification_uuid,
                transaction_info=transaction_info,
                renewal_info=renewal_info
            )
        else:
            logger.info(f"跳过非重要通知类型的邮件发送: {notification_type}")
            
        # 7. 如果是终身会员购买通知，确保数据库中有记录
        if product_id == 'lifetime' and notification_type in ['ONE_TIME_CHARGE', 'SUBSCRIBED']:
            logger.info(f"检测到终身会员购买通知: {notification_type}, product_id={product_id}")
            try:
                # 获取用户ID
                user_id = None
                app_account_token = transaction_info.get('appAccountToken')
                
                # 尝试从请求体中获取用户ID
                try:
                    request_body = await request.json()
                    if isinstance(request_body, dict) and 'user_id' in request_body:
                        user_id = request_body.get('user_id')
                        logger.info(f"从请求体中获取用户ID: {user_id}")
                except Exception as e:
                    logger.error(f"从请求体获取用户ID失败: {e}")
                
                # 如果没有从请求体获取到用户ID，尝试从appAccountToken获取
                if not user_id and app_account_token:
                    logger.info(f"尝试使用appAccountToken查找用户ID: {app_account_token}")
                    conn, db = get_db_connection()
                    cursor = db[MONGO_COLLECTION]
                    existing_user = cursor.find_one({"app_account_token": app_account_token})
                    if existing_user:
                        user_id = existing_user.get('user_id')
                        logger.info(f"通过appAccountToken找到用户ID: {user_id}")
                
                # 如果仍然没有用户ID，使用一个默认值
                if not user_id:
                    user_id = "unknown_user"
                    logger.warning(f"无法确定用户ID，使用默认值: {user_id}")
                
                # 写入数据库
                conn, db = get_db_connection()
                cursor = db[MONGO_COLLECTION]
                
                # 获取必要的信息
                original_transaction_id = transaction_info.get('originalTransactionId')
                transaction_id = transaction_info.get('transactionId')
                purchase_date_ms = int(transaction_info.get('purchaseDate', 0))
                
                # 如果没有购买时间，使用当前时间
                if not purchase_date_ms:
                    purchase_date_ms = int(time.time() * 1000)
                
                # 设置过期时间（终身会员设置为100年）
                expires_date_ms = purchase_date_ms + (36500 * 24 * 60 * 60 * 1000)
                
                logger.info(f"准备写入终身会员数据: user_id={user_id}, product_id={product_id}, transaction_id={transaction_id}")
                
                result = cursor.update_one(
                    {
                        "original_transaction_id": original_transaction_id, 
                        "product_id": product_id
                    },
                    {
                        "$set": {
                            "user_id": user_id,
                            "transaction_id": transaction_id,
                            "purchase_date_ms": purchase_date_ms,
                            "expires_date_ms": expires_date_ms,
                            "is_trial_period": False,
                            "is_active": True,
                            "cancellation_date_ms": None,
                            "purchase_type": "non-subscription",
                            "last_verified_at": int(time.time() * 1000),
                            "app_account_token": app_account_token
                        }
                    },
                    upsert=True
                )
                
                logger.info(f"终身会员数据写入结果: matched={result.matched_count}, modified={result.modified_count}, upserted_id={result.upserted_id}")
                
            except Exception as e:
                logger.error(f"处理终身会员购买通知时出错: {e}")
        
        # 8. 返回成功响应
        return Response(status_code=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error processing App Store Server Notification: {e}")
        # 即使处理失败，也返回200以避免Apple重试
        return Response(status_code=status.HTTP_200_OK)

# --- API 路由：请求测试通知 ---
@app.post(
    "/request-test-notification",
    response_model=TestNotificationResponse,
    summary="Request App Store Test Notification",
    description="向 Apple App Store 请求测试通知，用于验证服务器通知配置是否正确"
)
async def request_test_notification():
    if not all([APPLE_PRIVATE_KEY_PATH, APPLE_KEY_ID, APPLE_ISSUER_ID, BUNDLE_ID]):
        logger.error("App Store Server API configuration is incomplete.")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": "App Store Server API configuration is missing."}
        )
    
    try:
        # 读取私钥
        private_key = read_private_key(APPLE_PRIVATE_KEY_PATH)
        
        # 确定环境
        environment = Environment.PRODUCTION if APP_ENV == 'production' else Environment.SANDBOX
        
        # 创建API客户端
        client = AppStoreServerAPIClient(
            private_key, 
            APPLE_KEY_ID, 
            APPLE_ISSUER_ID, 
            BUNDLE_ID, 
            environment
        )
        
        # 请求测试通知
        response = client.request_test_notification()
        
        if response and hasattr(response, 'testNotificationToken'):
            logger.info(f"Test notification requested successfully. Token: {response.testNotificationToken}")
            return TestNotificationResponse(
                testNotificationToken=response.testNotificationToken,
                message="Test notification requested successfully. Check your server notifications endpoint."
            )
        else:
            logger.error("Failed to request test notification: Invalid response format")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "Failed to request test notification: Invalid response format"}
            )
            
    except APIException as e:
        logger.error(f"Apple App Store API Error (status: {e.status_code}): {e.message}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Apple App Store API Error: {e.message}", "status_code": e.status_code}
        )
    except Exception as e:
        logger.error(f"Unexpected error requesting test notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Unexpected error: {str(e)}"}
        )

# main 模块仅在直接运行此文件时执行
if __name__ == '__main__':
    # 检查必要的API密钥配置
    if not all([APPLE_PRIVATE_KEY_PATH, APPLE_KEY_ID, APPLE_ISSUER_ID, BUNDLE_ID]):
        logger.critical("App Store Server API configuration is incomplete. Receipt validation will fail.")
        exit(1)
        
    if not all([MAIL_SERVER_HOST, MAIL_USERNAME, MAIL_PASSWORD, MAIL_SENDER_EMAIL, MAIL_ADMIN_EMAIL]):
        logger.warning("Email configuration is incomplete for local run. Email notifications may not work.")
    
    logger.info(f"Starting FastAPI server in {APP_ENV} mode directly (development mode).")
    import uvicorn
    uvicorn.run(app, host='0.0.0.0', port=5000, reload=True if APP_ENV in ['development', 'sandbox'] else False)
