.card {
    font-family: arial;
    font-size: 20px;
    text-align: left;
    color: black;
    background-color: white;
}

.header{
    font-size: x-large;
    font-weight: bold;
    padding: 8px;
}

.image-container {
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
}

/* 小屏幕：全宽显示 */
@media (max-width: 600px) {
    img {
      width: 100%;
      height: auto;
      max-width: none;
      max-height: none;
      position: relative;
      /* object-fit: contain; */
    }
  }
  
  /* 大屏幕：限制最大宽度 */
  @media (min-width: 1200px) {
    img {
      height: auto;
      max-width: none;
      max-height: none;
      position: relative;
      /* object-fit: contain; */
    }
  }

.mask-overlay {
    position: absolute;
    top: 0;
    left: 0;
    background-color: red;
    opacity: 1;
    pointer-events: none;
}

.overlay {
  position: absolute;
  background: #ff5656;
  opacity: 1;
}

.overlay-second {
  position: absolute;
  background: #ffeba2;
  opacity: 1;
}

* {
    margin: 0;
}