import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'dart:io';

class PDFSplitPageController extends GetxController {
  // 基础数据
  final splitModeList = [
    {"value": "uniform", "label": "toolbox.split.uniformSplit".tr},
    {"value": "custom", "label": "toolbox.split.customSplit".tr},
    {"value": "bookmark", "label": "toolbox.split.bookmarkSplit".tr},
  ];
  final bookmarkLevelList = [
    {"value": "1", "label": "toolbox.split.level1".tr},
    {"value": "2", "label": "toolbox.split.level2".tr},
    {"value": "3", "label": "toolbox.split.level3".tr},
  ];
  // 表单参数
  final splitMode = "uniform".obs;
  final chunkSize = 10.obs;
  final bookmarkLevel = 1.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'uniform');

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        "common.error".tr,
        "toolbox.split.pleaseSelectFile".tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      for (String filePath in selectedFilePaths) {
        if (Platform.isAndroid || Platform.isIOS) {
          if (splitMode.value == "uniform") {
            // 均匀拆分逻辑
            final chunk = chunkSize.value;
            final totalPages =
                PdfDocument(inputBytes: await File(filePath).readAsBytes())
                    .pages
                    .count;
            final numFiles = (totalPages / chunk).ceil();

            for (int i = 0; i < numFiles; i++) {
              final start = i * chunk + 1; // PDF页面从1开始
              final end =
                  (i + 1) * chunk > totalPages ? totalPages : (i + 1) * chunk;
              final pageRange = '$start-$end';

              final outputPath = await PathUtils(filePath).convertPath(
                outputMode.value,
                stem_append:
                    "_${"toolbox.split.uniformSplitSuffix".tr}_${(i + 1).toString().padLeft(3, '0')}",
                outputDir: outputDir.value,
              );

              await PDFUtils.extractPages(
                filePath,
                pageRange,
                outputPath,
                showProgress: false,
              );
            }
          } else if (splitMode.value == "custom") {
            // 自定义拆分逻辑
            final ranges = pageRange.value.split(',');
            for (final range in ranges) {
              final outputPath = await PathUtils(filePath).convertPath(
                outputMode.value,
                stem_append: "_${"toolbox.split.customSplitSuffix".tr}_$range",
                outputDir: outputDir.value,
              );
              await PDFUtils.extractPages(
                filePath,
                range,
                outputPath,
                showProgress: false,
              );
            }
          } else if (splitMode.value == "bookmark") {
            // 书签拆分逻辑
            progressController.updateProgress(
              status: "error", message: "toolbox.split.desktopOnly".tr);
            return;
          }
          progressController.updateProgress(
            status: "completed", message: "toolbox.common.process.success".tr,
          );
        } else {
          for (String filePath in selectedFilePaths) {
            progressController.updateProgress(
              status: "running",
              message: "toolbox.common.process.running".tr,
            );
            final outputPath = await PathUtils(filePath).convertPath(
              outputMode.value,
              stem_append: "_${"toolbox.split.splitSuffix".tr}",
              suffix: "",
              outputDir: outputDir.value,
            );
            await Directory(outputPath).create(recursive: true);
            final data = {
              'split_mode': splitMode.value,
              'chunk_size': chunkSize.value,
              'level': bookmarkLevel.value,
              'input_path': filePath,
              'page_range': pageRange.value,
              'output_path': outputPath,
              'show_progress': true,
            };
            final resp = await messageController.request(data, 'pdf/split');
            logger.w("resp: $resp");
            if (resp.status == "success") {
              progressController.outputPath.value = outputPath;
              progressController.updateProgress(
                status: "completed", message: "common.completed".tr);
            } else {
              progressController.updateProgress(
                  status: "error", message: resp.message);
              return;
            }
          }
          progressController.updateProgress(status: "completed", message: "");
        }
      }
      progressController.updateProgress(status: "completed", message: "");
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: 'toolbox.common.error_with_msg'.trParams({'error': e.toString()}),
      );
    }
  }
}
