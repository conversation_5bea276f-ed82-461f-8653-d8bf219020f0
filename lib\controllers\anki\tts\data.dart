import 'package:get/get.dart';

List<Map<String, String>> get lang_options => [
  {
    'value': 'zh-CN',
    'label': 'anki.tts.lang.chinese'.tr,
  },
  {
    'value': 'en-US',
    'label': 'anki.tts.lang.english_us'.tr,
  },
  {
    'value': 'en-GB',
    'label': 'anki.tts.lang.english_uk'.tr,
  },
  {
    'value': 'ja-JP',
    'label': 'anki.tts.lang.japanese'.tr,
  },
  {
    'value': 'fr-FR',
    'label': 'anki.tts.lang.french'.tr,
  },
  {
    'value': 'es-ES',
    'label': 'anki.tts.lang.spanish'.tr,
  },
  {
    'value': 'ru-RU',
    'label': 'anki.tts.lang.russian'.tr,
  },
  {
    'value': 'it-IT',
    'label': 'anki.tts.lang.italian'.tr,
  },
  {
    'value': 'de-DE',
    'label': 'anki.tts.lang.german'.tr,
  },
  {
    'value': 'ko-KR',
    'label': 'anki.tts.lang.korean'.tr,
  },
  {
    'value': 'pt-PT',
    'label': 'anki.tts.lang.portuguese'.tr,
  },
];

Map<String, List<Map<String, String>>> get voice_options => {
  "zh-CN": [
    {
      'value': "zh-CN-XiaoxiaoNeural",
      'label': "晓晓(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "zh-CN-XiaoyiNeural",
      'label': "晓艺(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "zh-CN-YunjianNeural",
      'label': "云健(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "zh-CN-YunxiaNeural",
      'label': "云夏(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "zh-CN-YunxiNeural",
      'label': "云希(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "zh-CN-YunyangNeural",
      'label': "云扬(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "zh-CN-liaoning-XiaobeiNeural",
      'label': "晓北(${'anki.tts.voice.northeast_dialect'.tr})",
    },
    {
      'value': "zh-CN-shaanxi-XiaoniNeural",
      'label': "晓妮(${'anki.tts.voice.shaanxi_dialect'.tr})",
    },
    {
      'value': "zh-HK-HiuGaaiNeural",
      'label': "HiuGaai(${'anki.tts.voice.cantonese_hongkong'.tr})",
    },
    {
      'value': "zh-HK-WanLungNeural",
      'label': "WanLung(${'anki.tts.voice.cantonese_hongkong'.tr})",
    },
    {
      'value': "zh-HK-HiuMaanNeural",
      'label': "HiuMaan(${'anki.tts.voice.cantonese_hongkong'.tr})",
    },
  ],
  'en-GB': [
    {
      'value': "en-GB-LibbyNeural",
      'label': "Libby(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "en-GB-MaisieNeural",
      'label': "Maisie(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "en-GB-RyanNeural",
      'label': "Ryan(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "en-GB-SoniaNeural",
      'label': "Sonia(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "en-GB-ThomasNeural",
      'label': "Thomas(${'anki.tts.voice.male'.tr})",
    },
  ],
  'en-US': [
    {
      'value': "en-US-AvaMultilingualNeural",
      'label': "Ava(${'anki.tts.voice.multilingual'.tr})",
    },
    {
      'value': "en-US-AndrewMultilingualNeural",
      'label': "Andrew(${'anki.tts.voice.multilingual'.tr})",
    },
    {
      'value': "en-US-EmmaMultilingualNeural",
      'label': "Emma(${'anki.tts.voice.multilingual'.tr})",
    },
    {
      'value': "en-US-BrianMultilingualNeural",
      'label': "Brian(${'anki.tts.voice.multilingual'.tr})",
    },
    {
      'value': "en-US-AvaNeural",
      'label': "Ava(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "en-US-AndrewNeural",
      'label': "Andrew(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "en-US-EmmaNeural",
      'label': "Emma(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "en-US-BrianNeural",
      'label': "Brian(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "en-US-AnaNeural",
      'label': "Ana(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "en-US-AriaNeural",
      'label': "Aria(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "en-US-ChristopherNeural",
      'label': "Christopher(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "en-US-EricNeural",
      'label': "Eric(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "en-US-GuyNeural",
      'label': "Guy(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "en-US-JennyNeural",
      'label': "Jenny(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "en-US-MichelleNeural",
      'label': "Michelle(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "en-US-RogerNeural",
      'label': "Roger(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "en-US-SteffanNeural",
      'label': "Steffan(${'anki.tts.voice.male'.tr})",
    },
  ],
  'ja-JP': [
    {
      'value': "ja-JP-KeitaNeural",
      'label': "Keita(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "ja-JP-NanamiNeural",
      'label': "Nanami(${'anki.tts.voice.female'.tr})",
    },
  ],
  'fr-FR': [
    {
      'value': "fr-FR-VivienneMultilingualNeural",
      'label': "Vivienne(${'anki.tts.voice.multilingual'.tr})",
    },
    {
      'value': "fr-FR-RemyMultilingualNeural",
      'label': "Remy(${'anki.tts.voice.multilingual'.tr})",
    },
    {
      'value': "fr-FR-DeniseNeural",
      'label': "Denise(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "fr-FR-EloiseNeural",
      'label': "Eloise(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "fr-FR-HenriNeural",
      'label': "Henri(${'anki.tts.voice.male'.tr})",
    },
  ],
  'es-ES': [
    {
      'value': "es-ES-XimenaNeural",
      'label': "Ximena(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "es-ES-AlvaroNeural",
      'label': "Alvaro(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "es-ES-ElviraNeural",
      'label': "Elvira(${'anki.tts.voice.female'.tr})",
    },
  ],
  'ru-RU': [
    {
      'value': "ru-RU-DmitryNeural",
      'label': "Dmitry(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "ru-RU-SvetlanaNeural",
      'label': "Svetlana(${'anki.tts.voice.female'.tr})",
    },
  ],
  'it-IT': [
    {
      'value': "it-IT-GiuseppeNeural",
      'label': "Giuseppe(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "it-IT-DiegoNeural",
      'label': "Diego(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "it-IT-ElsaNeural",
      'label': "Elsa(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "it-IT-IsabellaNeural",
      'label': "Isabella(${'anki.tts.voice.female'.tr})",
    },
  ],
  'de-DE': [
    {
      'value': "de-DE-SeraphinaMultilingualNeural",
      'label': "Seraphina(${'anki.tts.voice.multilingual'.tr})",
    },
    {
      'value': "de-DE-FlorianMultilingualNeural",
      'label': "Florian(${'anki.tts.voice.multilingual'.tr})",
    },
    {
      'value': "de-DE-AmalaNeural",
      'label': "Amala(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "de-DE-ConradNeural",
      'label': "Conrad(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "de-DE-KatjaNeural",
      'label': "Katja(${'anki.tts.voice.female'.tr})",
    },
    {
      'value': "de-DE-KillianNeural",
      'label': "Killian(${'anki.tts.voice.male'.tr})",
    },
  ],
  'ko-KR': [
    {
      'value': "ko-KR-HyunsuNeural",
      'label': "Hyunsu(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "ko-KR-InJoonNeural",
      'label': "InJoon(${'anki.tts.voice.male'.tr})",
    },
    {
      'value': "ko-KR-SunHiNeural",
      'label': "SunHi(${'anki.tts.voice.female'.tr})",
    },
  ],
  'pt-PT': [
    {
      'value': "pt-BR-ThalitaNeural",
      'label': "ThalitaNeural(${'anki.tts.voice.female'.tr}, Brazil)",
    },
    {
      'value': "pt-BR-AntonioNeural",
      'label': "AntonioNeural(${'anki.tts.voice.male'.tr}, Brazil)",
    },
    {
      'value': "pt-BR-FranciscaNeural",
      'label': "FranciscaNeural(${'anki.tts.voice.female'.tr}, Brazil)",
    },
    {
      'value': "pt-PT-DuarteNeural",
      'label': "DuarteNeural(${'anki.tts.voice.male'.tr}, Portugal)",
    },
    {
      'value': "pt-PT-RaquelNeural",
      'label': "RaquelNeural(${'anki.tts.voice.female'.tr}, Portugal)",
    },
  ]
};
