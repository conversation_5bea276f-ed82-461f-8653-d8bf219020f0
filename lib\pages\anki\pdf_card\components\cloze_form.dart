import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/pdf_card.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'dart:io';

class ClozeForm extends GetView<PDFCardFormController> {
  const ClozeForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(
        () => Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          spacing: 4,
          children: [
            ShadSelectWithInput(
              key: ValueKey(
                  "deck-${ankiConnectController.parentDeckList.length}"),
              label: 'anki.common.target_deck'.tr,
              placeholder: 'anki.placeholder.target_deck_search_input'.tr,
              searchPlaceholder: 'anki.placeholder.target_deck_search_input'.tr,
              isMultiple: false,
              initialValue: [controller.parentDeck.value],
              options: ankiConnectController.parentDeckList
                  .map((e) => {'value': e, 'label': e})
                  .toList(),
              onChanged: (value) {
                logger.i(value);
                controller.parentDeck.value = value.single;
              },
            onAddNew: (newDeckName) {
              // Add to the deck list if not already present
              if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                ankiConnectController.parentDeckList.add(newDeckName);
              }

              // Set as selected deck
              controller.parentDeck.value = newDeckName;
            },
              hasSuffix: true,
              onRefresh: () async {
                logger.i("refresh");
                final result =
                    await ankiConnectController.resetAnkiConnectData();
                if (result) {
                  showToastNotification(
                      context, 'anki.common.refresh_success'.tr, "");
                }
              },
            ),
            ShadSwitchCustom(
              key: ValueKey("subdeck-${controller.isCreateSubDeck.hashCode}"),
              label: 'anki.common.create_subdeck'.tr,
              initialValue: controller.isCreateSubDeck.value,
              onChanged: (v) {
                controller.isCreateSubDeck.value = v;
              },
            ),
            // ShadSelectCustom(
            //   label: "卡片模版",
            //   placeholder: "选择卡片模版",
            //   initialValue: const ['Kevin Image Cloze v5'],
            //   options: [
            //     {
            //       "value": "Kevin Image Cloze v5",
            //       "label": "Kevin Image Cloze v5"
            //     },
            //   ].toList(),
            //   onChanged: (value) {
            //     logger.i(value);
            //     controller.cardModel.value = value.single;
            //     controller.updateFieldList(controller.cardModel.value);
            //   },
            // ),
            if (controller.cardModel.value == "Kevin Image Cloze v5")
              ShadRadioGroupCustom(
                label: 'anki.common.card_mode'.tr,
                initialValue: controller.clozeParams.mode.value,
                items: controller.clozeModeList,
                onChanged: (value) {
                  controller.clozeParams.mode.value = value;
                },
              ),
            ShadCheckboxGroup(
              label: 'anki.pdf_card.mask_type'.tr,
              initialValues: controller.maskTypes.toList(),
              items: controller.clozeParams.maskTypeList.toList(),
              onChanged: (value) {
                logger.i(value);
                controller.maskTypes.value = value;
              },
              onValidate: (value) async {
                if (value.isEmpty) {
                  return 'anki.placeholder.atLeastOneMaskType'.tr;
                }
                return "";
              },
              onValidateError: (error) {},
            ),
            if (controller.cardModel.value == "Kevin Image Cloze v5") ...[
              ShadColorPickerDualColorsCustom(
                label: 'anki.pdf_card.mask_color'.tr,
                label1: 'anki.pdf_card.main_color'.tr,
                label2: 'anki.pdf_card.second_color'.tr,
                initialValue1: controller.clozeParams.primaryMaskColor.value,
                initialValue2: controller.clozeParams.secondaryMaskColor.value,
                onChanged: (color1, color2) {
                  controller.clozeParams.primaryMaskColor.value = color1;
                  controller.clozeParams.secondaryMaskColor.value = color2;
                },
              ),
            ],
            ListTile(
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              title: Text('anki.common.advanced_options'.tr,
                  style: defaultTitleStyle),
              subtitle: Wrap(
                spacing: 16,
                runSpacing: 8,
                children: [
                  ShadCheckbox(
                    value: controller.isShowSource.value,
                    label: Text('anki.common.show_source'.tr),
                    padding: EdgeInsets.zero,
                    onChanged: (v) {
                      controller.isShowSource.value = v;
                    },
                  ),
                  ShadCheckbox(
                    value: controller.clozeParams.isFullPageCloze.value,
                    label: Text('anki.pdf_card.full_page_cloze'.tr),
                    padding: EdgeInsets.zero,
                    onChanged: (v) {
                      controller.clozeParams.isFullPageCloze.value = v;
                    },
                  ),
                  ShadCheckbox(
                    value: controller.clozeParams.isPerClozePerCard.value,
                    label: Text('anki.pdf_card.one_cloze_per_card'.tr),
                    padding: EdgeInsets.zero,
                    onChanged: (v) {
                      controller.clozeParams.isPerClozePerCard.value = v;
                    },
                  ),
                  ShadCheckbox(
                    value: controller.isMixCloze.value,
                    label: Text('anki.pdf_card.mix_card'.tr),
                    padding: EdgeInsets.zero,
                    onChanged: (v) {
                      controller.isMixCloze.value = v;
                    },
                  ),
                  if (Platform.isWindows ||
                      Platform.isMacOS ||
                      Platform.isLinux)
                    ShadCheckbox(
                      value: controller.isZotero.value,
                      label: Text('anki.pdf_card.zotero_card'.tr),
                      padding: EdgeInsets.zero,
                      onChanged: (v) {
                        controller.isZotero.value = v;
                      },
                    )
                ],
              ),
            ),
            ShadInputWithValidate(
                label: 'anki.pdf_card.pdf_columns'.tr,
                placeholder: 'anki.placeholder.pdf_columns'.tr,
                initialValue: controller.nCols.value.toString(),
                onChanged: (value) {
                  controller.nCols.value = int.parse(value);
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'anki.placeholder.pdf_columns_required'.tr;
                  }
                  final reg = RegExp(r'^\d+$');
                  if (!reg.hasMatch(value)) {
                    return 'anki.placeholder.mustBeInteger'.tr;
                  }
                  final size = int.parse(value);
                  if (size <= 0) {
                    return 'anki.placeholder.mustBeGreaterThan0'.tr;
                  }
                  return "";
                }),
            ShadInputWithValidate(
                key: ValueKey("extra_info-${controller.extraInfo.hashCode}"),
                label: 'anki.pdf_card.extra_info'.tr,
                placeholder: 'anki.placeholder.extra_info'.tr,
                initialValue: controller.extraInfo.value,
                onChanged: (value) {
                  controller.extraInfo.value = value;
                },
                onValidate: (value) async {
                  return "";
                }),
            ShadSelectWithInput(
              key: ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
              label: 'anki.common.tags'.tr,
              placeholder: 'anki.placeholder.select_tags'.tr,
              searchPlaceholder: 'anki.placeholder.input_tags'.tr,
              isMultiple: true,
              initialValue: controller.tags.toList(),
              options: ankiConnectController.tagsList
                  .map((e) => {'value': e, 'label': e})
                  .toList(),
              onChanged: (value) {
                logger.i(value);
                controller.tags.value = value;
              },
              onAddNew: (newTag) {
                // Add new tag to the global tags list if not already present
                if (!ankiConnectController.tagsList.contains(newTag)) {
                  ankiConnectController.tagsList.add(newTag);
                }

                // Add to selected tags if not already present
                if (!controller.tags.contains(newTag)) {
                  controller.tags.add(newTag);
                }
              },
            ),
            ShadInputWithValidate(
                label: 'anki.common.page_range'.tr,
                placeholder: 'anki.placeholder.page_range'.tr,
                initialValue: controller.pageRange.value,
                onChanged: (value) {
                  controller.pageRange.value = value;
                },
                onValidate: (value) async {
                  if (validatePageRange(value)) {
                    return "";
                  }
                  return 'anki.placeholder.page_range_required'.tr;
                }),
            if (!controller.isZotero.value)
              ShadInputWithFileSelect(
                title: 'toolbox.common.inputFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const ['pdf'],
                isRequired: true,
                allowMultiple: true,
                onFilesSelected: (files) {
                  controller.selectedFilePaths.value = files;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
            if (controller.isZotero.value)
              ShadInputWithValidate(
                label: 'anki.pdf_card.zotero_item_id'.tr,
                placeholder: 'anki.placeholder.input_item_id'.tr,
                initialValue: controller.qItemId.value,
                onChanged: (value) {
                  controller.qItemId.value = value;
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'anki.placeholder.item_id_required'.tr;
                  }
                  return "";
                },
              ),
          ],
        ),
      ),
    );
  }
}
