import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';

class SubtitleController extends GetxController {
  final subtitles = <SubtitleEntry>[].obs;
  final currentSubtitleIndex = RxInt(-1);
  final currentSubtitlePath = RxString('');

  Future<void> loadSubtitleFile(BuildContext context,
      {int? playlistIndex}) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['srt'],
        allowMultiple: false,
        compressionQuality: 0
      );

      if (result != null) {
        final file = File(result.files.single.path!);
        final content = await file.readAsString();
        final parser = SubtitleParser(content);
        subtitles.value = parser.parseFile();
        currentSubtitlePath.value = result.files.single.path!;

        if (playlistIndex != null) {
          final videoController = Get.find<VideoNoteController>();
          if (playlistIndex < videoController.playlist.length) {
            videoController.playlist[playlistIndex] = {
              ...videoController.playlist[playlistIndex],
              'subtitlePath': currentSubtitlePath.value,
            };
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading subtitle file: $e');
    }
  }

  Future<void> loadSubtitleFromPath(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        final content = await file.readAsString();
        final parser = SubtitleParser(content);
        subtitles.value = parser.parseFile();
        currentSubtitlePath.value = path;
      }
    } catch (e) {
      debugPrint('Error loading subtitle from path: $e');
    }
  }

  void updateCurrentSubtitle(Duration position) {
    for (int i = 0; i < subtitles.length; i++) {
      if (position >= subtitles[i].start && position <= subtitles[i].end) {
        currentSubtitleIndex.value = i;
        return;
      }
    }
    currentSubtitleIndex.value = -1;
  }

  void clearSubtitles() {
    subtitles.clear();
    currentSubtitlePath.value = '';
    currentSubtitleIndex.value = -1;
  }
}

class SubtitleEntry {
  final int index;
  final Duration start;
  final Duration end;
  final String text;

  SubtitleEntry({
    required this.index,
    required this.start,
    required this.end,
    required this.text,
  });
}

class SubtitleParser {
  final String content;

  SubtitleParser(this.content);

  List<SubtitleEntry> parseFile() {
    final List<SubtitleEntry> entries = [];
    final lines = content.split('\n');
    int i = 0;

    while (i < lines.length) {
      // Skip empty lines
      while (i < lines.length && lines[i].trim().isEmpty) {
        i++;
      }
      if (i >= lines.length) break;

      // Parse index
      final index = int.tryParse(lines[i].trim());
      if (index == null) {
        i++;
        continue;
      }
      i++;

      // Parse timestamp
      if (i >= lines.length) break;
      final timeStamps = lines[i].split(' --> ');
      if (timeStamps.length != 2) {
        i++;
        continue;
      }

      final start = _parseTimestamp(timeStamps[0].trim());
      final end = _parseTimestamp(timeStamps[1].trim());
      i++;

      // Parse text
      String text = '';
      while (i < lines.length && lines[i].trim().isNotEmpty) {
        text += (text.isEmpty ? '' : '\n') + lines[i].trim();
        i++;
      }

      entries.add(SubtitleEntry(
        index: index,
        start: start,
        end: end,
        text: text,
      ));
    }

    return entries;
  }

  Duration _parseTimestamp(String timestamp) {
    final parts = timestamp.split(':');
    if (parts.length != 3) return Duration.zero;

    final seconds = parts[2].split(',');
    if (seconds.length != 2) return Duration.zero;

    return Duration(
      hours: int.parse(parts[0]),
      minutes: int.parse(parts[1]),
      seconds: int.parse(seconds[0]),
      milliseconds: int.parse(seconds[1]),
    );
  }
}
