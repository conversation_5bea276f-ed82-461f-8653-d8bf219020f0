import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common/paywall_controller.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';

class PaywallPage extends StatefulWidget {
  const PaywallPage({super.key});

  @override
  State<PaywallPage> createState() => _PaywallPageState();
}

class _PaywallPageState extends State<PaywallPage> {
  late final PaywallController paywallController;

  @override
  void initState() {
    super.initState();
    paywallController = Get.put(PaywallController());

    // 在initState中设置默认选中的会员类型，而不是在build中
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (paywallController.selectedProductId.value.isEmpty) {
        paywallController.selectedProductId.value =
            paywallController.monthlyProductId;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('paywall.title'.tr),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Get.back(),
        ),
        // actions: [
        //   TextButton(
        //     onPressed: () async {
        //       final result =
        //           await paywallController.presentCodeRedemptionSheet();
        //       if (result) {
        //         logger.i('兑换码弹窗已成功显示，请按照提示进行兑换');
        //       }
        //     },
        //     child: const Text('使用兑换码'),
        //   ),
        // ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 8),
              const HeaderSection(),
              const SizedBox(height: 20),
              const FeatureListSection(),
              const SizedBox(height: 20),
              SubscriptionOptionsSection(controller: paywallController),
              const SizedBox(height: 20),
              PurchaseButtonSection(controller: paywallController),
              const SizedBox(height: 12),
              RestoreButtonSection(controller: paywallController),
              const SizedBox(height: 4),
              const TermsAndPrivacySection(),
            ],
          ),
        ),
      ),
    );
  }
}

class HeaderSection extends StatelessWidget {
  const HeaderSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const CircleAvatar(
          radius: 40,
          backgroundColor: Colors.white,
          foregroundImage: AssetImage('assets/images/logo.png'),
          child: Icon(Icons.school, size: 30, color: Colors.white),
        ),
        const SizedBox(height: 4),
        Text(
          'paywall.headerTitle'.tr,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          'paywall.headerSubtitle'.tr,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

class FeatureListSection extends StatelessWidget {
  const FeatureListSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              'paywall.benefitsTitle'.tr,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 12),
          const FeatureBulletinBoard(),
        ],
      ),
    );
  }
}

class FeatureBulletinBoard extends StatefulWidget {
  const FeatureBulletinBoard({super.key});

  @override
  State<FeatureBulletinBoard> createState() => _FeatureBulletinBoardState();
}

class _FeatureBulletinBoardState extends State<FeatureBulletinBoard> {
  final List<String> features = [
    'paywall.features.aiCard'.tr,
    'paywall.features.pdfCard'.tr,
    'paywall.features.wordCard'.tr,
    'paywall.features.excelCard'.tr,
    'paywall.features.textCard'.tr,
    'paywall.features.multipleChoiceCard'.tr,
    'paywall.features.markdownCard'.tr,
    'paywall.features.imageCard'.tr,
    'paywall.features.xmindCard'.tr,
    'paywall.features.obsidianCard'.tr,
    'paywall.features.logseqCard'.tr,
    'paywall.features.notionCard'.tr,
    'paywall.features.siyuanCard'.tr,
    'paywall.features.eudicCard'.tr,
    'paywall.features.wordListCard'.tr,
    'paywall.features.zhixiCard'.tr,
    'paywall.features.wechatReadCard'.tr,
    'paywall.features.mubuCard'.tr,
    'paywall.features.multimediaCard'.tr,
    'paywall.features.videoNote'.tr,
    'paywall.features.quickNote'.tr,
    'paywall.features.textToSpeech'.tr,
    'paywall.features.fastSync'.tr,
    'paywall.features.prioritySupport'.tr,
  ];

  final List<Color> colors = [
    Colors.blue.shade100,
    Colors.green.shade100,
    Colors.purple.shade100,
    Colors.orange.shade100,
    Colors.teal.shade100,
    Colors.blueGrey.shade100,
    Colors.indigo.shade100,
    Colors.brown.shade100,
  ];

  final _random = Random();
  final _scrollController1 = ScrollController();
  final _scrollController2 = ScrollController();
  final _scrollController3 = ScrollController();

  // 添加三个列表用于无缝循环
  late List<Widget> _row1Items;
  late List<Widget> _row2Items;
  late List<Widget> _row3Items;

  @override
  void initState() {
    super.initState();

    // 初始化三行的卡片
    _initItems();

    // 启动三个不同速度的自动滚动
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startAutoScroll();
    });
  }

  void _initItems() {
    // 将特性列表分成三部分，并打乱顺序以避免重复
    final shuffled = [...features]..shuffle(_random);
    final partSize = shuffled.length ~/ 3;

    // 确保每行的特性都不相同
    final firstPart = shuffled.sublist(0, partSize);
    final secondPart = shuffled.sublist(partSize, partSize * 2);
    final thirdPart = shuffled.sublist(partSize * 2);

    // 为每行创建足够的卡片，并添加足够的间距，以实现无缝循环
    _row1Items = [];
    _row2Items = [];
    _row3Items = [];

    // 第一行
    _row1Items.add(const SizedBox(width: 20));
    for (var feature in firstPart) {
      _row1Items.add(_buildFeatureChip(feature, 0));
      _row1Items.add(const SizedBox(width: 40)); // 添加较大间距
    }
    // 再添加一次，确保滚动循环
    for (var feature in firstPart) {
      _row1Items.add(_buildFeatureChip(feature, 0));
      _row1Items.add(const SizedBox(width: 40));
    }

    // 第二行，起始位置不同
    _row2Items.add(const SizedBox(width: 150));
    for (var feature in secondPart) {
      _row2Items.add(_buildFeatureChip(feature, 1));
      _row2Items.add(const SizedBox(width: 50)); // 添加更大间距
    }
    for (var feature in secondPart) {
      _row2Items.add(_buildFeatureChip(feature, 1));
      _row2Items.add(const SizedBox(width: 50));
    }

    // 第三行，又不同的起始位置
    _row3Items.add(const SizedBox(width: 80));
    for (var feature in thirdPart) {
      _row3Items.add(_buildFeatureChip(feature, 2));
      _row3Items.add(const SizedBox(width: 60)); // 添加最大间距
    }
    for (var feature in thirdPart) {
      _row3Items.add(_buildFeatureChip(feature, 2));
      _row3Items.add(const SizedBox(width: 60));
    }
  }

  void _startAutoScroll() {
    // 三个滚动控制器，不同速度，较慢以确保用户能看清
    _animateInfiniteScroll(_scrollController1, 25);
    _animateInfiniteScroll(_scrollController2, 30);
    _animateInfiniteScroll(_scrollController3, 20);
  }

  void _animateInfiniteScroll(
      ScrollController controller, double pixelsPerSecond) {
    if (!mounted) return;

    // 计算滚动到最大位置所需的时间
    final contentWidth = controller.position.maxScrollExtent;
    final duration =
        Duration(milliseconds: (contentWidth / pixelsPerSecond * 1000).toInt());

    // 滚动到最大位置
    controller
        .animateTo(
      contentWidth,
      duration: duration,
      curve: Curves.linear,
    )
        .then((_) {
      if (!mounted) return;
      // 不再瞬间跳回开始位置，而是从末尾自然衔接
      // 重新设置滚动位置，使其看起来像是连续的
      final currentOffset = controller.offset;
      final maxOffset = controller.position.maxScrollExtent;
      final viewportWidth = controller.position.viewportDimension;

      // 计算需要重置的位置，保持视觉上的连续性
      final resetOffset = currentOffset - (maxOffset / 2);

      // 重置到新位置，但保持视觉上的连续
      controller.jumpTo(resetOffset);

      // 继续下一轮滚动
      _animateInfiniteScroll(controller, pixelsPerSecond);
    });
  }

  @override
  void dispose() {
    _scrollController1.dispose();
    _scrollController2.dispose();
    _scrollController3.dispose();
    super.dispose();
  }

  Color _getRandomColor() {
    return colors[_random.nextInt(colors.length)];
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 195,
      child: Column(
        children: [
          Expanded(
            child: ListView(
              controller: _scrollController1,
              scrollDirection: Axis.horizontal,
              physics: const NeverScrollableScrollPhysics(),
              children: _row1Items,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView(
              controller: _scrollController2,
              scrollDirection: Axis.horizontal,
              physics: const NeverScrollableScrollPhysics(),
              children: _row2Items,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView(
              controller: _scrollController3,
              scrollDirection: Axis.horizontal,
              physics: const NeverScrollableScrollPhysics(),
              children: _row3Items,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureChip(String feature, int row) {
    // 根据行号调整样式，使布局错落有致
    final double verticalPadding = row == 1 ? 2.0 : 4.0;
    final double horizontalPadding = 6.0 + (row * 2.0);
    final double avatarSize = 18.0 + (row * 1.0);

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: verticalPadding,
      ),
      child: Chip(
        backgroundColor: _getRandomColor().withOpacity(0.8),
        labelStyle: TextStyle(
          color: Colors.black87,
          fontSize: 15.0 + (row * 0.5),
          fontWeight: FontWeight.w500,
        ),
        avatar: Icon(
          Icons.check_circle,
          color: Colors.green,
          size: avatarSize,
        ),
        label: Text(feature),
        padding: EdgeInsets.symmetric(
          horizontal: 8.0 + (row * 1.0),
          vertical: 4.0,
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 6),
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}

class FeatureItem extends StatelessWidget {
  final IconData icon;
  final String text;

  const FeatureItem({
    super.key,
    required this.icon,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(icon, color: Colors.green, size: 18),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(fontSize: 14),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

class SubscriptionOptionsSection extends StatelessWidget {
  final PaywallController controller;

  const SubscriptionOptionsSection({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final monthlyProduct = controller.monthlyProduct.value;
      final yearlyProduct = controller.yearlyProduct.value;
      final lifetimeProduct = controller.lifetimeProduct.value;

      // 获取免费试用期信息
      final showTrialForMonthly = controller.showTrialForMonthly.value;
      final showTrialForYearly = controller.showTrialForYearly.value;
      final trialDays = controller.trialDurationDays.value;

      return Column(
        children: [
          SubscriptionCard(
            title: monthlyProduct?.title ?? 'paywall.monthlySubscription'.tr,
            price: monthlyProduct != null &&
                    monthlyProduct is AppStoreProductDetails
                ? monthlyProduct.price
                : '¥9.90',
            period: monthlyProduct?.description?.isNotEmpty == true
                ? monthlyProduct!.description!
                : 'paywall.perMonth'.tr,
            isPopular: false,
            isSelected: controller.selectedProductId.value ==
                controller.monthlyProductId,
            onTap: () => controller.selectedProductId.value =
                controller.monthlyProductId,
            showTrial: showTrialForMonthly,
            trialDays: trialDays,
          ),
          const SizedBox(height: 12),
          SubscriptionCard(
            title: yearlyProduct?.title ?? 'paywall.yearlySubscription'.tr,
            price:
                yearlyProduct != null && yearlyProduct is AppStoreProductDetails
                    ? yearlyProduct.price
                    : '¥49.90',
            period: yearlyProduct?.description?.isNotEmpty == true
                ? yearlyProduct!.description!
                : 'paywall.perYear'.tr,
            isPopular: false,
            isSelected: controller.selectedProductId.value ==
                controller.yearlyProductId,
            onTap: () =>
                controller.selectedProductId.value = controller.yearlyProductId,
            showTrial: showTrialForYearly,
            trialDays: trialDays,
          ),
          const SizedBox(height: 12),
          SubscriptionCard(
            title: lifetimeProduct?.title ?? 'paywall.lifetimeSubscription'.tr,
            price: lifetimeProduct != null &&
                    lifetimeProduct is AppStoreProductDetails
                ? lifetimeProduct.price
                : '¥99.90',
            period: lifetimeProduct?.description?.isNotEmpty == true
                ? lifetimeProduct!.description!
                : 'paywall.oneTimePurchase'.tr,
            isPopular: true,
            isSelected: controller.selectedProductId.value ==
                controller.lifetimeProductId,
            onTap: () => controller.selectedProductId.value =
                controller.lifetimeProductId,
            showTrial: false,
            trialDays: 0,
          ),
        ],
      );
    });
  }
}

class SubscriptionCard extends StatelessWidget {
  final String title;
  final String price;
  final String period;
  final bool isPopular;
  final bool isSelected;
  final VoidCallback onTap;
  final bool showTrial;
  final int trialDays;

  const SubscriptionCard({
    super.key,
    required this.title,
    required this.price,
    required this.period,
    required this.isPopular,
    required this.isSelected,
    required this.onTap,
    this.showTrial = false,
    this.trialDays = 0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Colors.blue : Colors.black,
                        ),
                      ),
                      if (isPopular) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'paywall.mostPopular'.tr,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 2),
                  Text(
                    period,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected ? Colors.blue : Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              price,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.blue : Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PurchaseButtonSection extends StatelessWidget {
  final PaywallController controller;

  const PurchaseButtonSection({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isSelected = controller.selectedProductId.value.isNotEmpty;
      final isLoading = controller.isPurchasing.value;
      final selectedProductId = controller.selectedProductId.value;

      // 检查用户是否有资格获得免费试用期
      final bool isTrialEligible = controller.isTrialEligible.value;

      // 只有有资格获得试用并且选择了可试用产品的用户才显示试用按钮
      final bool isTrialProduct =
          (selectedProductId == controller.monthlyProductId &&
                  controller.showTrialForMonthly.value) ||
              (selectedProductId == controller.yearlyProductId &&
                  controller.showTrialForYearly.value);
      final bool showTrialButton =
          isSelected && isTrialEligible && isTrialProduct;

      // 试用天数
      final int trialDays = controller.trialDurationDays.value;

      return SizedBox(
        width: double.infinity,
        height: 50,
        child: ElevatedButton(
          onPressed: isSelected && !isLoading
              ? () => controller.purchaseProduct(
                  controller.selectedProductId.value, context)
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            isSelected ? 'paywall.purchase'.tr : 'paywall.selectPlan'.tr,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
    });
  }
}

class RestoreButtonSection extends StatelessWidget {
  final PaywallController controller;

  const RestoreButtonSection({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: TextButton(
        onPressed: () => controller.restorePurchases(),
        child: Text('paywall.restorePurchase'.tr),
      ),
    );
  }
}

class TermsAndPrivacySection extends StatelessWidget {
  const TermsAndPrivacySection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ShadButton.link(
              foregroundColor: Colors.grey,
              onPressed: () {
                // 打开服务条款
                launchUrl(Uri.parse('https://guru.kevin2li.com/terms.html'));
              },
              child: Text('paywall.termsOfService'.tr),
            ),
            const Text(' | ', style: TextStyle(color: Colors.grey)),
            ShadButton.link(
              foregroundColor: Colors.grey,
              onPressed: () {
                // 打开隐私政策
                launchUrl(Uri.parse('https://guru.kevin2li.com/privacy.html'));
              },
              child: Text('paywall.privacyPolicy'.tr),
            ),
          ],
        ),
        const SizedBox(height: 12),
        const SubscriptionNoticeSection(),
      ],
    );
  }
}

class SubscriptionNoticeSection extends StatelessWidget {
  const SubscriptionNoticeSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'paywall.subscriptionNotice'.tr,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.left,
          ),
          const SizedBox(height: 8),
          Text(
            'paywall.subscriptionDetails'.tr,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.left,
          ),
        ],
      ),
    );
  }
}
