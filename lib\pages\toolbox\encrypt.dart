import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/toolbox/encrypt.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFEncryptPage extends StatefulWidget {
  const PDFEncryptPage({super.key});

  @override
  State<PDFEncryptPage> createState() => _PDFEncryptPageState();
}

class _PDFEncryptPageState extends State<PDFEncryptPage> {
  final controller = Get.put(PDFEncryptPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.encrypt.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('toolbox.common.functionDescription'.tr,
                style: defaultPageTitleStyle),
            Text('toolbox.encrypt.description'.tr,
                style: theme.textTheme.muted),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 2;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        logger.i(controller.tabController.selected);
                      },
                      tabs: [
                        ShadTab(
                          value: 'encrypt',
                          content: const EncryptForm(),
                          width: tabWidth,
                          child: Text('toolbox.encrypt.encryptTab'.tr),
                        ),
                        ShadTab(
                          value: 'decrypt',
                          content: const DecryptForm(),
                          width: tabWidth,
                          child: Text('toolbox.encrypt.decryptTab'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class EncryptForm extends GetView<PDFEncryptPageController> {
  const EncryptForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Obx(
        () => Column(
          spacing: 6,
          children: [
            ShadCheckboxGroup(
              label: 'toolbox.encrypt.passwordType'.tr,
              initialValues: controller.encryptTypes.toList(),
              items: controller.encryptTypeList.toList(),
              onChanged: (value) {
                logger.i(value);
                controller.encryptTypes.value = value;
              },
              onValidate: (value) async {
                if (value.isEmpty) {
                  return 'toolbox.encrypt.passwordTypeRequired'.tr;
                }
                return "";
              },
              onValidateError: (error) {},
            ),
            if (controller.encryptTypes.contains('user')) ...[
              ShadInputPasswordCustom(
                label: 'toolbox.encrypt.user.password'.tr,
                placeholder: 'toolbox.encrypt.user.passwordPlaceholder'.tr,
                initialValue: controller.userPassword.value,
                onChanged: (value) {
                  controller.userPassword.value = value;
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'toolbox.encrypt.user.passwordEmpty'.tr;
                  }
                  if (value.length < 6) {
                    return 'toolbox.encrypt.user.passwordTooShort'.tr;
                  }
                  return "";
                },
                onValidateError: (error) {},
              ),
              ShadInputPasswordCustom(
                label: 'toolbox.encrypt.user.confirmPassword'.tr,
                placeholder:
                    'toolbox.encrypt.user.confirmPasswordPlaceholder'.tr,
                initialValue: controller.confirmUserPassword.value,
                onChanged: (value) {
                  controller.confirmUserPassword.value = value;
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'toolbox.encrypt.user.confirmEmpty'.tr;
                  }
                  if (value != controller.userPassword.value) {
                    return 'toolbox.encrypt.user.passwordMismatch'.tr;
                  }
                  return "";
                },
                onValidateError: (error) {},
              ),
            ],
            if (controller.encryptTypes.contains('permission')) ...[
              ShadInputPasswordCustom(
                label: 'toolbox.encrypt.type.permission'.tr,
                placeholder: 'toolbox.encrypt.user.passwordPlaceholder'.tr,
                initialValue: controller.permissionPassword.value,
                onChanged: (value) {
                  controller.permissionPassword.value = value;
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'toolbox.encrypt.user.passwordEmpty'.tr;
                  }
                  if (value.length < 6) {
                    return 'toolbox.encrypt.user.passwordTooShort'.tr;
                  }
                  return "";
                },
                onValidateError: (error) {},
              ),
              ShadInputPasswordCustom(
                label: 'toolbox.encrypt.type.permission'.tr,
                placeholder:
                    'toolbox.encrypt.user.confirmPasswordPlaceholder'.tr,
                initialValue: controller.confirmPermissionPassword.value,
                onChanged: (value) {
                  controller.confirmPermissionPassword.value = value;
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'toolbox.encrypt.user.confirmEmpty'.tr;
                  }
                  if (value != controller.permissionPassword.value) {
                    return 'toolbox.encrypt.user.passwordMismatch'.tr;
                  }
                  return "";
                },
                onValidateError: (error) {},
              ),
              ShadCheckboxGroup(
                label: 'toolbox.encrypt.passwordType'.tr,
                initialValues: controller.encryptPermissions.toList(),
                items: controller.encryptPermissionList.toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.encryptPermissions.value = value;
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'toolbox.encrypt.passwordTypeRequired'.tr;
                  }
                  return "";
                },
                onValidateError: (error) {},
              ),
            ],
            if (PathUtils.isDesktop)
              ShadSelectCustom(
                label: 'toolbox.common.outputLocation'.tr,
                placeholder: 'toolbox.common.selectOutputLocation'.tr,
                initialValue: [controller.outputMode.value],
                options: outputModeList,
                onChanged: (value) {
                  controller.outputMode.value = value.single;
                },
              ),
            if (controller.outputMode.value == 'custom')
              ShadInputWithFileSelect(
                key: ValueKey("output-dir-${controller.outputDir.value}"),
                title: 'toolbox.common.outputDirectory'.tr,
                placeholder: Text('toolbox.common.outputDirectory'.tr),
                initialValue: [controller.outputDir.value],
                isRequired: true,
                isFolder: true,
                onFilesSelected: (value) {
                  controller.outputDir.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {
                  controller.outputDirError.value = error;
                },
              ),
            ShadInputWithFileSelect(
              key: const ValueKey("input-file"),
              title: 'toolbox.common.inputFile'.tr,
              placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
              allowedExtensions: const ['pdf'],
              isRequired: true,
              allowMultiple: true,
              initialValue: controller.selectedFilePaths,
              onFilesSelected: (files) {
                controller.selectedFilePaths.value = files;
              },
              onValidate: (value, files) async {
                return await validateFile(value, files);
              },
              onValidateError: (error) {},
            ),
          ],
        ),
      ),
    );
  }
}

class DecryptForm extends GetView<PDFEncryptPageController> {
  const DecryptForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Column(
          spacing: 6,
          children: [
            ShadInputPasswordCustom(
              label: 'toolbox.decrypt.password'.tr,
              placeholder: 'toolbox.decrypt.passwordPlaceholder'.tr,
              initialValue: controller.userPassword.value,
              onChanged: (value) {
                controller.userPassword.value = value;
              },
              onValidate: (value) async {
                if (value.isEmpty) {
                  return 'toolbox.encrypt.user.passwordEmpty'.tr;
                }
                return "";
              },
              onValidateError: (error) {},
            ),
            if (PathUtils.isDesktop)
              ShadSelectCustom(
                label: 'toolbox.common.outputLocation'.tr,
                placeholder: 'toolbox.common.selectOutputLocation'.tr,
                initialValue: [controller.outputMode.value],
                options: outputModeList,
                onChanged: (value) {
                  controller.outputMode.value = value.single;
                },
              ),
            if (controller.outputMode.value == 'custom')
              ShadInputWithFileSelect(
                key: ValueKey("output-dir-${controller.outputDir.value}"),
                title: 'toolbox.common.outputDirectory'.tr,
                placeholder: Text('toolbox.common.outputDirectory'.tr),
                initialValue: [controller.outputDir.value],
                isRequired: true,
                isFolder: true,
                onFilesSelected: (value) {
                  controller.outputDir.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {
                  controller.outputDirError.value = error;
                },
              ),
            ShadInputWithFileSelect(
              key: const ValueKey("input-file"),
              title: 'toolbox.common.inputFile'.tr,
              placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
              allowedExtensions: const ['pdf'],
              isRequired: true,
              allowMultiple: true,
              initialValue: controller.selectedFilePaths,
              onFilesSelected: (files) {
                controller.selectedFilePaths.value = files;
              },
              onValidate: (value, files) async {
                return await validateFile(value, files);
              },
              onValidateError: (error) {},
            ),
          ],
        ),
      ),
    );
  }
}
