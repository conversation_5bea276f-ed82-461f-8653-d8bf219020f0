import { defineBackground } from 'wxt/sandbox';
// @ts-ignore 忽略类型检查问题
import browser from 'webextension-polyfill';

console.log('Guru Connector 后台脚本加载');

// 默认WebSocket端口 - 修正为Flutter应用使用的端口
const DEFAULT_WEBSOCKET_PORT = 52025;

// 获取WebSocket URL
async function getWebSocketUrl(): Promise<string> {
  try {
    console.log('获取WebSocket URL');
    const result = await browser.storage.local.get({ port: DEFAULT_WEBSOCKET_PORT });
    const port = result.port || DEFAULT_WEBSOCKET_PORT;
    console.log('获取到端口:', port);
    return `ws://localhost:${port}/connect`;
  } catch (error) {
    console.error('获取WebSocket URL失败:', error);
    return `ws://localhost:${DEFAULT_WEBSOCKET_PORT}/connect`;
  }
}

// 生成带时间戳的视频URL
function generateTimestampUrl(originalUrl: string, timestampSeconds: number): string {
  try {
    const url = new URL(originalUrl);
    const hostname = url.hostname.toLowerCase();

    console.log('🔗 为平台生成时间戳URL:', hostname, '时间戳:', timestampSeconds);

    if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
      // YouTube: 使用 t=123s 格式
      url.searchParams.set('t', `${timestampSeconds}s`);
      console.log('📺 YouTube格式: t=' + timestampSeconds + 's');
    } else if (hostname.includes('bilibili.com')) {
      // Bilibili: 使用 t=123 格式
      url.searchParams.set('t', timestampSeconds.toString());
      console.log('📺 Bilibili格式: t=' + timestampSeconds);
    } else if (hostname.includes('vimeo.com')) {
      // Vimeo: 使用 #t=123s 格式
      url.hash = `t=${timestampSeconds}s`;
      console.log('📺 Vimeo格式: #t=' + timestampSeconds + 's');
    } else {
      // 通用格式: 使用 t=123 参数
      url.searchParams.set('t', timestampSeconds.toString());
      console.log('📺 通用格式: t=' + timestampSeconds);
    }

    const result = url.toString();
    console.log('🔗 生成的最终URL:', result);
    return result;
  } catch (error) {
    console.error('❌ 生成时间戳URL失败:', error);
    return originalUrl; // 返回原始URL作为后备
  }
}

// 查找匹配视频URL的现有标签页
async function findVideoTab(targetUrl: string): Promise<browser.Tabs.Tab | null> {
  try {
    const targetUrlObj = new URL(targetUrl);
    const targetHostname = targetUrlObj.hostname.toLowerCase();
    const targetPathname = targetUrlObj.pathname;

    console.log('🔍 查找匹配的标签页:', targetHostname, targetPathname);

    // 获取所有标签页
    const tabs = await browser.tabs.query({});

    for (const tab of tabs) {
      if (!tab.url) continue;

      try {
        const tabUrl = new URL(tab.url);
        const tabHostname = tabUrl.hostname.toLowerCase();
        const tabPathname = tabUrl.pathname;

        // 检查是否为相同的视频
        if (tabHostname === targetHostname) {
          // 对于视频网站，检查路径是否匹配
          if (tabHostname.includes('youtube.com')) {
            // YouTube: 检查 watch?v= 参数
            const targetVideoId = targetUrlObj.searchParams.get('v');
            const tabVideoId = tabUrl.searchParams.get('v');
            if (targetVideoId && tabVideoId && targetVideoId === tabVideoId) {
              console.log('✅ 找到匹配的YouTube标签页:', tab.id);
              return tab;
            }
          } else if (tabHostname.includes('bilibili.com')) {
            // Bilibili: 检查视频ID路径
            const targetVideoPath = targetPathname.split('?')[0];
            const tabVideoPath = tabPathname.split('?')[0];
            if (targetVideoPath === tabVideoPath) {
              console.log('✅ 找到匹配的Bilibili标签页:', tab.id);
              return tab;
            }
          } else {
            // 其他网站: 检查完整路径
            if (targetPathname === tabPathname) {
              console.log('✅ 找到匹配的标签页:', tab.id);
              return tab;
            }
          }
        }
      } catch (urlError) {
        // 忽略无效URL的标签页
        continue;
      }
    }

    console.log('❌ 未找到匹配的标签页');
    return null;
  } catch (error) {
    console.error('❌ 查找标签页失败:', error);
    return null;
  }
}

// 显示连接成功通知
function showConnectionSuccessNotification() {
  try {
    browser.notifications.create({
      type: 'basic',
      iconUrl: '/icon/icon-48.png',
      title: 'Guru Connector',
      message: '已成功连接到Anki Guru应用！'
    });
    console.log('显示连接成功通知');
  } catch (error) {
    console.error('显示连接成功通知失败:', error);
  }
}

// 显示连接失败通知
function showConnectionFailureNotification(message: string) {
  try {
    browser.notifications.create({
      type: 'basic',
      iconUrl: '/icon/icon-48.png',
      title: 'Guru Connector',
      message: `连接失败: ${message}`
    });
    console.log('显示连接失败通知');
  } catch (error) {
    console.error('显示连接失败通知失败:', error);
  }
}

// 显示重连通知
function showReconnectingNotification() {
  try {
    browser.notifications.create({
      type: 'basic',
      iconUrl: '/icon/icon-48.png',
      title: 'Guru Connector',
      message: '连接已断开，正在尝试重新连接...'
    });
    console.log('显示重连通知');
  } catch (error) {
    console.error('显示重连通知失败:', error);
  }
}

export default defineBackground({
  persistent: true,

  main() {
    console.log('Guru Connector 后台脚本初始化');

    // WebSocket实例
    let ws: WebSocket | null = null;

    // 连接状态
    let isConnected = false;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;
    let reconnectTimeout: ReturnType<typeof setTimeout> | null = null;

    // 调试获取服务端口的信息
    browser.storage.local.get({ port: DEFAULT_WEBSOCKET_PORT }).then(
      (result: any) => console.log('初始化时获取到的端口:', result.port)
    );

    // 广播连接状态到popup
    function broadcastConnectionStatus(connected: boolean) {
      try {
        browser.runtime.sendMessage({
          type: 'connection_status',
          connected: connected
        }).catch(() => {
          // 忽略发送失败的错误，popup可能未打开
        });
      } catch (e) {
        // 忽略错误
      }
    }

    // 发送消息到WebSocket
    function sendMessageToWebSocket(message: any) {
      if (ws && ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(message));
          console.log('发送消息到WebSocket:', message);
        } catch (error) {
          console.error('发送WebSocket消息失败:', error);
        }
      } else {
        console.warn('WebSocket未连接，无法发送消息:', message);
      }
    }

    // 连接到WebSocket服务器
    async function connect() {
      try {
        const timestamp = new Date().toISOString();

        // 防止重复连接
        if (ws && (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN)) {
          console.log(`[${timestamp}] WebSocket已连接或正在连接中，跳过重复连接`);
          return;
        }

        const wsUrl = await getWebSocketUrl();
        console.log(`[${timestamp}] 尝试连接到WebSocket: ${wsUrl}`);

        // 关闭现有连接
        if (ws) {
          try {
            ws.close();
          } catch (e) {
            console.warn(`[${timestamp}] 关闭现有连接时出错:`, e);
          }
          ws = null;
        }

        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          const connectTimestamp = new Date().toISOString();
          console.log(`[${connectTimestamp}] WebSocket连接已建立`);
          isConnected = true;
          reconnectAttempts = 0; // 重置重连计数

          // 清除任何待处理的重连定时器
          if (reconnectTimeout) {
            clearTimeout(reconnectTimeout);
            reconnectTimeout = null;
          }

          // 显示连接成功通知
          showConnectionSuccessNotification();

          // 广播连接状态
          broadcastConnectionStatus(true);

          // 发送识别消息
          sendMessageToWebSocket({
            type: 'identify',
            source: 'browser_extension',
            timestamp: Date.now()
          });
        };

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            console.log('🔄 Background script 收到WebSocket原始消息:', event.data);
            console.log('🔄 解析后的消息对象:', message);
            handleMessage(message);
          } catch (error) {
            console.error('❌ 解析WebSocket消息失败:', error);
          }
        };

        ws.onclose = (event) => {
          const closeTimestamp = new Date().toISOString();
          console.log(`[${closeTimestamp}] WebSocket连接已关闭:`, event.code, event.reason);
          isConnected = false;

          // 广播连接状态
          broadcastConnectionStatus(false);

          // 改进的重连逻辑
          const shouldReconnect = event.code !== 1000 && // 非正常关闭
                                 event.code !== 1001 && // 非端点离开
                                 reconnectAttempts < maxReconnectAttempts;

          if (shouldReconnect) {
            reconnectAttempts++;
            const delay = Math.min(5000 * reconnectAttempts, 30000); // 指数退避，最大30秒
            console.log(`[${closeTimestamp}] 非正常关闭 (code: ${event.code})，${delay/1000}秒后尝试第${reconnectAttempts}次重连`);

            if (reconnectAttempts <= 3) {
              showReconnectingNotification();
            }

            reconnectTimeout = setTimeout(() => {
              if (!isConnected) { // 确保在重连前仍未连接
                connect();
              }
            }, delay);
          } else if (reconnectAttempts >= maxReconnectAttempts) {
            console.log(`[${closeTimestamp}] 已达到最大重连次数 (${maxReconnectAttempts})，停止重连`);
            showConnectionFailureNotification('连接失败次数过多，请手动重新连接');
          } else {
            console.log(`[${closeTimestamp}] 正常关闭连接 (code: ${event.code})，不进行重连`);
          }
        };

        ws.onerror = (error) => {
          const errorTimestamp = new Date().toISOString();
          console.error(`[${errorTimestamp}] WebSocket错误:`, error);
          isConnected = false;
          
          // 广播连接状态
          broadcastConnectionStatus(false);
        };

      } catch (error) {
        const timestamp = new Date().toISOString();
        console.error(`[${timestamp}] WebSocket连接失败:`, error);
        isConnected = false;
        
        // 显示连接失败通知
        showConnectionFailureNotification('无法连接到Anki Guru应用');
        
        // 通知popup连接状态和错误信息
        broadcastConnectionStatus(false);
        
        // 发送错误消息到popup
        try {
          browser.runtime.sendMessage({
            type: 'connection_error',
            error: `连接失败: ${(error as Error).message || '未知错误'}`
          }).catch(() => {
            // 忽略发送失败的错误，popup可能未打开
          });
        } catch (e) {
          console.error(`[${timestamp}] 发送错误消息失败:`, e);
        }
      }
    }

    // 初始化WebSocket连接
    connect();

    // 监听来自popup的消息
    browser.runtime.onMessage.addListener(async (message: any, sender: any) => {
      console.log('收到runtime消息:', message, '发送者:', sender);

      if (message.type === 'get_connection_status') {
        console.log('返回连接状态:', isConnected);
        return { connected: isConnected };
      }

      if (message.type === 'reconnect_websocket') {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] 收到重连请求, 端口:`, message.port);

        try {
          // 清除重连定时器和重置计数
          if (reconnectTimeout) {
            clearTimeout(reconnectTimeout);
            reconnectTimeout = null;
          }
          reconnectAttempts = 0;

          // 先关闭现有连接
          if (ws) {
            console.log(`[${timestamp}] 关闭现有WebSocket连接`);
            ws.close(1000, 'Manual reconnect');
            ws = null;
          }

          if (message.port) {
            console.log(`[${timestamp}] 更新端口设置: ${message.port}`);
            await browser.storage.local.set({ port: message.port });
            console.log(`[${timestamp}] 端口已更新，开始连接`);
          }

          // 尝试连接
          connect();

          return { success: true, message: '连接请求已发送' };
        } catch (error) {
          console.error(`[${timestamp}] 重连请求处理失败:`, error);
          return { success: false, error: (error as Error).message || '未知错误' };
        }
      }

      if (message.type === 'disconnect_websocket') {
        console.log('关闭WebSocket连接');

        // 清除重连定时器
        if (reconnectTimeout) {
          clearTimeout(reconnectTimeout);
          reconnectTimeout = null;
        }

        // 重置重连计数
        reconnectAttempts = 0;

        if (ws) {
          try {
            ws.close(1000, 'User requested disconnect'); // 正常关闭
            ws = null;
          } catch (e) {
            console.error('关闭WebSocket出错:', e);
          }
        }
        isConnected = false;
        broadcastConnectionStatus(false);
        return { success: true };
      }

      // 转发从内容脚本来的消息到WebSocket
      if (sender.tab && message.type && (
        message.type === 'screenshot' ||
        message.type === 'video_info' ||
        message.type === 'timestamp_link' ||
        message.type === 'timestamp_data' ||
        message.type === 'timestamp_error' ||
        message.type === 'screenshot_data' ||
        message.type === 'screenshot_error' ||
        message.type === 'report'  // 新的统一报告格式
      )) {
        console.log('转发消息到WebSocket:', message);
        sendMessageToWebSocket(message);
        return { success: true };
      }

      return false;
    });

    // 处理从Flutter发来的消息
    async function handleMessage(message: any) {
      console.log('🔄 收到WebSocket消息:', message);

      // 获取当前活动标签页
      const tabs = await browser.tabs.query({ active: true, currentWindow: true });
      if (!tabs || tabs.length === 0 || !tabs[0].id) {
        console.error('❌ 未找到活动标签页');
        return;
      }

      const tabId = tabs[0].id;
      console.log('📤 向标签页发送命令:', tabId, '消息类型:', message.type);

      // 处理不同类型的消息
      if (message.type === 'identify') {
        console.log('🔍 处理identify消息');
        // 响应识别消息
        sendMessageToWebSocket({
          type: 'identify_response',
          source: 'browser_extension',
          timestamp: Date.now()
        });
      } else {
        // 处理直接视频命令
        console.log('� 处理视频命令:', message.type);
        await handleVideoCommand(message, tabId);
      }
    }

    // 处理视频命令 (直接命令格式)
    async function handleVideoCommand(message: any, tabId: number) {
      console.log('🎯 处理视频命令:', message.type, '参数:', message);

      try {
        // 根据消息类型发送相应命令到内容脚本
        switch (message.type) {
          case 'take_screenshot':
            console.log('📸 发送截图命令');
            await browser.tabs.sendMessage(tabId, { type: 'take_screenshot' });
            break;

          case 'get_video_info':
            console.log('📊 发送获取视频信息命令');
            await browser.tabs.sendMessage(tabId, { type: 'get_video_info' });
            break;

          case 'toggle_play':
            console.log('⏯️ 发送播放/暂停命令');
            await browser.tabs.sendMessage(tabId, { type: 'toggle_play' });
            break;

          case 'play':
            console.log('▶️ 发送播放命令');
            await browser.tabs.sendMessage(tabId, { type: 'play' });
            break;

          case 'pause':
            console.log('⏸️ 发送暂停命令');
            await browser.tabs.sendMessage(tabId, { type: 'pause' });
            break;

          case 'seek':
            console.log('⏭️ 发送绝对跳转命令, time:', message.time);
            await browser.tabs.sendMessage(tabId, {
              type: 'seek',
              time: message.time
            });
            break;

          case 'seek_relative':
            console.log('⏩ 发送相对跳转命令, seconds:', message.seconds);
            await browser.tabs.sendMessage(tabId, {
              type: 'seek_relative',
              seconds: message.seconds
            });
            break;

          case 'set_playback_rate':
            console.log('🏃 发送设置播放速度命令, rate:', message.rate);
            await browser.tabs.sendMessage(tabId, {
              type: 'set_playback_rate',
              rate: message.rate
            });
            break;

          case 'generate_timestamp_link':
            console.log('🔗 发送生成时间戳链接命令');
            await browser.tabs.sendMessage(tabId, { type: 'generate_timestamp_link' });
            break;

          case 'open_video_with_timestamp':
            console.log('🌐 处理打开视频并跳转到时间戳命令');
            await handleOpenVideoWithTimestamp(message);
            break;

          case 'seek_to_timestamp':
            console.log('⏰ 处理在现有标签页中跳转到时间戳命令');
            await handleSeekToTimestamp(message);
            break;

          default:
            console.warn('❓ 未知的视频命令类型:', message.type);
        }
      } catch (error) {
        console.error('❌ 处理视频命令失败:', error);
      }
    }

    // 处理打开视频并跳转到时间戳的命令
    async function handleOpenVideoWithTimestamp(message: any) {
      const { url, timestamp } = message;
      try {
        console.log('🌐 打开视频URL:', url, '时间戳:', timestamp);

        if (!url) {
          console.error('❌ 缺少视频URL参数');
          return;
        }

        // 检查是否已有相同视频的标签页
        const existingTab = await findVideoTab(url);

        if (existingTab && existingTab.url) {
          console.log('✅ 找到现有标签页，检查是否为同一视频');

          // 比较基础URL（忽略时间戳参数）以确定是否为同一视频
          const currentUrl = new URL(existingTab.url);
          const targetUrl = new URL(url);

          // 移除时间戳相关参数进行比较
          const currentUrlForComparison = new URL(currentUrl.toString());
          const targetUrlForComparison = new URL(targetUrl.toString());

          // 移除常见的时间戳参数
          ['t', 'time', 'start', 'at'].forEach(param => {
            currentUrlForComparison.searchParams.delete(param);
            targetUrlForComparison.searchParams.delete(param);
          });

          // 移除hash中的时间戳
          if (currentUrlForComparison.hash.includes('t=')) {
            currentUrlForComparison.hash = '';
          }
          if (targetUrlForComparison.hash.includes('t=')) {
            targetUrlForComparison.hash = '';
          }

          // 特殊处理不同平台的URL比较
          const currentHostname = currentUrlForComparison.hostname.toLowerCase();
          const targetHostname = targetUrlForComparison.hostname.toLowerCase();

          let isSameVideo = false;

          if (currentHostname === targetHostname) {
            if (currentHostname.includes('youtube.com') || currentHostname.includes('youtu.be')) {
              // YouTube: 比较视频ID
              const currentVideoId = currentUrl.searchParams.get('v') ||
                                   (currentHostname.includes('youtu.be') ? currentUrl.pathname.slice(1) : null);
              const targetVideoId = targetUrl.searchParams.get('v') ||
                                  (targetHostname.includes('youtu.be') ? targetUrl.pathname.slice(1) : null);
              isSameVideo = currentVideoId === targetVideoId && currentVideoId !== null;
            } else if (currentHostname.includes('bilibili.com')) {
              // Bilibili: 比较视频路径（去除查询参数）
              const currentPath = currentUrlForComparison.pathname;
              const targetPath = targetUrlForComparison.pathname;
              isSameVideo = currentPath === targetPath;
            } else {
              // 其他平台: 比较完整URL（已移除时间戳参数）
              isSameVideo = currentUrlForComparison.toString() === targetUrlForComparison.toString();
            }
          }

          console.log('🔍 URL比较结果:', {
            currentUrl: currentUrlForComparison.toString(),
            targetUrl: targetUrlForComparison.toString(),
            isSameVideo,
            platform: currentHostname
          });

          if (isSameVideo && timestamp > 0) {
            console.log('🎯 同一视频，使用内页跳转避免刷新');
            // 同一视频，使用seek命令避免页面刷新
            await handleSeekToTimestamp({ url, timestamp });
            return;
          } else {
            console.log('🔄 不同视频或无时间戳，更新URL');
            // 不同视频，需要更新URL
            const timestampUrl = generateTimestampUrl(url, timestamp || 0);
            await browser.tabs.update(existingTab.id!, {
              url: timestampUrl,
              active: true
            });
            // 聚焦到该标签页的窗口
            if (existingTab.windowId) {
              await browser.windows.update(existingTab.windowId, { focused: true });
            }
          }
        } else {
          console.log('🆕 创建新标签页');
          // 生成带时间戳的URL
          const timestampUrl = generateTimestampUrl(url, timestamp || 0);
          console.log('🔗 生成的时间戳URL:', timestampUrl);

          // 创建新标签页
          await browser.tabs.create({
            url: timestampUrl,
            active: true
          });
        }

        // 发送成功响应到Flutter (新的统一报告格式)
        const finalUrl = existingTab ? (existingTab.url || url) : generateTimestampUrl(url, timestamp || 0);
        sendMessageToWebSocket({
          type: 'report',
          reportType: 'browser_navigation',
          status: 'success',
          action: 'open_video_with_timestamp',
          data: {
            url: finalUrl,
            originalUrl: url,
            timestamp: timestamp || 0
          },
          message: '视频已在浏览器中打开并跳转到指定时间',
          timestamp: Date.now()
        });

      } catch (error) {
        console.error('❌ 打开视频失败:', error);
        sendMessageToWebSocket({
          type: 'report',
          reportType: 'browser_navigation',
          status: 'error',
          action: 'open_video_with_timestamp',
          data: {
            originalUrl: url,
            timestamp: timestamp || 0
          },
          message: (error as Error).message || '打开视频失败',
          timestamp: Date.now()
        });
      }
    }

    // 处理在现有标签页中跳转到时间戳的命令
    async function handleSeekToTimestamp(message: any) {
      const { url, timestamp } = message;
      try {
        console.log('⏰ 在现有标签页中跳转:', url, '时间戳:', timestamp);

        if (!url) {
          console.error('❌ 缺少视频URL参数');
          return;
        }

        // 查找匹配的标签页
        const existingTab = await findVideoTab(url);

        if (existingTab && existingTab.id) {
          console.log('✅ 找到匹配的标签页，发送跳转命令');

          // 聚焦到该标签页
          await browser.tabs.update(existingTab.id, { active: true });
          if (existingTab.windowId) {
            await browser.windows.update(existingTab.windowId, { focused: true });
          }

          // 发送跳转命令到内容脚本
          await browser.tabs.sendMessage(existingTab.id, {
            type: 'seek',
            time: timestamp || 0
          });

          // 发送成功响应到Flutter (新的统一报告格式)
          sendMessageToWebSocket({
            type: 'report',
            reportType: 'browser_navigation',
            status: 'success',
            action: 'seek_to_timestamp',
            data: {
              url: url,
              timestamp: timestamp || 0
            },
            message: '浏览器视频已跳转到指定时间戳',
            timestamp: Date.now()
          });
        } else {
          console.log('❌ 未找到匹配的标签页，尝试打开新标签页');
          // 如果没有找到现有标签页，则打开新的
          await handleOpenVideoWithTimestamp(message);
        }

      } catch (error) {
        console.error('❌ 跳转时间戳失败:', error);
        sendMessageToWebSocket({
          type: 'report',
          reportType: 'browser_navigation',
          status: 'error',
          action: 'seek_to_timestamp',
          data: {
            url: url,
            timestamp: timestamp || 0
          },
          message: (error as Error).message || '跳转时间戳失败',
          timestamp: Date.now()
        });
      }
    }
  }
});
