use rinf::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};

#[derive(Deserialize, <PERSON>tSignal, Debug)]
pub struct DartRequest {
    pub interaction_id: String,
    pub msg_type: String,
    pub params: String,
}

#[derive(Deserialize, DartSignal, Debug)]
pub struct DartResponse {
    pub interaction_id: String,
    pub status: String,
    pub message: String,
    pub data: String,
}

#[derive(Serialize, RustSignal, Debug)]
pub struct RustRequest {
    pub interaction_id: String,
    pub msg_type: String,
    pub params: String,
}

#[derive(Serialize, RustSignal, Debug)]
pub struct RustResponse {
    pub interaction_id: String,
    pub status: String,
    pub message: String,
    pub data: String,
}

#[derive(Serialize, RustSignal, Debug)]
pub struct ProgressResponse {
    pub status: String,
    pub message: String,
    pub current: f64,
    pub total: f64,
}
