import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:subtitle/subtitle.dart';
import 'package:ulid/ulid.dart';

// 定义媒体类型分类
final imageExts = ['jpg', 'jpeg', 'png', 'gif'];
final audioExts = ['mp3', 'wav', 'm4a'];
final videoExts = ['mp4', 'avi', 'mov'];
final textExts = ['txt', 'md', 'html', 'docx'];

class TextParams extends GetxController {
  // 表单参数
  final parentDeck = ''.obs;
  final tags = <String>[].obs;
  final selectedFilePaths = <String>[].obs;

  // 表单控制器
  final parentDeckController = TextEditingController();
  final tagsController = TextEditingController();
}

class ImageParams extends GetxController {
  // 表单参数
  final parentDeck = ''.obs;
  final tags = <String>[].obs;
  final selectedFilePaths = <String>[].obs;

  // 表单控制器
  final parentDeckController = TextEditingController();
  final tagsController = TextEditingController();
}

class AudioParams extends GetxController {
  // 表单参数
  final parentDeck = ''.obs;
  final tags = <String>[].obs;
  final selectedFilePaths = <String>[].obs;

  // 表单控制器
  final parentDeckController = TextEditingController();
  final tagsController = TextEditingController();
}

class VideoParams extends GetxController {
  // 表单参数
  final parentDeck = ''.obs;
  final tags = <String>[].obs;
  final selectedFilePaths = <String>[].obs;

  // 表单控制器
  final parentDeckController = TextEditingController();
  final tagsController = TextEditingController();
}

class MediaCardPageController extends GetxController {
  // 已有数据
  List<Map<String, String>> get mediaTypeList => [
    {"label": 'anki.media_card.text'.tr, "value": "text"},
    {"label": 'anki.media_card.image'.tr, "value": "image"},
    {"label": 'anki.media_card.audio'.tr, "value": "audio"},
    {"label": 'anki.media_card.video'.tr, "value": "video"},
  ];
  final suffixListData = <Map<String, String>>[
    {"label": "html", "value": "html"},
    {"label": "txt", "value": "txt"},
    {"label": "md", "value": "md"},
    {"label": "docx", "value": "docx"},
    {"label": "jpg", "value": "jpg"},
    {"label": "jpeg", "value": "jpeg"},
    {"label": "png", "value": "png"},
    {"label": "gif", "value": "gif"},
    {"label": "mp3", "value": "mp3"},
    {"label": "mp4", "value": "mp4"},
    {"label": "m4a", "value": "m4a"},
    {"label": "wav", "value": "wav"},
    {"label": "avi", "value": "avi"},
    {"label": "mov", "value": "mov"},
  ].obs;
  List<Map<String, String>> get matchTypeList => [
    {"label": 'anki.media_card.none'.tr, "value": ""},
    {"label": 'anki.media_card.filename'.tr, "value": "filename"},
    {"label": 'anki.media_card.file_content'.tr, "value": "content"},
  ];
  final videoOutputFormatList = <Map<String, String>>[
    {"label": "mp4", "value": "mp4"},
    {"label": "webm", "value": "webm"},
  ].obs;
  final tabController = ShadTabsController(value: 'qa');

  // 表单参数
  final parentDeck = 'anki.media_card.guru_import'.tr.obs;
  final isCreateSubDeck = false.obs;
  final cardModel = "Kevin Choice Card v2".obs;
  final mediaType = "image".obs;
  final mediaFolder = "".obs;
  final suffixList = <String>[].obs;
  final fieldMappings = <String, String?>{}.obs;
  final subtitleFile = "".obs;
  final mediaFile = "".obs;
  final isAdvanced = false.obs;
  final timeDelta = 0.obs;
  final padLeft = 0.obs;
  final padRight = 0.obs;
  final startAt = "".obs;
  final endAt = "".obs;
  final videoOutputFormat = "mp4".obs;
  final ffmpegPath = "".obs;
  final tags = <String>[].obs;

  // 新增的媒体列表定义
  final mediaList = <String>[].obs;

  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final ankiConnectController = Get.find<AnkiConnectController>();
  @override
  void onInit() async {
    super.onInit();

    // Check if the AnkiConnectController data is loaded, if not, load it
    if (ankiConnectController.modelList.isEmpty ||
        ankiConnectController.parentDeckList.isEmpty) {
      await ankiConnectController.resetAnkiConnectData();
    }

    // Now set the initial values with null checks
    if (ankiConnectController.modelList.isNotEmpty) {
      cardModel.value = ankiConnectController.modelList[0];
    }
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }

    ffmpegPath.value = settingController.ffmpegPath.value;
  }

  Future<void> updateFieldList(String modelName) async {
    try {
      await ankiConnectController.updateFieldList(modelName);
      fieldMappings.clear();
      update();
    } catch (e) {
      logger.e("updateFieldList error: $e");
    }
  }

  Future<void> submit(BuildContext context) async {
    try {
      final exportMode = settingController.getExportCardMode();
      progressController.reset(
        showOutputHint: exportMode == "apkg",
        numberButtons: exportMode == "apkg" ? 2 : 0,
      );

      if (tabController.selected == "qa") {
        if (mediaFolder.value.isEmpty) {
          showToastNotification(context, "anki.media_card.please_select_media_directory".tr, "", type: "error");
          return;
        }
        if (suffixList.isEmpty) {
          showToastNotification(context, "anki.media_card.please_select_file_suffix".tr, "", type: "error");
          return;
        }
        progressController.showProgressDialog(context);
        // 递归扫描媒体目录
        final mediaDir = Directory(mediaFolder.value);
        if (!await mediaDir.exists()) {
          showToastNotification(context, "anki.media_card.media_directory_not_exist".tr, "", type: "error");
          return;
        }

        // 收集所有匹配文件
        final files = await _scanMediaFiles(mediaDir);
        if (files.isEmpty) {
          showToastNotification(context, "anki.media_card.no_matching_files_found".tr, "", type: "error");
          return;
        }

        // 按子目录分组（当开启创建子牌组时）
        final fileGroups = isCreateSubDeck.value
            ? _groupFilesBySubdirectory(files)
            : {"": files};

        // 生成Anki卡片
        final notes = <AnkiNote>[];
        mediaList.clear();

        for (final entry in fileGroups.entries) {
          final subDeck = entry.key.isNotEmpty
              ? "${parentDeck.value}::${entry.key}"
              : parentDeck.value;

          for (final file in entry.value) {
            try {
              progressController.updateProgress(
                status: "running",
                message: "anki.media_card.processing_file".trParams({'filename': PathUtils(file).name}),
                current: 0.0,
                total: 100.0,
              );

              // 生成字段内容
              final fields = _generateFields(file);
              final note = AnkiNote(
                deckName: subDeck,
                modelName: cardModel.value,
                fields: fields,
                tags: tags.toList(),
              );
              notes.add(note);
            } catch (e) {
              logger.e("处理文件失败: $file - $e");
            }
          }
        }

        // 导入Anki
        if (notes.isNotEmpty) {
          await AnkiConnectController()
              .generateAndImportCards(notes, mediaList: mediaList);
        } else {
          progressController.updateProgress(
            status: "error",
            message: "anki.media_card.no_valid_cards_generated".tr,
            current: 0.0,
            total: 100.0,
          );
        }
      } else if (tabController.selected == "subtitle") {
        await _processSubtitle(context);
      }
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error",
        message: "anki.media_card.operation_failed".trParams({'error': e.toString()}),
      );
    }
  }

  Future<void> _processSubtitle(BuildContext context) async {
    // 验证必要参数
    if (subtitleFile.value.isEmpty || mediaFile.value.isEmpty) {
      showToastNotification(context, "anki.media_card.please_select_subtitle_and_media_files".tr, "", type: "error");
      return;
    }
    if (PathUtils.isDesktop && ffmpegPath.value.isEmpty) {
      showToastNotification(context, "anki.media_card.please_configure_ffmpeg_path".tr, "", type: "error");
      return;
    }
    progressController.showProgressDialog(context);

    try {
      // 解析字幕文件
      final subtitle = await _parseSubtitle();
      final filteredSubtitle = _filterByTimeRange(subtitle);
      // 生成卡片数据
      final notes = <AnkiNote>[];
      final mediaPaths = <String>[];

      for (var i = 0; i < filteredSubtitle.length; i++) {
        final entry = filteredSubtitle[i];
        progressController.updateProgress(
          status: "running",
          current: i.toDouble(),
          total: filteredSubtitle.length.toDouble(),
          message: "anki.media_card.processing".tr,
        );
        final clipPath = await _generateMediaClip(entry);
        mediaPaths.add(clipPath);

        notes.add(AnkiNote(
          deckName: parentDeck.value,
          modelName: "Kevin Media Card",
          fields: _buildNoteFields(entry, clipPath),
          tags: tags.toList(),
        ));
      }
      logger.w("notes: $notes");
      // 导入卡片
      logger.i("mediaPaths: $mediaPaths");
      if (notes.isNotEmpty) {
        await AnkiConnectController().generateAndImportCards(notes,
            mediaList: mediaPaths, isDelete: false);
        // 删除临时媒体文件
        for (final path in mediaPaths) {
          try {
            final file = File(path);
            if (file.existsSync()) {
              await file.delete();
              logger.d('Deleted temporary media file: $path');
            }
          } catch (e, stack) {
            logger.e('Failed to delete $path: $e\n$stack');
            // 不中断流程，继续删除其他文件
          }
        }
      }
    } catch (e) {
      logger.e("失败: $e");
      progressController.updateProgress(
        status: "error",
        message: "$e",
      );
    }
  }

  Future<List<Subtitle>> _parseSubtitle() async {
    final file = File(subtitleFile.value);
    final controller = SubtitleController(
      provider: SubtitleProvider.fromFile(file),
    );
    await controller.initial();
    return controller.subtitles;
  }

  List<Subtitle> _filterByTimeRange(List<Subtitle> subtitles) {
    if (startAt.value.isEmpty && endAt.value.isEmpty) {
      return subtitles;
    }
    logger.w("startAt: $startAt.value, endAt: $endAt.value");
    final start = parseTimeToMs(startAt.value);
    final end = parseTimeToMs(endAt.value);

    return subtitles.where((s) {
      final sStart = s.start.inMilliseconds + timeDelta.value;
      final sEnd = s.end.inMilliseconds + timeDelta.value;
      return (start == 0 || sStart >= start) && (end == 0 || sEnd <= end);
    }).toList();
  }

  Future<String> _generateMediaClip(Subtitle entry) async {
    try {
      // final outputDir = await PathUtils.tempDir;
      final outputDir = await PathUtils.downloadDir;
      final mediaPath = mediaFile.value;

      // 验证原始文件存在性
      final inputFile = File(mediaPath);
      if (!await inputFile.exists()) {
        throw Exception('anki.media_card.input_file_not_exist'.trParams({'path': mediaPath}));
      }

      final mediaExt =
          PathUtils(mediaPath).extension.toLowerCase().replaceAll(".", "");
      final isVideo = videoExts.contains(mediaExt);
      final isAudio = audioExts.contains(mediaExt);

      // 生成输出路径（使用URI编码处理特殊字符）
      final clipName =
          'clip_${Ulid()}.${isVideo ? videoOutputFormat.value : (isAudio ? 'mp3' : videoOutputFormat.value)}';
      final outputPath = PathUtils.join([outputDir, clipName]);

      final startMs =
          entry.start.inMilliseconds + timeDelta.value - padLeft.value;
      final endMs = entry.end.inMilliseconds + timeDelta.value + padRight.value;

      // 构建FFmpeg命令参数
      List<String> arguments = [
        "-i",
        mediaPath,
        "-ss",
        msToTime(startMs),
        "-to",
        msToTime(endMs),
        "-y",
        outputPath,
      ];
      if (isVideo && videoOutputFormat.value == "webm") {
        arguments = [
          "-i",
          mediaPath,
          "-ss",
          msToTime(startMs),
          "-to",
          msToTime(endMs),
          "-c:v",
          "libvpx-vp9",
          "-crf",
          "30",
          "-b:v",
          "0",
          // "-b:a",
          // "libopus",
          "-y",
          outputPath,
        ];
      }
      logger.i("执行FFmpeg命令: ffmpeg ${arguments.join(" ")}");
      // if (isVideo && videoOutputFormat.value == "webm") {
      final process = await Process.run(
        ffmpegPath.value,
        arguments,
      );
      if (process.exitCode != 0) {
        throw Exception('anki.media_card.ffmpeg_processing_failed'.trParams({'error': process.stderr.toString()}));
      }
      return outputPath;
    } catch (e) {
      logger.e('媒体剪辑生成失败: $e');
      rethrow;
    }
  }

  List<String> _buildNoteFields(Subtitle entry, String clipPath) {
    final isVideo = videoExts
        .contains(PathUtils(mediaFile.value).extension.replaceAll(".", ""));
    final p = PathUtils(clipPath);
    return [
      Ulid().toString(),
      isVideo ? p.name : '',
      !isVideo ? p.name : '',
      entry.data,
      '',
      ''
    ];
  }

  /// 递归扫描媒体文件
  Future<List<String>> _scanMediaFiles(Directory dir) async {
    final files = <String>[];
    await for (final entity in dir.list(recursive: true)) {
      if (entity is File) {
        final ext =
            PathUtils(entity.path).extension.toLowerCase().replaceAll(".", "");
        if (suffixList.contains(ext)) {
          files.add(entity.path);
        }
      }
    }
    return files;
  }

  /// 按子目录分组文件
  Map<String, List<String>> _groupFilesBySubdirectory(List<String> files) {
    final groups = <String, List<String>>{};
    final mediaPath = mediaFolder.value;

    for (final file in files) {
      final parentDir = PathUtils(file).parent;
      final relativePath =
          parentDir.substring(mediaPath.length).replaceAll("\\", "/");
      final subDir =
          relativePath.split("/").where((s) => s.isNotEmpty).join("::");

      groups.putIfAbsent(subDir, () => []).add(file);
    }
    return groups;
  }

  /// 根据字段映射生成字段内容
  List<String> _generateFields(String filePath) {
    final fields =
        List<String>.filled(ankiConnectController.fieldList.length, "");
    final pathUtils = PathUtils(filePath);
    final ext = pathUtils.extension.toLowerCase().replaceAll(".", "");

    // 判断文件类型
    final isImage = imageExts.contains(ext);
    final isAudio = audioExts.contains(ext);
    final isVideo = videoExts.contains(ext);
    final isText = textExts.contains(ext);

    // 获取文件名（不带扩展名）
    final fileName = pathUtils.stem;
    // 获取媒体文件名（带扩展名）
    final mediaName = pathUtils.name;

    for (int i = 0; i < ankiConnectController.fieldList.length; i++) {
      final fieldName = ankiConnectController.fieldList[i];
      final mappingType = fieldMappings[fieldName];

      switch (mappingType) {
        case "filename":
          fields[i] = fileName;
          break;

        case "content":
          if (isImage) {
            fields[i] = '<img src="$mediaName"/>';
          } else if (isAudio || isVideo) {
            fields[i] = '[sound:$mediaName]';
          } else if (isText) {
            fields[i] = File(filePath).readAsStringSync();
          }
          break;
        default:
          fields[i] = ""; // 未映射的字段留空
      }
    }

    // 添加媒体文件到列表（图片/音频/视频需要添加）
    if (isImage || isAudio || isVideo) {
      mediaList.add(filePath);
    }

    return fields;
  }
}
