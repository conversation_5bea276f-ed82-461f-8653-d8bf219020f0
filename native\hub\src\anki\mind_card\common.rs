#![allow(unused)]

use crate::anki::models::{get_kevin_mindmap_card_model, AnkiNote};
use indexmap::indexmap;
use scraper::{Html, Selector};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::default::Default;
use std::fs;
use std::io;
use std::path::Path;
use std::path::PathBuf;
use tempfile::TempDir;
use ulid::Ulid;
use zip::ZipArchive;

#[derive(Debug, Serialize, Deserialize)]
pub struct MindNode {
    pub id: String,
    pub content: String, // 包含图片和文本的HTML内容
    pub children: Vec<MindNode>,
}

impl MindNode {
    pub fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            // "id": self.id, // 忽略id 字段
            "content": self.content,
            "children": self.children.iter().map(|child| child.to_json()).collect::<Vec<_>>()
        })
    }

    pub fn to_js(&self) -> String {
        let json = self.to_json();
        format!(
            "var mindmap_data = {};window.sessionStorage.setItem(\"mindmap_data\", JSON.stringify(mindmap_data));",
            serde_json::to_string(&json).unwrap_or_default()
        )
    }
}

#[derive(Debug, Clone, PartialEq)]
pub enum StyleAttribute {
    Colors(Vec<String>),
    Highlights(Vec<String>),
    Italic,
    Bold,
    StrikeThrough,
    Underline,
}

pub struct ClozeContext {
    count: usize,
}

impl ClozeContext {
    pub fn new() -> Self {
        Self { count: 0 }
    }

    pub fn next_id(&mut self) -> usize {
        self.count += 1;
        self.count
    }
}

/// 添加一个辅助函数来标准化颜色格式
pub fn normalize_color(color: &str) -> String {
    // 移除所有空格并转换为小写
    let color = color.trim().to_lowercase();
    // 如果颜色是 8 位格式 (#rrggbbaa)，截取前 7 位 (#rrggbb)
    if color.len() == 9 && color.starts_with('#') {
        color[..7].to_string()
    } else {
        color
    }
}

/// 将 MindNode 转换为 AnkiNotes
///
/// # Arguments
/// * `node` - 思维导图节点
/// * `deck_name` - 牌组名称
/// * `tags` - 标签列表
///
/// # Returns
/// * `Vec<AnkiNote>` - Anki 笔记列表
pub fn mind_node_to_anki_notes(
    map_id: String,
    node: &MindNode,
    deck_name: String,
    tags: Vec<String>,
) -> Vec<AnkiNote> {
    let mut notes = Vec::new();

    // 递归处理节点
    fn process_node(
        node: &MindNode,
        path: Vec<usize>,
        parent_texts: Vec<String>,
        map_id: &str,
        deck_name: &str,
        tags: &[String],
        notes: &mut Vec<AnkiNote>,
    ) {
        // 如果是叶子节点则直接返回（没有子节点）
        if node.children.is_empty() {
            return;
        }

        // 提取当前节点的纯文本
        let current_text = extract_text_from_html(&node.content);

        // 构建 Source 路径
        let mut source_texts = parent_texts.clone();
        source_texts.push(current_text.clone());
        let source = source_texts.join(" -> ");

        // 构建子节点的 Back 内容
        let back = if !node.children.is_empty() {
            generate_details_html(node, 0)
        } else {
            String::new()
        };
        let path_str = format!(
            "[{}]",
            path.iter()
                .map(|i| i.to_string())
                .collect::<Vec<_>>()
                .join(",")
        );
        // 构建字段映射
        let fields = indexmap! {
            "ID" => node.id.clone(),
            "MapID" => map_id.to_string(),
            "Path" => path_str.clone(),
            "Front" => node.content.clone(),
            "Back" => back,
            "Source" => source,
            "Notes" => String::new(),
        };
        let mut guid = String::new();
        if node.id.clone().starts_with("xmind_")
            || node.id.clone().starts_with("zhixi_")
            || node.id.clone().starts_with("mubu_")
        {
            guid = format!("{}", node.id.clone());
        } else {
            guid = format!("{}__{}", map_id, path_str.clone());
        }
        // 创建笔记
        let note = AnkiNote {
            deck_name: deck_name.to_string(),
            model_name: "Kevin Mindmap Card v3".to_string(),
            fields: fields
                .into_iter()
                .map(|(key, value)| value.to_string())
                .collect(),
            tags: Some(tags.to_vec()),
            guid: Some(guid.to_string()),
        };
        notes.push(note);

        // 递归处理子节点
        for (i, child) in node.children.iter().enumerate() {
            let mut child_path = path.clone();
            child_path.push(i);
            process_node(
                child,
                child_path,
                source_texts.clone(),
                map_id,
                deck_name,
                tags,
                notes,
            );
        }
    }

    // 从根节点开始处理
    process_node(node, vec![], vec![], &map_id, &deck_name, &tags, &mut notes);

    notes
}

/// 从 HTML 内容中提取纯文本
pub fn extract_text_from_html(html: &str) -> String {
    let fragment = Html::parse_fragment(html);
    let text = fragment.root_element().text().collect::<Vec<_>>().join(" ");
    text.split_whitespace().collect::<Vec<_>>().join(" ")
}

/// 递归生成带展开折叠的多级节点HTML
fn generate_details_html(node: &MindNode, depth: usize) -> String {
    let mut html = String::new();
    let padding = depth * 20; // 每层缩进20px

    for child in &node.children {
        if child.children.is_empty() {
            // 叶子节点
            html.push_str(&format!(r#"{}"#, child.content));
        } else {
            // 可展开节点
            html.push_str(&format!(
                r#"
                <article>
                <details style="margin-left: 1rem;">
                    <summary>
                    {}
                    </summary>
                    {}
                </details>
                </article>
                "#,
                child.content,
                generate_details_html(child, depth + 1)
            ));
        }
    }
    html
}
