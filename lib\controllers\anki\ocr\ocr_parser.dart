import 'dart:math' as math;
import 'dart:convert';
import 'package:flutter/material.dart' show Rect;
import 'package:anki_guru/controllers/common.dart';

class Point<T extends num> {
  final T x;
  final T y;
  const Point(this.x, this.y);
  @override
  String toString() => 'Point($x, $y)';
}

class OcrBlock {
  final String text;
  final double score;
  final List<Point<int>> boxPoints;

  OcrBlock({required this.text, required this.score, required this.boxPoints});

  factory OcrBlock.fromJson(Map<String, dynamic> json) {
    final boxData = (json['box_points'] as List).map((point) {
      final pointMap = point as Map<String, dynamic>;
      return Point<int>(pointMap['x'] as int, pointMap['y'] as int);
    }).toList();
    return OcrBlock(
      text: json['text'] as String,
      score: (json['score'] as num).toDouble(),
      boxPoints: boxData,
    );
  }

  // 获取文本块的边界矩形
  Rect getBoundingRect() {
    final xCoords = boxPoints.map((p) => p.x);
    final yCoords = boxPoints.map((p) => p.y);
    final minX = xCoords.reduce(math.min);
    final minY = yCoords.reduce(math.min);
    final maxX = xCoords.reduce(math.max);
    final maxY = yCoords.reduce(math.max);
    return Rect.fromLTWH(minX.toDouble(), minY.toDouble(),
        (maxX - minX).toDouble(), (maxY - minY).toDouble());
  }
}

class ProcessedBlock {
  final String text;
  final List<Point<int>> box;
  final int xStart;
  final double yCenter;
  final int height;

  ProcessedBlock(
      {required this.text,
      required this.box,
      required this.xStart,
      required this.yCenter,
      required this.height});
}

class TextLine {
  final String text;
  final double yCenter;
  final int height;
  final int xStart;
  final int xEnd; // NEW: 增加行尾的x坐标

  TextLine({
    required this.text,
    required this.yCenter,
    required this.height,
    required this.xStart,
    required this.xEnd, // NEW: 在构造函数中要求xEnd
  });

  @override
  String toString() {
    // UPDATED: 更新toString以包含xEnd，方便调试
    return 'TextLine(text: "$text", yCenter: $yCenter, height: $height, xStart: $xStart, xEnd: $xEnd)';
  }
}

// ===================================================================
// 2. 统计和聚类辅助工具 (新)
// ===================================================================

class StatsUtils {
  static double median(List<double> numbers) {
    if (numbers.isEmpty) return 0.0;
    var sorted = List<double>.from(numbers)..sort();
    int middle = sorted.length ~/ 2;
    if (sorted.length % 2 == 1) {
      return sorted[middle];
    } else {
      return (sorted[middle - 1] + sorted[middle]) / 2.0;
    }
  }

  static Map<double, List<double>> kmeans1D(List<double> data,
      {int k = 3, int maxIters = 20}) {
    if (data.isEmpty) return {};
    if (data.length < k) {
      return {
        for (var d in data) d: [d]
      };
    }

    var uniqueData = data.toSet().toList()..sort();
    var centroids = <double>[];
    if (uniqueData.length <= k) {
      centroids.addAll(uniqueData);
    } else {
      for (int i = 0; i < k; i++) {
        centroids
            .add(uniqueData[(i * (uniqueData.length - 1) / (k - 1)).round()]);
      }
    }

    Map<double, List<double>> clusters = {};
    for (int iter = 0; iter < maxIters; iter++) {
      clusters = {for (var c in centroids) c: <double>[]};
      for (var point in data) {
        double minDistance = double.infinity;
        double closestCentroid = centroids[0];
        for (var centroid in centroids) {
          double distance = (point - centroid).abs();
          if (distance < minDistance) {
            minDistance = distance;
            closestCentroid = centroid;
          }
        }
        clusters[closestCentroid]!.add(point);
      }

      var newCentroids = <double>[];
      bool converged = true;
      centroids.sort();
      for (var centroid in centroids) {
        var clusterPoints = clusters[centroid]!;
        if (clusterPoints.isNotEmpty) {
          var newCentroid =
              clusterPoints.reduce((a, b) => a + b) / clusterPoints.length;
          newCentroids.add(newCentroid);
          if ((newCentroid - centroid).abs() > 1.0) {
            converged = false;
          }
        } else {
          newCentroids.add(centroid);
        }
      }

      if (converged) break;
      centroids = newCentroids..sort();
    }

    // 返回最终聚类结果（使用最新的中心点作为key）
    var finalClusters = {for (var c in centroids) c: <double>[]};
    for (var point in data) {
      double minDistance = double.infinity;
      double closestCentroid = centroids[0];
      for (var centroid in centroids) {
        double distance = (point - centroid).abs();
        if (distance < minDistance) {
          minDistance = distance;
          closestCentroid = centroid;
        }
      }
      finalClusters[closestCentroid]!.add(point);
    }
    return finalClusters;
  }
}

/// 对生成的文本进行清洗和格式化，删除汉字之间的空格，移除多余重复换行等
///
/// [text]: 需要清洗的原始文本。
/// [maxConsecutiveNewlines]: 允许的最大连续换行符数量。
String cleanUpText(String text, {int maxConsecutiveNewlines = 2}) {
  if (text.isEmpty) return "";
  // 删除汉字之间的空格
  text = text.replaceAll(RegExp(r'[\p{Script=Han}]'), '');
  // 移除多余重复换行
  text = text.replaceAll(RegExp(r'\n{2,}'), '\n');
  return text;
}

class OcrParser {
  /// **主入口函数**: 解析JSON字符串，为每个顶层元素生成独立的自然文本。
  /// 返回一个字符串列表，每个字符串对应一个顶层JSON元素的解析结果。
  static List<String> parse(
    String jsonString, {
    double yToleranceFactor = 0.5,
    double paragraphThreshold = 1.7,
    bool isConvertNewLine = true,
  }) {
    final List<String> resultTexts = [];
    try {
      // 步骤1: 解析外层JSON数组
      final List<dynamic> outerList = json.decode(jsonString);

      // 步骤2: 遍历每个内层JSON字符串并独立处理
      for (final innerJsonString in outerList) {
        if (innerJsonString is String) {
          final Map<String, dynamic> innerJson = json.decode(innerJsonString);

          if (innerJson.containsKey('blocks') && innerJson['blocks'] is List) {
            final blocksData = innerJson['blocks'] as List;

            // a. 结构化: 为当前文档创建OcrBlock列表
            final currentBlocks = blocksData
                .map((blockData) =>
                    OcrBlock.fromJson(blockData as Map<String, dynamic>))
                .toList();

            // b. 行分组: 对当前文档的blocks进行处理
            final lines = _groupBlocksIntoLines(currentBlocks,
                yToleranceFactor: yToleranceFactor);

            // c. 文本重构: 对当前文档的lines进行处理
            String naturalText = _reconstructText(lines);
            naturalText = cleanUpText(naturalText);
            if (isConvertNewLine) {
              naturalText = naturalText.replaceAll('\n', '<br>');
            }

            // d. 添加到结果列表
            resultTexts.add(naturalText);
          }
        }
      }
      return resultTexts;
    } catch (e, stackTrace) {
      print('Error parsing OCR JSON: $e');
      print('Stack trace: $stackTrace');
      // 返回一个包含错误信息的列表，以便调用者知道出了问题
      return ['Error: Invalid JSON format or data.'];
    }
  }

  /// **直接处理文本块**: 直接接收OcrBlock列表，而不需要JSON转换步骤。
  /// 返回一个处理后的自然段落文本。
  static String parseBlocks(
    List<OcrBlock> blocks, {
    double yToleranceFactor = 0.5,
    double paragraphThreshold = 1.7,
    bool isConvertNewLine = true,
  }) {
    try {
      // a. 行分组: 对文本块进行处理
      final lines =
          _groupBlocksIntoLines(blocks, yToleranceFactor: yToleranceFactor);
      logger.d(lines);
      // b. 文本重构: 对行进行处理
      String naturalText = _reconstructText(lines);
      naturalText = cleanUpText(naturalText);
      if (isConvertNewLine) {
        naturalText = naturalText.replaceAll('\n', '<br>');
      }

      return naturalText;
    } catch (e, stackTrace) {
      print('Error parsing OCR blocks: $e');
      print('Stack trace: $stackTrace');
      // 返回错误信息
      return 'Error: Failed to process text blocks.';
    }
  }

  // _groupBlocksIntoLines 和 _reconstructText 方法保持不变
  static List<TextLine> _groupBlocksIntoLines(
    List<OcrBlock> blocks, {
    double yToleranceFactor = 0.5,
  }) {
    if (blocks.isEmpty) return [];

    // ProcessedBlock的创建部分保持不变
    var processedBlocks = blocks.map((block) {
      final xCoords = block.boxPoints.map((p) => p.x);
      final yCoords = block.boxPoints.map((p) => p.y);
      final xStart = xCoords.reduce(math.min);
      final yCenter = yCoords.reduce((a, b) => a + b) / 4.0;
      final height = (block.boxPoints[3].y - block.boxPoints[0].y).abs();
      return ProcessedBlock(
          text: block.text,
          box: block.boxPoints,
          xStart: xStart,
          yCenter: yCenter,
          height: height);
    }).toList();

    processedBlocks.sort((a, b) => a.yCenter.compareTo(b.yCenter));

    final List<List<ProcessedBlock>> linesOfBlocks = [];
    if (processedBlocks.isNotEmpty) {
      var currentLineBlocks = [processedBlocks.first];
      for (int i = 1; i < processedBlocks.length; i++) {
        final prevBlock = currentLineBlocks.last;
        final currentBlock = processedBlocks[i];
        final yDiff = (currentBlock.yCenter - prevBlock.yCenter).abs();
        if (yDiff < prevBlock.height * yToleranceFactor) {
          currentLineBlocks.add(currentBlock);
        } else {
          linesOfBlocks.add(List.from(currentLineBlocks));
          currentLineBlocks = [currentBlock];
        }
      }
      linesOfBlocks.add(currentLineBlocks);
    }

    // UPDATED: 在创建TextLine时计算并传入xEnd
    return linesOfBlocks.map((lineBlocks) {
      lineBlocks.sort((a, b) => a.xStart.compareTo(b.xStart));

      final lineText = lineBlocks.map((b) => b.text).join(' ');
      final avgYCenter =
          lineBlocks.map((b) => b.yCenter).reduce((a, b) => a + b) /
              lineBlocks.length;
      final maxHeight = lineBlocks.map((b) => b.height).reduce(math.max);
      final xStart = lineBlocks.first.xStart;

      // NEW: 计算行的结束x坐标
      // 它是最后一个文本块的最右侧坐标
      final xEnd = lineBlocks.last.box.map((p) => p.x).reduce(math.max);

      return TextLine(
        text: lineText,
        yCenter: avgYCenter,
        height: maxHeight,
        xStart: xStart,
        xEnd: xEnd, // NEW: 传入xEnd
      );
    }).toList();
  }

// --- 全新的、基于高级规则的重构函数 ---
  static String _reconstructText(List<TextLine> lines) {
    if (lines.length < 2) {
      return lines.isEmpty ? "" : lines.first.text;
    }

    // ================== 1. 预计算与统计分析 ==================

    final lineHeights = lines.map((l) => l.height.toDouble()).toList();
    final avgLineHeight =
        lineHeights.reduce((a, b) => a + b) / lineHeights.length;

    final lineWidths = lines
        .map((l) => (l.xEnd - l.xStart).toDouble())
        .where((w) => w > 0)
        .toList();
    final avgLineWidth = lineWidths.isEmpty
        ? 500.0
        : lineWidths.reduce((a, b) => a + b) / lineWidths.length;

    final titleHeightThreshold = avgLineHeight * 1.3;
    final titleWidthThreshold = avgLineWidth * 0.8;

    final verticalGaps = <double>[];
    for (int i = 0; i < lines.length - 1; i++) {
      final gap = lines[i + 1].yCenter - lines[i].yCenter;
      if (gap > 0) verticalGaps.add(gap);
    }
    final medianVerticalGap = StatsUtils.median(verticalGaps);
    final statsParagraphThreshold = medianVerticalGap * 1.8;
    final ratioParagraphThreshold = avgLineHeight * 1.5;

    final xStarts = lines.map((l) => l.xStart.toDouble()).toList();
    final significantXStarts = xStarts.where((x) => x > 10).toList();
    final indentClusters = StatsUtils.kmeans1D(significantXStarts, k: 3);

    int getIndentCluster(double x) {
      if (indentClusters.isEmpty) return 0;
      final centroids = indentClusters.keys.toList();
      if (centroids.isEmpty) return 0;
      double closestCentroid =
          centroids.reduce((a, b) => (x - a).abs() < (x - b).abs() ? a : b);
      return centroids.indexOf(closestCentroid);
    }

    // ================== 2. 规则定义 ==================

    final buffer = StringBuffer();
    // **FIX**: 移除了冒号，以避免对键值对格式的错误判断
    final endOfSentenceChars = {'.', '?', '!', '。', '？', '！'};
    final listMarkers = [
      RegExp(r'^\d{1,3}\.(?!\d)'),
      RegExp(r'^[A-Z]\.(?!\w)'),
      RegExp(r'^\(\s*\d+\s*\)'),
      RegExp(r'^\(\s*[a-zA-Z]\s*\)'),
      RegExp(r'^[•\-*▪➢■●·]'),
      RegExp(r'^[IVXLCDM]+\.|[ivxlcdm]+\.'),
    ];

    // ================== 3. 遍历与规则应用 ==================

    for (int i = 0; i < lines.length; i++) {
      final currentLine = lines[i];
      buffer.write(currentLine.text);

      if (i == lines.length - 1) break;
      final nextLine = lines[i + 1];
      final trimmedCurrentText = currentLine.text.trim();

      String? breakType;

      // --- 规则优先级从高到低 ---

      // **规则 0: 智能标题检测**
      bool hasHighHeight = currentLine.height > titleHeightThreshold;
      bool hasShortWidth =
          (currentLine.xEnd - currentLine.xStart) < titleWidthThreshold;
      bool endsWithoutPunctuation = trimmedCurrentText.isNotEmpty &&
          !endOfSentenceChars.contains(
              trimmedCurrentText.substring(trimmedCurrentText.length - 1));

      if (hasHighHeight && (hasShortWidth || endsWithoutPunctuation)) {
        breakType = '\n\n';
      }

      // **规则 1: 列表项开始**
      if (breakType == null) {
        bool nextIsListItem =
            listMarkers.any((re) => re.hasMatch(nextLine.text.trim()));
        if (nextIsListItem) {
          bool currentIsListItem =
              listMarkers.any((re) => re.hasMatch(trimmedCurrentText));
          breakType = currentIsListItem ? '\n' : '\n\n';
        }
      }

      // **规则 2: 句子结束**
      if (breakType == null &&
          trimmedCurrentText.isNotEmpty &&
          endOfSentenceChars.contains(
              trimmedCurrentText.substring(trimmedCurrentText.length - 1))) {
        breakType = '\n';
      }

      // **规则 3: 显著的缩进变化**
      if (breakType == null) {
        final currentCluster = getIndentCluster(currentLine.xStart.toDouble());
        final nextCluster = getIndentCluster(nextLine.xStart.toDouble());
        final xDiff = (currentLine.xStart - nextLine.xStart).abs();

        if (xDiff > 20 && currentCluster != nextCluster) {
          breakType = '\n';
        }
      }

      // **规则 4: 垂直间距过大 (逻辑更新)**
      final verticalGap = nextLine.yCenter - currentLine.yCenter;
      // **FIX**: 如果间距大于任一阈值，就认为是一个硬换行
      if (breakType == null &&
          (verticalGap > statsParagraphThreshold ||
              verticalGap > ratioParagraphThreshold)) {
        // 使用 '\n' 因为这更像是列表项之间的分隔，而不是一个全新的段落
        breakType = '\n';
      }

      // **规则 5: 断词修复 (英文场景)**
      if (breakType == null &&
          RegExp(r'[a-zA-Z]$').hasMatch(trimmedCurrentText) &&
          RegExp(r'^[a-z]').hasMatch(nextLine.text.trim())) {
        breakType = '';
      }

      // --- 应用决策 ---
      if (breakType != null) {
        buffer.write(breakType);
      } else {
        buffer.write(' ');
      }
    }

    return buffer.toString();
  }
}
