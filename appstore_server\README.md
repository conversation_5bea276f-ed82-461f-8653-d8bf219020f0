
### 启动后端
```
docker-compose up --build -d

```

## 内购流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant iOSApp as iOS App (客户端)
    participant MyBackend as 我的后端服务器
    participant AppleAppStore as Apple App Store 服务器

    User->>iOSApp: 1. 发起内购请求
    iOSApp->>AppleAppStore: 2. 请求购买商品 (StoreKit API)
    AppleAppStore-->>User: 3. (可选) 提示用户输入Apple ID密码
    User-->>AppleAppStore: 4. (可选) 用户完成支付验证
    AppleAppStore-->>iOSApp: 5. 交易成功，提供交易收据 (Transaction Receipt)

    iOSApp->>MyBackend: 6. 发送收据和用户ID到我的后端服务器
    MyBackend->>AppleAppStore: 7. 后端将收据发送到Apple验证服务器进行验证 (生产URL优先)
    AppleAppStore-->>MyBackend: 8. Apple验证服务器返回收据验证结果 (包含状态码、购买信息等)

    alt 验证成功 (Status = 0)
        MyBackend->>MyBackend: 9a. 解析Apple响应，更新用户在数据库中的权益 (如解锁功能，更新订阅状态)
        MyBackend-->>iOSApp: 10a. 告知App购买成功及权益更新 (App可根据此信息更新UI/功能)
    else 验证失败 (Status != 0, 例如 21007/21008)
        MyBackend->>AppleAppStore: 9b. (如果状态码是 21007 或 21008) 切换验证URL (沙盒/生产) 并重试验证
        AppleAppStore-->>MyBackend: 10b. Apple验证服务器返回重试后的结果
        alt 重试成功 (Status = 0)
            MyBackend->>MyBackend: 11b. 解析Apple响应，更新用户权益
            MyBackend-->>iOSApp: 12b. 告知App购买成功及权益更新
        else 重试仍失败
            MyBackend->>MyBackend: 11c. 记录错误日志
            MyBackend-->>iOSApp: 12c. 告知App验证失败，附带错误信息
        end
    end

    iOSApp->>iOSApp: 13. (如果验证成功) 完成交易 (finishTransaction)
    iOSApp->>User: 14. 告知用户购买完成
    
```


## 事件类型

INITIAL_BUY: 用户首次购买订阅。
DID_RENEW: 订阅自动续期成功。
GRACE_PERIOD_EXPIRED: 订阅宽限期结束（通常是因为支付失败）。
DID_FAIL_TO_RENEW: 订阅续期失败。
DID_CHANGE_RENEWAL_STATUS: 续订状态改变（如用户开启/关闭自动续订）。
DID_CHANGE_RENEWAL_PREF: 订阅升级/降级/交叉销售。
PRICE_INCREASE_CONSENT: 价格上涨通知。
REFUND: 订阅被退款。
DID_REVOKE: 订阅被撤销（例如，Apple 发现欺诈行为）。
CANCELLATION: 订阅被用户取消。


## 数据库

### 环境变量

在`.env.production`和`.env.sandbox`文件中添加以下MongoDB相关配置：

```
# MongoDB配置
MONGO_URI=**************************************/
MONGO_DB_NAME=iap_production  # 或 iap_sandbox
MONGO_ROOT_USERNAME=admin     # 修改为安全的用户名
MONGO_ROOT_PASSWORD=password  # 修改为安全的密码
```

### 查看MongoDB日志

```bash
docker logs iap-mongodb
```

### 连接到MongoDB Shell

```bash
docker exec -it iap-mongodb mongosh -u admin -p password
```

### 备份MongoDB数据

```bash
docker exec -it iap-mongodb mongodump --out /data/backup --db iap_production -u admin -p password
``` 