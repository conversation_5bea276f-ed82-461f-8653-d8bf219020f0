use docx2html::DocxDocument;
use std::env;
use std::error::Error;
use std::path::Path;

fn main() -> Result<(), Box<dyn Error>> {
    let args: Vec<String> = env::args().collect();

    let (input_path, output_path) = match args.len() {
        1 => {
            println!("使用方法: {} <输入文件.docx> [输出文件.html]", args[0]);
            println!("未指定输入文件，使用默认测试文件");
            // 使用默认测试文件
            (
                String::from("/Users/<USER>/Documents/制卡素材/制卡测试/word制卡/挖空题测试_副本.docx"),
                String::from("output.html"),
            )
        }
        2 => {
            // 只提供了输入文件，使用默认输出文件名
            (args[1].clone(), String::from("output.html"))
        }
        _ => {
            // 提供了输入和输出文件
            (args[1].clone(), args[2].clone())
        }
    };

    println!("正在处理文档: {}", input_path);

    // 检查输入是文件还是目录
    let path = Path::new(&input_path);
    if !path.exists() {
        return Err(format!("错误: 输入文件 '{}' 不存在", input_path).into());
    }

    // 加载文档，判断是文件还是目录
    let doc = if path.is_file() {
        // 从文件加载
        DocxDocument::from_file(&input_path, Some(false))?
    } else if path.is_dir() {
        // 从解压后的目录加载
        DocxDocument::from_directory(&input_path, Some(false))?
    } else {
        return Err(format!("错误: 输入路径 '{}' 既不是文件也不是目录", input_path).into());
    };

    // 转换为 HTML
    let html = doc.to_html()?;

    // 保存转换后的 HTML
    std::fs::write(&output_path, html)?;

    println!("HTML转换完成，已保存到: {}", output_path);
    Ok(())
}
