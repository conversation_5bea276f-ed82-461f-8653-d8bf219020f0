# --- Stage 1: Builder for dependencies ---
# 这个阶段的目的是安装所有 Python 依赖，它会利用 Docker 缓存。
# 只有当 requirements.txt 发生变化时，这个阶段才会被重新构建。
FROM python:3.12-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# 设置工作目录
WORKDIR /app

# 复制 requirements.txt 到工作目录
COPY requirements.txt .

# 升级 pip 并安装所有依赖
# --no-cache-dir 减少缓存，以减小镜像层大小
# --break-system-packages (可选，如果遇到权限问题或pip警告)
RUN pip install --no-cache-dir --upgrade pip -i https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/  --trusted-host mirrors.aliyun.com


# --- Stage 2: Final image ---
# 这个阶段构建最终的生产镜像，它只包含必要的运行时文件。
FROM python:3.12-slim as final

# 安装MongoDB客户端工具用于健康检查
RUN apt-get update && apt-get install -y gnupg curl && \
    curl -fsSL https://pgp.mongodb.com/server-7.0.asc | \
    gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor && \
    echo "deb [ signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] http://repo.mongodb.org/apt/debian bullseye/mongodb-org/7.0 main" | \
    tee /etc/apt/sources.list.d/mongodb-org-7.0.list && \
    apt-get update && \
    apt-get install -y mongodb-mongosh && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 从 builder 阶段复制已安装的 Python 依赖
# /usr/local/lib/python3.9/site-packages 是 Python 在这个基础镜像中的默认包安装路径
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
# 新增: 从 builder 阶段复制已安装的可执行文件 (例如 gunicorn, uvicorn)
COPY --from=builder /usr/local/bin/ /usr/local/bin/
# 复制您的应用代码到容器中
# 这一步应放在 COPY dependencies 之后，以最大化缓存利用率。
# 如果只有应用代码变化，而 requirements.txt 不变，那么 Docker 可以跳过前一个阶段的构建。
COPY . .

# 创建日志目录并设置权限
RUN mkdir -p /app/logs && \
    chmod -R 777 /app/logs && \
    mkdir -p /tmp/logs && chmod 777 /tmp/logs

# 创建一个非 root 用户并切换到该用户，提高安全性
RUN adduser --system --group appuser
USER appuser

# 暴露应用监听的端口
EXPOSE 5000

# 启动 Gunicorn 服务器来运行 FastAPI 应用
# main:app 表示运行 main.py 文件中的 'app' FastAPI 实例
# --workers: 根据你的 CPU 核心数调整，通常 2*CPU_CORES + 1 是一个好的起点
# --worker-class: 指定使用 Uvicorn 的 worker 类来运行 ASGI 应用
# --bind: 绑定到所有网络接口和指定的端口
CMD ["gunicorn", "main:app", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:5000"]