// ignore_for_file: type=lint, type=warning
// Copyright (c) Facebook, Inc. and its affiliates
// SPDX-License-Identifier: MIT OR Apache-2.0

library serde;

import 'dart:convert' show utf8;
import 'dart:typed_data';

import 'package:meta/meta.dart';

part 'binary_deserializer.dart';
part 'binary_serializer.dart';
part 'bytes.dart';
part 'hash_utils.dart';
part 'int_128.dart';
part 'slice.dart';
part 'uint_128.dart';
part 'uint_64.dart';
part 'unit.dart';
