#include "include/flutter_machineid/flutter_machineid_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "flutter_machineid_plugin.h"

void FlutterMachineidPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  flutter_machineid::FlutterMachineidPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
