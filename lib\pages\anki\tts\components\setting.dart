import 'package:flutter/material.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/tts/index_controller.dart';

class SettingsPage extends GetView<CardTTSPageController> {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ShadDialog(
        title: Text('anki.tts.voice_settings'.tr),
        child: Material(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Obx(() => Column(
                  spacing: 8,
                  children: [
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      minVerticalPadding: 0,
                      title: Text('anki.tts.language'.tr, style: defaultTitleStyle),
                      subtitle: ShadSelect(
                        anchor: const ShadAnchor<PERSON><PERSON>(),
                        initialValue: controller.lang.value,
                        placeholder: Text('anki.tts.select_language'.tr),
                        options: controller.langOptions
                            .map((e) => ShadOption(
                                value: e['value']!, child: Text(e['label']!)))
                            .toList(),
                        selectedOptionBuilder: (context, value) {
                          return Text(controller.langOptions.firstWhere(
                              (e) => e['value'] == value)['label']!);
                        },
                        onChanged: (value) {
                          if (value != null) {
                            controller.lang.value = value;
                            controller.lang.value = value;
                            controller.roiVoiceOptions.value =
                                controller.voiceOptions[value] ?? [];
                            controller.voice.value =
                                controller.roiVoiceOptions.first['value']!;
                            // controller.update();
                          }
                        },
                      ),
                    ),
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      minVerticalPadding: 0,
                      title: Text('anki.tts.style'.tr, style: defaultTitleStyle),
                      subtitle: ShadSelect(
                        anchor: const ShadAnchorAuto(),
                        initialValue: controller.voice.value,
                        placeholder: Text('anki.tts.select_style'.tr),
                        options: controller.roiVoiceOptions
                            .map((e) => ShadOption(
                                value: e['value']!, child: Text(e['label']!)))
                            .toList(),
                        selectedOptionBuilder: (context, value) {
                          return Text(controller.roiVoiceOptions.firstWhere(
                              (e) => e['value'] == value)['label']!);
                        },
                        onChanged: (value) {
                          if (value != null) {
                            controller.voice.value = value;
                          }
                        },
                      ),
                    ),
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      minVerticalPadding: 0,
                      title: Text('anki.tts.pitch'.tr, style: defaultTitleStyle),
                      subtitle: ShadSlider(
                        initialValue: controller.pitch.value,
                        min: -50,
                        max: 50,
                        divisions: 100,
                        label: '${controller.pitch.value.toStringAsFixed(1)}%',
                        onChanged: (value) {
                          controller.pitch.value = value;
                        },
                      ),
                    ),
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      minVerticalPadding: 0,
                      title: Text('anki.tts.rate'.tr, style: defaultTitleStyle),
                      subtitle: ShadSlider(
                        initialValue: controller.rate.value,
                        min: -200,
                        max: 500,
                        divisions: 700,
                        label: '${controller.rate.value.toStringAsFixed(1)}%',
                        onChanged: (value) {
                          controller.rate.value = value;
                        },
                      ),
                    ),
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      minVerticalPadding: 0,
                      title: Text('anki.tts.volume'.tr, style: defaultTitleStyle),
                      subtitle: ShadSlider(
                        initialValue: controller.volume.value,
                        min: 0,
                        max: 500,
                        divisions: 500,
                        label: '${controller.volume.value.toStringAsFixed(1)}%',
                        onChanged: (value) {
                          controller.volume.value = value;
                        },
                      ),
                    ),
                  ],
                )),
          ),
        ),
      ),
    );
  }
}
