// src/main.rs

use anyhow::{bail, Context, Result};
use clap::Parser;
use regex::Regex;
use rusqlite::Connection;
use serde::Deserialize;
use std::collections::HashMap;
use std::fs::{self, File};
// --- 修正 1: 导入 Seek trait ---
use std::io::{self, Cursor, Read, Seek, Write};
use std::path::{Path, PathBuf};
use tar::Archive as TarArchive;
use zip::ZipArchive;

// --- 数据结构 (保持不变) ---
#[derive(Debug, Deserialize)]
struct AnkiModel { name: String, flds: Vec<Field>, tmpls: Vec<Template> }
#[derive(Debug, Deserialize)]
struct Field { name: String }
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
struct Template { name: String, qfmt: String, afmt: String }
#[derive(Debug, Deserialize)]
struct AnkiDeck { name: String }

// --- 命令行参数 (保持不变) ---
#[derive(Parser, Debug)]
#[command(author, version, about, long_about = "从 Anki .apkg 文件中提取卡片为 HTML")]
struct Cli {
    #[arg(short, long)]
    input: PathBuf,
    #[arg(short, long, default_value = "output")]
    output: PathBuf,
}

// --- 主逻辑 ---
fn main() -> Result<()> {
    let cli = Cli::parse();
    println!("开始处理 Anki 包: {:?}", cli.input);

    fs::create_dir_all(&cli.output).context("无法创建输出目录")?;

    let mut file = File::open(&cli.input).context("无法打开 .apkg 文件")?;
    let mut magic_bytes = [0u8; 4];
    file.read_exact(&mut magic_bytes)?;
    file.seek(io::SeekFrom::Start(0))?; // 读完后指针归位

    let files_data = if &magic_bytes == b"PK\x03\x04" {
        println!("检测到旧版 ZIP 格式 .apkg 包。");
        extract_from_zip(file)?
    } else if &magic_bytes == &[0x28, 0xb5, 0x2f, 0xfd] {
        println!("检测到新版 ZSTD 格式 .apkg 包。");
        extract_from_tar_zst(file)?
    } else {
        bail!("未知的 .apkg 文件格式。文件开头不是 ZIP 或 ZSTD。");
    };

    process_files(files_data, &cli.output)?;

    println!("\n处理完成！HTML 文件已保存到 {:?} 目录。", cli.output);
    Ok(())
}

fn extract_from_zip(file: File) -> Result<HashMap<String, Vec<u8>>> {
    let mut archive = ZipArchive::new(file).context("无法读取 ZIP 存档")?;
    let mut files_data = HashMap::new();
    for i in 0..archive.len() {
        let mut file_in_zip = archive.by_index(i)?;
        let mut buffer = Vec::new();
        file_in_zip.read_to_end(&mut buffer)?;
        files_data.insert(file_in_zip.name().to_string(), buffer);
    }
    Ok(files_data)
}

fn extract_from_tar_zst(file: File) -> Result<HashMap<String, Vec<u8>>> {
    let decoder = zstd::stream::read::Decoder::new(file).context("无法创建 ZSTD 解码器")?;
    let mut archive = TarArchive::new(decoder);
    let mut files_data = HashMap::new();
    for entry in archive.entries()? {
        let mut entry = entry?;
        let path = entry.path()?.to_string_lossy().to_string();
        let mut buffer = Vec::new();
        entry.read_to_end(&mut buffer)?;
        files_data.insert(path, buffer);
    }
    Ok(files_data)
}

fn process_files(mut files_data: HashMap<String, Vec<u8>>, output_dir: &Path) -> Result<()> {
    // 1. 处理媒体文件
    // --- 修正 2: 明确指定 HashMap 类型，并用 Ok() 包装返回值 ---
    let media_map: HashMap<String, String> = match files_data.remove("media") {
        Some(media_bytes) => {
            let media_json = String::from_utf8_lossy(&media_bytes);
            if media_json.trim().is_empty() {
                Ok(HashMap::new())
            } else {
                serde_json::from_str(&media_json).context("解析 media JSON 失败")
            }
        }
        None => {
            println!("信息：未找到 media 文件，跳过媒体处理。");
            Ok(HashMap::new())
        }
    }?;

    let media_dir = output_dir.join("media");
    fs::create_dir_all(&media_dir)?;
    for (numeric_name, original_name) in &media_map {
        if let Some(data) = files_data.get(numeric_name) {
            let target_path = media_dir.join(sanitize_filename::sanitize(original_name));
            fs::write(&target_path, data)
                .with_context(|| format!("无法写入媒体文件: {:?}", target_path))?;
        }
    }
    println!("处理了 {} 个媒体文件映射。", media_map.len());
    
    let db_filename = files_data.keys()
        .find(|k| k.starts_with("collection.anki2"))
        .context("在包中找不到数据库文件 (collection.anki2 or .anki21)")?
        .to_string(); // Clone the key so we can remove from files_data
    
    let db_data = files_data.remove(&db_filename).unwrap();
    let mut temp_db = tempfile::Builder::new().suffix(".sqlite").tempfile()?;
    temp_db.write_all(&db_data)?;
    let conn = Connection::open(temp_db.path()).context("无法连接到 SQLite 数据库")?;
    println!("已成功连接到 Anki 数据库。");
    
    let (models, decks) = load_col_data(&conn)?;
    process_cards(&conn, &models, &decks, output_dir)?;

    Ok(())
}

fn load_col_data(conn: &Connection) -> Result<(HashMap<String, AnkiModel>, HashMap<String, AnkiDeck>)> {
    let mut stmt = conn.prepare("SELECT models, decks FROM col LIMIT 1")?;
    let (models_json, decks_json): (String, String) = stmt.query_row([], |row| Ok((row.get(0)?, row.get(1)?)))?;
    let models = serde_json::from_str(&models_json).context("解析 models JSON 失败")?;
    let decks = serde_json::from_str(&decks_json).context("解析 decks JSON 失败")?;
    Ok((models, decks))
}

fn process_cards(conn: &Connection, models: &HashMap<String, AnkiModel>, decks: &HashMap<String, AnkiDeck>, output_dir: &Path) -> Result<()> {
    let mut stmt = conn.prepare("SELECT c.id, c.did, n.mid, n.flds, c.ord FROM cards c JOIN notes n ON c.nid = n.id")?;
    let mut rows = stmt.query([])?;
    let mut count = 0;
    while let Some(row) = rows.next()? {
        count += 1;
        print!("\r正在生成卡片: {}", count);
        io::stdout().flush()?;
        if let Err(e) = generate_html_for_card(row, models, decks, output_dir) {
            eprintln!("\n警告：跳过卡片 ID {}，原因: {}", row.get::<_, i64>(0)?, e);
        }
    }
    Ok(())
}

fn generate_html_for_card(row: &rusqlite::Row, models: &HashMap<String, AnkiModel>, decks: &HashMap<String, AnkiDeck>, output_dir: &Path) -> Result<()> {
    let card_id: i64 = row.get("id")?;
    let deck_id: String = row.get::<_, i64>("did")?.to_string();
    let model_id: String = row.get::<_, i64>("mid")?.to_string();
    let fields_str: String = row.get("flds")?;
    let template_idx: usize = row.get::<_, i64>("ord")? as usize;

    let model = models.get(&model_id).context(format!("找不到模型 ID: {}", model_id))?;
    let deck = decks.get(&deck_id).context(format!("找不到牌组 ID: {}", deck_id))?;
    let template = model.tmpls.get(template_idx).context(format!("在模型 '{}' 中找不到模板索引: {}", model.name, template_idx))?;

    let field_values: Vec<&str> = fields_str.split('\x1f').collect();
    let mut field_map = HashMap::new();
    for (i, field_def) in model.flds.iter().enumerate() {
        if let Some(value) = field_values.get(i) {
            field_map.insert(field_def.name.clone(), *value);
        }
    }

    let question_html = render_template(&template.qfmt, &field_map);
    let answer_html = render_template(&template.afmt, &field_map);
    
    if question_html.trim().is_empty() && !answer_html.contains("{{FrontSide}}") {
        return Ok(());
    }
    
    let final_answer_html = answer_html.replace("{{FrontSide}}", &question_html);

    let full_html = format!(r#"<!DOCTYPE html><html lang="zh"><head><meta charset="UTF-8"><title>Anki Card - {title}</title><style>body{{font-family:sans-serif;line-height:1.6;padding:20px;max-width:800px;margin:0 auto;}} .card{{border:1px solid #ddd;border-radius:8px;padding:20px;}} .question{{margin-bottom:20px;}} .answer{{border-top:1px dashed #ccc;padding-top:20px;}} img{{max-width:100%;height:auto;}}</style></head><body><div class="card"><div class="question"><h2>问题</h2>{q}</div><hr><div class="answer"><h2>答案</h2>{a}</div></div></body></html>"#,
        title = sanitize_filename::sanitize(field_values.get(0).unwrap_or(&"card")),
        q = question_html,
        a = final_answer_html
    );

    let deck_dir = output_dir.join(sanitize_filename::sanitize(&deck.name));
    fs::create_dir_all(&deck_dir)?;
    fs::write(deck_dir.join(format!("card-{}.html", card_id)), full_html)?;
    Ok(())
}

fn render_template(template_str: &str, field_map: &HashMap<String, &str>) -> String {
    let re_field = Regex::new(r"\{\{([^}]+?)\}\}").unwrap();
    // --- 修正 3: 确保闭包所有返回路径都是 String 类型 ---
    let rendered = re_field.replace_all(template_str, |caps: &regex::Captures| {
        let tag = caps.get(1).unwrap().as_str().trim();
        // 简化处理，忽略条件渲染等复杂情况
        if tag.starts_with('#') || tag.starts_with('/') || tag.starts_with('^') {
            return "".to_string(); 
        }
        // 如果是特殊字段，则保留原样，由上层处理
        if tag == "FrontSide" {
            return "{{FrontSide}}".to_string();
        }
        field_map.get(tag).map_or("".to_string(), |v| v.to_string())
    }).to_string();

    // 修复媒体文件相对路径
    let re_media = Regex::new(r#"src="([^"]+)""#).unwrap();
    let rendered = re_media.replace_all(&rendered, |caps: &regex::Captures| {
        format!(r#"src="../media/{}""#, caps.get(1).unwrap().as_str())
    }).to_string();

    let re_sound = Regex::new(r"\[sound:(.+?)\]").unwrap();
    re_sound.replace_all(&rendered, |caps: &regex::Captures| {
        format!(r#"<audio controls src="../media/{}"></audio>"#, caps.get(1).unwrap().as_str())
    }).to_string()
}