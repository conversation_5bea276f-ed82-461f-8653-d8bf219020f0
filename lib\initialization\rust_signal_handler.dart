import 'dart:convert';
import 'dart:io';
import 'package:get/get.dart';
import 'package:ulid/ulid.dart';
import 'package:flutter_udid/flutter_udid.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/bookmark.dart';
import 'package:flutter_machineid/flutter_machineid.dart';
import 'package:path_provider/path_provider.dart';

/// Rust signal handler module
/// Handles all Rust-Dart communication and message routing
class RustSignalHandler {
  /// Initialize Rust signal handlers
  /// This sets up the communication bridge between Rust backend and Dart frontend
  static void initializeSignalHandlers(
    MessageController messageController,
    ProgressController progressController,
    WebviewController webviewController,
    AnkiConnectController ankiConnectController,
  ) {
    logger.i('Setting up Rust signal handlers...');
    
    // Set up DartRequest signal handler
    _setupRustRequestHandler(webviewController, ankiConnectController);
    
    // Set up RinfResponse signal handler
    _setupRinfResponseHandler(messageController);
    
    // Set up ProgressResponse signal handler
    _setupProgressResponseHandler(progressController);
    
    logger.i('Rust signal handlers initialized');
  }
  
  /// Set up RustRequest signal stream handler
  static void _setupRustRequestHandler(
    WebviewController webviewController,
    AnkiConnectController ankiConnectController,
  ) {
    RustRequest.rustSignalStream.listen((rustSignal) async {
      final message = rustSignal.message;
      logger.i('RustRequest: $message');

      try {
        await _handleRustRequest(message, webviewController, ankiConnectController);
      } catch (e, stackTrace) {
        logger.e('Error handling RustRequest: $e\n$stackTrace');
        _sendErrorResponse(message.interactionId, 'Internal error: $e');
      }
    });
  }

  /// Set up RustResponse signal stream handler
  static void _setupRinfResponseHandler(MessageController messageController) {
    RustResponse.rustSignalStream.listen((rustSignal) {
      final message = rustSignal.message;
      messageController.messageChannel.value[message.interactionId]!
          .complete(message);
    });
  }
  
  /// Set up ProgressResponse signal stream handler
  static void _setupProgressResponseHandler(ProgressController progressController) {
    ProgressResponse.rustSignalStream.listen((rustSignal) {
      final message = rustSignal.message;
      progressController.updateProgress(
        status:message.status,
        message: message.message,
        current: message.current,
        total: message.total,
        );
    });
  }
  
  /// Handle individual RustRequest messages
  static Future<void> _handleRustRequest(
    RustRequest message,
    WebviewController webviewController,
    AnkiConnectController ankiConnectController,
  ) async {
    switch (message.msgType) {
      case "get_temp_dir":
        await _handleGetTempDir(message);
        break;
      case "get_page_count":
        await _handleGetPageCount(message);
        break;
      case "markdown2json":
        await _handleMarkdownToJson(message, webviewController);
        break;
      case "get_page_subdeck_map":
        await _handleGetPageSubdeckMap(message);
        break;
      case "export_pdf_annotations":
        await _handleExportPdfAnnotations(message);
        break;
      case "delete_pdf_annotations":
        await _handleDeletePdfAnnotations(message);
        break;
      case "json_repair":
        await _handleJsonRepair(message, webviewController);
        break;
      case "transform_formula":
        await _handleTransformFormula(message, ankiConnectController);
        break;
      case "html2txt":
        await _handleHtmlToText(message, webviewController);
        break;
      case "get_device_id":
        await _handleGetDeviceId(message);
        break;
      case "get_license_dir":
        await _handleGetLicenseDir(message);
        break;
      default:
        _sendErrorResponse(message.interactionId, "Unsupported message type");
    }
  }
  
  /// Send error response to Rust
  static void _sendErrorResponse(String interactionId, String errorMessage) {
    DartResponse(
      interactionId: interactionId,
      status: "error",
      message: errorMessage,
      data: "",
    ).sendSignalToRust();
  }
  
  /// Send success response to Rust
  static void _sendSuccessResponse(String interactionId, String data, [String? message]) {
    DartResponse(
      interactionId: interactionId,
      status: "success",
      message: message ?? "Success",
      data: data,
    ).sendSignalToRust();
  }

  /// Handle get_temp_dir request
  static Future<void> _handleGetTempDir(RustRequest message) async {
    final tempDir = await PathUtils.tempDir;
    _sendSuccessResponse(message.interactionId, tempDir);
  }

  /// Handle get_page_count request
  static Future<void> _handleGetPageCount(RustRequest message) async {
    final outerParams = jsonDecode(message.params);
    final innerParams = jsonDecode(outerParams['params']);
    final String filePath = innerParams['filePath'];

    final PdfDocument document = PdfDocument(
      inputBytes: File(filePath).readAsBytesSync(),
    );
    final pageCount = document.pages.count;
    document.dispose();

    _sendSuccessResponse(message.interactionId, pageCount.toString());
  }

  /// Handle markdown2json request
  static Future<void> _handleMarkdownToJson(RustRequest message, WebviewController webviewController) async {
    final outerParams = jsonDecode(message.params);
    final innerParams = jsonDecode(outerParams['params']);
    logger.i("params: $innerParams");
    final content = innerParams['content'].toString();
    final result = await webviewController.convertMD2JSON(content);
    _sendSuccessResponse(message.interactionId, result);
  }

  /// Handle get_page_subdeck_map request
  static Future<void> _handleGetPageSubdeckMap(RustRequest message) async {
    final outerParams = jsonDecode(message.params);
    final innerParams = jsonDecode(outerParams['params']);
    final filePath = innerParams['filePath'];
    final bookmarkController = Get.find<PDFBookmarkPageController>();
    final pageSubdeckMap = await bookmarkController.getPageSubdeckMap(filePath);

    // Convert Map<int, String> to a JSON-serializable Map<String, String>
    final serializedMap = pageSubdeckMap.map((key, value) => MapEntry(key.toString(), value));
    _sendSuccessResponse(message.interactionId, jsonEncode(serializedMap));
  }

  /// Handle export_pdf_annotations request
  static Future<void> _handleExportPdfAnnotations(RustRequest message) async {
    try {
      final outerParams = jsonDecode(message.params);
      final innerParams = jsonDecode(outerParams['params']);
      logger.i("Export PDF annotations params: $innerParams");
      final String filePath = innerParams['filePath'];

      // Create a delayed response to let UI render progress dialog first
      Future.delayed(Duration.zero, () async {
        try {
          final outputPath = await _exportPDFAnnotations(filePath);
          DartResponse(
            interactionId: message.interactionId,
            status: "success",
            message: "Annotations exported successfully",
            data: outputPath,
          ).sendSignalToRust();
        } catch (e, stackTrace) {
          logger.e("Error exporting PDF annotations: $e\n$stackTrace");
          DartResponse(
            interactionId: message.interactionId,
            status: "error",
            message: "Failed to export annotations: $e",
            data: "",
          ).sendSignalToRust();
        }
      });
      // Return immediately, don't block UI thread
      return;
    } catch (e, stackTrace) {
      logger.e("Error exporting PDF annotations: $e\n$stackTrace");
      _sendErrorResponse(message.interactionId, "Failed to export annotations: $e");
    }
  }

  /// Handle delete_pdf_annotations request
  static Future<void> _handleDeletePdfAnnotations(RustRequest message) async {
    try {
      final outerParams = jsonDecode(message.params);
      final innerParams = jsonDecode(outerParams['params']);
      logger.i("Delete PDF annotations params: $innerParams");
      final String filePath = innerParams['filePath'];
      final tempDir = await PathUtils.tempDir;
      final String outputPath = PathUtils.join([tempDir, "${Ulid().toString()}.pdf"]);
      final String pageRange = innerParams['pageRange'];
      final List<String> annotationIds = (innerParams['annotationIds'] as List)
          .map((item) => item.toString())
          .toList();

      logger.i("Delete PDF annotations: $filePath, $pageRange, $annotationIds");
      await deleteAnnotations(filePath, outputPath, pageRange, annotationIds);

      DartResponse(
        interactionId: message.interactionId,
        status: "success",
        message: "Annotations deleted successfully",
        data: outputPath,
      ).sendSignalToRust();
    } catch (e, stackTrace) {
      logger.e("Error deleting PDF annotations: $e\n$stackTrace");
      _sendErrorResponse(message.interactionId, "Failed to delete annotations: $e");
    }
  }

  /// Handle json_repair request
  static Future<void> _handleJsonRepair(RustRequest message, WebviewController webviewController) async {
    final outerParams = jsonDecode(message.params);
    final innerParams = jsonDecode(outerParams['params']);
    final json = innerParams['json'];
    final result = await webviewController.jsonRepair(json);
    _sendSuccessResponse(message.interactionId, result);
  }

  /// Handle transform_formula request
  static Future<void> _handleTransformFormula(RustRequest message, AnkiConnectController ankiConnectController) async {
    final outerParams = jsonDecode(message.params);
    final innerParams = jsonDecode(outerParams['params']);
    final String content = innerParams['content'];

    // Use AnkiConnectController's convertLatexFormula method
    final result = ankiConnectController.convertLatexFormula(content);
    _sendSuccessResponse(message.interactionId, result);
  }

  /// Handle html2txt request
  static Future<void> _handleHtmlToText(RustRequest message, WebviewController webviewController) async {
    final outerParams = jsonDecode(message.params);
    final innerParams = jsonDecode(outerParams['params']);
    final String html = innerParams['html'];
    final String txt = await webviewController.htmlToText(html);
    _sendSuccessResponse(message.interactionId, txt);
  }

  /// Handle get_device_id request
  static Future<void> _handleGetDeviceId(RustRequest message) async {
    try {
      String deviceId = "";
      if (Platform.isAndroid || Platform.isIOS) {
        // Use flutter_udid for mobile platforms
        deviceId = await FlutterUdid.udid;
      } else {
        // Desktop platforms
        deviceId = await FlutterMachineid.protectedID('anki') ?? '';
      }
      _sendSuccessResponse(message.interactionId, deviceId);
    } catch (e) {
      logger.e('Error getting mobile device ID: $e');
      _sendErrorResponse(message.interactionId, "Failed to get mobile device ID: $e");
    }
  }
 
  /// Handle get_license_dir request
  static Future<void> _handleGetLicenseDir(RustRequest message) async {
    try {
      String externalDir = "";
      if (Platform.isAndroid) {
        // Use flutter_udid for mobile platforms
        externalDir = (await getExternalStorageDirectory())?.path ?? '';
      } else if( Platform.isIOS){
        // Desktop platforms
        externalDir = (await getApplicationSupportDirectory()).path;
      }
      _sendSuccessResponse(message.interactionId, externalDir);
    } catch (e) {
      logger.e('Error getting mobile license dir: $e');
      _sendErrorResponse(message.interactionId, "Failed to get mobile license dir: $e");
    }
  }

  /// Export PDF annotations to XFDF format
  static Future<String> _exportPDFAnnotations(String path) async {
    final tempDir = await PathUtils.tempDir;
    final String outputPath = PathUtils.join([tempDir, "${Ulid().toString()}.xfdf"]);

    // Use syncfusion_flutter_pdf library to export annotations
    final File pdfFile = File(path);
    if (!pdfFile.existsSync()) {
      throw Exception("PDF file not found: $path");
    }

    final PdfDocument document = PdfDocument(
      inputBytes: pdfFile.readAsBytesSync(),
    );
    List<int> bytes = document.exportAnnotation(PdfAnnotationDataFormat.xfdf);
    File(outputPath).writeAsBytesSync(bytes);
    document.dispose();

    return outputPath;
  }
}
