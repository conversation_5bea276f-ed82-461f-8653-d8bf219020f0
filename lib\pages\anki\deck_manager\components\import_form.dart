import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/deck_manager.dart';

class ImportDeckForm extends GetView<DeckManagerController> {
  const ImportDeckForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: 8,
        children: [
          ShadInputWithFileSelect(
            key: const ValueKey("input-file"),
            title: 'toolbox.common.inputFile'.tr,
            placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
            allowedExtensions: const ['apkg'],
            isRequired: true,
            allowMultiple: true,
            initialValue: controller.selectedFilePaths,
            onFilesSelected: (files) {
              controller.selectedFilePaths.clear();
              controller.selectedFilePaths.addAll(files);
            },
            onValidate: (value, files) async {
              return await validateFile(value, files);
            },
            onValidateError: (error) {},
          ),
        ],
      ),
    );
  }
}
