import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:pro_image_editor/pro_image_editor.dart';

class ImageEditorPage extends StatelessWidget {
  const ImageEditorPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ImageCardController>();

    return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        // appBar: AppBar(
        //   title: const Text("截图编辑"),
        //   backgroundColor: Theme.of(context).colorScheme.surface,
        //   leading: IconButton(
        //     icon: const Icon(Icons.arrow_back),
        //     onPressed: () => Get.back(),
        //   ),
        // ),
        body: ProImageEditor.memory(controller.byteArray.value!,
            key: controller.imageEditorKey, callbacks: ProImageEditorCallbacks(
          onImageEditingComplete: (Uint8List bytes) async {
            /*
                  Your code to handle the edited image. Upload it to your server as an example.
                  You can choose to use await, so that the loading-dialog remains visible until your code is ready, or no async, so that the loading-dialog closes immediately.
                  By default, the bytes are in `jpg` format.
                */
            final clipboardController = Get.find<ClipboardController>();
            clipboardController.copyImage(bytes, "jpeg");
            showToastNotification(context, "videoNotes.message.success".tr, "videoNotes.message.annotationCopied".tr, type: "success");
            Navigator.pop(context);
          },
        ),
            configs: const ProImageEditorConfigs(
              paintEditor: PaintEditorConfigs(),
              textEditor: TextEditorConfigs(enabled: false),
              cropRotateEditor: CropRotateEditorConfigs(enabled: false),
              filterEditor: FilterEditorConfigs(enabled: false),
              emojiEditor: EmojiEditorConfigs(enabled: false),
              stickerEditor: StickerEditorConfigs(enabled: false),
            )));
  }
}
