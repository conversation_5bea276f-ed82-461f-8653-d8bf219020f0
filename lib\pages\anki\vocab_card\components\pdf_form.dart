import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/vocab_card.dart';

class PDFForm extends GetView<VocabCardPageController> {
  const PDFForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {},
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadSelectWithInput(
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
              onAddNew: (newDeckName) {
                // Add to the deck list if not already present
                if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                  ankiConnectController.parentDeckList.add(newDeckName);
                }

                // Set as selected deck
                controller.parentDeck.value = newDeckName;
              },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              ShadSelectWithInput(
                label: 'anki.common.tags'.tr,
                placeholder: '选择标签',
                searchPlaceholder: '输入标签',
                isMultiple: true,
                initialValue: controller.tags.toList(),
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the global tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tags.contains(newTag)) {
                    controller.tags.add(newTag);
                  }
                },
              ),
            ],
          )),
    );
  }
}
