import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/pdf_note.dart';

class PDFNoteConfig extends GetView<PDFNoteController> {
  const PDFNoteConfig({super.key});

  @override
  Widget build(context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('anki.pdf_note.actions.save'.tr),
            ),
          )
        ],
      ),
      child: Obx(
        () => Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          spacing: 8,
          children: [
            ShadSwitchCustom(
              label: 'anki.pdf_note.config.auto_paste'.tr,
              initialValue: controller.autoPaste.value,
              onChanged: (v) {
                controller.autoPaste.value = v;
              },
            ),
            ShadRadioGroupCustom(
              label: 'anki.pdf_note.config.link_protocol'.tr,
              initialValue: controller.linkProtocol.value,
              items: controller.linkProtocolList,
              onChanged: (value) {
                controller.linkProtocol.value = value;
              },
            ),
            ShadRadioGroupCustom(
              label: 'anki.pdf_note.config.link_format'.tr,
              initialValue: controller.linkFormat.value,
              items: controller.linkFormatList,
              onChanged: (value) {
                controller.linkFormat.value = value;
              },
            ),
            if (controller.linkFormat.value == "custom")
              ShadInputWithValidate(
                  label: 'anki.pdf_note.config.custom_link_format'.tr,
                  placeholder: 'anki.pdf_note.config.custom_link_format_placeholder'.tr,
                  initialValue: controller.customLinkFormat.value,
                  onChanged: (value) {
                    controller.customLinkFormat.value = value;
                  },
                  onValidate: (value) async {
                    return "";
                  }),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
