import { defineStore } from 'pinia';
import { ref } from 'vue';
// @ts-ignore 忽略类型检查问题
import browser from 'webextension-polyfill';

export const useVideoStore = defineStore('video', () => {
  const currentVideo = ref<HTMLVideoElement | null>(null);
  const isPlaying = ref(false);
  const currentTime = ref(0);
  const duration = ref(0);
  const playbackRate = ref(1);
  const volume = ref(1);
  const muted = ref(false);
  const websocketUrl = ref('ws://localhost:52025/connect');
  const websocket = ref<WebSocket | null>(null);

  function setVideo(video: HTMLVideoElement | null) {
    // Remove event listeners from previous video
    if (currentVideo.value) {
      currentVideo.value.removeEventListener('timeupdate', updateVideoInfo);
      currentVideo.value.removeEventListener('loadedmetadata', updateVideoInfo);
      currentVideo.value.removeEventListener('play', updateVideoInfo);
      currentVideo.value.removeEventListener('pause', updateVideoInfo);
      currentVideo.value.removeEventListener('ratechange', updateVideoInfo);
      currentVideo.value.removeEventListener('volumechange', updateVideoInfo);
    }

    currentVideo.value = video;

    if (video) {
      console.log('🎬 设置新的视频元素:', {
        src: video.src || video.currentSrc,
        duration: video.duration,
        readyState: video.readyState,
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight
      });

      // Add event listeners for real-time updates
      video.addEventListener('timeupdate', updateVideoInfo);
      video.addEventListener('loadedmetadata', updateVideoInfo);
      video.addEventListener('play', updateVideoInfo);
      video.addEventListener('pause', updateVideoInfo);
      video.addEventListener('ratechange', updateVideoInfo);
      video.addEventListener('volumechange', updateVideoInfo);

      // Initial update
      updateVideoInfo();
    } else {
      console.log('🚫 清除视频元素');
      // Reset all values
      isPlaying.value = false;
      currentTime.value = 0;
      duration.value = 0;
      playbackRate.value = 1;
      volume.value = 1;
      muted.value = false;
    }
  }

  function updateVideoInfo() {
    if (!currentVideo.value) return;

    try {
      const video = currentVideo.value;

      // Update all video state values
      isPlaying.value = !video.paused;
      currentTime.value = video.currentTime || 0;
      duration.value = video.duration || 0;
      playbackRate.value = video.playbackRate || 1;
      volume.value = video.volume || 1;
      muted.value = video.muted || false;

      // Log detailed info for debugging (only occasionally to avoid spam)
      if (Math.floor(video.currentTime) % 10 === 0 && Math.floor(video.currentTime) !== Math.floor(currentTime.value)) {
        console.log('📊 视频状态更新:', {
          playing: isPlaying.value,
          currentTime: currentTime.value,
          duration: duration.value,
          playbackRate: playbackRate.value,
          volume: volume.value,
          muted: muted.value
        });
      }
    } catch (error) {
      console.error('❌ 更新视频信息失败:', error);
    }
  }

  function play() {
    if (currentVideo.value) {
      currentVideo.value.play();
      isPlaying.value = true;
    }
  }

  function pause() {
    if (currentVideo.value) {
      currentVideo.value.pause();
      isPlaying.value = false;
    }
  }

  function togglePlay() {
    if (isPlaying.value) {
      pause();
    } else {
      play();
    }
  }

  function seek(time: number) {
    if (currentVideo.value) {
      currentVideo.value.currentTime = time;
      currentTime.value = time;
    }
  }

  // Helper function to ensure video element is valid and ready
  function ensureVideoReady(): HTMLVideoElement | null {
    if (!currentVideo.value) {
      console.warn('⚠️ 没有可用的视频元素');
      return null;
    }

    const video = currentVideo.value;

    // Check if video element is still in DOM
    if (!document.contains(video)) {
      console.warn('⚠️ 视频元素已从DOM中移除，尝试重新查找');
      currentVideo.value = null;
      return null;
    }

    // Update video info to ensure store is current
    updateVideoInfo();

    return video;
  }

  function seekRelative(seconds: number) {
    console.log(`🎯 执行相对跳转: ${seconds}秒`);

    const video = ensureVideoReady();
    if (!video) {
      console.error('❌ 无法执行跳转：没有有效的视频元素');
      return;
    }

    // Get real-time values from video element
    const currentVideoTime = video.currentTime;
    const videoDuration = video.duration || 0;

    console.log(`📊 当前时间: ${currentVideoTime}秒, 总时长: ${videoDuration}秒`);

    // Calculate new time with bounds checking
    const newTime = Math.max(0, Math.min(videoDuration, currentVideoTime + seconds));

    console.log(`⏭️ 跳转到: ${newTime}秒`);

    try {
      // Apply the seek
      video.currentTime = newTime;

      // Update store values immediately
      currentTime.value = newTime;

      // Verify the seek was successful
      setTimeout(() => {
        const actualTime = video.currentTime;
        console.log(`✅ 跳转完成，实际时间: ${actualTime}秒`);
        if (Math.abs(actualTime - newTime) > 1) {
          console.warn(`⚠️ 跳转可能不准确，期望: ${newTime}秒，实际: ${actualTime}秒`);
        }
      }, 100);

    } catch (error) {
      console.error('❌ 跳转失败:', error);
    }
  }

  function setPlaybackRate(rate: number) {
    if (currentVideo.value) {
      currentVideo.value.playbackRate = rate;
      playbackRate.value = rate;
    }
  }

  function setVolume(vol: number) {
    if (currentVideo.value) {
      currentVideo.value.volume = Math.max(0, Math.min(1, vol));
      volume.value = currentVideo.value.volume;
    }
  }

  function toggleMute() {
    if (currentVideo.value) {
      currentVideo.value.muted = !currentVideo.value.muted;
      muted.value = currentVideo.value.muted;
    }
  }

  // WebSocket connection management
  function connectWebsocket() {
    try {
      console.log('🔌 尝试连接WebSocket:', websocketUrl.value);

      if (websocket.value) {
        websocket.value.close();
      }

      websocket.value = new WebSocket(websocketUrl.value);

      websocket.value.onopen = () => {
        console.log('✅ WebSocket连接已建立');
      };

      websocket.value.onmessage = (event) => {
        console.log('📨 收到WebSocket消息:', event.data);
      };

      websocket.value.onclose = () => {
        console.log('❌ WebSocket连接已关闭');
      };

      websocket.value.onerror = (error) => {
        console.error('❌ WebSocket连接错误:', error);
      };
    } catch (error) {
      console.error('❌ WebSocket连接失败:', error);
    }
  }

  // Update WebSocket URL
  function updateWebsocketUrl(port: number) {
    websocketUrl.value = `ws://localhost:${port}/connect`;
    console.log('🔄 WebSocket URL已更新:', websocketUrl.value);

    // Reconnect with new URL
    connectWebsocket();
  }

  // Take screenshot of current video
  async function takeScreenshot() {
    try {
      console.log('📸 开始截取视频截图');

      // Ensure video element is valid and ready
      const video = ensureVideoReady();
      if (!video) {
        console.error('❌ 没有可用的视频元素进行截图');
        // Send error message to background script
        browser.runtime.sendMessage({
          type: 'screenshot_error',
          error: '请添加视频'
        });
        return;
      }

      // Check if video is loaded and has dimensions
      if (video.readyState < 2) {
        console.warn('⚠️ 视频尚未加载完成，等待加载...');
        // Wait for video to be ready
        await new Promise((resolve) => {
          const checkReady = () => {
            if (video.readyState >= 2) {
              resolve(true);
            } else {
              setTimeout(checkReady, 100);
            }
          };
          checkReady();
        });
      }

      // Verify video has dimensions
      const videoWidth = video.videoWidth || video.clientWidth || 640;
      const videoHeight = video.videoHeight || video.clientHeight || 360;

      if (videoWidth === 0 || videoHeight === 0) {
        console.error('❌ 视频尺寸无效:', { videoWidth, videoHeight });
        browser.runtime.sendMessage({
          type: 'screenshot_error',
          error: '视频尺寸无效'
        });
        return;
      }

      console.log(`� 视频尺寸: ${videoWidth}x${videoHeight}`);

      // Create canvas to capture video frame
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        console.error('❌ 无法创建canvas上下文');
        browser.runtime.sendMessage({
          type: 'screenshot_error',
          error: '无法创建canvas上下文'
        });
        return;
      }

      canvas.width = videoWidth;
      canvas.height = videoHeight;

      // Draw current video frame to canvas
      ctx.drawImage(video, 0, 0, videoWidth, videoHeight);

      console.log('🎨 视频帧已绘制到canvas');

      // Convert to blob and then to base64 for WebSocket transmission
      canvas.toBlob(async (blob) => {
        if (blob) {
          try {
            console.log('🔄 转换blob到base64格式...');

            // Convert blob to base64
            const reader = new FileReader();
            reader.onload = function() {
              const base64Data = reader.result as string;
              console.log('✅ Base64转换完成，数据长度:', base64Data.length);

              // Send screenshot data to background script with base64 image (新的统一报告格式)
              browser.runtime.sendMessage({
                type: 'report',
                reportType: 'screenshot',
                status: 'success',
                action: 'take_screenshot',
                data: {
                  image: base64Data, // Base64 encoded image data
                  timestamp: video.currentTime || 0,
                  url: window.location.href,
                  title: document.title,
                  videoWidth: videoWidth,
                  videoHeight: videoHeight
                },
                message: '视频截图已成功生成',
                timestamp: Date.now()
              });

              console.log('✅ 截图已发送到后台脚本 (base64格式)');
            };

            reader.onerror = function() {
              console.error('❌ Base64转换失败');
              browser.runtime.sendMessage({
                type: 'report',
                reportType: 'screenshot',
                status: 'error',
                action: 'take_screenshot',
                data: {},
                message: 'Base64转换失败',
                timestamp: Date.now()
              });
            };

            // Read blob as data URL (base64)
            reader.readAsDataURL(blob);

          } catch (error) {
            console.error('❌ 处理截图blob失败:', error);
            browser.runtime.sendMessage({
              type: 'report',
              reportType: 'screenshot',
              status: 'error',
              action: 'take_screenshot',
              data: {},
              message: '处理截图数据失败',
              timestamp: Date.now()
            });
          }
        } else {
          console.error('❌ 无法创建截图blob');
          browser.runtime.sendMessage({
            type: 'report',
            reportType: 'screenshot',
            status: 'error',
            action: 'take_screenshot',
            data: {},
            message: '无法创建截图数据',
            timestamp: Date.now()
          });
        }
      }, 'image/png');

    } catch (error) {
      console.error('❌ 截图失败:', error);
      browser.runtime.sendMessage({
        type: 'screenshot_error',
        error: `截图失败: ${(error as Error).message || '未知错误'}`
      });
    }
  }

  // Generate timestamp data for Flutter processing
  function generateTimestampLink() {
    try {
      console.log('🔗 开始生成时间戳数据');

      // Ensure video element is valid and ready
      const video = ensureVideoReady();
      if (!video) {
        console.error('❌ 没有可用的视频元素生成时间戳');
        // Send error message to background script (新的统一报告格式)
        browser.runtime.sendMessage({
          type: 'report',
          reportType: 'timestamp',
          status: 'error',
          action: 'generate_timestamp_link',
          data: {},
          message: '请添加视频',
          timestamp: Date.now()
        });
        return '';
      }

      // Check if video has valid time
      if (isNaN(video.currentTime) || video.currentTime < 0) {
        console.error('❌ 视频时间无效:', video.currentTime);
        browser.runtime.sendMessage({
          type: 'report',
          reportType: 'timestamp',
          status: 'error',
          action: 'generate_timestamp_link',
          data: {},
          message: '视频时间无效',
          timestamp: Date.now()
        });
        return '';
      }

      const currentVideoTime = video.currentTime; // Keep as float for precision
      const currentVideoTimeSeconds = Math.floor(currentVideoTime);

      // Convert seconds to HH:MM:SS format (same logic as Flutter)
      const hours = Math.floor(currentVideoTimeSeconds / 3600);
      const minutes = Math.floor((currentVideoTimeSeconds % 3600) / 60);
      const seconds = currentVideoTimeSeconds % 60;

      let timeStr = '';
      if (hours > 0) {
        timeStr = `${hours.toString().padStart(2, '0')}:`;
      }
      timeStr += `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      console.log(`⏰ 当前视频时间: ${currentVideoTime}秒 (${timeStr})`);
      console.log(`🌐 当前页面URL: ${window.location.href}`);
      console.log(`📄 页面标题: ${document.title}`);

      // Send raw data to background script for Flutter processing (新的统一报告格式)
      browser.runtime.sendMessage({
        type: 'report',
        reportType: 'timestamp',
        status: 'success',
        action: 'generate_timestamp_link',
        data: {
          url: window.location.href,           // Original video URL
          currentTime: currentVideoTime,       // Current time in seconds (float)
          title: document.title,               // Video title
          timestamp: timeStr,                  // Formatted time string (HH:MM:SS)
          platform: new URL(window.location.href).hostname
        },
        message: '时间戳数据已成功生成',
        timestamp: Date.now()
      });

      console.log('✅ 时间戳数据已发送到后台脚本');
      return timeStr; // Return formatted timestamp

    } catch (error) {
      console.error('❌ 生成时间戳数据失败:', error);
      browser.runtime.sendMessage({
        type: 'report',
        reportType: 'timestamp',
        status: 'error',
        action: 'generate_timestamp_link',
        data: {},
        message: `生成时间戳数据失败: ${(error as Error).message || '未知错误'}`,
        timestamp: Date.now()
      });
      return '';
    }
  }

  // Send video info to background script
  function sendVideoInfo() {
    try {
      if (!currentVideo.value) {
        console.warn('⚠️ 没有可用的视频元素发送信息');
        return;
      }

      const videoInfo = {
        currentTime: currentVideo.value.currentTime,
        duration: currentVideo.value.duration || 0,
        paused: currentVideo.value.paused,
        playbackRate: currentVideo.value.playbackRate,
        volume: currentVideo.value.volume,
        muted: currentVideo.value.muted,
        src: currentVideo.value.src || currentVideo.value.currentSrc,
        title: document.title,
        url: window.location.href,
        videoWidth: currentVideo.value.videoWidth,
        videoHeight: currentVideo.value.videoHeight
      };

      console.log('📊 发送视频信息:', videoInfo);

      // Send to background script
      browser.runtime.sendMessage({
        type: 'video_info',
        data: videoInfo
      });

    } catch (error) {
      console.error('❌ 发送视频信息失败:', error);
    }
  }

  return {
    currentVideo,
    isPlaying,
    currentTime,
    duration,
    playbackRate,
    volume,
    muted,
    websocketUrl,
    websocket,
    setVideo,
    updateVideoInfo,
    ensureVideoReady,
    play,
    pause,
    togglePlay,
    seek,
    seekRelative,
    setPlaybackRate,
    setVolume,
    toggleMute,
    connectWebsocket,
    updateWebsocketUrl,
    takeScreenshot,
    generateTimestampLink,
    sendVideoInfo
  };
});
