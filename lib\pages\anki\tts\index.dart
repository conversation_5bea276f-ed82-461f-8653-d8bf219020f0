import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/card_tts.dart';
import 'components/text_tts.dart';
import 'components/setting.dart';
import 'package:anki_guru/controllers/anki/tts/index_controller.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';

class CardTTSPage extends StatefulWidget {
  const CardTTSPage({super.key});

  @override
  State<CardTTSPage> createState() => _CardTTSPageState();
}

class _CardTTSPageState extends State<CardTTSPage> {
  final controller = Get.put(CardTTSPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.tts.card_tts_title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Get.to(() => const SettingsPage(),
              //     transition: Transition.cupertino);
              showShadDialog(
                context: context,
                builder: (context) => const SettingsPage(),
              );
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('anki.tts.function_description'.tr, style: defaultPageTitleStyle),
                Text('anki.tts.feature_description'.tr,
                    style: theme.textTheme.muted),
                const SizedBox(height: 16),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 2;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        print(controller.tabController.selected);
                      },
                      tabs: [
                        ShadTab(
                          value: 'card_tts',
                          content: const CardTTS(),
                          width: tabWidth,
                          child: Text('anki.tts.card_tts_tab'.tr),
                        ),
                        ShadTab(
                          value: 'text_tts',
                          content: const TextTTS(),
                          width: tabWidth,
                          child: Text('anki.tts.text_tts_tab'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
