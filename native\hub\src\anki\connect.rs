use rand::Rng;
use rinf::debug_print;
use serde::Serialize;
use serde_json::{json, Value};
use std::collections::HashMap;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum AnkiConnectError {
    #[error("Request failed: {0}")]
    RequestFailed(String),

    #[error("Invalid response format: {0}")]
    InvalidResponse(String),

    #[error("Network error: {0}")]
    NetworkError(#[from] reqwest::Error),
}

async fn send_request(address: &str, request: Value) -> Result<Value, AnkiConnectError> {
    let client = reqwest::Client::new();
    let response = client
        .post(address)
        .json(&request)
        .send()
        .await
        .map_err(AnkiConnectError::NetworkError)?;

    let result: Value = response
        .json()
        .await
        .map_err(AnkiConnectError::NetworkError)?;
    // debug_print!("request result: {:?}", result);
    if let Some(error) = result.get("error").and_then(|e| e.as_str()) {
        return Err(AnkiConnectError::RequestFailed(error.to_string()));
    }

    result
        .get("result")
        .ok_or_else(|| AnkiConnectError::InvalidResponse("Missing 'result' field".to_string()))
        .map(|v| v.clone())
}

pub async fn get_deck_names(address: &str) -> Result<Vec<String>, AnkiConnectError> {
    let request = json!({
        "action": "deckNames",
        "version": 6
    });

    let response = send_request(address, request).await?;
    response
        .as_array()
        .map(|arr| {
            arr.iter()
                .filter_map(|v| v.as_str())
                .map(String::from)
                .collect()
        })
        .ok_or_else(|| {
            AnkiConnectError::InvalidResponse("Expected array of deck names".to_string())
        })
}

pub async fn create_deck(address: &str, deck_name: &str) -> Result<i64, AnkiConnectError> {
    let request = json!({
        "action": "createDeck",
        "version": 6,
        "params": {
            "deck": deck_name
        }
    });

    let response = send_request(address, request).await?;
    response
        .as_i64()
        .ok_or_else(|| AnkiConnectError::InvalidResponse("Expected deck ID".to_string()))
}

pub async fn create_decks(
    address: &str,
    deck_names: Vec<String>,
    batch_size: Option<usize>,
) -> Result<Vec<Option<i64>>, AnkiConnectError> {
    // 去重
    let deck_names: Vec<String> = deck_names
        .into_iter()
        .collect::<std::collections::HashSet<_>>()
        .into_iter()
        .collect();
    println!("create_decks: {:?}", deck_names);
    let batch_size = batch_size.unwrap_or(deck_names.len());
    let mut results = Vec::new();

    for chunk in deck_names.chunks(batch_size) {
        let mut chunk_results = Vec::new();
        for deck_name in chunk {
            match create_deck(address, deck_name).await {
                Ok(id) => chunk_results.push(Some(id)),
                Err(_) => chunk_results.push(None),
            }
        }
        results.extend(chunk_results);
    }

    Ok(results)
}

pub async fn delete_decks(address: &str, deck_names: Vec<String>) -> Result<(), AnkiConnectError> {
    let request = json!({
        "action": "deleteDecks",
        "version": 6,
        "params": {
            "decks": deck_names,
            "cardsToo": true
        }
    });

    send_request(address, request).await?;
    Ok(())
}

pub async fn get_media_dir_path(address: &str) -> Result<String, String> {
    let request = json!({
        "action": "getMediaDirPath",
        "version": 6
    });

    let response = match send_request(address, request).await {
        Ok(value) => value,
        Err(_) => return Ok(String::new()),
    };

    match response.as_str() {
        Some(path) => Ok(path.to_string()),
        None => Ok(String::new()),
    }
}

pub enum MediaFileData {
    Path(String),
    Url(String),
    Data(String),
}

pub async fn store_media_file(
    address: &str,
    filename: &str,
    file_data: MediaFileData,
) -> Result<bool, AnkiConnectError> {
    let mut params = serde_json::Map::new();
    params.insert("filename".to_string(), json!(filename));

    match file_data {
        MediaFileData::Path(path) => {
            params.insert("path".to_string(), json!(path));
        }
        MediaFileData::Url(url) => {
            params.insert("url".to_string(), json!(url));
        }
        MediaFileData::Data(data) => {
            params.insert("data".to_string(), json!(data));
        }
    }

    let request = json!({
        "action": "storeMediaFile",
        "version": 6,
        "params": params
    });

    let _ = send_request(address, request).await?;
    Ok(true)
}

pub fn gen_id() -> i64 {
    let mut rng = rand::thread_rng();
    rng.gen_range((1 << 30)..(1 << 31))
}

/// 根据查询字符串查找笔记
///
/// # Arguments
///
/// * `address` - AnkiConnect 服务器地址
/// * `query` - 查询字符串，使用 Anki 的搜索语法，例如 "deck:current tag:marked"
///
/// # Returns
///
/// 返回匹配的笔记 ID 列表
pub async fn find_notes(address: &str, query: &str) -> Result<Vec<i64>, AnkiConnectError> {
    let request = json!({
        "action": "findNotes",
        "version": 6,
        "params": {
            "query": query
        }
    });

    let response = send_request(address, request).await?;

    // 解析响应中的笔记 ID 列表
    response
        .as_array()
        .map(|arr| arr.iter().filter_map(|v| v.as_i64()).collect())
        .ok_or_else(|| AnkiConnectError::InvalidResponse("Expected array of note IDs".to_string()))
}

/// 根据笔记 ID 获取笔记信息
///
/// # Arguments
///
/// * `address` - AnkiConnect 服务器地址
/// * `note_ids` - 笔记 ID 列表
///
/// # Returns
///
/// 返回笔记信息列表，每个笔记包含 noteId、modelName、fields、tags 等信息
pub async fn notes_info(address: &str, note_ids: Vec<i64>) -> Result<Vec<Value>, AnkiConnectError> {
    let request = json!({
        "action": "notesInfo",
        "version": 6,
        "params": {
            "notes": note_ids
        }
    });

    let response = send_request(address, request).await?;

    response
        .as_array()
        .map(|arr| arr.to_vec())
        .ok_or_else(|| AnkiConnectError::InvalidResponse("Expected array of note info".to_string()))
}

/// 组合使用 findNotes 和 notesInfo 获取完整的笔记信息
///
/// # Arguments
///
/// * `address` - AnkiConnect 服务器地址
/// * `query` - 查询字符串
///
/// # Returns
///
/// 返回匹配查询的完整笔记信息列表
pub async fn find_notes_info(address: &str, query: &str) -> Result<Vec<Value>, AnkiConnectError> {
    // 先查找匹配的笔记 ID
    let note_ids = find_notes(address, query).await?;

    if note_ids.is_empty() {
        return Ok(Vec::new());
    }

    // 获取这些笔记的详细信息
    notes_info(address, note_ids).await
}

#[derive(Debug)]
pub struct ModelInfo {
    pub id: i64,
    pub fields: Vec<String>,
    pub front: String,
    pub back: String,
    pub css: String,
}

// 获取模型模板信息
pub async fn get_model_templates(
    address: &str,
    model_name: &str,
) -> Result<serde_json::Value, AnkiConnectError> {
    let request = json!({
        "action": "modelTemplates",
        "version": 6,
        "params": {
            "modelName": model_name
        }
    });

    send_request(address, request).await
}

// 获取模型字段名称
pub async fn get_model_field_names(
    address: &str,
    model_name: &str,
) -> Result<serde_json::Value, AnkiConnectError> {
    let request = json!({
        "action": "modelFieldNames",
        "version": 6,
        "params": {
            "modelName": model_name
        }
    });

    send_request(address, request).await
}

// 获取模型样式
pub async fn get_model_styling(
    address: &str,
    model_name: &str,
) -> Result<serde_json::Value, AnkiConnectError> {
    let request = json!({
        "action": "modelStyling",
        "version": 6,
        "params": {
            "modelName": model_name
        }
    });

    send_request(address, request).await
}

// 获取所有模型ID
pub async fn get_model_names_and_ids(address: &str) -> Result<serde_json::Value, AnkiConnectError> {
    let request = json!({
        "action": "modelNamesAndIds",
        "version": 6
    });

    send_request(address, request).await
}

// 使用拆分后的API函数重构get_model_info_from_anki_connect
pub async fn get_model_info_from_anki_connect(
    address: &str,
    model_name: &str,
) -> Result<ModelInfo, AnkiConnectError> {
    // 并行获取所有需要的信息
    let (templates, fields, styling, model_ids) = tokio::try_join!(
        get_model_templates(address, model_name),
        get_model_field_names(address, model_name),
        get_model_styling(address, model_name),
        get_model_names_and_ids(address)
    )?;

    // 提取模型ID
    let id = model_ids
        .as_object()
        .and_then(|obj| obj.get(model_name))
        .and_then(|v| v.as_i64())
        .ok_or_else(|| {
            AnkiConnectError::InvalidResponse(format!("Model ID not found for {}", model_name))
        })?;

    // 提取模板信息
    let templates = templates
        .as_object()
        .and_then(|obj| obj.iter().next())
        .map(|(_, value)| value)
        .ok_or_else(|| AnkiConnectError::InvalidResponse("Missing template data".to_string()))?;

    let front = templates
        .get("Front")
        .and_then(|v| v.as_str())
        .ok_or_else(|| AnkiConnectError::InvalidResponse("Missing front template".to_string()))?
        .to_string();

    let back = templates
        .get("Back")
        .and_then(|v| v.as_str())
        .ok_or_else(|| AnkiConnectError::InvalidResponse("Missing back template".to_string()))?
        .to_string();

    // 提取CSS样式
    let css = styling
        .get("css")
        .and_then(|v| v.as_str())
        .ok_or_else(|| AnkiConnectError::InvalidResponse("Missing CSS".to_string()))?
        .to_string();

    // 提取字段列表
    let fields = fields
        .as_array()
        .ok_or_else(|| AnkiConnectError::InvalidResponse("Invalid fields format".to_string()))?
        .iter()
        .filter_map(|v| v.as_str())
        .map(String::from)
        .collect();

    Ok(ModelInfo {
        id,
        fields,
        front,
        back,
        css,
    })
}
