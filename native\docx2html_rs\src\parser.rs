// parser copy.txt

#![allow(unused)]
use crate::formula::FormulaConverter;
use crate::image_converter::ImageConverter;

use crate::{DocxContent, Image, Style, StyleProperties, TextRun};
use crate::{DocxDocument, DocxError};
use crate::{ListLevel, ListStyle};
use crate::{ParagraphType, VerticalAlign};
use base64::{self, Engine as _};
use quick_xml::events::Event;
use quick_xml::Reader;
use std::collections::HashMap;

struct ParagraphData {
    runs: Vec<TextRun>,
    properties: StyleProperties,
}

enum ParsedElement {
    Paragraph(ParagraphData),
    Table(String),
}

pub(crate) struct DocxParser {
    content: DocxContent,
    lists: Vec<ListStyle>,
    // 用于跟踪列表项编号的计数器
    list_counters: HashMap<(String, u8), u32>,
    // 公式转换器
    formula_converter: FormulaConverter,
    // 控制LaTeX是否需要为JavaScript转义（将\转义为\\）
    escape_latex_for_js: bool,
    // 文档默认字体大小
    document_default_font_size: Option<f32>,
}
// 表示一个单元格的属性和状态
#[derive(Debug, Clone)]
struct TableCell {
    content: String,
    colspan: u32,
    vmerge_type: String, // "none", "restart" 或 "continue"
    rowspan: u32,        // 计算出的实际行跨度
    occupied: bool,      // 单元格是否需要渲染
    source_row: usize,   // 单元格的原始行索引（用于调试）
    source_col: usize,   // 单元格的原始列索引（用于调试）
}

impl TableCell {
    fn new(content: String, colspan: u32, vmerge_type: String, row: usize, col: usize) -> Self {
        let occupied = vmerge_type != "continue"; // continue单元格默认不渲染

        Self {
            content,
            colspan,
            vmerge_type,
            rowspan: 1, // 默认为1
            occupied,
            source_row: row,
            source_col: col,
        }
    }

    fn is_restart(&self) -> bool {
        self.vmerge_type == "restart"
    }

    fn is_continue(&self) -> bool {
        self.vmerge_type == "continue"
    }
}

// 表示表格结构
#[derive(Debug)]
struct TableStructure {
    grid: Vec<Vec<Option<TableCell>>>, // 二维网格
    max_cols: usize,                   // 表格的最大列数
}

impl TableStructure {
    fn new() -> Self {
        Self {
            grid: Vec::new(),
            max_cols: 0,
        }
    }

    fn add_row(&mut self) {
        self.grid.push(Vec::new());
    }

    // 在特定位置放置单元格
    fn place_cell(&mut self, row: usize, col: usize, cell: Option<TableCell>) -> bool {
        // 确保网格足够大
        while self.grid.len() <= row {
            self.add_row();
        }

        let current_row = &mut self.grid[row];
        // 确保行足够宽以放置单元格
        while current_row.len() <= col {
            current_row.push(None);
        }

        // 如果位置已经有单元格，记录一个警告
        if current_row[col].is_some() {
            println!("Warning: Overwriting cell at row {}, col {}", row, col);
            return false;
        }

        current_row[col] = cell;

        // 更新最大列数
        self.max_cols = self.max_cols.max(col + 1);
        true
    }

    // 查找网格中第一个空闲位置
    fn find_first_available(&self, row: usize) -> usize {
        if row >= self.grid.len() {
            return 0; // 新行，从0开始
        }

        let row_data = &self.grid[row];
        for col in 0..row_data.len() {
            if row_data[col].is_none() {
                return col;
            }
        }

        row_data.len() // 返回行末尾
    }

    // 确保特定位置是否可用（无单元格或为None）
    fn is_position_available(&self, row: usize, col: usize) -> bool {
        if row >= self.grid.len() {
            return true; // 新行
        }

        let row_data = &self.grid[row];
        col >= row_data.len() || row_data[col].is_none()
    }

    // 计算所有单元格的实际rowspan
    fn calculate_rowspans(&mut self) {
        let rows = self.grid.len();

        // 第一遍: 扩展所有行到最大列数
        for row in 0..rows {
            while self.grid[row].len() < self.max_cols {
                self.grid[row].push(None);
            }
        }

        // 第二遍: 收集rowspan信息
        // 存储 (row, col, rowspan) 元组，避免同时借用mutable和immutable
        let mut rowspan_info = Vec::new();

        for row in 0..rows {
            for col in 0..self.max_cols {
                if col < self.grid[row].len() {
                    if let Some(ref cell) = self.grid[row][col] {
                        if cell.is_restart() {
                            // 计算实际rowspan：向下查找有多少个continue单元格
                            let mut rowspan = 1;
                            let mut next_row = row + 1;

                            while next_row < rows {
                                if col < self.grid[next_row].len() {
                                    if let Some(ref next_cell) = self.grid[next_row][col] {
                                        if next_cell.is_continue() {
                                            rowspan += 1;
                                            next_row += 1;
                                            continue;
                                        }
                                    }
                                }
                                break;
                            }

                            // 保存rowspan信息，稍后应用
                            rowspan_info.push((row, col, rowspan));
                        }
                    }
                }
            }
        }

        // 第三遍: 应用rowspan值
        for (row, col, rowspan) in rowspan_info {
            if let Some(ref mut cell) = self.grid[row][col] {
                cell.rowspan = rowspan;
            }
        }
    }

    // 打印网格结构的调试信息
    fn debug_print(&self) {
        println!("Table Grid Structure:");
        println!(
            "Total rows: {}, Max columns: {}",
            self.grid.len(),
            self.max_cols
        );

        for (row_idx, row) in self.grid.iter().enumerate() {
            print!("Row {}: ", row_idx);
            for (col_idx, cell) in row.iter().enumerate() {
                if let Some(ref cell) = cell {
                    print!(
                        "[{}x{} {}] ",
                        cell.colspan,
                        cell.rowspan,
                        if cell.occupied { "O" } else { "C" }
                    );
                } else {
                    print!("[ ] ");
                }
            }
            println!();
        }
    }

    // 渲染为HTML
    fn to_html(&self) -> String {
        let mut html =
            String::from("\n<table border=\"1\" style=\"border-collapse: collapse;\">\n");

        for row in 0..self.grid.len() {
            html.push_str("    <tr>\n");

            let mut col = 0;
            while col < self.max_cols {
                // 检查单元格是否存在且需要渲染
                let should_render = if col < self.grid[row].len() {
                    if let Some(ref cell) = self.grid[row][col] {
                        let result = cell.occupied;
                        if result {
                            // 渲染占用的单元格
                            html.push_str("        <td");

                            if cell.colspan > 1 {
                                html.push_str(&format!(" colspan=\"{}\"", cell.colspan));
                            }

                            if cell.rowspan > 1 {
                                html.push_str(&format!(" rowspan=\"{}\"", cell.rowspan));
                            }

                            html.push_str(">\n");
                            html.push_str(&cell.content);
                            html.push_str("        </td>\n");

                            // 移动到下一个位置，跳过被colspan占据的列
                            col += cell.colspan as usize;
                        } else {
                            // 不渲染此单元格，只移动到下一列
                            col += 1;
                        }

                        result
                    } else {
                        // 空位置，移动到下一列
                        col += 1;
                        false
                    }
                } else {
                    // 超出当前行长度，移动到下一列
                    col += 1;
                    false
                };

                // 如果单元格已被渲染，则跳过下一步的col递增
                if !should_render {
                    // 什么也不做，col已经在上面的逻辑中递增了
                }
            }

            html.push_str("    </tr>\n");
        }

        html.push_str("</table>\n");
        html
    }
}

impl DocxParser {
    pub fn new(content: DocxContent, escape_latex_for_js: Option<bool>) -> Self {
        let mut parser = Self {
            content,
            lists: Vec::new(),
            list_counters: HashMap::new(),
            formula_converter: FormulaConverter::new(),
            escape_latex_for_js: escape_latex_for_js.unwrap_or(false),
            document_default_font_size: None,
        };
        // Initialize list styles at creation time
        if let Ok(lists) = parser.parse_numbering() {
            // LOG: Print all parsed list styles
            println!("[DEBUG] Parsed {} list style definitions.", lists.len());
            for l in &lists {
                println!("  - ListStyle ID: {}", l.id);
            }
            parser.lists = lists;
        } else {
            println!("[WARN] Could not parse numbering definitions.");
        }

        // Detect document default font size
        parser.detect_default_font_size();

        parser
    }

    pub fn parse(mut self) -> Result<DocxDocument, DocxError> {
        let styles = self.parse_styles()?;
        let lists = self.lists.clone();
        let images = self.parse_relationships_and_media()?;
        let content = self.parse_document_and_build_html()?;

        Ok(DocxDocument {
            content,
            styles,
            images,
            lists,
        })
    }

    // REFACTOR: The main parsing and rendering loop.
    // This function now orchestrates the entire process, maintaining state correctly.
    fn parse_document_and_build_html(&mut self) -> Result<String, DocxError> {
        let document_content = self.content.document.clone();
        let mut reader = Reader::from_str(&document_content);
        reader.trim_text(true);
        let mut buf = Vec::new();
        let mut html_output = String::new();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) => match e.name().as_ref() {
                    b"w:p" => {
                        let para_data = self.parse_paragraph(&mut reader)?;
                        let para_html = self.build_paragraph_html(para_data)?;
                        html_output.push_str(&para_html);
                    }
                    b"w:tbl" => {
                        let table_html = self.parse_table(&mut reader)?;
                        html_output.push_str(&table_html);
                    }
                    _ => {}
                },
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        Ok(html_output)
    }

    // REFACTOR: This helper function decides how to format a paragraph based on its type.
    fn build_paragraph_html(&mut self, data: ParagraphData) -> Result<String, DocxError> {
        let combined_html = self.combine_runs_to_html(&data.runs, &data.properties);

        match data.properties.paragraph_type {
            ParagraphType::ListItem { list_id, level } => {
                // All list logic is now centralized here
                self.format_list_item_html(&list_id, level, &combined_html)
            }
            ParagraphType::Heading(level) => {
                Ok(format!("\n<h{}>{}</h{}>\n", level, combined_html, level))
            }
            ParagraphType::Normal => Ok(format!("<p>\n    {}\n</p>\n", combined_html)),
        }
    }

    // Helper function to combine text runs into styled HTML
    fn combine_runs_to_html(&self, runs: &[TextRun], paragraph_properties: &StyleProperties) -> String {
        runs.iter()
            .map(|run| self.text_run_to_html_with_fallback(run, paragraph_properties))
            .collect::<Vec<_>>()
            .join("")
    }

    // Convert a single text run to HTML with inline styling
    fn text_run_to_html(&self, run: &TextRun) -> String {
        if self.has_styling(&run.properties) {
            let style_attr = self.build_style_attribute(&run.properties);
            format!("<span{}>{}</span>", style_attr, run.text)
        } else {
            run.text.clone()
        }
    }

    // Convert a single text run to HTML with paragraph-level styling fallback
    fn text_run_to_html_with_fallback(&self, run: &TextRun, paragraph_properties: &StyleProperties) -> String {
        // Merge run properties with paragraph properties as fallback
        let mut merged_properties = run.properties.clone();

        // Apply paragraph-level properties if run doesn't have them
        if merged_properties.font_size.is_none() && paragraph_properties.font_size.is_some() {
            merged_properties.font_size = paragraph_properties.font_size;
        }

        // Apply default font size if neither run nor paragraph has font size
        if merged_properties.font_size.is_none() {
            // Use document default font size if available, otherwise fallback to 12pt
            merged_properties.font_size = Some(self.document_default_font_size.unwrap_or(12.0));
        }

        if !merged_properties.bold && paragraph_properties.bold {
            merged_properties.bold = paragraph_properties.bold;
        }
        if !merged_properties.italic && paragraph_properties.italic {
            merged_properties.italic = paragraph_properties.italic;
        }
        if merged_properties.color.is_none() && paragraph_properties.color.is_some() {
            merged_properties.color = paragraph_properties.color.clone();
        }
        if merged_properties.font_family.is_none() && paragraph_properties.font_family.is_some() {
            merged_properties.font_family = paragraph_properties.font_family.clone();
        }

        if self.has_styling(&merged_properties) {
            let style_attr = self.build_style_attribute(&merged_properties);
            eprintln!("[DEBUG] Text run '{}' with merged styling: font_size={:?}, bold={}, italic={}, color={:?}",
                     run.text, merged_properties.font_size, merged_properties.bold, merged_properties.italic, merged_properties.color);
            format!("<span{}>{}</span>", style_attr, run.text)
        } else {
            eprintln!("[DEBUG] Text run '{}' has no styling after merge", run.text);
            run.text.clone()
        }
    }

    // Check if the run has any styling that needs to be applied
    fn has_styling(&self, properties: &StyleProperties) -> bool {
        properties.bold
            || properties.italic
            || properties.underline
            || properties.strike_through
            || properties.color.is_some()
            || properties.highlight.is_some()
            || properties.font_family.is_some()
            || properties.font_size.is_some()
            || properties.vertical_align.is_some()
    }

    // Build the style attribute string for a text run
    fn build_style_attribute(&self, properties: &StyleProperties) -> String {
        let mut styles = Vec::new();

        if properties.bold {
            styles.push("font-weight: bold".to_string());
        }

        if properties.italic {
            styles.push("font-style: italic".to_string());
        }

        if properties.underline {
            styles.push("text-decoration: underline".to_string());
        }

        if properties.strike_through {
            styles.push("text-decoration: line-through".to_string());
        }

        if let Some(color) = &properties.color {
            // 跳过纯黑色(#000000)，因为这是默认颜色
            if !Self::is_black_color(color) {
                styles.push(format!("color: {}", Self::format_color(color)));
            }
        }

        if let Some(highlight) = &properties.highlight {
            styles.push(format!("background-color: {}", Self::format_color(highlight)));
        }

        if let Some(font_family) = &properties.font_family {
            styles.push(format!("font-family: '{}'", font_family));
        }

        if let Some(font_size) = properties.font_size {
            styles.push(format!("font-size: {}pt", font_size));
        }

        if let Some(vertical_align) = &properties.vertical_align {
            match vertical_align {
                VerticalAlign::Superscript => styles.push("vertical-align: super".to_string()),
                VerticalAlign::Subscript => styles.push("vertical-align: sub".to_string()),
            }
        }

        if styles.is_empty() {
            String::new()
        } else {
            format!(" style=\"{}\"", styles.join("; "))
        }
    }

    // Helper function to format color values correctly
    fn format_color(color: &str) -> String {
        // Check if the color is a hex color (all characters are hex digits)
        if color.len() == 6 && color.chars().all(|c| c.is_ascii_hexdigit()) {
            format!("#{}", color)
        } else {
            // It's a named color, use it as-is
            color.to_string()
        }
    }

    // Helper function to check if a color is pure black (#000000)
    fn is_black_color(color: &str) -> bool {
        // Check for hex format without # prefix
        if color.len() == 6 && color.chars().all(|c| c.is_ascii_hexdigit()) {
            return color.to_lowercase() == "000000";
        }
        // Check for hex format with # prefix
        if color.len() == 7 && color.starts_with('#') {
            return color.to_lowercase() == "#000000";
        }
        // Check for named colors
        color.to_lowercase() == "black"
    }

    // REFACTOR & FIX: This version correctly resolves the borrow checker error.
    fn format_list_item_html(
        &mut self,
        list_id: &str,
        level: u8,
        text: &str,
    ) -> Result<String, DocxError> {
        // LOG: Log the requested list ID before processing
        println!(
            "[DEBUG] Formatting list item. list_id: '{}', level: {}, text: '{}'",
            list_id,
            level,
            text.chars().take(30).collect::<String>()
        );

        // --- Step 1: READ all necessary data. Clone it to break the borrow lifetime. ---
        let (list_level, all_levels) = {
            let list_style = match self.get_list_style(list_id)? {
                Some(style) => style,
                None => {
                    // LOG: Add a much more detailed error message  
                    let available_ids: Vec<String> = self.lists.iter().map(|l| l.id.clone()).collect();
                    println!(
                        "[WARN] List style with id '{}' not found. Available list IDs are: {:?}. Using default bullet formatting.",
                        list_id, available_ids
                    );
                    
                    // Return a simple formatted paragraph instead of failing
                    return Ok(format!("<p>{}</p>\n", text));
                }
            };

            let current_level_style = list_style
                .levels
                .iter()
                .find(|l| l.level == level)
                .cloned() // Clone the ListLevel to own it.
                .ok_or_else(|| {
                    DocxError::Structure(format!(
                        "Level {} not found for list id '{}'.",
                        level, list_id
                    ))
                })?;

            // Clone the whole vector of levels for later lookups.
            let all_level_styles = list_style.levels.clone();

            (current_level_style, all_level_styles)
        };
        // The immutable borrow of `self` from `get_list_style` ends here.

        // --- Step 2: MUTATE the state now that we are free to do so. ---
        // 1. Reset counters for all deeper levels.
        for l in (level + 1)..9 {
            self.list_counters.remove(&(list_id.to_string(), l));
        }

        // 2. Get or initialize the counter for the current level.
        let counter = self
            .list_counters
            .entry((list_id.to_string(), level))
            .or_insert(list_level.start_at);

        // 3. The number for *this* item is the current value of the counter.
        let current_number = *counter;

        // 4. Increment the counter for the *next* item at this level.
        *counter += 1;
        // --- End of State Mutation ---

        // --- Step 3: FORMAT the output using the data we collected. ---
        let mut level_text = list_level.text.clone();

        // Replace placeholders like %1, %2, etc.
        for i in 1..=9 {
            let placeholder = format!("%{}", i);
            if level_text.contains(&placeholder) {
                let level_idx = (i - 1) as u8;

                // For the current level, use the number we just determined.
                // For parent levels, look up their current counter value.
                let number_to_format = if level_idx == level {
                    current_number
                } else {
                    // Use a safe fallback of 1 if a parent counter isn't set (shouldn't happen in valid docx)
                    self.list_counters
                        .get(&(list_id.to_string(), level_idx))
                        .cloned()
                        .unwrap_or(1)
                };

                // Get the formatting style for the placeholder's level from our cloned data
                let (num_format, num_format_ext) = all_levels
                    .iter()
                    .find(|l| l.level == level_idx)
                    .map(|l| (l.format.as_str(), l.format_ext.as_ref()))
                    .unwrap_or(("decimal", None));

                let formatted_number =
                    self.format_number(number_to_format, num_format, num_format_ext);
                level_text = level_text.replace(&placeholder, &formatted_number);
            }
        }

        // Handle special bullet characters (from PUA fonts like Symbol/Wingdings)
        let final_marker = level_text
            .chars()
            .map(|c| {
                if c >= '\u{F000}' && c <= '\u{F8FF}' {
                    // PUA range
                    let hex_code = format!("{:X}", c as u32);
                    self.get_special_list_character(&hex_code).unwrap_or('•')
                } else {
                    c
                }
            })
            .collect::<String>();

        let indent_px = list_level.indent / 15;

        Ok(format!(
            "<div class=\"list-item\" style=\"margin-left: {}px;\">\n    <span class=\"list-marker\">{}</span><div class=\"list-content\">{}</div>\n</div>\n",
            indent_px, final_marker, text
        ))
    }

    // ... The rest of the file is identical to the previous version ...
    // (I'm omitting it here for brevity, but make sure it's all present in your file)
    fn parse_paragraph(&mut self, reader: &mut Reader<&[u8]>) -> Result<ParagraphData, DocxError> {
        let mut buf = Vec::new();
        let mut runs = Vec::new();
        let mut style_properties = StyleProperties::default();
        let mut in_hyperlink = false;
        let mut hyperlink_runs = Vec::new();
        let mut hyperlink_id = String::new();

        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(ref e)) => match e.name().as_ref() {
                    b"w:pPr" => {
                        self.parse_paragraph_properties(&mut style_properties, reader)?;
                    }
                    b"w:hyperlink" => {
                        in_hyperlink = true;
                        hyperlink_runs.clear();
                        if let Some(attr) = e
                            .attributes()
                            .find(|a| a.as_ref().map_or(false, |a| a.key.as_ref() == b"r:id"))
                        {
                            if let Ok(val) = attr.unwrap().unescape_value() {
                                hyperlink_id = val.into_owned();
                            }
                        }
                    }
                    b"w:r" => {
                        let text_run = self.parse_run(reader)?;
                        if in_hyperlink {
                            hyperlink_runs.push(text_run);
                        } else {
                            runs.push(text_run);
                        }
                    }
                    b"w:tbl" => {
                        let table_html = self.parse_table(reader)?;
                        runs.push(TextRun::plain(table_html));
                    }
                    b"m:oMathPara" => {
                        let e = e.clone();
                        let math_xml = self.parse_math_formula(reader, &e)?;
                        let latex = match self.formula_converter.convert(&math_xml) {
                            Ok(pure_latex) => {
                                let wrapped = format!("\\[{}\\]", pure_latex);
                                if self.escape_latex_for_js {
                                    wrapped.replace('\\', "\\\\")
                                } else {
                                    wrapped
                                }
                            }
                            Err(e) => {
                                eprintln!("[ERROR] Failed to convert math formula: {:?}", e);
                                "[Math formula conversion error]".to_string()
                            }
                        };
                        runs.push(TextRun::plain(latex));
                    }
                    b"m:oMath" => {
                        let e = e.clone();
                        let math_xml = self.parse_math_formula(reader, &e)?;
                        let latex = match self.formula_converter.convert(&math_xml) {
                            Ok(pure_latex) => {
                                let wrapped = format!("\\({}\\)", pure_latex);
                                if self.escape_latex_for_js {
                                    wrapped.replace('\\', "\\\\")
                                } else {
                                    wrapped
                                }
                            }
                            Err(e) => {
                                eprintln!("[ERROR] Failed to convert math formula: {:?}", e);
                                "[Math formula conversion error]".to_string()
                            }
                        };
                        runs.push(TextRun::plain(latex));
                    }
                    _ => {}
                },
                Ok(Event::End(ref e)) => {
                    match e.name().as_ref() {
                        b"w:hyperlink" => {
                            in_hyperlink = false;
                            if !hyperlink_id.is_empty() {
                                if let Ok(Some(target)) =
                                    self.get_relationship_target(&hyperlink_id)
                                {
                                    // Combine all hyperlink runs into a single hyperlink
                                    let combined_text = hyperlink_runs.iter()
                                        .map(|run| run.text.as_str())
                                        .collect::<Vec<_>>()
                                        .join("");
                                    let hyperlink_html = format!("<a href=\"{}\">{}</a>", target, combined_text);
                                    runs.push(TextRun::plain(hyperlink_html));
                                } else {
                                    // Add runs without hyperlink formatting
                                    runs.extend(hyperlink_runs.drain(..));
                                }
                            } else {
                                // Add runs without hyperlink formatting
                                runs.extend(hyperlink_runs.drain(..));
                            }
                            hyperlink_runs.clear();
                            hyperlink_id.clear();
                        }
                        b"w:p" => {
                            // The paragraph has been fully parsed. Return the structured data.
                            return Ok(ParagraphData {
                                runs,
                                properties: style_properties,
                            });
                        }
                        _ => {}
                    }
                }
                Ok(Event::Empty(ref e)) if e.name().as_ref() == b"w:br" => {
                    runs.push(TextRun::plain("<br/>".to_string()));
                }
                Ok(Event::Eof) => break,
                Err(e) => return Err(DocxError::Xml(e)),
                _ => {}
            }
            buf.clear();
        }

        // Fallback for paragraphs that end at EOF
        Ok(ParagraphData {
            runs,
            properties: style_properties,
        })
    }

    // EXPANDED: Handles more numbering formats.

    fn format_number(&self, number: u32, format: &str, format_ext: Option<&String>) -> String {
        match format {
            "decimal" => number.to_string(),
            "lowerLetter" => {
                if number > 0 {
                    ((b'a' + ((number - 1) % 26) as u8) as char).to_string()
                } else {
                    "a".to_string()
                }
            }
            "upperLetter" => {
                if number > 0 {
                    ((b'A' + ((number - 1) % 26) as u8) as char).to_string()
                } else {
                    "A".to_string()
                }
            }
            "lowerRoman" => self.to_roman_numeral(number, false),
            "upperRoman" => self.to_roman_numeral(number, true),
            "bullet" => "•".to_string(),
            "none" => "".to_string(),

            "chineseCounting" => self.to_chinese_counting(number),

            // FIX: Correctly handle circled numbers and their conversion to String.
            "decimalEnclosedCircle" | "decimalEnclosedCircleChinese" => {
                if number > 0 && number <= 20 {
                    // 0x2460 is ①, so we need to add number - 1.
                    // Unicode circled numbers start at U+2460 for ①.
                    let circled_char = std::char::from_u32(0x2460 + number - 1);
                    circled_char.map_or_else(
                        || format!("({})", number), // Fallback if char is invalid
                        |c| c.to_string(),          // Convert valid char to String
                    )
                } else {
                    format!("({})", number) // Fallback for numbers > 20
                }
            }

            "specialChar" if format_ext.is_some() => {
                let code = format_ext.unwrap();
                if let Some(c) = self.get_special_list_character(code) {
                    c.to_string()
                } else if code.len() == 1 {
                    code.clone()
                } else {
                    "•".to_string()
                }
            }
            _ => number.to_string(),
        }
    }
    // NEW HELPER
    fn to_chinese_counting(&self, num: u32) -> String {
        let numerals = [
            "零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
        ];
        if num < numerals.len() as u32 {
            numerals[num as usize].to_string()
        } else {
            num.to_string()
        }
    }
    fn to_roman_numeral(&self, num: u32, uppercase: bool) -> String {
        if num == 0 {
            return "0".to_string();
        }
        let symbols = if uppercase {
            [
                ("M", 1000),
                ("CM", 900),
                ("D", 500),
                ("CD", 400),
                ("C", 100),
                ("XC", 90),
                ("L", 50),
                ("XL", 40),
                ("X", 10),
                ("IX", 9),
                ("V", 5),
                ("IV", 4),
                ("I", 1),
            ]
        } else {
            [
                ("m", 1000),
                ("cm", 900),
                ("d", 500),
                ("cd", 400),
                ("c", 100),
                ("xc", 90),
                ("l", 50),
                ("xl", 40),
                ("x", 10),
                ("ix", 9),
                ("v", 5),
                ("iv", 4),
                ("i", 1),
            ]
        };

        let mut result = String::new();
        let mut n = num;

        for &(sym, val) in symbols.iter() {
            while n >= val {
                result.push_str(sym);
                n -= val;
            }
        }

        result
    }

    fn parse_run(&self, reader: &mut Reader<&[u8]>) -> Result<TextRun, DocxError> {
        let mut buf = Vec::new();
        let mut text = String::new();
        let mut in_text = false;
        let mut in_math = false;
        let mut math_xml = String::new();
        let mut run_properties = StyleProperties::default();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) => match e.name().as_ref() {
                    b"w:rPr" => {
                        // Parse run properties to capture styling information
                        self.parse_run_properties(&mut run_properties, reader)?;
                    }
                    b"w:t" => {
                        in_text = true;
                    }
                    b"w:drawing" => {
                        // 解析图片
                        text.push_str(&self.parse_drawing(reader)?);
                    }
                    b"w:object" => {
                        // 解析VML对象（包含图片）
                        text.push_str(&self.parse_vml_object(reader)?);
                    }
                    b"w:br" => {
                        // 处理换行符
                        text.push_str("<br/>");
                    }
                    b"w:tab" => {
                        // 处理制表符
                        text.push_str("    ");
                    }
                    // 使用这个分支来处理所有段落级(Display)公式
                    b"m:oMathPara" | b"m:omathpara" | b"oMathPara" | b"omathpara" => {
                        // 关键：克隆开始事件'e'，因为它将被传递并被reader消耗
                        let e = e.clone();
                        println!(
                            "Found Display Math tag: <{}>",
                            String::from_utf8_lossy(e.name().as_ref())
                        );

                        // 1. 提取完整的、格式正确的公式XML
                        let math_xml = self.parse_math_formula(reader, &e)?;

                        // 2. 转换公式
                        let latex = match self.formula_converter.convert(&math_xml) {
                            Ok(latex) => {
                                // 对于段落公式，使用 '\[ ... \]'
                                format!("\\[{}\\]", latex)
                            }
                            Err(e) => {
                                println!("Failed to convert math: {:?}", e);
                                "[Math formula conversion error]".to_string()
                            }
                        };

                        // 3. 将结果添加到文本中
                        text.push_str(&latex);
                    }

                    // 使用这个分支来处理所有行内(Inline)公式
                    b"m:oMath" | b"m:omath" | b"oMath" | b"omath" => {
                        // 克隆开始事件'e'
                        let e = e.clone();
                        println!(
                            "Found Inline Math tag: <{}>",
                            String::from_utf8_lossy(e.name().as_ref())
                        );

                        // 1. 提取完整的、格式正确的公式XML
                        let math_xml = self.parse_math_formula(reader, &e)?;

                        // 2. 转换公式
                        let latex = match self.formula_converter.convert(&math_xml) {
                            Ok(latex) => {
                                // 对于行内公式，使用 '\( ... \)'
                                format!("\\({}\\)", latex)
                            }
                            Err(e) => {
                                println!("Failed to convert math: {:?}", e);
                                "[Math formula conversion error]".to_string()
                            }
                        };

                        // 3. 将结果添加到文本中
                        text.push_str(&latex);
                    }
                    _ => {
                        if in_math {
                            // 收集公式XML
                            let name = String::from_utf8_lossy(e.name().as_ref()).into_owned();
                            math_xml.push_str(&format!("<{}>", name));
                        }
                    }
                },
                Event::Text(e) if in_text => {
                    let content = e.unescape()?.into_owned();
                    text.push_str(&content);
                }
                Event::End(e) => match e.name().as_ref() {
                    b"w:r" => break,
                    b"w:t" => {
                        in_text = false;
                    }
                    _ => {
                        if in_math {
                            // 收集公式XML结束标签
                            let name = String::from_utf8_lossy(e.name().as_ref()).into_owned();
                            math_xml.push_str(&format!("</{}>", name));
                        }
                    }
                },
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        Ok(TextRun::new(text, run_properties))
    }

    /// 跳过当前元素（直到遇到匹配的结束标签）
    fn skip_element(&self, reader: &mut Reader<&[u8]>, elem_name: &[u8]) -> Result<(), DocxError> {
        let mut count = 1;
        let mut buf = Vec::new();
        while count > 0 {
            match reader.read_event_into(&mut buf)? {
                Event::Start(ref e) if e.name().as_ref() == elem_name => count += 1,
                Event::End(ref e) if e.name().as_ref() == elem_name => count -= 1,
                Event::Eof => break,
                _ => (),
            }
            buf.clear();
        }
        Ok(())
    }

    /// [最终版] 解析并提取一个完整的数学公式XML片段。
    /// 此函数利用 quick-xml 的 writer 将事件流重新序列化为字符串，
    /// 从而保证提取的XML与原始文档字节完美一致。
    fn parse_math_formula(
        &self,
        reader: &mut Reader<&[u8]>,
        start_element: &quick_xml::events::BytesStart<'_>,
    ) -> Result<String, DocxError> {
        let mut writer = quick_xml::Writer::new(Vec::new());
        // 1. 首先，手动将触发此函数的那个开始标签写入 writer
        writer.write_event(Event::Start(start_element.clone()))?;

        // 2. 循环读取事件，并将它们直接写入 writer，直到找到匹配的结束标签
        let mut depth = 1;
        let expected_end_name = start_element.name();
        let mut buf = Vec::new();

        loop {
            let event = reader.read_event_into(&mut buf)?;

            // 将当前事件直接写入writer
            writer.write_event(&event)?;

            match event {
                Event::Start(e) if e.name() == expected_end_name => {
                    depth += 1;
                }
                Event::End(e) if e.name() == expected_end_name => {
                    depth -= 1;
                    if depth == 0 {
                        break;
                    }
                }
                Event::Eof => {
                    return Err(DocxError::Custom(format!(
                        "Reached EOF while waiting for closing tag for <{}>",
                        String::from_utf8_lossy(expected_end_name.as_ref())
                    )));
                }
                _ => (),
            }
            buf.clear();
        }

        // 3. 从 writer 中获取最终的、字节完美的XML字符串
        let xml_bytes = writer.into_inner();
        let full_xml = String::from_utf8(xml_bytes).map_err(|e| {
            DocxError::Custom(format!("UTF-8 conversion error after extraction: {}", e))
        })?;

        // println!("[DEBUG][Extractor] Extracted XML:\n{}", full_xml);
        Ok(full_xml)
    }
    fn parse_drawing(&self, reader: &mut Reader<&[u8]>) -> Result<String, DocxError> {
        let mut buf = Vec::new();
        let mut image_rid = String::new();
        let mut width = 0;
        let mut height = 0;

        // println!("[DEBUG] Starting to parse drawing/image");

        // 查找图片ID和尺寸
        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Empty(e) | Event::Start(e) => {
                    match e.name().as_ref() {
                        b"a:blip" => {
                            // 获取图片引用ID (rId)
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"r:embed" {
                                    image_rid = String::from_utf8_lossy(&attr.value).into_owned();
                                    // println!("[DEBUG] Found image rId: {}", image_rid);
                                }
                            }
                        }
                        b"wp:extent" => {
                            // 获取图片尺寸
                            for attr in e.attributes().flatten() {
                                match attr.key.as_ref() {
                                    b"cx" => {
                                        if let Ok(w) =
                                            String::from_utf8_lossy(&attr.value).parse::<i32>()
                                        {
                                            width = w / 9525;
                                        }
                                    }
                                    b"cy" => {
                                        if let Ok(h) =
                                            String::from_utf8_lossy(&attr.value).parse::<i32>()
                                        {
                                            height = h / 9525;
                                        }
                                    }
                                    _ => {}
                                }
                            }
                        }
                        _ => {}
                    }
                }
                Event::End(e) if e.name().as_ref() == b"w:drawing" => break,
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        // 从 relationships 中获取图片文件名
        if let Some(image_name) = self.get_image_name(&image_rid)? {
            // println!("[DEBUG] Found image name from relationships: {}", image_name);

            // 尝试多种可能的路径格式
            let possible_paths = vec![
                format!("word/media/{}", image_name),
                image_name.clone(),
                format!("media/{}", image_name),
            ];

            for path in &possible_paths {
                // println!("[DEBUG] Trying path: {}", path);
                if let Some(image) = self.content.media.get(path) {
                    // println!("[DEBUG] Found image at path: {}, size: {} bytes", path, image.len());
                    let base64_data = base64::engine::general_purpose::STANDARD.encode(image);
                    let content_type = self.guess_content_type(&image_name);

                    if content_type == "image/emf" || content_type == "image/wmf" {
                        // 不支持SVG转换，尝试PNG转换
                        match ImageConverter::convert_to_png(image, &content_type) {
                            Ok(png_data) => {
                                let png_base64 =
                                    base64::engine::general_purpose::STANDARD.encode(&png_data);
                                println!("[INFO] Successfully converted {} to PNG", content_type);
                                return Ok(format!(
                                        "<img src=\"data:image/png;base64,{}\" width=\"{}\" height=\"{}\" alt=\"Converted from {}\"/>",
                                        png_base64, width, height, content_type
                                    ));
                            }
                            Err(_) => {
                                println!(
                                    "[WARNING] {} conversion failed, using fallback display",
                                    content_type
                                );
                            }
                        }

                        // EMF/WMF转换失败：使用简单的img标签，alt中提示解析失败
                        return Ok(format!(
                            "<img src=\"\" width=\"{}\" height=\"{}\" alt=\"图片解析失败：{} 格式暂不支持\" />",
                            width, height, content_type
                        ));
                    } else if ImageConverter::is_conversion_supported(&content_type) {
                        // 对于其他支持转换的格式，也尝试转换到PNG以确保兼容性
                        match ImageConverter::convert_to_png(image, &content_type) {
                            Ok(png_data) => {
                                let png_base64 =
                                    base64::engine::general_purpose::STANDARD.encode(&png_data);
                                return Ok(format!(
                                    "<img src=\"data:image/png;base64,{}\" width=\"{}\" height=\"{}\" alt=\"Converted from {}\"/>",
                                    png_base64, width, height, content_type
                                ));
                            }
                            Err(_) => {
                                // 其他格式转换失败：使用简单的img标签，alt中提示解析失败
                                return Ok(format!(
                                    "<img src=\"\" width=\"{}\" height=\"{}\" alt=\"图片解析失败：{} 格式转换错误\" />",
                                    width, height, content_type
                                ));
                            }
                        }
                    } else {
                        // 直接支持的格式，不需要转换
                        return Ok(format!(
                            "<img src=\"data:{};base64,{}\" width=\"{}\" height=\"{}\" alt=\"{} format\"/>",
                            content_type, base64_data, width, height, content_type
                        ));
                    }
                }
            }

            // 如果找不到图片，输出调试信息
            // println!("[DEBUG] Image not found: {}", image_name);
            // println!("[DEBUG] Available media files: {:?}", self.content.media.keys().collect::<Vec<_>>());
        } else {
            // println!("[DEBUG] Could not get image name from rId: {}", image_rid);
        }
        Ok(String::new())
    }

    fn parse_vml_object(&self, reader: &mut Reader<&[u8]>) -> Result<String, DocxError> {
        let mut buf = Vec::new();
        let mut image_rid = String::new();
        let mut width = 0;
        let mut height = 0;

        // println!("[DEBUG] Starting to parse VML object/image");

        // 查找图片ID和尺寸
        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Empty(e) | Event::Start(e) => {
                    match e.name().as_ref() {
                        b"v:imagedata" => {
                            // 获取图片引用ID (r:id)
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"r:id" {
                                    image_rid = String::from_utf8_lossy(&attr.value).into_owned();
                                    // println!("[DEBUG] Found VML image rId: {}", image_rid);
                                }
                            }
                        }
                        b"v:shape" => {
                            // 解析VML形状的样式（包含尺寸信息）
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"style" {
                                    let style_value = String::from_utf8_lossy(&attr.value);
                                    // 解析样式字符串，查找width和height
                                    for style_part in style_value.split(';') {
                                        let style_part = style_part.trim();
                                        if style_part.starts_with("width:") {
                                            if let Some(width_str) = style_part.strip_prefix("width:") {
                                                if let Some(width_str) = width_str.strip_suffix("pt") {
                                                    if let Ok(w) = width_str.parse::<f32>() {
                                                        width = w as i32;
                                                    }
                                                }
                                            }
                                        } else if style_part.starts_with("height:") {
                                            if let Some(height_str) = style_part.strip_prefix("height:") {
                                                if let Some(height_str) = height_str.strip_suffix("pt") {
                                                    if let Ok(h) = height_str.parse::<f32>() {
                                                        height = h as i32;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        _ => {}
                    }
                }
                Event::End(e) if e.name().as_ref() == b"w:object" => break,
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        // 处理找到的图片
        if !image_rid.is_empty() {
            if let Some(image_name) = self.get_image_name(&image_rid)? {
                // println!("[DEBUG] Found VML image name from relationships: {}", image_name);
                
                // 尝试多种可能的路径格式
                let possible_paths = vec![
                    format!("word/media/{}", image_name),
                    image_name.clone(),
                    format!("media/{}", image_name),
                ];

                for path in &possible_paths {
                    if let Some(image) = self.content.media.get(path) {
                        // println!("[DEBUG] Found VML image at path: {}, size: {} bytes", path, image.len());
                        let base64_data = base64::engine::general_purpose::STANDARD.encode(image);
                        let content_type = self.guess_content_type(&image_name);

                        if content_type == "image/emf" || content_type == "image/wmf" {
                            // 尝试转换
                            match ImageConverter::convert_to_png(image, &content_type) {
                                Ok(png_data) => {
                                    let png_base64 = base64::engine::general_purpose::STANDARD.encode(&png_data);
                                    // println!("[INFO] Successfully converted VML {} to PNG", content_type);
                                    return Ok(format!(
                                        "<img src=\"data:image/png;base64,{}\" width=\"{}\" height=\"{}\" alt=\"Converted from {}\"/>",
                                        png_base64, width, height, content_type
                                    ));
                                }
                                Err(e) => {
                                    // println!("[WARNING] VML {} conversion failed: {:?}", content_type, e);
                                }
                            }

                            // VML EMF/WMF转换失败：使用简单的img标签，alt中提示解析失败
                            return Ok(format!(
                                "<img src=\"\" width=\"{}\" height=\"{}\" alt=\"图片解析失败：{} 格式暂不支持\" />",
                                width, height, content_type
                            ));
                        } else {
                            // 其他支持的格式
                            match ImageConverter::convert_to_png(image, &content_type) {
                                Ok(png_data) => {
                                    let png_base64 = base64::engine::general_purpose::STANDARD.encode(&png_data);
                                    return Ok(format!(
                                        "<img src=\"data:image/png;base64,{}\" width=\"{}\" height=\"{}\" alt=\"Converted from {}\"/>",
                                        png_base64, width, height, content_type
                                    ));
                                }
                                Err(_) => {
                                    // VML其他格式转换失败：使用简单的img标签，alt中提示解析失败
                                    return Ok(format!(
                                        "<img src=\"\" width=\"{}\" height=\"{}\" alt=\"图片解析失败：{} 格式转换错误\" />",
                                        width, height, content_type
                                    ));
                                }
                            }
                        }
                    }
                }
                
                // println!("[DEBUG] VML image not found: {}", image_name);
                // println!("[DEBUG] Available media files: {:?}", self.content.media.keys().collect::<Vec<_>>());
            } else {
                // println!("[DEBUG] Could not get VML image name from rId: {}", image_rid);
            }
        } else {
            // println!("[DEBUG] No VML image rId found in object");
        }

        Ok(String::new())
    }

    fn get_image_name(&self, rid: &str) -> Result<Option<String>, DocxError> {
        let mut reader = Reader::from_str(&self.content.relationships);
        let mut buf = Vec::new();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Empty(e) => {
                    if e.name().as_ref() == b"Relationship" {
                        let mut is_target_rel = false;
                        let mut target = String::new();

                        for attr in e.attributes().flatten() {
                            match attr.key.as_ref() {
                                b"Id" if String::from_utf8_lossy(&attr.value) == rid => {
                                    is_target_rel = true;
                                }
                                b"Target" => {
                                    target = String::from_utf8_lossy(&attr.value).into_owned();
                                }
                                _ => {}
                            }
                        }

                        if is_target_rel {
                            return Ok(Some(target.trim_start_matches("media/").to_string()));
                        }
                    }
                }
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }
        Ok(None)
    }

    fn guess_content_type(&self, filename: &str) -> String {
        match filename
            .rsplit('.')
            .next()
            .unwrap_or("")
            .to_lowercase()
            .as_str()
        {
            "png" => "image/png",
            "jpg" | "jpeg" => "image/jpeg",
            "gif" => "image/gif",
            "bmp" => "image/bmp",
            "emf" => "image/emf",
            "wmf" => "image/wmf",
            "svg" => "image/svg+xml",
            "tiff" | "tif" => "image/tiff",
            "webp" => "image/webp",
            _ => "application/octet-stream",
        }
        .to_string()
    }

    fn parse_styles(&self) -> Result<Vec<Style>, DocxError> {
        let mut styles = Vec::new();
        let mut reader = Reader::from_str(&self.content.styles);
        let mut buf = Vec::new();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) => {
                    if e.name().as_ref() == b"w:style" {
                        if let Some(style) = self.parse_style(&mut reader)? {
                            styles.push(style);
                        }
                    }
                }
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        Ok(styles)
    }

    fn parse_style(&self, reader: &mut Reader<&[u8]>) -> Result<Option<Style>, DocxError> {
        let mut buf = Vec::new();
        let mut style_id = String::new();
        let mut properties = StyleProperties {
            font_family: None,
            font_size: None,
            bold: false,
            italic: false,
            color: None,
            highlight: None,
            underline: false,
            strike_through: false,
            vertical_align: None,
            paragraph_type: ParagraphType::Normal,
            indent_left: None,
            indent_right: None,
            indent_first_line: None,
        };

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) => {
                    match e.name().as_ref() {
                        b"w:rPr" => {
                            // 解析运行属性（字体、大小等）
                            self.parse_run_properties(&mut properties, reader)?;
                        }
                        b"w:pPr" => {
                            // 解析段落属性（缩进等）
                            self.parse_paragraph_properties(&mut properties, reader)?;
                        }
                        _ => {}
                    }
                }
                Event::Empty(e) => {
                    if e.name().as_ref() == b"w:styleId" {
                        for attr in e.attributes().flatten() {
                            if attr.key.as_ref() == b"w:val" {
                                style_id = String::from_utf8_lossy(&attr.value).into_owned();
                            }
                        }
                    }
                }
                Event::End(e) if e.name().as_ref() == b"w:style" => break,
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        if style_id.is_empty() {
            Ok(None)
        } else {
            Ok(Some(Style {
                id: style_id,
                properties,
            }))
        }
    }

    fn parse_run_properties(
        &self,
        properties: &mut StyleProperties,
        reader: &mut Reader<&[u8]>,
    ) -> Result<(), DocxError> {
        let mut buf = Vec::new();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Empty(e) | Event::Start(e) => {
                    match e.name().as_ref() {
                        b"w:b" => {
                            // 检查 w:val 属性
                            let mut is_bold = true;
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    is_bold = String::from_utf8_lossy(&attr.value) != "none"
                                        && String::from_utf8_lossy(&attr.value) != "0";
                                }
                            }
                            properties.bold = is_bold;
                        }
                        b"w:i" => {
                            // Check w:val attribute
                            let mut is_italic = true;
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    is_italic = String::from_utf8_lossy(&attr.value) != "none"
                                        && String::from_utf8_lossy(&attr.value) != "0";
                                }
                            }
                            properties.italic = is_italic;
                        }
                        b"w:u" => {
                            // Check w:val attribute
                            let mut is_underline = true;
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    is_underline = String::from_utf8_lossy(&attr.value) != "none"
                                        && String::from_utf8_lossy(&attr.value) != "0";
                                }
                            }
                            properties.underline = is_underline;
                        }
                        b"w:strike" => {
                            // Check w:val attribute
                            let mut is_strike_through = true;
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    is_strike_through = String::from_utf8_lossy(&attr.value)
                                        != "none"
                                        && String::from_utf8_lossy(&attr.value) != "0";
                                }
                            }
                            properties.strike_through = is_strike_through;
                        }
                        b"w:vertAlign" => {
                            // 解析上下标
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    properties.vertical_align = match attr.value.as_ref() {
                                        b"superscript" => Some(VerticalAlign::Superscript),
                                        b"subscript" => Some(VerticalAlign::Subscript),
                                        _ => None,
                                    };
                                }
                            }
                        }
                        b"w:color" => {
                            // 解析颜色
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    let color = String::from_utf8_lossy(&attr.value).into_owned();
                                    // 只有当颜色值不是 "auto" 时才设置颜色
                                    if color != "auto" {
                                        properties.color = Some(color);
                                    }
                                }
                            }
                        }
                        b"w:highlight" => {
                            // 解析高亮
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    properties.highlight =
                                        Some(String::from_utf8_lossy(&attr.value).into_owned());
                                }
                            }
                        }
                        b"w:shd" => {
                            // 解析背景色高亮
                            let mut fill_color = None;
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:fill" {
                                    fill_color =
                                        Some(String::from_utf8_lossy(&attr.value).into_owned());
                                }
                            }
                            // 只有当fill属性存在且不为auto时才设置高亮
                            if let Some(color) = fill_color {
                                if color != "auto" {
                                    properties.highlight = Some(color);
                                }
                            }
                        }
                        b"w:rFonts" => {
                            // 解析字体
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:ascii"
                                    || attr.key.as_ref() == b"w:eastAsia"
                                {
                                    properties.font_family =
                                        Some(String::from_utf8_lossy(&attr.value).into_owned());
                                }
                            }
                        }
                        b"w:sz" => {
                            // 解析字号
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    if let Ok(size) =
                                        String::from_utf8_lossy(&attr.value).parse::<f32>()
                                    {
                                        properties.font_size = Some(size / 2.0);
                                        // 转换为磅值
                                    }
                                }
                            }
                        }
                        _ => {}
                    }
                }
                Event::End(e) if e.name().as_ref() == b"w:rPr" => break,
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        Ok(())
    }

    fn parse_paragraph_properties(
        &self,
        properties: &mut StyleProperties,
        reader: &mut Reader<&[u8]>,
    ) -> Result<(), DocxError> {
        let mut buf = Vec::new();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Empty(e) | Event::Start(e) => {
                    match e.name().as_ref() {
                        b"w:pStyle" => {
                            // 解析段落样式，包括标题级别
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    let style_val = String::from_utf8_lossy(&attr.value);
                                    // Word 中的标题样式值为 "1" 到 "9"
                                    if let Ok(level) = style_val.parse::<u8>() {
                                        if level >= 1 && level <= 6 {
                                            properties.paragraph_type =
                                                ParagraphType::Heading(level);
                                        }
                                    }
                                }
                            }
                        }
                        b"w:outlineLvl" => {
                            // 直接通过大纲级别判断
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    if let Ok(level) =
                                        String::from_utf8_lossy(&attr.value).parse::<u8>()
                                    {
                                        if level < 6 {
                                            properties.paragraph_type =
                                                ParagraphType::Heading(level + 1);
                                        }
                                    }
                                }
                            }
                        }
                        b"w:ind" => {
                            // 解析缩进
                            for attr in e.attributes().flatten() {
                                match attr.key.as_ref() {
                                    b"w:left" => {
                                        if let Ok(val) =
                                            String::from_utf8_lossy(&attr.value).parse()
                                        {
                                            properties.indent_left = Some(val);
                                        }
                                    }
                                    b"w:right" => {
                                        if let Ok(val) =
                                            String::from_utf8_lossy(&attr.value).parse()
                                        {
                                            properties.indent_right = Some(val);
                                        }
                                    }
                                    b"w:firstLine" => {
                                        if let Ok(val) =
                                            String::from_utf8_lossy(&attr.value).parse()
                                        {
                                            properties.indent_first_line = Some(val);
                                        }
                                    }
                                    _ => {}
                                }
                            }
                        }
                        b"w:numPr" => {
                            // 解析列表样式
                            self.parse_number_properties(properties, reader)?;
                        }
                        b"w:rPr" => {
                            // 解析段落级别的运行属性（包括字体大小）
                            self.parse_run_properties(properties, reader)?;
                        }
                        _ => {}
                    }
                }
                Event::End(e) if e.name().as_ref() == b"w:pPr" => break,
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        Ok(())
    }

    // Detect the document's default font size by analyzing the most common font size
    fn detect_default_font_size(&mut self) {
        let mut font_size_counts: HashMap<u32, u32> = HashMap::new();

        // Parse the document.xml to collect font size information
        let mut reader = Reader::from_str(&self.content.document);
        let mut buf = Vec::new();

        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Empty(e)) | Ok(Event::Start(e)) => {
                    if e.name().as_ref() == b"w:sz" {
                        for attr in e.attributes().flatten() {
                            if attr.key.as_ref() == b"w:val" {
                                if let Ok(size) = String::from_utf8_lossy(&attr.value).parse::<u32>() {
                                    *font_size_counts.entry(size).or_insert(0) += 1;
                                }
                            }
                        }
                    }
                }
                Ok(Event::Eof) => break,
                Err(_) => break,
                _ => {}
            }
            buf.clear();
        }

        // Find the most common font size
        if let Some((&most_common_size, _)) = font_size_counts.iter().max_by_key(|(_, &count)| count) {
            self.document_default_font_size = Some(most_common_size as f32 / 2.0); // Convert from half-points to points
        } else {
            // Fallback to 12pt if no font sizes found
            self.document_default_font_size = Some(12.0);
        }
    }

    fn parse_number_properties(
        &self,
        properties: &mut StyleProperties,
        reader: &mut Reader<&[u8]>,
    ) -> Result<(), DocxError> {
        let mut buf = Vec::new();
        let mut list_id = String::new();
        let mut level = 0;

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Empty(e) => match e.name().as_ref() {
                    b"w:numId" => {
                        // 获取列表ID
                        for attr in e.attributes().flatten() {
                            if attr.key.as_ref() == b"w:val" {
                                list_id = String::from_utf8_lossy(&attr.value).into_owned();
                            }
                        }
                    }
                    b"w:ilvl" => {
                        // 获取列表级别
                        for attr in e.attributes().flatten() {
                            if attr.key.as_ref() == b"w:val" {
                                if let Ok(val) = String::from_utf8_lossy(&attr.value).parse() {
                                    level = val;
                                }
                            }
                        }
                    }
                    _ => {}
                },
                Event::End(e) if e.name().as_ref() == b"w:numPr" => break,
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        // 只有同时有 numId 和 ilvl 时才设置列表属性
        if !list_id.is_empty() {
            properties.paragraph_type = ParagraphType::ListItem {
                list_id: list_id.clone(),
                level,
            };
        }

        Ok(())
    }

    fn get_list_style(&self, list_id: &str) -> Result<Option<&ListStyle>, DocxError> {
        // 从已解析的列表样式中查找
        Ok(self.lists.iter().find(|style| style.id == list_id))
    }

    // 实现一个完整的parse_numbering方法来解析numbering.xml
    fn parse_numbering(&self) -> Result<Vec<ListStyle>, DocxError> {
        if self.content.numbering.is_empty() {
            return Ok(Vec::new());
        }
        let mut reader = Reader::from_str(&self.content.numbering);
        let mut buf = Vec::new();
        println!("[DEBUG] Starting to parse numbering.xml");

        // Step 1: Parse all abstract templates into a map.
        let mut abstract_nums: HashMap<String, Vec<ListLevel>> = HashMap::new();
        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) if e.name().as_ref() == b"w:abstractNum" => {
                    let mut abstract_id = String::new();
                    for attr in e.attributes().flatten() {
                        if attr.key.as_ref() == b"w:abstractNumId" {
                            abstract_id = String::from_utf8_lossy(&attr.value).into_owned();
                            break;
                        }
                    }
                    if !abstract_id.is_empty() {
                        println!(
                            "[DEBUG] Found abstractNum template with w:abstractNumId='{}'",
                            &abstract_id
                        );
                        let levels = self.parse_abstract_num_levels(&mut reader)?;
                        abstract_nums.insert(abstract_id, levels);
                    }
                }
                Event::Eof => break,
                _ => (),
            }
            buf.clear();
        }

        // Step 2: Parse all concrete list instances (<w:num>), applying overrides.
        let mut list_styles = Vec::new();
        let mut reader = Reader::from_str(&self.content.numbering);
        buf.clear();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) if e.name().as_ref() == b"w:num" => {
                    if let Some(style) = self.parse_num_instance(&mut reader, &e, &abstract_nums)? {
                        list_styles.push(style);
                    }
                }
                Event::Eof => break,
                _ => (),
            }
            buf.clear();
        }

        // Step 3: Handle implicit mappings where a paragraph might refer to an abstractNumId directly.
        for (abstract_id, levels) in &abstract_nums {
            if !list_styles.iter().any(|style| style.id == *abstract_id) {
                println!(
                    "[DEBUG] Creating implicit ListStyle for abstractNumId '{}'",
                    abstract_id
                );
                list_styles.push(ListStyle {
                    id: abstract_id.clone(),
                    levels: levels.clone(),
                });
            }
        }

        println!("[DEBUG] Finished parsing numbering.xml");
        Ok(list_styles)
    }

    fn parse_num_instance(
        &self,
        reader: &mut Reader<&[u8]>,
        start_element: &quick_xml::events::BytesStart<'_>,
        abstract_nums: &HashMap<String, Vec<ListLevel>>,
    ) -> Result<Option<ListStyle>, DocxError> {
        let mut num_id = String::new();
        for attr in start_element.attributes().flatten() {
            if attr.key.as_ref() == b"w:numId" {
                num_id = String::from_utf8_lossy(&attr.value).into_owned();
            }
        }
        if num_id.is_empty() {
            return Ok(None);
        }

        let mut abstract_id = String::new();
        let mut level_overrides: HashMap<u8, ListLevel> = HashMap::new();
        let mut buf = Vec::new();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) if e.name().as_ref() == b"w:lvlOverride" => {
                    let mut level_to_override = 0;
                    for attr in e.attributes().flatten() {
                        if attr.key.as_ref() == b"w:ilvl" {
                            level_to_override =
                                String::from_utf8_lossy(&attr.value).parse().unwrap_or(0);
                        }
                    }
                    if let Some(level) = self.parse_level_override(reader, level_to_override)? {
                        level_overrides.insert(level_to_override, level);
                    }
                }
                Event::Empty(e) if e.name().as_ref() == b"w:abstractNumId" => {
                    for attr in e.attributes().flatten() {
                        if attr.key.as_ref() == b"w:val" {
                            abstract_id = String::from_utf8_lossy(&attr.value).into_owned();
                        }
                    }
                }
                Event::End(e) if e.name().as_ref() == b"w:num" => break,
                Event::Eof => break,
                _ => (),
            }
            buf.clear();
        }

        if abstract_id.is_empty() {
            return Ok(None);
        }

        let base_levels = match abstract_nums.get(&abstract_id) {
            Some(levels) => levels.clone(),
            None => {
                println!(
                    "[WARN] Abstract num '{}' not found for num '{}'. Skipping.",
                    abstract_id, num_id
                );
                return Ok(None);
            }
        };

        let mut final_levels = Vec::new();
        for base_level in base_levels {
            if let Some(override_level) = level_overrides.remove(&base_level.level) {
                final_levels.push(override_level);
            } else {
                final_levels.push(base_level);
            }
        }
        final_levels.extend(level_overrides.into_values());

        println!(
            "[DEBUG] Created ListStyle '{}' from template '{}'",
            num_id, abstract_id
        );
        Ok(Some(ListStyle {
            id: num_id,
            levels: final_levels,
        }))
    }

    fn parse_level_override(
        &self,
        reader: &mut Reader<&[u8]>,
        level: u8,
    ) -> Result<Option<ListLevel>, DocxError> {
        let mut start_val = None;
        let mut level_props = None;
        let mut buf = Vec::new();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) if e.name().as_ref() == b"w:lvl" => {
                    level_props = Some(self.parse_level_properties(reader, level)?);
                }
                Event::Empty(e) if e.name().as_ref() == b"w:startOverride" => {
                    for attr in e.attributes().flatten() {
                        if attr.key.as_ref() == b"w:val" {
                            start_val = String::from_utf8_lossy(&attr.value).parse::<u32>().ok();
                        }
                    }
                }
                Event::End(e) if e.name().as_ref() == b"w:lvlOverride" => break,
                Event::Eof => break,
                _ => (),
            }
            buf.clear();
        }

        if let Some(mut props) = level_props {
            if let Some(start) = start_val {
                props.start_at = start;
            }
            return Ok(Some(props));
        }

        if let Some(start) = start_val {
            return Ok(Some(ListLevel {
                level,
                start_at: start,
                format: "decimal".to_string(),
                format_ext: None,
                text: "%1.".to_string(),
                indent: 0,
            }));
        }

        Ok(None)
    }

    fn parse_abstract_num_levels(
        &self,
        reader: &mut Reader<&[u8]>,
    ) -> Result<Vec<ListLevel>, DocxError> {
        let mut buf = Vec::new();
        let mut levels = Vec::new();
        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) if e.name().as_ref() == b"w:lvl" => {
                    let mut level_num = 0;
                    for attr in e.attributes().flatten() {
                        if attr.key.as_ref() == b"w:ilvl" {
                            if let Ok(val) = String::from_utf8_lossy(&attr.value).parse() {
                                level_num = val;
                                break;
                            }
                        }
                    }
                    levels.push(self.parse_level_properties(reader, level_num)?);
                }
                Event::End(e) if e.name().as_ref() == b"w:abstractNum" => break,
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }
        levels.sort_by_key(|level| level.level);
        Ok(levels)
    }

    fn parse_level_properties(
        &self,
        reader: &mut Reader<&[u8]>,
        level: u8,
    ) -> Result<ListLevel, DocxError> {
        let mut buf = Vec::new();
        let mut format = "decimal".to_string();
        let mut start_at = 1;
        let mut text = "%1.".to_string();
        let mut indent = 0;
        let mut special_char = None;
        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Empty(e) | Event::Start(e) => match e.name().as_ref() {
                    b"w:numFmt" => {
                        for attr in e.attributes().flatten() {
                            if attr.key.as_ref() == b"w:val" {
                                format = String::from_utf8_lossy(&attr.value).into_owned();
                            }
                        }
                    }
                    b"w:start" => {
                        for attr in e.attributes().flatten() {
                            if attr.key.as_ref() == b"w:val" {
                                if let Ok(val) = String::from_utf8_lossy(&attr.value).parse() {
                                    start_at = val;
                                }
                            }
                        }
                    }
                    b"w:lvlText" => {
                        for attr in e.attributes().flatten() {
                            if attr.key.as_ref() == b"w:val" {
                                let text_value = String::from_utf8_lossy(&attr.value).into_owned();
                                if text_value.len() == 1 && !text_value.contains('%') {
                                    if let Some(c) = text_value.chars().next() {
                                        if c >= '\u{F000}' && c <= '\u{F8FF}' {
                                            let hex_code = format!("{:X}", c as u32);
                                            special_char = Some(hex_code);
                                        }
                                    }
                                }
                                text = text_value;
                            }
                        }
                    }
                    b"w:pPr" => {
                        let mut ppr_buf = Vec::new();
                        loop {
                            match reader.read_event_into(&mut ppr_buf)? {
                                Event::Empty(e) if e.name().as_ref() == b"w:ind" => {
                                    for attr in e.attributes().flatten() {
                                        if attr.key.as_ref() == b"w:left" {
                                            if let Ok(val) =
                                                String::from_utf8_lossy(&attr.value).parse()
                                            {
                                                indent = val;
                                            }
                                        }
                                    }
                                }
                                Event::End(e) if e.name().as_ref() == b"w:pPr" => break,
                                Event::Eof => break,
                                _ => (),
                            }
                            ppr_buf.clear();
                        }
                    }
                    _ => {}
                },
                Event::End(e) if e.name().as_ref() == b"w:lvl" => break,
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        Ok(ListLevel {
            level,
            format,
            format_ext: special_char,
            start_at,
            text,
            indent,
        })
    }

    fn get_special_list_character(&self, code: &str) -> Option<char> {
        // Map Word's special character codes to Unicode characters
        match code {
            // From your numbering.xml
            // Wingdings Mappings from the provided XML file
            "F06C" => Some('●'),
            "F06E" => Some('■'),
            "F075" => Some('◆'),
            "F0FC" => Some('✔'),
            "F076" => Some('❖'),
            "F0B2" => Some('⟡'),
            "F0A8" => Some('◻'),
            "F0D8" => Some('⮚'),

            // Common PUA bullets
            "F0B7" => Some('•'),
            "2022" => Some('•'),
            "F0A7" => Some('■'),

            // Circled numbers
            "2460" => Some('①'), // Circled 1
            "2461" => Some('②'), // Circled 2
            "2462" => Some('③'), // Circled 3
            "2463" => Some('④'), // Circled 4
            "2464" => Some('⑤'), // Circled 5
            "2465" => Some('⑥'), // Circled 6
            "2466" => Some('⑦'), // Circled 7
            "2467" => Some('⑧'), // Circled 8
            "2468" => Some('⑨'), // Circled 9
            "2469" => Some('⑩'), // Circled 10
            _ => None,
        }
    }
    fn parse_cell_properties(
        &self,
        reader: &mut Reader<&[u8]>,
    ) -> Result<(u32, String), DocxError> {
        let mut buf = Vec::new();
        let mut colspan = 1;
        let mut vmerge_type = String::from("none");

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) | Event::Empty(e) => {
                    match e.name().as_ref() {
                        b"w:gridSpan" => {
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    match String::from_utf8_lossy(&attr.value).parse::<u32>() {
                                        Ok(span) => colspan = span,
                                        Err(_) => colspan = 1, // 默认值
                                    }
                                }
                            }
                        }
                        b"w:vMerge" => {
                            // 默认无属性值的<w:vMerge/>视为Continue
                            vmerge_type = String::from("continue");

                            // 检查是否有明确的值
                            let mut found_val = false;
                            for attr in e.attributes().flatten() {
                                if attr.key.as_ref() == b"w:val" {
                                    found_val = true;
                                    let val = String::from_utf8_lossy(&attr.value).to_lowercase();
                                    if val == "restart" {
                                        vmerge_type = String::from("restart");
                                    } else if val == "continue" {
                                        vmerge_type = String::from("continue");
                                    } else {
                                        // 未知值，使用默认
                                        vmerge_type = String::from("none");
                                    }
                                }
                            }

                            // 如果没有值属性并且是空标签，则是continue
                            if !found_val && e.name().as_ref() == b"w:vMerge" {
                                vmerge_type = String::from("continue");
                            }
                        }
                        _ => {}
                    }
                }
                Event::End(e) => {
                    if e.name().as_ref() == b"w:tcPr" {
                        break;
                    }
                }
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        Ok((colspan, vmerge_type))
    }

    // 修改后的parse_cell_content方法，用于处理嵌套表格
    fn parse_cell_content(&mut self, reader: &mut Reader<&[u8]>) -> Result<String, DocxError> {
        let mut buf = Vec::new();
        let mut content = String::new();
        let mut tc_depth = 1; // 跟踪w:tc标签的嵌套深度

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) => {
                    match e.name().as_ref() {
                        b"w:tc" => {
                            tc_depth += 1;
                        }
                        b"w:tbl" => {
                            // 发现嵌套表格，递归解析
                            content.push_str(&self.parse_nested_table(reader)?);
                        }
                        b"w:p" => {
                            // 解析段落
                            let para_data = self.parse_paragraph(reader)?;
                            let para_html = self.build_paragraph_html(para_data)?;
                            content.push_str(&para_html);
                        }
                        _ => {}
                    }
                }
                Event::End(e) => match e.name().as_ref() {
                    b"w:tc" => {
                        tc_depth -= 1;
                        if tc_depth == 0 {
                            break;
                        }
                    }
                    _ => {}
                },
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        Ok(content)
    }

    // 专门用于解析嵌套表格的方法
    fn parse_nested_table(&mut self, reader: &mut Reader<&[u8]>) -> Result<String, DocxError> {
        // 创建一个标记，表示这是嵌套表格
        let is_nested = true;

        // 直接调用主表格解析方法，但标记为嵌套
        self.parse_table_internal(reader, is_nested)
    }

    // 完全重写的parse_table方法
    fn parse_table(&mut self, reader: &mut Reader<&[u8]>) -> Result<String, DocxError> {
        // 非嵌套表格
        self.parse_table_internal(reader, false)
    }

    // 内部表格解析方法，处理嵌套和非嵌套情况
    fn parse_table_internal(
        &mut self,
        reader: &mut Reader<&[u8]>,
        is_nested: bool,
    ) -> Result<String, DocxError> {
        let mut buf = Vec::new();
        let mut table = TableStructure::new();
        let mut current_row = 0;

        // 清空第一个缓冲区
        buf.clear();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) => match e.name().as_ref() {
                    b"w:tr" => {
                        // 处理新行
                        while table.grid.len() <= current_row {
                            table.add_row();
                        }

                        let mut col = 0; // 追踪当前列位置

                        // 处理行中的所有单元格
                        let mut row_buf = Vec::new();
                        loop {
                            // 找到下一个可用列位置
                            while col < table.grid[current_row].len()
                                && table.grid[current_row][col].is_some()
                            {
                                col += 1;
                            }

                            match reader.read_event_into(&mut row_buf)? {
                                Event::Start(ref e) if e.name().as_ref() == b"w:tc" => {
                                    // 解析单元格属性
                                    let (colspan, vmerge_type) =
                                        self.parse_cell_properties(reader)?;

                                    // 解析单元格内容
                                    let content = self.parse_cell_content(reader)?;

                                    // 创建单元格
                                    let cell = TableCell::new(
                                        content,
                                        colspan,
                                        vmerge_type.clone(),
                                        current_row,
                                        col,
                                    );

                                    // 放置单元格在表格中
                                    if !table.place_cell(current_row, col, Some(cell)) {
                                        println!(
                                            "Warning: Failed to place cell at row {}, col {}",
                                            current_row, col
                                        );
                                    }

                                    // 处理colspan，标记后续列为None（被占用）
                                    for i in 1..colspan as usize {
                                        if col + i < table.max_cols
                                            || table.is_position_available(current_row, col + i)
                                        {
                                            table.place_cell(current_row, col + i, None);
                                        }
                                    }

                                    // 移动到下一个可能的位置
                                    col += colspan as usize;
                                }
                                Event::End(ref e) if e.name().as_ref() == b"w:tr" => {
                                    // 行结束
                                    break;
                                }
                                Event::End(ref e) if e.name().as_ref() == b"w:tbl" => {
                                    // 表格结束
                                    table.calculate_rowspans();

                                    // 如果是调试模式，打印表格结构
                                    if !is_nested {
                                        table.debug_print();
                                    }

                                    return Ok(table.to_html());
                                }
                                Event::Eof => {
                                    // 文件结束
                                    table.calculate_rowspans();
                                    return Ok(table.to_html());
                                }
                                _ => {}
                            }
                            row_buf.clear();
                        }

                        // 行处理完毕，移动到下一行
                        current_row += 1;
                    }
                    _ => {}
                },
                Event::End(e) => {
                    if e.name().as_ref() == b"w:tbl" {
                        // 表格结束
                        table.calculate_rowspans();

                        // 如果是调试模式，打印表格结构
                        if !is_nested {
                            table.debug_print();
                        }

                        return Ok(table.to_html());
                    }
                }
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        // 表格解析完成
        table.calculate_rowspans();

        // 如果是调试模式，打印表格结构
        if !is_nested {
            table.debug_print();
        }

        Ok(table.to_html())
    }

    fn parse_relationships_and_media(&self) -> Result<Vec<Image>, DocxError> {
        let mut images = Vec::new();
        let mut reader = Reader::from_str(&self.content.relationships);
        let mut buf = Vec::new();

        // 解析关系文件，获取图片ID和路径的映射
        let mut image_map = HashMap::new();
        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(e) => {
                    if e.name().as_ref() == b"Relationship" {
                        if let Some((id, target)) = self.parse_relationship(&e)? {
                            image_map.insert(id, target);
                        }
                    }
                }
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        // 根据映射关系处理媒体文件
        for (id, target) in image_map {
            if let Some(data) = self.content.media.get(&format!("word/{}", target)) {
                let content_type = self.guess_content_type(&target);
                images.push(Image {
                    id,
                    data: data.clone(),
                    content_type,
                });
            }
        }

        Ok(images)
    }

    fn parse_relationship(
        &self,
        element: &quick_xml::events::BytesStart,
    ) -> Result<Option<(String, String)>, DocxError> {
        let mut id = None;
        let mut target = None;

        for attr in element.attributes().flatten() {
            match attr.key.as_ref() {
                b"Id" => id = Some(String::from_utf8_lossy(&attr.value).into_owned()),
                b"Target" => target = Some(String::from_utf8_lossy(&attr.value).into_owned()),
                _ => {}
            }
        }

        match (id, target) {
            (Some(id), Some(target)) => Ok(Some((id, target))),
            _ => Ok(None),
        }
    }

    fn get_relationship_target(&self, id: &str) -> Result<Option<String>, DocxError> {
        let mut reader = Reader::from_str(&self.content.relationships);
        reader.trim_text(true);

        let mut buf = Vec::new();

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Empty(e) => {
                    if e.name().as_ref() == b"Relationship" {
                        let mut rel_id = None;
                        let mut target = None;

                        for attr in e.attributes() {
                            if let Ok(attr) = attr {
                                if attr.key.as_ref() == b"Id" {
                                    rel_id = Some(attr.unescape_value()?.to_string());
                                } else if attr.key.as_ref() == b"Target" {
                                    target = Some(attr.unescape_value()?.to_string());
                                }
                            }
                        }

                        if let (Some(rid), Some(t)) = (rel_id, target) {
                            if rid == id {
                                return Ok(Some(t));
                            }
                        }
                    }
                }
                Event::Eof => break,
                _ => {}
            }
            buf.clear();
        }

        Ok(None)
    }
}
