# DOCX2HTML with LaTeX Formula Support

一个将DOCX文档转换为HTML的Rust库，支持数学公式（LaTeX）解析和渲染。

## 功能特性

- 将.docx文件准确转换为HTML
- 支持文档基本格式（段落、字体、颜色等）
- 支持表格与图片
- 支持列表（有序列表和无序列表）
- **支持数学公式**：自动将DOCX中的OMML（Office Math Markup Language）公式转换为LaTeX格式
- 转换后的HTML包含KaTeX库以渲染LaTeX公式
- **支持解压后的DOCX目录**：可以直接处理解压后的DOCX目录结构

## 编译和安装

确保已安装Rust和Cargo：

```bash
# 克隆项目（假设你已经有项目）
git clone <repository-url>
cd docx2html_rs

# 编译
cargo build --release

# 安装（可选）
cargo install --path .
```

## 使用方法

### 命令行工具

```bash
# 基本用法
docx2html input.docx output.html

# 或者使用默认输出文件名（output.html）
docx2html input.docx

# 处理解压后的DOCX目录
docx2html extracted_docx_folder output.html

# 不提供参数会显示帮助信息
docx2html
```

### 作为库使用

```rust
use docx2html::DocxDocument;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 从DOCX文件加载文档
    let doc = DocxDocument::from_file("path/to/document.docx")?;
    
    // 或者从解压后的DOCX目录加载
    // let doc = DocxDocument::from_directory("path/to/extracted_docx")?;
    
    // 转换为HTML
    let html = doc.to_html()?;
    
    // 保存HTML文件
    std::fs::write("output.html", html)?;
    
    Ok(())
}
```

## 数学公式支持

本库支持将DOCX中的OMML（Office Math Markup Language）格式的数学公式转换为LaTeX格式，并在生成的HTML中使用KaTeX进行渲染。支持的公式元素包括：

- 基本运算符（+, -, ×, ÷, =, ≠, ≥, ≤等）
- 希腊字母（α, β, γ等）
- 上标和下标
- 分数
- 求和、积分等大型运算符
- 常用数学函数（sin, cos, log等）

## 已知限制

- 复杂的嵌套公式可能转换不完全准确
- 某些特殊的OMML标签可能不被支持
- 表格中的公式可能需要额外处理

## 贡献

欢迎提交问题和拉取请求！

## 许可证

MIT 