<div class="my-card">
    <!-- 标题部分 -->
    {{#Header}}
    <div class="header">{{Header}}</div>
    {{/Header}}
    <!-- 反转卡片：显示带遮盖的图片 -->
    {{#Reversed}}
    <div class="image-container" id="imageContainer">
        <div class="main-image" id="mainImage">{{Image}}</div>
        <div id="masksContainer"></div>
        <div class="scratch-canvas-container" id="scratchCanvasContainer"></div>
    </div>
    {{/Reversed}}

    <!-- 常规卡片：在正面基础上显示Notes -->
    {{^Reversed}}
    <div class="image-container" id="imageContainer">
        <div class="main-image" id="mainImage">
            {{Image}}
        </div>
        <div id="masksContainer"></div>
        <div class="scratch-canvas-container" id="scratchCanvasContainer"></div>
    </div>

    <!-- 源信息 -->
    {{#Source}}
    <div class="source">{{Source}}</div>
    {{/Source}}

    <!-- 文本部分 (可折叠) -->
    {{#Text}}
    <div class="text-container">
        <div class="text-toggle" id="textToggle">显示文本 ▼</div>
        <div class="text hidden" id="textContent">{{Text}}</div>
    </div>
    {{/Text}}
    {{/Reversed}}

    <!-- 备注 -->
    {{#Notes}}
    <div class="notes">
        <div class="notes-title">我的笔记</div>
        <div class="notes-content">{{Notes}}</div>
    </div>
    {{/Notes}}
</div>

<script>
    // 遮盖数据和设置
    var masks = [];
    var currentMaskIndex = 0;
    var mode = "{{Mode}}";
    var specificIndex = "{{Index}}";
    var colors = "{{Colors}}".split(',');
    var mainColor = colors[0] || "#FF0000";
    var secondaryColor = colors[1] || "#00FF00";

    // 解析遮盖数据
    try {
        masks = JSON.parse('{{Masks}}');
        console.log('解析后的遮盖数据:', masks);
    } catch (e) {
        console.error("遮盖数据解析错误:", e);
        masks = [];
    }

    // 设置颜色变量
    document.documentElement.style.setProperty('--main-color', mainColor);
    document.documentElement.style.setProperty('--secondary-color', secondaryColor);
    if ("{{Reversed}}".trim().length != 0) {
        mode = "free_guess";
    }
    // 创建遮盖
    createMasks();

    // 根据模式显示遮盖
    revealMasksBasedOnMode();
    // 设置文本折叠功能
    setupTextToggle();
    // 设置导航和快捷键

    setupNavigation();
    setupKeyboardShortcuts();
    // 创建遮盖元素
    function createMasks() {
        const container = document.getElementById('imageContainer');
        const masksContainer = document.getElementById('masksContainer');
        const imgContainer = document.getElementById('mainImage');

        if (!container || !masksContainer || !imgContainer) {
            console.error('找不到必要的DOM元素');
            return;
        }

        // 查找实际的img元素
        const img = imgContainer.querySelector('img');
        if (!img) {
            console.error('找不到图片元素');
            return;
        }

        // 清除现有遮盖
        masksContainer.innerHTML = '';

        // 如果图片已加载，直接创建遮盖，否则等待加载完成
        if (img.complete) {
            createMasksForImage(img);
        } else {
            img.onload = function () {
                createMasksForImage(img);
            };
        }
    }

    // 为已加载的图片创建遮盖
    function createMasksForImage(img) {
        const masksContainer = document.getElementById('masksContainer');
        const scratchCanvasContainer = document.getElementById('scratchCanvasContainer');

        if (!masksContainer) return;

        // Function to update mask positions based on current image dimensions
        function updateMaskPositions() {
            // Clear existing masks first
            masksContainer.innerHTML = '';
            if (scratchCanvasContainer) {
                scratchCanvasContainer.innerHTML = '';
            }

            // Get current image dimensions and position
            const imgRect = img.getBoundingClientRect();
            const containerRect = masksContainer.parentElement.getBoundingClientRect();

            // Calculate the offset of the image relative to its container
            const offsetLeft = imgRect.left - containerRect.left;
            const offsetTop = imgRect.top - containerRect.top;

            const imgWidth = img.offsetWidth;
            const imgHeight = img.offsetHeight;

            // Update containers to match image size and position
            masksContainer.style.width = imgWidth + 'px';
            masksContainer.style.height = imgHeight + 'px';
            masksContainer.style.left = offsetLeft + 'px';
            masksContainer.style.top = offsetTop + 'px';

            if (scratchCanvasContainer) {
                scratchCanvasContainer.style.width = imgWidth + 'px';
                scratchCanvasContainer.style.height = imgHeight + 'px';
                scratchCanvasContainer.style.left = offsetLeft + 'px';
                scratchCanvasContainer.style.top = offsetTop + 'px';
            }

            // Create each mask group
            masks.forEach((maskGroup, groupIndex) => {
                maskGroup.forEach((maskCoords, coordIndex) => {
                    const maskElement = document.createElement('div');
                    maskElement.className = 'mask';
                    maskElement.dataset.groupIndex = groupIndex;
                    maskElement.dataset.normalizedCoords = JSON.stringify(maskCoords);

                    // Calculate pixel values from normalized coordinates
                    const left = maskCoords[0] * imgWidth;
                    const top = maskCoords[1] * imgHeight;
                    const width = maskCoords[2] * imgWidth;
                    const height = maskCoords[3] * imgHeight;

                    maskElement.style.left = left + 'px';
                    maskElement.style.top = top + 'px';
                    maskElement.style.width = width + 'px';
                    maskElement.style.height = height + 'px';

                    // Apply mask color
                    applyMaskColor(maskElement, groupIndex);

                    // Add click handler
                    maskElement.addEventListener('click', function () {
                        handleMaskClick(groupIndex);
                    });

                    masksContainer.appendChild(maskElement);

                    // If in scratch mode, set up canvas for each mask
                    if (mode === 'scratch_guess' && scratchCanvasContainer) {
                        setupScratchCanvasForMask(scratchCanvasContainer, left, top, width, height, groupIndex);
                    }
                });
            });
            // Based on mode, initialize mask display
            if (mode === 'mask_one_guess_one') {
                showOnlyCurrentMask();
            } else if (mode.includes('mask_all_guess_one')) {
                updateMaskHighlight();
            }
            revealMasksBasedOnMode();
        }

        // Initial update
        updateMaskPositions();

        // Add window resize listener
        window.addEventListener('resize', function () {
            // Use debounce to avoid too many updates
            clearTimeout(window.resizeTimer);
            window.resizeTimer = setTimeout(function () {
                updateMaskPositions();
            }, 250);
        });

        // Add MutationObserver to watch for DOM changes that might affect image size
        const observer = new MutationObserver(function (mutations) {
            mutations.forEach(function (mutation) {
                if (mutation.type === 'attributes' &&
                    (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
                    updateMaskPositions();
                }
            });
        });

        // Start observing the image and its parent containers
        observer.observe(img, { attributes: true });
        observer.observe(img.parentElement, { attributes: true });
        observer.observe(masksContainer.parentElement, { attributes: true });

        // Also observe for resize events of the image
        const resizeObserver = new ResizeObserver(() => {
            updateMaskPositions();
        });
        resizeObserver.observe(img);

        // 根据模式显示遮盖
        revealMasksBasedOnMode();
    }

    // 应用遮盖颜色
    function applyMaskColor(maskElement, groupIndex) {
        let isMainMask = false;

        if (mode.includes('mask_all_guess_one')) {
            // 在全部遮盖但只猜一个模式下，检查是否为主遮盖
            if (mode.includes('multi') && specificIndex) {
                // 多卡版模式，检查指定索引
                const targetIndex = parseInt(specificIndex.replace('c', '')) - 1;
                isMainMask = (groupIndex === targetIndex);
            }
        } else {
            // 其他模式，所有遮盖都是主色
            isMainMask = true;
        }

        if (isMainMask) {
            maskElement.classList.add('main-mask');
        } else {
            maskElement.classList.add('secondary-mask');
        }
    }

    // 只显示当前遮盖
    function showOnlyCurrentMask() {
        const maskElements = document.querySelectorAll('.mask');

        maskElements.forEach(mask => {
            const groupIndex = parseInt(mask.dataset.groupIndex);

            if (groupIndex === currentMaskIndex) {
                mask.style.display = 'block';
            } else {
                mask.style.display = 'none';
            }
        });

        // 重置当前遮罩状态
        currentMaskRevealed = isCurrentMaskRevealed();
    }

    // 更新遮盖高亮
    function updateMaskHighlight() {
        const maskElements = document.querySelectorAll('.mask');

        maskElements.forEach(mask => {
            const groupIndex = parseInt(mask.dataset.groupIndex);

            // 移除现有类
            mask.classList.remove('main-mask', 'secondary-mask');

            // 应用正确的类
            if (groupIndex === currentMaskIndex) {
                mask.classList.add('main-mask');
            } else {
                mask.classList.add('secondary-mask');
            }
        });
    }

    // 处理遮盖点击
    function handleMaskClick(groupIndex) {
        if (mode === 'mask_one_guess_one' || mode === 'mask_one_guess_one_multi') {
            // 切换当前遮盖
            toggleMaskGroup(currentMaskIndex);
        } else if (mode.includes('mask_all_guess_one')) {
            // 切换点击的遮盖组
            toggleMaskGroup(groupIndex);

            // 更新当前遮罩的状态
            if (groupIndex === currentMaskIndex) {
                currentMaskRevealed = isCurrentMaskRevealed();
            }
        } else if (mode === 'mask_all_guess_all') {
            // 切换所有遮盖
            toggleAllMasks();
        } else if (mode === 'free_guess') {
            // 切换点击的特定遮盖
            // 在free_guess模式下，点击某个遮盖应该只切换该遮盖的状态
            // 而不会影响当前索引
            const clickedMasks = document.querySelectorAll(`.mask[data-group-index="${groupIndex}"]`);
            clickedMasks.forEach(mask => {
                mask.classList.toggle('revealed');
            });
        }
        // scratch_guess模式由画布处理
    }

    // 切换遮盖组显示状态
    function toggleMaskGroup(groupIndex) {
        const maskElements = document.querySelectorAll('.mask');
        let anyToggled = false;

        maskElements.forEach(mask => {
            if (parseInt(mask.dataset.groupIndex) === groupIndex) {
                mask.classList.toggle('revealed');
                anyToggled = true;
            }
        });

        // 更新当前遮罩的状态
        if (groupIndex === currentMaskIndex) {
            // 检查当前遮罩组中是否所有遮罩都已揭示
            const currentMasks = Array.from(maskElements).filter(
                mask => parseInt(mask.dataset.groupIndex) === currentMaskIndex
            );
            currentMaskRevealed = currentMasks.every(mask => mask.classList.contains('revealed'));
        }

        return anyToggled;
    }
    // 检查当前遮罩是否已揭示
    function isCurrentMaskRevealed() {
        const maskElements = document.querySelectorAll('.mask');
        const currentMasks = Array.from(maskElements).filter(
            mask => parseInt(mask.dataset.groupIndex) === currentMaskIndex
        );

        // 如果没有当前索引的遮罩，返回false
        if (currentMasks.length === 0) {
            return false;
        }

        // 检查当前索引的所有遮罩是否都已揭示
        return currentMasks.every(mask => mask.classList.contains('revealed'));
    }
    // 切换所有遮盖
    function toggleAllMasks() {
        const maskElements = document.querySelectorAll('.mask');

        // 检查是否所有遮盖都已显示
        const allRevealed = Array.from(maskElements).every(mask =>
            mask.classList.contains('revealed'));

        // 根据当前状态切换所有遮盖
        maskElements.forEach(mask => {
            if (allRevealed) {
                mask.classList.remove('revealed');
            } else {
                mask.classList.add('revealed');
            }
        });
    }

    // 上一个遮盖
    function prevMask() {
        // 自由猜模式处理
        if (mode === 'free_guess') {
            // 确定当前遮盖和前一个遮盖的索引
            const prevIndex = (currentMaskIndex - 1 + masks.length) % masks.length;
            console.log(prevIndex);
            // 查找当前和前一个遮盖元素
            const currentMasks = document.querySelectorAll(`.mask[data-group-index="${currentMaskIndex}"]`);
            const prevMasks = document.querySelectorAll(`.mask[data-group-index="${prevIndex}"]`);

            // 确保当前遮盖是隐藏的（盖住）
            currentMasks.forEach(mask => {
                if (mask.classList.contains('revealed')) {
                    mask.classList.remove('revealed');
                }
            });
            currentMaskIndex = prevIndex;
            return;
        }

        // 其他模式处理代码保持不变...
        // mask_all_guess_all 模式处理
        if (mode === 'mask_all_guess_all') {
            // 恢复所有遮盖（确保所有遮盖都不可见）
            const maskElements = document.querySelectorAll('.mask');
            maskElements.forEach(mask => {
                if (mask.classList.contains('revealed')) {
                    mask.classList.remove('revealed');
                }
            });
            return;
        }

        // mask_all_guess_one 模式的处理逻辑
        if (mode.includes('mask_all_guess_one')) {
            // 检查当前遮罩是否已揭示
            if (!currentMaskRevealed) {
                // 如果当前遮罩未揭示，先揭示它
                toggleMaskGroup(currentMaskIndex);
                currentMaskRevealed = true;
            } else {
                // 当前遮罩已揭示，移动到上一个
                if (currentMaskIndex > 0) {
                    // 恢复当前遮盖（隐藏它）
                    toggleMaskGroup(currentMaskIndex);

                    // 移动到上一个遮盖
                    currentMaskIndex--;

                    // 更新高亮显示
                    updateMaskHighlight();

                    // 重置新当前遮罩的状态
                    currentMaskRevealed = isCurrentMaskRevealed();
                } else {
                    // 如果已经是第一个，只切换当前遮盖
                    toggleMaskGroup(currentMaskIndex);
                    currentMaskRevealed = !currentMaskRevealed;
                }
            }
            return;
        }

        // mask_one_guess_one 模式处理
        if (mode === 'mask_one_guess_one' || mode === 'mask_one_guess_one_multi') {
            // 检查当前遮罩是否已揭示
            if (!currentMaskRevealed) {
                // 如果当前遮罩未揭示，先揭示它
                toggleMaskGroup(currentMaskIndex);
                currentMaskRevealed = true;
            } else {
                // 当前遮罩已揭示，移动到上一个
                if (currentMaskIndex > 0) {
                    // 如果不是第一个，先恢复当前遮盖
                    toggleMaskGroup(currentMaskIndex);

                    currentMaskIndex--;

                    // 只显示当前遮盖
                    showOnlyCurrentMask();

                    // 重置新当前遮罩的状态
                    currentMaskRevealed = isCurrentMaskRevealed();
                } else {
                    // 如果已经是第一个，只切换当前遮盖
                    toggleMaskGroup(currentMaskIndex);
                    currentMaskRevealed = !currentMaskRevealed;
                }
            }
            return;
        }

        // 其他模式的原有逻辑
        if (currentMaskIndex <= 0) {
            currentMaskIndex = 0; // 确保不会小于0
            // 如果在第一个，直接切换当前遮盖
            toggleMaskGroup(currentMaskIndex);
            return;
        }

        // 其他模式先切换当前，再前移
        toggleMaskGroup(currentMaskIndex);
        currentMaskIndex--;
    }

    // 下一个遮盖
    function nextMask() {
        // 自由猜模式处理
        if (mode === 'free_guess') {
            // 确定当前遮盖和下一个遮盖的索引
            const nextIndex = (currentMaskIndex + 1) % masks.length;
            console.log(nextIndex);
            // 查找当前遮盖元素
            const currentMasks = document.querySelectorAll(`.mask[data-group-index="${currentMaskIndex}"]`);

            // 确保当前遮盖是可见的（揭开）
            currentMasks.forEach(mask => {
                if (!mask.classList.contains('revealed')) {
                    mask.classList.add('revealed');
                }
            });
            currentMaskIndex = nextIndex;
            return;
        }

        // 其他模式处理代码保持不变...
        // mask_all_guess_all 模式处理
        if (mode === 'mask_all_guess_all') {
            // 显示所有遮盖（确保所有遮盖都可见）
            const maskElements = document.querySelectorAll('.mask');
            maskElements.forEach(mask => {
                if (!mask.classList.contains('revealed')) {
                    mask.classList.add('revealed');
                }
            });
            return;
        }

        // mask_all_guess_one 和 mask_one_guess_one 模式的新处理逻辑
        if (mode === 'mask_one_guess_one' || mode === 'mask_one_guess_one_multi' || mode.includes('mask_all_guess_one')) {
            // 检查当前遮罩是否已揭示
            if (!currentMaskRevealed) {
                // 如果当前遮罩未揭示，先揭示它
                toggleMaskGroup(currentMaskIndex);
                currentMaskRevealed = true;
            } else {
                // 当前遮罩已揭示，移动到下一个
                if (currentMaskIndex < masks.length - 1) {
                    // 如果不是最后一个，移动到下一个
                    if (mode === 'mask_one_guess_one' || mode === 'mask_one_guess_one_multi') {
                        // 对于只显示一个遮盖的模式，隐藏当前遮盖
                        toggleMaskGroup(currentMaskIndex);
                    } else if (mode.includes('mask_all_guess_one')) {
                        // 恢复当前遮盖（隐藏它）
                        toggleMaskGroup(currentMaskIndex);
                    }

                    currentMaskIndex++;

                    // 应用正确的显示逻辑
                    if (mode === 'mask_one_guess_one' || mode === 'mask_one_guess_one_multi') {
                        showOnlyCurrentMask();
                    } else if (mode.includes('mask_all_guess_one')) {
                        updateMaskHighlight();
                    }

                    // 重置新当前遮罩的状态
                    currentMaskRevealed = isCurrentMaskRevealed();
                } else {
                    // 如果已经是最后一个，只切换当前遮盖
                    toggleMaskGroup(currentMaskIndex);
                    currentMaskRevealed = !currentMaskRevealed;
                }
            }
            return;
        }

        // 其他模式的原有逻辑
        if (currentMaskIndex >= masks.length - 1) {
            currentMaskIndex = masks.length - 1; // 确保不会超出范围
            // 如果在最后一个，直接切换当前遮盖
            toggleMaskGroup(currentMaskIndex);
            return;
        }

        // 其他模式先切换当前，再后移
        toggleMaskGroup(currentMaskIndex);
        currentMaskIndex++;
    }
    // 设置导航按钮
    function setupNavigation() {
        // 刮刮乐模式不需要导航按钮
        if (mode === 'scratch_guess') return;

        const container = document.getElementById('imageContainer');

        // 如果没有找到容器或只有一个遮盖，不添加导航
        if (!container || masks.length <= 1) return;

        const navigation = document.createElement('div');
        navigation.className = 'navigation';

        // 上一个按钮 - 使用SVG箭头图标
        const prevBtn = document.createElement('a');
        prevBtn.className = 'nav-btn';
        prevBtn.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="19" y1="12" x2="5" y2="12"></line>
        <polyline points="12 19 5 12 12 5"></polyline>
      </svg>`;
        prevBtn.href = 'javascript:void(0);';
        prevBtn.title = '上一个';
        prevBtn.addEventListener('click', prevMask);

        // 下一个按钮 - 使用SVG箭头图标
        const nextBtn = document.createElement('a');
        nextBtn.className = 'nav-btn';
        nextBtn.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="5" y1="12" x2="19" y2="12"></line>
        <polyline points="12 5 19 12 12 19"></polyline>
      </svg>`;
        nextBtn.href = 'javascript:void(0);';
        nextBtn.title = '下一个';
        nextBtn.addEventListener('click', nextMask);

        // 切换全部按钮 - 使用SVG眼睛图标
        const toggleAllBtn = document.createElement('a');
        toggleAllBtn.className = 'nav-btn toggle-all';
        toggleAllBtn.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="eye-open">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
      </svg>
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="eye-closed" style="display:none;">
        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
      </svg>`;
        toggleAllBtn.href = 'javascript:void(0);';
        toggleAllBtn.title = '显示/隐藏所有遮盖';
        toggleAllBtn.addEventListener('click', toggleAllMasks);

        // 添加按钮到导航栏
        navigation.appendChild(prevBtn);
        navigation.appendChild(toggleAllBtn);
        navigation.appendChild(nextBtn);

        // 将导航栏添加到容器后面
        container.parentNode.insertBefore(navigation, container.nextSibling);

        // 初始更新切换按钮图标状态
        updateToggleButtonIcon();
    }

    function updateToggleButtonIcon() {
        const toggleBtn = document.querySelector('.toggle-all');
        if (!toggleBtn) return;

        const maskElements = document.querySelectorAll('.mask');
        const allRevealed = Array.from(maskElements).every(mask =>
            mask.classList.contains('revealed'));

        // 根据当前状态更新SVG图标显示
        const eyeOpen = toggleBtn.querySelector('.eye-open');
        const eyeClosed = toggleBtn.querySelector('.eye-closed');

        if (allRevealed) {
            // 所有遮罩已显示，显示闭眼图标
            if (eyeOpen) eyeOpen.style.display = 'none';
            if (eyeClosed) eyeClosed.style.display = 'inline';
            toggleBtn.title = '隐藏所有遮盖';
        } else {
            // 有遮罩未显示，显示开眼图标
            if (eyeOpen) eyeOpen.style.display = 'inline';
            if (eyeClosed) eyeClosed.style.display = 'none';
            toggleBtn.title = '显示所有遮盖';
        }
    }

    function toggleAllMasks() {
        const maskElements = document.querySelectorAll('.mask');

        // 检查是否所有遮盖都已显示
        const allRevealed = Array.from(maskElements).every(mask =>
            mask.classList.contains('revealed'));

        // 根据当前状态切换所有遮盖
        maskElements.forEach(mask => {
            if (allRevealed) {
                mask.classList.remove('revealed');
            } else {
                mask.classList.add('revealed');
            }
        });

        // 更新按钮图标
        updateToggleButtonIcon();
    }
    // 设置键盘快捷键
    function setupKeyboardShortcuts() {
        document.addEventListener('keydown', function (e) {
            if (e.key === 'j') {
                prevMask();
            } else if (e.key === 'k') {
                nextMask();
            } else if (e.key === ' ' || e.key === 'Enter') {
                // 空格或回车键直接切换当前遮盖
                toggleMaskGroup(currentMaskIndex);
            }
        }, { once: true });
    }

    // 根据模式显示遮盖
    function revealMasksBasedOnMode() {
        if ("{{Reversed}}".trim().length != 0) {
            return;
        }
        const maskElements = document.querySelectorAll('.mask');

        // 在mask_all_guess_one_multi模式下，只显示指定索引的遮盖
        if (mode.includes('mask_all_guess_one_multi') && specificIndex) {
            const targetIndex = parseInt(specificIndex.replace('c', '')) - 1;

            maskElements.forEach(mask => {
                const groupIndex = parseInt(mask.dataset.groupIndex);

                // 更新颜色设置
                mask.classList.remove('main-mask', 'secondary-mask');
                if (groupIndex === targetIndex) {
                    // 目标索引使用主色并显示
                    mask.classList.add('main-mask');
                    mask.classList.add('revealed');
                } else {
                    // 其他遮盖使用次要颜色并保持隐藏
                    mask.classList.add('secondary-mask');
                    mask.classList.remove('revealed');
                }
            });
        } else {
            // 其他模式下，默认所有遮罩都被显示
            maskElements.forEach(mask => {
                mask.classList.add('revealed');
            });
        }
    }
</script>