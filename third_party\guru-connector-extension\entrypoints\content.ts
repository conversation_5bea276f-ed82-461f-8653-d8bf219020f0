import { defineContentScript } from 'wxt/sandbox';
// @ts-ignore 忽略类型检查问题
import browser from 'webextension-polyfill';
import { pinia } from '../store';
import { useVideoStore } from '../store/videoStore';

// 输出扩展加载信息
console.log('🎬 Guru Connector 内容脚本已加载');

// 添加视频控制按钮的CSS样式
function injectStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .guru-video-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
      z-index: 99999;
      opacity: 0;
      transition: opacity 0.3s ease;
      background-color: rgba(0, 0, 0, 0.3);
      padding: 5px 8px;
      border-radius: 4px;
      backdrop-filter: blur(2px);
      pointer-events: auto;
    }
    
    .guru-video-controls.show {
      opacity: 1;
    }
    
    .guru-video-controls.always-show {
      opacity: 1;
    }
    
    .guru-video-wrap {
      position: relative !important;
    }
    
    .guru-control-btn {
      background-color: transparent;
      color: white;
      border: none;
      border-radius: 4px;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      padding: 0;
      pointer-events: auto;
    }
    
    .guru-control-btn:hover {
      transform: scale(1.1);
      background-color: rgba(80, 80, 80, 0.7);
    }
    
    .guru-control-btn:active {
      transform: scale(0.95);
    }
    
    .guru-control-btn svg {
      width: 20px;
      height: 20px;
    }
    
    .guru-control-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .guru-tooltip {
      position: absolute;
      bottom: -25px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      white-space: nowrap;
      opacity: 0;
      transition: opacity 0.2s ease;
      pointer-events: none;
    }
    
    .guru-control-btn:hover .guru-tooltip {
      opacity: 1;
    }
    
    @keyframes guru-success-flash {
      0% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.8); }
      70% { box-shadow: 0 0 0 10px rgba(74, 222, 128, 0); }
      100% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0); }
    }
    
    .guru-success-flash {
      animation: guru-success-flash 1s forwards;
    }
    
    @keyframes guru-loading-spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .guru-loading {
      position: relative;
    }
    
    .guru-loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top-color: white;
      border-radius: 50%;
      animation: guru-loading-spin 1s linear infinite;
    }
  `;
  document.head.appendChild(style);
}

// 定义全局变量
let videoStore: any; // 视频存储全局变量
let isDirectMP4: boolean; // 直链MP4视频标志

// 寻找页面中的视频元素
function findVideoElement(): HTMLVideoElement | null {
  console.log('🔍 开始查找视频元素...');
  const hostname = window.location.hostname;
  const pathname = window.location.pathname;
  console.log('📌 当前网站:', hostname, '路径:', pathname);

  // 检查是否是直链MP4视频
  if (isDirectMP4 || pathname.includes('/video/') || document.contentType === 'video/mp4') {
    console.log('🎯 检测到直链视频:', {
      pathname,
      contentType: document.contentType,
      hostname
    });

    // 直接寻找video标签
    const videos = Array.from(document.querySelectorAll('video'));
    console.log(`🎬 在直链视频页面找到 ${videos.length} 个视频元素`);

    if (videos.length > 0) {
      // 记录找到的视频
      videos.forEach((video, i) => {
        const rect = video.getBoundingClientRect();
        console.log(`🎬 直链视频 #${i}:`, {
          src: video.src || video.currentSrc || '无源',
          readyState: video.readyState,
          dimensions: `${rect.width}x${rect.height}`,
          offsetDimensions: `${video.offsetWidth}x${video.offsetHeight}`,
          paused: video.paused,
          parent: video.parentElement?.tagName || '无父元素'
        });
      });

      console.log('✅ 直链视频选中第一个');
      return videos[0];
    } else {
      console.log('⚠️ 是直链视频但未找到video元素，可能在iframe中');

      // 查找iframe
      const iframes = document.querySelectorAll('iframe');
      if (iframes.length > 0) {
        console.log(`🖼️ 找到 ${iframes.length} 个iframe，可能包含视频`);
      }
    }
  }

  // 确定使用哪组选择器
  let selectors = VIDEO_SELECTORS.default;

  // 匹配域名前缀
  for (const domain in VIDEO_SELECTORS) {
    if (hostname.includes(domain)) {
      selectors = VIDEO_SELECTORS[domain as keyof typeof VIDEO_SELECTORS] as string[];
      console.log(`使用 ${domain} 专用选择器:`, selectors);
      break;
    }
  }

  // 按选择器查找视频元素
  for (const selector of selectors) {
    try {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        console.log(`通过选择器 ${selector} 找到 ${elements.length} 个元素`);

        for (let i = 0; i < elements.length; i++) {
          const element = elements[i] as HTMLVideoElement;
          if (element && element instanceof HTMLVideoElement) {
            // 检查视频是否有效
            // 对于某些视频网站，视频可能尚未加载完成但仍然是有效的
            const rect = element.getBoundingClientRect();
            console.log(`视频元素 #${i}: readyState=${element.readyState}, 尺寸=${rect.width}x${rect.height}`);

            // 只要有视频元素，无论readyState如何都返回
            console.log(`通过选择器 ${selector} 选中视频元素`);
            return element;
          }
        }
      }
    } catch (error) {
      console.error(`使用选择器 ${selector} 出错:`, error);
    }
  }

  // 找不到特定选择器时，尝试获取页面中的所有视频
  const videos = Array.from(document.querySelectorAll('video'));
  console.log(`在页面中找到 ${videos.length} 个视频元素`);

  if (videos.length === 0) {
    console.log('未找到任何视频元素');
    return null;
  }

  // 如果有多个视频，尝试找到"主"视频（可见的、最大的或正在播放的）
  const visibleVideos = videos.filter(video => {
    const rect = video.getBoundingClientRect();
    // 放宽条件，只要有大小就认为可能是可见的
    const isVisible = rect.width > 0 && rect.height > 0;

    console.log(`视频元素: ${video.id || '无ID'}, 尺寸=${rect.width}x${rect.height}, readyState=${video.readyState}, 可见=${isVisible}`);

    return isVisible;
  });

  console.log(`找到 ${visibleVideos.length} 个可见的视频`);

  if (visibleVideos.length > 0) {
    // 优先选择正在播放的视频
    const playingVideo = visibleVideos.find(video => !video.paused);
    if (playingVideo) {
      console.log('选择正在播放的视频');
      return playingVideo;
    }

    // 否则选择尺寸最大的视频
    console.log('选择尺寸最大的视频');
    return visibleVideos.reduce((largest, current) => {
      const largestArea = largest.offsetWidth * largest.offsetHeight;
      const currentArea = current.offsetWidth * current.offsetHeight;
      return currentArea > largestArea ? current : largest;
    });
  }

  // 任何视频元素都返回
  console.log('返回第一个视频元素');
  return videos[0];
}

// 创建视频控制按钮
function createVideoControls(videoElement: HTMLVideoElement, videoStore: any) {
  console.log('开始创建视频控制按钮');

  // 1. 首先移除可能已存在的控制按钮
  document.querySelectorAll('.guru-video-controls').forEach(el => {
    el.remove();
  });

  // 2. 获取视频元素的父元素
  const videoParent = videoElement.parentElement;
  if (!videoParent) {
    console.error('视频元素没有父元素，无法添加控制按钮');
    return null;
  }

  // 输出视频和父元素信息，帮助调试
  console.log('视频元素信息:', {
    tag: videoElement.tagName,
    id: videoElement.id,
    className: videoElement.className,
    src: videoElement.src || videoElement.currentSrc,
    size: `${videoElement.offsetWidth}x${videoElement.offsetHeight}`,
    style: videoElement.getAttribute('style')
  });

  console.log('视频父元素信息:', {
    tag: videoParent.tagName,
    id: videoParent.id,
    className: videoParent.className
  });

  // 检查是否是直链视频（直链视频通常是在body下直接显示的视频）
  const isDirectVideo = isDirectMP4 ||
    videoParent === document.body ||
    videoElement.src?.endsWith('.mp4');

  console.log('是否是直链视频:', isDirectVideo);

  let targetElement;

  if (isDirectVideo) {
    // 直链视频的特殊处理 - 创建一个覆盖层来放置控制按钮，而不是修改DOM结构
    console.log('对直链视频应用特殊处理');

    // 查找是否已存在覆盖层
    let overlay = document.querySelector('.guru-direct-video-overlay');
    if (!overlay) {
      // 创建覆盖层
      overlay = document.createElement('div');
      overlay.className = 'guru-direct-video-overlay';
      (overlay as HTMLElement).style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999999;
        pointer-events: none;
      `;
      // 直接添加到body，不修改视频父元素
      document.body.appendChild(overlay);
    }

    targetElement = overlay;

    // 为直链视频添加特殊样式
    const directVideoStyle = document.createElement('style');
    directVideoStyle.textContent = `
      .guru-direct-video-overlay .guru-video-controls {
        position: fixed;
        top: 20px;
        right: 20px;
      }
    `;
    document.head.appendChild(directVideoStyle);
  } else {
    // 常规视频网站的处理
    // 3. 为父元素添加相对定位类，如果还没有的话
    if (!videoParent.classList.contains('guru-video-wrap')) {
      videoParent.classList.add('guru-video-wrap');
      console.log('为视频父元素添加了guru-video-wrap类');
    }

    targetElement = videoParent;
  }

  // 4. 创建控制按钮容器
  const controlsContainer = document.createElement('div');
  controlsContainer.className = 'guru-video-controls';

  // 直链视频始终显示按钮
  if (isDirectVideo) {
    controlsContainer.classList.add('always-show');
  }

  // 5. 创建按钮
  // 5.1 截图按钮
  const screenshotBtn = createControlButton(
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9a2 2 0 0 1 2-2h.93a2 2 0 0 0 1.664-.89l.812-1.22A2 2 0 0 1 10.07 4h3.86a2 2 0 0 1 1.664.89l.812 1.22A2 2 0 0 0 18.07 7H19a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9z"></path><circle cx="12" cy="13" r="3"></circle></svg>',
    '截图',
    () => {
      console.log('点击截图按钮');

      // 添加加载动画
      screenshotBtn.classList.add('guru-loading');

      // 截图
      videoStore.takeScreenshot().then(() => {
        // 移除加载动画
        screenshotBtn.classList.remove('guru-loading');

        // 添加成功动画
        screenshotBtn.classList.add('guru-success-flash');
        setTimeout(() => {
          screenshotBtn.classList.remove('guru-success-flash');
        }, 1000);
      });
    }
  );

  // 5.2 时间戳按钮
  const timestampBtn = createControlButton(
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path><line x1="4" y1="22" x2="4" y2="15"></line></svg>',
    '时间戳',
    () => {
      console.log('点击时间戳按钮');

      // 生成时间戳链接
      videoStore.generateTimestampLink();

      // 添加成功动画
      timestampBtn.classList.add('guru-success-flash');
      setTimeout(() => {
        timestampBtn.classList.remove('guru-success-flash');
      }, 1000);
    }
  );

  // 5.3 快退按钮
  const rewindBtn = createControlButton(
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>',
    '快退10秒',
    () => {
      console.log('点击快退按钮');
      videoStore.seekRelative(-10);
    }
  );

  // 5.4 快进按钮
  const forwardBtn = createControlButton(
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m12 5 7 7-7 7"></path><path d="M5 12h14"></path></svg>',
    '快进10秒',
    () => {
      console.log('点击快进按钮');
      videoStore.seekRelative(10);
    }
  );

  // 6. 添加按钮到容器
  controlsContainer.appendChild(screenshotBtn);
  controlsContainer.appendChild(timestampBtn);
  controlsContainer.appendChild(rewindBtn);
  controlsContainer.appendChild(forwardBtn);

  // 7. 将控制按钮添加到目标元素
  targetElement.appendChild(controlsContainer);
  console.log('控制按钮已添加到元素:', targetElement.className);

  // 为非直链视频设置点击时显示控制按钮的逻辑
  if (!isDirectVideo && videoElement) {
    let hideTimeout: number | null = null;

    // 点击视频时显示控制按钮
    videoElement.addEventListener('click', () => {
      // 显示控制按钮
      controlsContainer.classList.add('show');

      // 清除之前的定时器
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }

      // 3秒后自动隐藏
      hideTimeout = window.setTimeout(() => {
        controlsContainer.classList.remove('show');
        hideTimeout = null;
      }, 3000) as unknown as number;
    });

    // 视频暂停时保持控制按钮显示
    videoElement.addEventListener('pause', () => {
      controlsContainer.classList.add('show');

      // 清除之前的定时器
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }

      // 3秒后自动隐藏
      hideTimeout = window.setTimeout(() => {
        controlsContainer.classList.remove('show');
        hideTimeout = null;
      }, 3000) as unknown as number;
    });

    // 鼠标移动时也显示控制按钮
    videoElement.addEventListener('mousemove', () => {
      // 显示控制按钮
      controlsContainer.classList.add('show');

      // 清除之前的定时器
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }

      // 3秒后自动隐藏
      hideTimeout = window.setTimeout(() => {
        controlsContainer.classList.remove('show');
        hideTimeout = null;
      }, 3000) as unknown as number;
    });
  }

  // 8. 返回控制按钮容器
  return controlsContainer;
}

// 创建单个控制按钮的辅助函数
function createControlButton(
  iconSvg: string,
  tooltipText: string,
  clickHandler: () => void
): HTMLButtonElement {
  const button = document.createElement('button');
  button.className = 'guru-control-btn';
  button.innerHTML = iconSvg;

  // 添加提示文本
  const tooltip = document.createElement('span');
  tooltip.className = 'guru-tooltip';
  tooltip.textContent = tooltipText;
  button.appendChild(tooltip);

  // 添加点击事件
  button.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    clickHandler();
  });

  return button;
}

// 更新视频控制按钮
function updateVideoControls(controls: HTMLElement, videoStore: any) {
  // 根据视频状态更新按钮
  const buttons = controls.querySelectorAll('.guru-control-btn');
  const isVideoAvailable = !!videoStore.currentVideo;

  // 启用或禁用按钮
  buttons.forEach(button => {
    (button as HTMLButtonElement).disabled = !isVideoAvailable;
  });
}

// 启动页面监控
function startMonitoring() {
  console.log('🚀 开始监控页面视频元素');

  try {
    // 验证videoStore是否可用
    if (!videoStore || typeof videoStore.setVideo !== 'function') {
      console.error('❌ videoStore 不可用，无法启动监控');
      return;
    }

    // 尝试立即找到视频元素
    const videoElement = findVideoElement();
    if (videoElement) {
      console.log('✅ 找到视频元素，设置到store中');
      videoStore.setVideo(videoElement);

    // 为视频元素添加控制按钮
    createVideoControls(videoElement, videoStore);

    // 已找到视频，停止查找间隔
    if (findVideoInterval) {
      clearInterval(findVideoInterval);
      findVideoInterval = null;
    }
  } else if (!findVideoInterval) {
    // 没有找到视频，设置间隔继续查找
    console.log('⏰ 未找到视频元素，设置定时器每秒检查一次');
    findVideoInterval = window.setInterval(() => {
      console.log('⏰ 定时检查视频元素...');
      const newVideoElement = findVideoElement();
      if (newVideoElement) {
        console.log('✅ 定时器中找到视频元素');
        videoStore.setVideo(newVideoElement);

        // 为视频元素添加控制按钮
        createVideoControls(newVideoElement, videoStore);

        clearInterval(findVideoInterval!);
        findVideoInterval = null;
      }
    }, 1000) as unknown as number;
  }

    // 连接到 WebSocket
    if (typeof videoStore.connectWebsocket === 'function') {
      videoStore.connectWebsocket();
    } else {
      console.warn('⚠️ videoStore.connectWebsocket 方法不可用');
    }
  } catch (error) {
    console.error('❌ 启动监控失败:', error);
  }
}

// 监听页面变化
function setupMutationObserver() {
  const observer = new MutationObserver((mutations) => {
    // 检查是否有视频添加到 DOM
    for (const mutation of mutations) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(node => {
          if (node instanceof HTMLElement) {
            if (node.tagName === 'VIDEO' || node.querySelector('video')) {
              try {
                const videoElement = findVideoElement();
                if (videoElement && videoStore && typeof videoStore.setVideo === 'function') {
                  videoStore.setVideo(videoElement);

                  // 为视频元素添加控制按钮
                  createVideoControls(videoElement, videoStore);
                }
              } catch (error) {
                console.error('❌ 处理新视频元素失败:', error);
              }
            }
          }
        });
      }
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

// 定义寻找视频间隔变量
let findVideoInterval: number | null = null;

// 修改初始化函数，确保只创建一次控制按钮
function init() {
  console.log('🚀 Guru Connector 内容脚本初始化中...');
  console.log('📄 页面URL:', window.location.href);
  console.log('📄 contentType:', document.contentType || '未知');

  // 输出页面信息
  console.log('📄 页面状态:', {
    readyState: document.readyState,
    documentElement: !!document.documentElement,
    body: !!document.body,
    head: !!document.head
  });

  // 特别针对直链MP4视频的检测
  if (isDirectMP4) {
    console.log('🎯 这是直链MP4视频页面，立即查找视频元素');
    const videos = document.querySelectorAll('video');
    if (videos.length > 0) {
      console.log('✅ 发现直链视频元素:', videos[0]);
      videoStore.setVideo(videos[0] as HTMLVideoElement);

      // 为视频元素添加控制按钮
      createVideoControls(videos[0] as HTMLVideoElement, videoStore);
    } else {
      console.log('⚠️ 直链MP4页面但未找到视频元素');
    }
  }

  // 直接检查页面上所有video元素
  console.log('🔍 直接检查页面所有video元素:');
  const allVideos = document.querySelectorAll('video');
  console.log(`🎬 找到 ${allVideos.length} 个video元素`);

  Array.from(allVideos).forEach((video, index) => {
    const rect = video.getBoundingClientRect();
    console.log(`Video #${index}:`, {
      element: video,
      id: video.id || '无ID',
      className: video.className,
      src: video.src || video.currentSrc || '无src',
      srcObject: !!video.srcObject,
      readyState: video.readyState,
      paused: video.paused,
      width: rect.width,
      height: rect.height,
      offsetWidth: video.offsetWidth,
      offsetHeight: video.offsetHeight,
      parentNode: video.parentNode,
      isConnected: video.isConnected,
      style: video.getAttribute('style') || '无样式'
    });
  });

  // 注入样式
  injectStyles();

  // 尝试立即找到视频元素
  startMonitoring();
  setupMutationObserver();

  console.log('✅ 初始化完成，开始监控视频元素和DOM变化');
}

// 支持的视频网站选择器
const VIDEO_SELECTORS = {
  'youtube.com': [
    'video.html5-main-video',
    'video.video-stream'
  ],
  'bilibili.com': [
    'video.bilibili-player-video',
    '.bpx-player-video-wrap video',
    '#bilibili-player video',
    '.player-wrapper video',
    '.bpx-player-primary-area video',
    // 添加更明确的选择器，针对新的B站播放器结构
    'div.bpx-player-video-wrap > video',
    '.bpx-player-primary-area .bpx-player-video-area video',
    '.bpx-player-video-area video',
    'div[data-screen="normal"] video',
    // B站播放器中的视频可能深层嵌套
    '.bpx-player-container video'
  ],
  'coursera.org': [
    'video.vjs-tech',
    '.video-js video'
  ],
  'udemy.com': [
    'video#vjs_video_3_html5_api',
    '.vjs-tech'
  ],
  'vimeo.com': [
    'video.vp-video'
  ],
  'teachable.com': [
    'video.vjs-tech'
  ],
  'netflix.com': [
    'video.VideoContainer'
  ],
  // 通用视频选择器
  'default': [
    'video',
    'video[src]',
    'video[data-src]',
    '.video-js video',
    '.player video',
    '.video-player video'
  ]
};

// 在脚本启动部分
export default defineContentScript({
  matches: ['<all_urls>'],
  main() {
    // 输出当前页面信息
    console.log('🎬 Guru Connector 主函数已执行');
    console.log('📄 当前页面信息:', {
      url: window.location.href,
      hostname: window.location.hostname,
      pathname: window.location.pathname,
      contentType: document.contentType || '未知',
      readyState: document.readyState
    });

    // 检查是否是直链MP4视频
    isDirectMP4 = window.location.pathname.toLowerCase().endsWith('.mp4') ||
      document.contentType === 'video/mp4';

    console.log(isDirectMP4 ? '🎯 检测到直链MP4视频' : '❓ 非直链MP4视频');

    // 初始化 Pinia
    try {
      videoStore = useVideoStore(pinia);
      console.log('🏪 视频存储初始化完成');

      // 验证store方法是否可用
      if (typeof videoStore.setVideo !== 'function') {
        console.error('❌ videoStore.setVideo 方法不可用');
        return;
      }

      console.log('✅ videoStore 方法验证通过');
    } catch (error) {
      console.error('❌ 视频存储初始化失败:', error);
      return;
    }

    // 如果文档已加载完成，立即启动
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
      console.log('📄 文档已经准备就绪，立即初始化');
      init();
    } else {
      console.log('📄 文档加载中，等待load事件');
      window.addEventListener('load', () => {
        console.log('📄 load事件触发，开始初始化');
        init();
      });
    }

    // 监听来自后台脚本的消息
    browser.runtime.onMessage.addListener((message: any) => {
      console.log('📨 content脚本收到消息:', message);

      if (message.type === 'inspect_videos_debug') {
        console.log('🔍 收到视频元素调试检查请求');
        console.log('📄 当前页面URL:', window.location.href);
        console.log('📄 contentType:', document.contentType || '未知');

        // 扫描页面上所有视频元素并输出详细信息
        const allVideos = document.querySelectorAll('video');
        console.log(`🎬 调试信息: 找到 ${allVideos.length} 个video元素`);

        Array.from(allVideos).forEach((video, index) => {
          const rect = video.getBoundingClientRect();
          console.log(`调试 - Video #${index}:`, {
            element: video,
            id: video.id || '无ID',
            className: video.className,
            src: video.src || video.currentSrc || '无src',
            srcObject: !!video.srcObject,
            readyState: video.readyState,
            paused: video.paused,
            width: rect.width,
            height: rect.height,
            offsetWidth: video.offsetWidth,
            offsetHeight: video.offsetHeight,
            isConnected: video.isConnected,
            visible: rect.width > 0 && rect.height > 0
          });
        });

        // 检查是否是直链MP4视频
        console.log('🎯 是否是直链MP4视频:', isDirectMP4, {
          pathname: window.location.pathname,
          contentType: document.contentType
        });

        // 检查iframe
        const iframes = document.querySelectorAll('iframe');
        console.log(`🖼️ 找到 ${iframes.length} 个iframe元素`);

        // 尝试强制更新视频元素
        const videoElement = findVideoElement();
        if (videoElement) {
          console.log('✅ 调试: 找到合适的视频元素，强制更新');
          videoStore.setVideo(videoElement);

          // 为视频元素添加控制按钮
          createVideoControls(videoElement, videoStore);
        } else {
          console.log('❌ 调试: 未找到合适的视频元素');
        }

        return Promise.resolve({
          success: true,
          message: `找到 ${allVideos.length} 个视频元素，${iframes.length} 个iframe，详情请查看控制台`
        });
      }

      if (message.type === 'take_screenshot') {
        console.log('📨 收到截图命令');

        // Ensure we have a valid video element
        if (!videoStore.currentVideo) {
          console.log('🔄 没有视频元素，尝试重新查找...');
          const videoElement = findVideoElement();
          if (videoElement) {
            videoStore.setVideo(videoElement);
            console.log('✅ 重新找到视频元素');
          } else {
            console.error('❌ 无法找到视频元素');
            return Promise.resolve({ success: false, error: '没有找到视频元素' });
          }
        }

        videoStore.takeScreenshot();
        return Promise.resolve({ success: true });
      }

      if (message.type === 'get_video_info') {
        videoStore.sendVideoInfo();
        // 也返回视频信息给popup
        return Promise.resolve({
          success: true,
          data: {
            currentTime: videoStore.currentTime,
            duration: videoStore.duration,
            paused: !videoStore.isPlaying,
            playbackRate: videoStore.playbackRate,
            volume: videoStore.volume,
            muted: videoStore.muted,
            src: videoStore.currentVideo?.src || '',
            title: document.title,
            url: window.location.href
          }
        });
      }

      if (message.type === 'update_websocket_port') {
        if (typeof message.port === 'number') {
          console.log(`更新WebSocket端口为 ${message.port}`);
          videoStore.updateWebsocketUrl(message.port);
        }
        return Promise.resolve({ success: true });
      }

      if (message.type === 'generate_timestamp_link') {
        console.log('📨 收到时间戳链接生成命令');

        // Ensure we have a valid video element
        if (!videoStore.currentVideo) {
          console.log('🔄 没有视频元素，尝试重新查找...');
          const videoElement = findVideoElement();
          if (videoElement) {
            videoStore.setVideo(videoElement);
            console.log('✅ 重新找到视频元素');
          } else {
            console.error('❌ 无法找到视频元素');
            return Promise.resolve({ success: false, error: '没有找到视频元素' });
          }
        }

        const link = videoStore.generateTimestampLink();
        return Promise.resolve({ success: true, link });
      }

      if (message.type === 'toggle_play') {
        videoStore.togglePlay();
        return Promise.resolve({ success: true });
      }

      if (message.type === 'play') {
        videoStore.play();
        return Promise.resolve({ success: true });
      }

      if (message.type === 'pause') {
        videoStore.pause();
        return Promise.resolve({ success: true });
      }

      if (message.type === 'seek') {
        if (typeof message.time === 'number') {
          videoStore.seek(message.time);
        }
        return Promise.resolve({ success: true });
      }

      if (message.type === 'seek_relative') {
        if (typeof message.seconds === 'number') {
          console.log(`📨 收到跳转命令: ${message.seconds}秒`);

          // Ensure we have a valid video element
          if (!videoStore.currentVideo) {
            console.log('🔄 没有视频元素，尝试重新查找...');
            const videoElement = findVideoElement();
            if (videoElement) {
              videoStore.setVideo(videoElement);
              console.log('✅ 重新找到视频元素');
            } else {
              console.error('❌ 无法找到视频元素');
              return Promise.resolve({ success: false, error: '没有找到视频元素' });
            }
          }

          videoStore.seekRelative(message.seconds);
        }
        return Promise.resolve({ success: true });
      }

      if (message.type === 'set_playback_rate') {
        if (typeof message.rate === 'number') {
          videoStore.setPlaybackRate(message.rate);
        }
        return Promise.resolve({ success: true });
      }

      return Promise.resolve({ success: false });
    });
  }
});
