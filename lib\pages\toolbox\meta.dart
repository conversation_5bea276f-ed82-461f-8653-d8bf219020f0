import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/meta.dart';

class PDFMetaPage extends StatefulWidget {
  const PDFMetaPage({super.key});

  @override
  State<PDFMetaPage> createState() => _PDFMetaPageState();
}

class _PDFMetaPageState extends State<PDFMetaPage> {
  final controller = Get.put(PDFMetaController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.meta.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.meta.description'.tr,
                  style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        const SizedBox(height: 4),
                        ShadInputWithValidate(
                            key: const ValueKey("title"),
                            label: 'toolbox.meta.title_field'.tr,
                            placeholder:
                                'toolbox.meta.titlePlaceholder'.tr,
                            initialValue: controller.title.value,
                            onChanged: (value) {
                              controller.title.value = value;
                            },
                            onValidate: (value) async {
                              return "";
                            },
                            onValidateError: (error) {}),
                        ShadInputWithValidate(
                            key: const ValueKey("author"),
                            label: 'toolbox.meta.author'.tr,
                            placeholder:
                                'toolbox.meta.authorPlaceholder'.tr,
                            initialValue: controller.author.value,
                            onChanged: (value) {
                              controller.author.value = value;
                            },
                            onValidate: (value) async {
                              return "";
                            },
                            onValidateError: (error) {}),
                        ShadInputWithValidate(
                            key: const ValueKey("subject"),
                            label: 'toolbox.meta.subject'.tr,
                            placeholder:
                                'toolbox.meta.subjectPlaceholder'.tr,
                            initialValue: controller.subject.value,
                            onChanged: (value) {
                              controller.subject.value = value;
                            },
                            onValidate: (value) async {
                              return "";
                            },
                            onValidateError: (error) {}),
                        ShadInputWithValidate(
                            key: const ValueKey("keywords"),
                            label: 'toolbox.meta.keywords'.tr,
                            placeholder:
                                'toolbox.meta.keywordsPlaceholder'.tr,
                            initialValue: controller.keywords.value,
                            onChanged: (value) {
                              controller.keywords.value = value;
                            },
                            onValidate: (value) async {
                              return "";
                            },
                            onValidateError: (error) {}),
                        ShadInputWithValidate(
                            key: const ValueKey("creator"),
                            label: 'toolbox.meta.creator'.tr,
                            placeholder:
                                'toolbox.meta.creatorPlaceholder'.tr,
                            initialValue: controller.creator.value,
                            onChanged: (value) {
                              controller.creator.value = value;
                            },
                            onValidate: (value) async {
                              return "";
                            },
                            onValidateError: (error) {}),
                        ShadInputWithValidate(
                            key: const ValueKey("producer"),
                            label: 'toolbox.meta.producer'.tr,
                            placeholder:
                                'toolbox.meta.producerPlaceholder'.tr,
                            initialValue: controller.producer.value,
                            onChanged: (value) {
                              controller.producer.value = value;
                            },
                            onValidate: (value) async {
                              return "";
                            },
                            onValidateError: (error) {}),
                        ShadInputWithValidate(
                            key: const ValueKey("creation-date"),
                            label: 'toolbox.meta.creationDate'.tr,
                            placeholder:
                                'toolbox.meta.creationDatePlaceholder'.tr,
                            initialValue: controller.creationDate.value,
                            onChanged: (value) {
                              controller.creationDate.value = value;
                            },
                            onValidate: (value) async {
                              return "";
                            },
                            onValidateError: (error) {}),
                        ShadInputWithValidate(
                            key: const ValueKey("mod-date"),
                            label: 'toolbox.meta.modDate'.tr,
                            placeholder:
                                'toolbox.meta.modDatePlaceholder'.tr,
                            initialValue: controller.modDate.value,
                            onChanged: (value) {
                              controller.modDate.value = value;
                            },
                            onValidate: (value) async {
                              return "";
                            },
                            onValidateError: (error) {}),
                        if (PathUtils.isDesktop)
                          ShadSelectCustom(
                            key: const ValueKey("output-position"),
                            label: 'toolbox.common.outputLocation'.tr,
                            placeholder:
                                'toolbox.common.selectOutputLocation'.tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.common.outputDirectory'.tr,
                            placeholder:
                                Text('toolbox.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.common.inputFile'.tr,
                          placeholder: Text(
                              'toolbox.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['pdf'],
                          isRequired: true,
                          allowMultiple: true,
                          initialValue: controller.selectedFilePaths,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
