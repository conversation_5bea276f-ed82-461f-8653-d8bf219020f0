import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/card_form.dart';
import 'components/export_form.dart';
import 'package:anki_guru/controllers/anki/mubu.dart';
import 'package:anki_guru/pages/common.dart';

class MubuCardPage extends StatefulWidget {
  const MubuCardPage({super.key});

  @override
  State<MubuCardPage> createState() => _MubuCardPageState();
}

class _MubuCardPageState extends State<MubuCardPage> {
  final controller = Get.put(MubuCardPageController());
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.mubu.page_title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('anki.mubu.function_description_title'.tr, style: defaultPageTitleStyle),
                Text('anki.mubu.feature_description'.tr,
                    style: theme.textTheme.muted),
                const SizedBox(height: 16),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 2;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        print(controller.tabController.selected);
                        controller.update();
                      },
                      tabs: [
                        ShadTab(
                          value: 'card',
                          content: const CardForm(),
                          width: tabWidth,
                          child: Text('anki.mubu.tab_card_creation'.tr),
                        ),
                        ShadTab(
                          value: 'export',
                          content: const ExportForm(),
                          width: tabWidth,
                          child: Text('anki.mubu.tab_note_export'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
