#![allow(unused)]

use crate::anki::models::{gen_apkg, get_kevin_image_cloze_model, AnkiNote};
use crate::anki::pdf_card::common::{
    clean_page_annotations, get_page_subdeck_map, get_subdeck_by_bookmark, group_page_annotations,
    process_card_image_and_masks,
};
use crate::anki::pdf_utils::{
    delete_annotations, export_pdf_to_images, extract_annotations, get_pdfium, parse_page_ranges,
};
use crate::anki::types::{Annotation, AnnotationType, PdfError};

use genanki_rs::{Deck, Note, Package};
use image::ImageFormat;
use indexmap::indexmap;
use pdfium_render::prelude::*;
use rand::Rng;
use rinf::debug_print;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::collections::HashSet;
use std::path::{Path, PathBuf};
use thiserror::Error;
use ulid::Ulid;
use urlencoding;

#[derive(Debug)]
pub struct QACardConfig {
    pub doc_path: String,
    pub a_doc_path: Option<String>,
    pub page_range: String,
    pub a_page_range: String,
    pub address: String,
    pub deck_name: String,
    pub model_name: String,
    pub mask_types: Vec<String>,
    pub tags: Vec<String>,
    pub dpi: u32,
    pub is_create_subdeck: bool,
    pub subdeck_max_level: u32,
    pub is_mix_card: bool,
    pub is_answer_cloze: bool,
    pub is_show_source: bool,
    pub num_cols: u32,
    pub extra_info: Option<String>,
    pub item_key: Option<String>,
    pub output_path: String,
}

pub async fn make_qa_card_single_file(
    config: QACardConfig,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<(), PdfError> {
    let doc_path = config.doc_path.clone();
    let mut item_key = config.item_key.clone().unwrap_or_else(|| "".to_string());
    progress_callback(5.0, 100.0, "正在提取批注...".to_string());
    let annotations =
        extract_annotations(doc_path.clone(), &config.page_range, config.num_cols, false).await?;
    progress_callback(10.0, 100.0, "正在整理批注...".to_string());
    // 先按页码分组
    let mut page_annotations: HashMap<u32, Vec<Annotation>> = HashMap::new();
    for annotation in annotations {
        page_annotations
            .entry(annotation.page_num)
            .or_default()
            .push(annotation);
    }
    // 对每页进行分组，然后合并所有结果
    let mut all_groups = Vec::new();
    let mut extra_annotations_to_delete = Vec::new();
    let mut all_cloze_colors = Vec::new();
    for page_num in page_annotations.keys() {
        if let Some(page_annots) = page_annotations.get(page_num) {
            let (page_groups, annotations_to_delete, cloze_colors) =
                group_page_annotations(page_annots.clone());
            all_groups.extend(page_groups);
            extra_annotations_to_delete.extend(annotations_to_delete);
            all_cloze_colors.extend(cloze_colors);
        }
    }

    // 按page、column、rect上坐标、rect左坐标排序
    let mut sorted_groups = all_groups;
    if config.is_mix_card {
        // 对 all_cloze_colors 去重
        let mut unique_colors: HashSet<String> = HashSet::new();
        for color in all_cloze_colors.iter() {
            unique_colors.insert(color.clone());
        }

        // 过滤 sorted_groups，不保留第一个元素的颜色在 unique_colors 中的组
        sorted_groups.retain(|group| {
            if let Some(first_annot) = group.first() {
                if let Some(color) = &first_annot.color {
                    return !unique_colors.contains(&format!("{:?}", color));
                }
            }
            true
        });
    }
    sorted_groups.sort_by(|a, b| {
        // 获取每组的第一个元素
        let first_a = &a[0];
        let first_b = &b[0];

        // 先按page_num排序
        let page_cmp = first_a.page_num.cmp(&first_b.page_num);
        if page_cmp != std::cmp::Ordering::Equal {
            return page_cmp;
        }

        // 再按column排序
        let column_cmp = first_a.column.cmp(&first_b.column);
        if column_cmp != std::cmp::Ordering::Equal {
            return column_cmp;
        }

        // 再按rect上坐标(y坐标)排序
        let y_cmp = first_b.rect[3]
            .partial_cmp(&first_a.rect[3])
            .unwrap_or(std::cmp::Ordering::Equal);
        if y_cmp != std::cmp::Ordering::Equal {
            return y_cmp;
        }

        // 最后按rect左坐标(x坐标)排序
        first_a.rect[0]
            .partial_cmp(&first_b.rect[0])
            .unwrap_or(std::cmp::Ordering::Equal)
    });

    let image_map = clean_page_annotations(
        &Path::new(&doc_path),
        sorted_groups.clone(),
        extra_annotations_to_delete,
        "png",
        false,
    )
    .await?;
    dbg!("image_map: {}", &image_map);
    // 合并组
    let mut merged_groups = Vec::new();
    let mut current_group = Vec::new();
    for group in sorted_groups {
        // 获取当前组的第一个元素的颜色
        let first_annot = &group[0];
        let current_color = format!("{:?}", first_annot.color);

        if current_group.is_empty() {
            // 如果当前组为空，直接添加新组
            current_group.push(group);
        } else {
            // 获取当前组中第一个组的第一个注释的颜色
            let prev_color = format!("{:?}", current_group[0][0].color);

            if current_color == prev_color {
                // 如果颜色相同，添加到当前组
                current_group.push(group);
            } else {
                // 如果颜色不同，保存旧组并开始新组
                merged_groups.push(current_group);
                current_group = vec![group];
            }
        }
    }

    // 处理最后一个组
    if !current_group.is_empty() {
        merged_groups.push(current_group);
    }

    // 确保merged_groups中的元素个数是偶数
    if merged_groups.len() % 2 != 0 {
        if merged_groups.len() > 1 {
            merged_groups.pop();
        } else {
            return Err(PdfError::Other("没有发现卡片，请检查批注".to_string()));
        }
    }

    progress_callback(15.0, 100.0, "开始生成卡片...".to_string());
    let mut final_notes = Vec::new();
    let mut final_image_paths = Vec::new();
    let mut total_cards = merged_groups.len() as f64;
    let mut current_card = 0.0;
    let page_map = get_page_subdeck_map(Path::new(&doc_path))
        .await
        .map_err(|e| PdfError::Other(e.to_string()))?;
    let temp_dir = crate::anki::utils::get_temp_dir()
        .await
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;

    for chunk in merged_groups.chunks(2) {
        current_card += 1.0;
        progress_callback(
            15.0 + (current_card / total_cards) * 75.0,
            100.0,
            "正在生成卡片...".to_string(),
        );

        let mut front_image_path = PathBuf::new();
        let mut back_image_path = PathBuf::new();
        let mut back_masks: Vec<[f64; 4]> = Vec::new();

        // 处理正面（问题）
        let front_group = &chunk[0];
        let (front_image_path, front_masks) = process_card_image_and_masks(
            front_group,
            &config.mask_types,
            &doc_path,
            &temp_dir,
            &image_map,
        )
        .await?;

        // 处理背面（答案）
        let back_group = &chunk[1];
        let (back_image_path, back_masks) = process_card_image_and_masks(
            back_group,
            &config.mask_types,
            &doc_path,
            &temp_dir,
            &image_map,
        )
        .await?;
        // 处理标签和笔记
        let front_contents = front_group[0][0].contents.clone();
        let (header, notes) = if let Some(contents) = front_contents {
            if let Some((h, n)) = contents.split_once("---") {
                (h.trim().to_string(), n.trim().to_string())
            } else {
                ("".to_string(), contents.trim().to_string())
            }
        } else {
            ("".to_string(), "".to_string())
        };
        let page_num = front_group[0][0].page_num.clone();
        let mut cid = front_group[0][0].id.clone();
        if cid.is_none() {
            let rect = &front_group[0][0].rect;
            cid = Some(format!(
                "{}-[{:.3}, {:.3}, {:.3}, {:.3}]",
                front_group[0][0].page_num, rect[0], rect[1], rect[2], rect[3]
            ));
        }

        let mut tags = config.tags.clone();
        if !notes.is_empty() {
            let re = regex::Regex::new(r"#([^\s#]+)").unwrap();
            for cap in re.captures_iter(&notes) {
                if let Some(tag) = cap.get(1) {
                    tags.push(tag.as_str().to_string());
                }
            }
        }

        let notes = notes.replace('\n', "<br>");
        let front_file_name = front_image_path
            .file_name()
            .ok_or_else(|| PdfError::Other("Invalid front image path".to_string()))?
            .to_str()
            .ok_or_else(|| PdfError::Other("Invalid front file name".to_string()))?;
        let back_file_name = back_image_path
            .file_name()
            .ok_or_else(|| PdfError::Other("Invalid back image path".to_string()))?
            .to_str()
            .ok_or_else(|| PdfError::Other("Invalid back file name".to_string()))?;

        let deck_name = get_subdeck_by_bookmark(
            &page_map,
            config.deck_name.clone(),
            config.is_create_subdeck,
            config.subdeck_max_level,
            front_group[0][0].page_num,
        )?;
        let mut source = if config.is_show_source {
            let mut label = get_subdeck_by_bookmark(
                &page_map,
                config.deck_name.clone(),
                true,
                config.subdeck_max_level,
                front_group[0][0].page_num,
            )?;

            if let Some(cid_value) = cid.as_ref() {
                let cid_lower = cid_value.to_lowercase();
                if cid_lower.starts_with("zotero") {
                    // 处理以zotero开头的cid
                    let link = format!(
                        "zotero://open-pdf/library/items/{}?page={}",
                        &item_key, page_num
                    );
                    format!("<div><a href=\"{}\">P{}</a></div>", link, page_num)
                } else {
                    let link = format!(
                        "guru2://seek?category=pdf&path={}&page={}",
                        urlencoding::encode(&doc_path.to_string()),
                        page_num
                    );
                    // 以::切割，从第二个元素开始用"->"拼接，包含第二个元素
                    let parts: Vec<&str> = label.split("::").collect();
                    if parts.len() >= 2 {
                        let first = parts[0];
                        let rest = parts[1..].join("->");
                        label = format!("{}", rest);
                        format!(
                            "<div><a href=\"{}\">{}(P{})</a></div>",
                            link, label, page_num
                        )
                    } else {
                        format!("<div><a href=\"{}\">P{}</a></div>", link, page_num)
                    }
                }
            } else {
                "".to_string() // Handle the case where cid is None
            }
        } else {
            "".to_string()
        };
        let extra_info = config.extra_info.clone();
        if let Some(info) = extra_info {
            source = format!("{}<div style=\"font-size:12px;color:#C0C0C0;text-align:left;font-family:Arial\">{}</div>", source, info);
        }
        final_image_paths.push(front_image_path.clone());
        final_image_paths.push(back_image_path.clone());

        let masks_str = if config.is_answer_cloze {
            serde_json::to_string(&back_masks).unwrap_or_default()
        } else {
            "[]".to_string()
        };

        let fields = indexmap! {
            "ID" => format!(
                "{}__{:06x}",
                cid.clone().unwrap(),
                rand::thread_rng().gen::<u32>() & 0xFFFFFF
            ),
            "Header" => header.clone(),
            "Front" => format!("<img src=\"{}\" />", &front_file_name),
            "Back" => format!("<img src=\"{back_file_name}\" />"),
            "Source" => format!("{}", &source),
            "Masks" => masks_str,
            "Notes" => notes.clone(),
            "FrontText" => String::new(),
            "BackText" => String::new(),
            "Mode" => "".to_string(),
        };

        final_notes.push(AnkiNote {
            deck_name: deck_name.clone(),
            model_name: config.model_name.clone(),
            fields: fields
                .into_iter()
                .map(|(key, value)| value.to_string())
                .collect(),
            tags: Some(tags.clone()),
            guid: Some(format!("{}", cid.clone().unwrap())),
        });
    }
    progress_callback(90.0, 100.0, "开始导出卡片...".to_string());

    // 将 PathBuf 转换为 String
    let image_paths: Vec<String> = final_image_paths
        .into_iter()
        .map(|path| path.to_string_lossy().into_owned())
        .collect();
    // 检查 final_notes 是否为空
    if final_notes.is_empty() {
        return Err(PdfError::Other(
            "没有发现卡片。请检查是否已添加批注及批注规范性！".to_string(),
        ));
    }
    // 使用 gen_apkg 生成文件
    gen_apkg(
        final_notes,
        Some(image_paths),
        &config.output_path,
        Some(&config.address),
        true, // 生成后删除临时媒体文件
        None,
    )
    .await
    .map_err(|e| PdfError::Other(e.to_string()))?;
    progress_callback(100.0, 100.0, "已完成".to_string());
    Ok(())
}

pub async fn make_qa_card_dual_file(
    config: QACardConfig,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<(), PdfError> {
    let doc_path = config.doc_path.clone();
    let a_doc_path = config.a_doc_path.clone().ok_or_else(|| {
        PdfError::Other("Dual file mode requires answer document path".to_string())
    })?;
    let mut item_key = config.item_key.clone().unwrap_or_else(|| "".to_string());
    progress_callback(5.0, 100.0, "正在提取问题批注...".to_string());
    let q_annotations =
        extract_annotations(doc_path.clone(), &config.page_range, config.num_cols, false).await?;

    progress_callback(10.0, 100.0, "正在提取答案批注...".to_string());
    let a_annotations = extract_annotations(
        a_doc_path.clone(),
        &config.a_page_range,
        config.num_cols,
        false,
    )
    .await?;

    progress_callback(15.0, 100.0, "正在整理批注...".to_string());
    // 按页码分组问题批注
    let mut q_page_annotations: HashMap<u32, Vec<Annotation>> = HashMap::new();
    for annotation in q_annotations {
        q_page_annotations
            .entry(annotation.page_num)
            .or_default()
            .push(annotation);
    }

    // 按页码分组答案批注
    let mut a_page_annotations: HashMap<u32, Vec<Annotation>> = HashMap::new();
    for annotation in a_annotations {
        a_page_annotations
            .entry(annotation.page_num)
            .or_default()
            .push(annotation);
    }

    // 对每页进行分组
    let mut q_groups = Vec::new();
    let mut a_groups = Vec::new();
    let mut extra_annotations_to_delete = Vec::new();

    // 处理问题文档
    let mut q_cloze_colors = Vec::new();
    for page_num in q_page_annotations.keys() {
        if let Some(page_annots) = q_page_annotations.get(page_num) {
            let (page_groups, annotations_to_delete, cloze_colors) =
                group_page_annotations(page_annots.clone());
            q_groups.extend(page_groups);
            extra_annotations_to_delete.extend(annotations_to_delete);
            q_cloze_colors.extend(cloze_colors);
        }
    }

    // 对问题组进行排序
    q_groups.sort_by(|a, b| {
        let first_a = &a[0];
        let first_b = &b[0];

        // 先按page_num排序
        let page_cmp = first_a.page_num.cmp(&first_b.page_num);
        if page_cmp != std::cmp::Ordering::Equal {
            return page_cmp;
        }

        // 再按column排序
        let column_cmp = first_a.column.cmp(&first_b.column);
        if column_cmp != std::cmp::Ordering::Equal {
            return column_cmp;
        }

        // 再按rect上坐标(y坐标)排序
        let y_cmp = first_b.rect[3]
            .partial_cmp(&first_a.rect[3])
            .unwrap_or(std::cmp::Ordering::Equal);
        if y_cmp != std::cmp::Ordering::Equal {
            return y_cmp;
        }

        // 最后按rect左坐标(x坐标)排序
        first_a.rect[0]
            .partial_cmp(&first_b.rect[0])
            .unwrap_or(std::cmp::Ordering::Equal)
    });

    // 处理答案文档
    let mut a_cloze_colors = Vec::new();
    for page_num in a_page_annotations.keys() {
        if let Some(page_annots) = a_page_annotations.get(page_num) {
            let (page_groups, annotations_to_delete, cloze_colors) =
                group_page_annotations(page_annots.clone());
            a_groups.extend(page_groups);
            extra_annotations_to_delete.extend(annotations_to_delete);
            a_cloze_colors.extend(cloze_colors);
        }
    }

    // 对答案组进行排序
    a_groups.sort_by(|a, b| {
        let first_a = &a[0];
        let first_b = &b[0];

        // 先按page_num排序
        let page_cmp = first_a.page_num.cmp(&first_b.page_num);
        if page_cmp != std::cmp::Ordering::Equal {
            return page_cmp;
        }

        // 再按column排序
        let column_cmp = first_a.column.cmp(&first_b.column);
        if column_cmp != std::cmp::Ordering::Equal {
            return column_cmp;
        }

        // 再按rect上坐标(y坐标)排序
        let y_cmp = first_b.rect[3]
            .partial_cmp(&first_a.rect[3])
            .unwrap_or(std::cmp::Ordering::Equal);
        if y_cmp != std::cmp::Ordering::Equal {
            return y_cmp;
        }

        // 最后按rect左坐标(x坐标)排序
        first_a.rect[0]
            .partial_cmp(&first_b.rect[0])
            .unwrap_or(std::cmp::Ordering::Equal)
    });

    // 如果是混合卡片模式，过滤掉包含填空颜色的组
    if config.is_mix_card {
        // 对问题文档的 cloze_colors 去重
        let mut q_unique_colors: HashSet<String> = HashSet::new();
        for color in q_cloze_colors.iter() {
            q_unique_colors.insert(color.clone());
        }

        // 过滤问题组
        q_groups.retain(|group| {
            if let Some(first_annot) = group.first() {
                if let Some(color) = &first_annot.color {
                    return !q_unique_colors.contains(&format!("{:?}", color));
                }
            }
            true
        });

        // 对答案文档的 cloze_colors 去重
        let mut a_unique_colors: HashSet<String> = HashSet::new();
        for color in a_cloze_colors.iter() {
            a_unique_colors.insert(color.clone());
        }

        // 过滤答案组
        a_groups.retain(|group| {
            if let Some(first_annot) = group.first() {
                if let Some(color) = &first_annot.color {
                    return !a_unique_colors.contains(&format!("{:?}", color));
                }
            }
            true
        });
    }

    // 按颜色合并问题组
    let mut q_merged_groups = Vec::new();
    let mut current_q_group = Vec::new();
    for group in &q_groups {
        // 使用引用
        let first_annot = &group[0];
        let current_color = format!("{:?}", first_annot.color);

        if current_q_group.is_empty() {
            current_q_group.push(group.clone()); // 克隆组
        } else {
            let prev_color = format!("{:?}", current_q_group[0][0].color);

            if current_color == prev_color {
                current_q_group.push(group.clone()); // 克隆组
            } else {
                q_merged_groups.push(current_q_group);
                current_q_group = vec![group.clone()]; // 克隆组
            }
        }
    }
    if !current_q_group.is_empty() {
        q_merged_groups.push(current_q_group);
    }

    // 按颜色合并答案组
    let mut a_merged_groups = Vec::new();
    let mut current_a_group = Vec::new();
    for group in &a_groups {
        // 使用引用
        let first_annot = &group[0];
        let current_color = format!("{:?}", first_annot.color);

        if current_a_group.is_empty() {
            current_a_group.push(group.clone()); // 克隆组
        } else {
            let prev_color = format!("{:?}", current_a_group[0][0].color);
            if current_color == prev_color {
                current_a_group.push(group.clone()); // 克隆组
            } else {
                a_merged_groups.push(current_a_group);
                current_a_group = vec![group.clone()]; // 克隆组
            }
        }
    }
    if !current_a_group.is_empty() {
        a_merged_groups.push(current_a_group);
    }

    // 清理问题文档的批注
    let q_image_map = clean_page_annotations(
        &Path::new(&doc_path),
        q_groups.clone(),                    // 克隆 q_groups
        extra_annotations_to_delete.clone(), // 克隆 extra_annotations_to_delete
        "png",
        false,
    )
    .await?;

    // 清理答案文档的批注
    let a_image_map = clean_page_annotations(
        &Path::new(&a_doc_path),
        a_groups.clone(),            // 克隆 a_groups
        extra_annotations_to_delete, // 这个可以移动，因为后面不再使用
        "png",
        false,
    )
    .await?;

    progress_callback(20.0, 100.0, "开始生成卡片...".to_string());
    let mut final_notes = Vec::new();
    let mut final_image_paths = Vec::new();
    let total_cards = q_merged_groups.len() as f64;
    let mut current_card = 0.0;

    // 获取问题文档的页面子牌组映射
    let page_map = get_page_subdeck_map(Path::new(&doc_path))
        .await
        .map_err(|e| PdfError::Other(e.to_string()))?;
    // dbg!("q_merged_groups: {:?}", &q_merged_groups);
    // dbg!("a_merged_groups: {:?}", &a_merged_groups);
    let temp_dir = crate::anki::utils::get_temp_dir()
        .await
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;

    // 逐对处理问题和答案
    for (q_group, a_group) in q_merged_groups.iter().zip(a_merged_groups.iter()) {
        dbg!("q_group: {:?}", &q_group);
        dbg!("a_group: {:?}", &a_group);
        current_card += 1.0;
        progress_callback(
            20.0 + (current_card / total_cards) * 70.0,
            100.0,
            "正在生成卡片...".to_string(),
        );

        // 处理问题（正面）
        let (front_image_path, _) = process_card_image_and_masks(
            q_group,
            &config.mask_types,
            &doc_path,
            &temp_dir,
            &q_image_map,
        )
        .await?;

        // 处理答案（背面）
        let (back_image_path, back_masks) = process_card_image_and_masks(
            a_group,
            &config.mask_types,
            &a_doc_path,
            &temp_dir,
            &a_image_map,
        )
        .await?;

        // 处理标签和笔记
        let front_contents = q_group[0][0].contents.clone();
        let (header, notes) = if let Some(contents) = front_contents {
            if let Some((h, n)) = contents.split_once("---") {
                (h.trim().to_string(), n.trim().to_string())
            } else {
                ("".to_string(), contents.trim().to_string())
            }
        } else {
            ("".to_string(), "".to_string())
        };

        let page_num = q_group[0][0].page_num.clone();
        let mut cid = q_group[0][0].id.clone();
        if cid.is_none() {
            let rect = &q_group[0][0].rect;
            cid = Some(format!(
                "{}-[{:.3}, {:.3}, {:.3}, {:.3}]",
                q_group[0][0].page_num, rect[0], rect[1], rect[2], rect[3]
            ));
        }

        let mut tags = config.tags.clone();
        if !notes.is_empty() {
            let re = regex::Regex::new(r"#([^\s#]+)").unwrap();
            for cap in re.captures_iter(&notes) {
                if let Some(tag) = cap.get(1) {
                    tags.push(tag.as_str().to_string());
                }
            }
        }

        let notes = notes.replace('\n', "<br>");
        let front_file_name = front_image_path
            .file_name()
            .ok_or_else(|| PdfError::Other("Invalid front image path".to_string()))?
            .to_str()
            .ok_or_else(|| PdfError::Other("Invalid front file name".to_string()))?;
        let back_file_name = back_image_path
            .file_name()
            .ok_or_else(|| PdfError::Other("Invalid back image path".to_string()))?
            .to_str()
            .ok_or_else(|| PdfError::Other("Invalid back file name".to_string()))?;

        let deck_name = get_subdeck_by_bookmark(
            &page_map,
            config.deck_name.clone(),
            config.is_create_subdeck,
            config.subdeck_max_level,
            q_group[0][0].page_num,
        )?;
        let mut source = if config.is_show_source {
            let mut label = get_subdeck_by_bookmark(
                &page_map,
                config.deck_name.clone(),
                true,
                config.subdeck_max_level,
                q_group[0][0].page_num,
            )?;
            if let Some(cid_value) = cid.as_ref() {
                let cid_lower = cid_value.to_lowercase();
                if cid_lower.starts_with("zotero") {
                    // 处理以zotero开头的cid
                    let link = format!(
                        "zotero://open-pdf/library/items/{}?page={}",
                        &item_key, page_num
                    );
                    format!("<div><a href=\"{}\">P{}</a></div>", link, page_num)
                } else {
                    let link = format!(
                        "guru2://seek?category=pdf&path={}&page={}",
                        urlencoding::encode(&doc_path.to_string()),
                        page_num
                    );
                    // 以::切割，从第二个元素开始用"->"拼接，包含第二个元素
                    let parts: Vec<&str> = label.split("::").collect();
                    if parts.len() >= 2 {
                        let first = parts[0];
                        let rest = parts[1..].join("->");
                        label = format!("{}", rest);
                        format!(
                            "<div><a href=\"{}\">{}(P{})</a></div>",
                            link, label, page_num
                        )
                    } else {
                        format!("<div><a href=\"{}\">P{}</a></div>", link, page_num)
                    }
                }
            } else {
                "".to_string() // Handle the case where cid is None
            }
        } else {
            "".to_string()
        };
        let extra_info = config.extra_info.clone();
        if let Some(info) = extra_info {
            source = format!("{}<div style=\"font-size:12px;color:#C0C0C0;text-align:left;font-family:Arial\">{}</div>", source, info);
        }
        final_image_paths.push(front_image_path.clone());
        final_image_paths.push(back_image_path.clone());

        let masks_str = if config.is_answer_cloze {
            serde_json::to_string(&back_masks).unwrap_or_default()
        } else {
            "[]".to_string()
        };

        let fields = indexmap! {
            "ID" => format!(
                "{}__{:06x}",
                cid.clone().unwrap(),
                rand::thread_rng().gen::<u32>() & 0xFFFFFF
            ),
            "Header" => header.clone(),
            "Front" => format!("<img src=\"{}\" />", &front_file_name),
            "Back" => format!("<img src=\"{back_file_name}\" />"),
            "Source" => format!("{}", &source),
            "Masks" => masks_str,
            "Notes" => notes.clone(),
            "FrontText" => String::new(),
            "BackText" => String::new(),
            "Mode" => "".to_string(),
        };

        final_notes.push(AnkiNote {
            deck_name: deck_name.clone(),
            model_name: config.model_name.clone(),
            fields: fields
                .into_iter()
                .map(|(key, value)| value.to_string())
                .collect(),
            tags: Some(tags.clone()),
            guid: Some(format!("{}", cid.clone().unwrap())),
        });
    }

    progress_callback(90.0, 100.0, "开始导出卡片...".to_string());
    // dbg!("final_notes: {:?}", &final_notes);
    // 检查 final_notes 是否为空
    if final_notes.is_empty() {
        return Err(PdfError::Other(
            "没有发现卡片。请检查是否已添加批注及批注规范性！".to_string(),
        ));
    }
    // 生成 apkg 文件
    gen_apkg(
        final_notes,
        Some(
            final_image_paths
                .iter()
                .map(|p| p.to_string_lossy().into_owned())
                .collect(),
        ),
        &config.output_path,
        Some(&config.address),
        true,
        None,
    )
    .await
    .map_err(|e| PdfError::Other(e.to_string()))?;
    progress_callback(100.0, 100.0, "已完成".to_string());
    Ok(())
}

pub async fn make_qa_card_full_page(
    doc_path: String,
    deck_name: String,
    page_range: String,
    full_page_mode: String,
    output_path: String,
    address: String,
    tags: Vec<String>,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<(), PdfError> {
    progress_callback(5.0, 100.0, "正在解析页面范围...".to_string());
    let model_name = "Kevin Image QA Card v2".to_string();
    let total_pages = crate::anki::utils::get_page_count(&doc_path)
        .await
        .map_err(|e| PdfError::Other(e.to_string()))?;
    let pages = parse_page_ranges(&page_range, total_pages).map_err(PdfError::PageRange)?;
    progress_callback(10.0, 100.0, "正在导出页面...".to_string());
    // 创建临时目录用于存储导出的图片
    let uid = Ulid::new().to_string();
    let temp_dir = dirs::cache_dir()
        .ok_or_else(|| PdfError::Other("Failed to get download directory".to_string()))?;
    let img_temp_dir = temp_dir.as_path().join(&uid);
    std::fs::create_dir_all(&img_temp_dir)?;

    // 导出指定页面为图片
    let image_paths = export_pdf_to_images(
        &doc_path,
        &page_range,
        img_temp_dir.as_path().to_str().unwrap(),
        300,
        false,
        None,
        "png",
        |progress, total, message| println!("Progress: {}/{} - {}", progress, total, message),
    )
    .await?;

    progress_callback(30.0, 100.0, "正在生成卡片...".to_string());

    let mut final_notes = Vec::new();
    let mut final_image_paths = Vec::new();
    let total_cards = match full_page_mode.as_str() {
        "single_page" => pages.len(),
        "dual_page" => pages.len() / 2,
        _ => return Err(PdfError::Other("Invalid full_page_mode".to_string())),
    };

    match full_page_mode.as_str() {
        "single_page" => {
            // 单页模式：每页作为一个卡片的正面
            for (i, page_num) in pages.iter().enumerate() {
                progress_callback(
                    30.0 + (i as f64 / total_cards as f64) * 60.0,
                    100.0,
                    format!("正在处理第 {} 页...", page_num),
                );

                if let Some(front_path) = image_paths.get(&(page_num)) {
                    let front_file_name = Path::new(front_path)
                        .file_name()
                        .ok_or_else(|| PdfError::Other("Invalid front image path".to_string()))?
                        .to_str()
                        .ok_or_else(|| PdfError::Other("Invalid front file name".to_string()))?;

                    final_image_paths.push(front_path.clone());

                    let fields = indexmap! {
                        "ID" => format!("{}", ulid::Ulid::new()),
                        "Header" => "".to_string(),
                        "Front" => format!("<img src=\"{front_file_name}\" />"),
                        "Back" => String::new(),
                        "Source" => String::new(),
                        "Masks" => "[]".to_string(),
                        "Notes" => String::new(),
                        "FrontText" => String::new(),
                        "BackText" => String::new(),
                        "Mode" => "".to_string(),
                    };

                    final_notes.push(AnkiNote {
                        deck_name: deck_name.clone(),
                        model_name: model_name.clone(),
                        fields: fields.into_iter().map(|(_, value)| value).collect(),
                        tags: Some(tags.clone()),
                        guid: None,
                    });
                }
            }
        }
        "dual_page" => {
            // 双页模式：每两页作为一个卡片的正反面
            for chunk in pages.chunks(2) {
                if chunk.len() != 2 {
                    continue; // 跳过不完整的页面对
                }

                let front_num = chunk[0];
                let back_num = chunk[1];

                progress_callback(
                    30.0 + (front_num as f64 / total_cards as f64 / 2.0) * 60.0,
                    100.0,
                    format!("正在处理第 {} 和 {} 页...", front_num, back_num),
                );

                if let (Some(front_path), Some(back_path)) =
                    (image_paths.get(&front_num), image_paths.get(&back_num))
                {
                    let front_file_name = Path::new(front_path)
                        .file_name()
                        .ok_or_else(|| PdfError::Other("Invalid front image path".to_string()))?
                        .to_str()
                        .ok_or_else(|| PdfError::Other("Invalid front file name".to_string()))?;

                    let back_file_name = Path::new(back_path)
                        .file_name()
                        .ok_or_else(|| PdfError::Other("Invalid back image path".to_string()))?
                        .to_str()
                        .ok_or_else(|| PdfError::Other("Invalid back file name".to_string()))?;

                    final_image_paths.push(front_path.clone());
                    final_image_paths.push(back_path.clone());

                    let fields = indexmap! {
                        "ID" => format!("{}", ulid::Ulid::new()),
                        "Header" => "".to_string(),
                        "Front" => format!("<img src=\"{front_file_name}\" />"),
                        "Back" => format!("<img src=\"{back_file_name}\" />"),
                        "Source" => String::new(),
                        "Masks" => "[]".to_string(),
                        "Notes" => String::new(),
                        "FrontText" => String::new(),
                        "BackText" => String::new(),
                        "Mode" => "".to_string(),
                    };

                    final_notes.push(AnkiNote {
                        deck_name: deck_name.clone(),
                        model_name: model_name.clone(),
                        fields: fields.into_iter().map(|(_, value)| value).collect(),
                        tags: Some(tags.clone()),
                        guid: None,
                    });
                }
            }
        }
        _ => return Err(PdfError::Other("Invalid full_page_mode".to_string())),
    }

    progress_callback(90.0, 100.0, "正在生成 Anki 包...".to_string());

    // 生成 Anki 包
    gen_apkg(
        final_notes,
        Some(final_image_paths),
        &output_path,
        Some(&address),
        true,
        None,
    )
    .await
    .map_err(|e| PdfError::Other(e.to_string()))?;

    Ok(())
}
