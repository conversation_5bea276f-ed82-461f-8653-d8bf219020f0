import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/word_card.dart';
import 'dart:io';

class <PERSON><PERSON>zeForm extends StatefulWidget {
  const ClozeForm({super.key});

  @override
  State<ClozeForm> createState() => _ClozeFormState();
}

class _ClozeFormState extends State<ClozeForm> {
  final controller = Get.find<WordCardFormController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      controller.cardModel.value = "Kevin Text Cloze v3";
      await controller.updateFieldList(controller.cardModel.value);
      controller.update();
    });
  }

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 4,
            children: [
              ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
                onAddNew: (newDeckName) {
                  // Add to the deck list if not already present
                  if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                    ankiConnectController.parentDeckList.add(newDeckName);
                  }

                  // Set as selected deck
                  controller.parentDeck.value = newDeckName;
                },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              ShadSwitchCustom(
                key: ValueKey("subdeck-${controller.isCreateSubDeck.hashCode}"),
                label: 'anki.common.create_subdeck'.tr,
                initialValue: controller.isCreateSubDeck.value,
                onChanged: (v) {
                  controller.isCreateSubDeck.value = v;
                },
              ),
              if (controller.isCreateSubDeck.value)
                ShadSelectWithInput(
                  label: 'anki.word_card.subdeck_prefix'.tr,
                  placeholder: 'anki.word_card.select_subdeck_prefix'.tr,
                  searchPlaceholder: 'anki.word_card.input_subdeck_prefix'.tr,
                  isMultiple: false,
                  initialValue: [controller.subDeckPrefix.value],
                  options: controller.subDeckPrefixList,
                  onChanged: (value) {
                    logger.i(value);
                    controller.subDeckPrefix.value = value.single;
                  },
                  onAddNew: (newPrefix) {
                    // Add new sub deck prefix to the list if not already present
                    final newOption = {'value': newPrefix, 'label': newPrefix};
                    if (!controller.subDeckPrefixList.any((option) => option['value'] == newPrefix)) {
                      controller.subDeckPrefixList.add(newOption);
                    }

                    // Set as selected prefix
                    controller.subDeckPrefix.value = newPrefix;
                  },
                ),
              ShadRadioGroupCustom(
                label: 'anki.common.card_mode'.tr,
                initialValue: controller.clozeParams.mode.value,
                items: controller.clozeModeList,
                onChanged: (value) {
                  controller.clozeParams.mode.value = value;
                },
              ),
              ShadSelectCustom(
                label: 'anki.word_card.card_template'.tr,
                placeholder: 'anki.word_card.select_card_template'.tr,
                initialValue: const ['Kevin Text Cloze v3'],
                options: [
                  {
                    "value": "Kevin Text Cloze v3",
                    "label": "Kevin Text Cloze v3"
                  },
                ].toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.cardModel.value = value.single;
                  controller.updateFieldList(controller.cardModel.value);
                },
              ),
              ShadFieldMappingTable(
                key: ValueKey(
                    "word_card_field_table_${controller.cardModel.value}_${ankiConnectController.fieldList.length}_cloze"),
                fieldList: ankiConnectController.fieldList,
                optionsList: {
                  "Front": controller.questionRegexList.toList(),
                  "Back": controller.qaAnswerRegexList.toList(),
                },
                defaultOptionsList: controller.commonRegexList,
                cardModel: controller.cardModel.value,
                onUpdateFieldMapping: (field, patternMatch) {
                  controller.updateFieldMapping(field, patternMatch);
                },
                getFieldMappingValue: (field) {
                  return controller.getFieldMappingValue(field);
                },
              ),
              ShadCheckboxGroup(
                label: 'anki.word_card.cloze_style'.tr,
                initialValues: controller.clozeParams.clozeGrammar.toList(),
                items: controller.clozeGrammarList.toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.clozeParams.clozeGrammar.value = value;
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'anki.word_card.at_least_one_cloze_style'.tr;
                  }
                  return "";
                },
                onValidateError: (error) {},
              ),
              if (controller.clozeParams.clozeGrammar.contains("text_color"))
                ShadSelectWithInput(
                  key: const ValueKey("text_color"),
                  label: 'anki.word_card.text_color'.tr,
                  placeholder: 'anki.word_card.select_color'.tr,
                  searchPlaceholder: 'anki.word_card.input_color_placeholder'.tr,
                  isMultiple: true,
                  initialValue: controller.textColorList.toList(),
                  options: controller.commonColorList,
                  onChanged: (value) {
                    logger.i(value);
                    controller.textColorList.value = value;
                  },
                  onAddNew: (newColor) {
                    // Add new color to the common color list if not already present
                    final newOption = {'value': newColor, 'label': newColor};
                    if (!controller.commonColorList.any((option) => option['value'] == newColor)) {
                      controller.commonColorList.add(newOption);
                    }

                    // Add to selected colors if not already present
                    if (!controller.textColorList.contains(newColor)) {
                      controller.textColorList.add(newColor);
                    }
                  },
                ),
              if (controller.clozeParams.clozeGrammar
                  .contains("text_highlight"))
                ShadSelectWithInput(
                  key: const ValueKey("text_highlight"),
                  label: 'anki.word_card.highlight_color'.tr,
                  placeholder: 'anki.word_card.select_color'.tr,
                  searchPlaceholder: 'anki.word_card.input_color_placeholder'.tr,
                  isMultiple: true,
                  initialValue: controller.highlightColorList.toList(),
                  options: controller.commonColorList,
                  onChanged: (value) {
                    logger.i(value);
                    controller.highlightColorList.value = value;
                  },
                  onAddNew: (newColor) {
                    // Add new color to the common color list if not already present
                    final newOption = {'value': newColor, 'label': newColor};
                    if (!controller.commonColorList.any((option) => option['value'] == newColor)) {
                      controller.commonColorList.add(newOption);
                    }

                    // Add to selected highlight colors if not already present
                    if (!controller.highlightColorList.contains(newColor)) {
                      controller.highlightColorList.add(newColor);
                    }
                  },
                ),
              ShadSwitchCustom(
                label: 'anki.word_card.one_cloze_per_card'.tr,
                initialValue: controller.clozeParams.isPerClozePerCard.value,
                onChanged: (v) {
                  controller.clozeParams.isPerClozePerCard.value = v;
                },
              ),
              ShadSelectWithInput(
                key:
                    ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.word_card.select_tags'.tr,
                searchPlaceholder: 'anki.word_card.input_tags'.tr,
                isMultiple: true,
                initialValue: controller.tags.toList(),
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the global tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tags.contains(newTag)) {
                    controller.tags.add(newTag);
                  }
                },
              ),
              ShadInputWithFileSelect(
                title: 'toolbox.common.inputFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const ['txt', 'md', 'docx'],
                isRequired: true,
                allowMultiple: true,
                onFilesSelected: (files) {
                  controller.selectedFilePaths.value = files;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
          )),
    );
  }
}
