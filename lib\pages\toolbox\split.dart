import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/split.dart';

class PDFSplitPage extends StatefulWidget {
  const PDFSplitPage({super.key});

  @override
  State<PDFSplitPage> createState() => _PDFSplitPageState();
}

class _PDFSplitPageState extends State<PDFSplitPage> {
  final controller = Get.put(PDFSplitPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.split.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.split.description'.tr,
                  style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        const SizedBox(height: 4),
                        ShadRadioGroupCustom(
                          label: 'toolbox.split.splitType'.tr,
                          initialValue: controller.splitMode.value,
                          items: controller.splitModeList,
                          onChanged: (value) {
                            controller.splitMode.value = value;
                          },
                        ),
                        if (controller.splitMode.value == "uniform") ...[
                          ShadInputWithValidate(
                              key: const ValueKey("chunk_size"),
                              label: 'toolbox.split.chunkSize'.tr,
                              placeholder:
                                  'toolbox.split.chunkSizePlaceholder'.tr,
                              initialValue:
                                  controller.chunkSize.value.toString(),
                              onChanged: (value) {
                                controller.chunkSize.value = int.parse(value);
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.split.chunkSizeRequired'.tr;
                                }
                                final reg = RegExp(r'^\d+$');
                                if (!reg.hasMatch(value)) {
                                  return 'toolbox.split.enterInteger'.tr;
                                }
                                final size = int.parse(value);
                                if (size <= 0) {
                                  return 'toolbox.split.chunkSizeGreaterThanZero'
                                      .tr;
                                }
                                return "";
                              }),
                        ],
                        if (controller.splitMode.value == "custom") ...[
                          ShadInputWithValidate(
                              key: const ValueKey("split-range"),
                              label: 'toolbox.common.pageRange'.tr,
                              placeholder:
                                  'toolbox.common.pageRangePlaceholder'.tr,
                              initialValue: controller.pageRange.value,
                              onChanged: (value) {
                                controller.pageRange.value = value;
                              },
                              onValidate: (value) async {
                                if (validatePageRange(value)) {
                                  return "";
                                }
                                return 'toolbox.common.enterPageRange'.tr;
                              }),
                        ],
                        if (controller.splitMode.value == "bookmark") ...[
                          ShadSelectCustom(
                            key: ValueKey(
                                "bookmark-level-${controller.bookmarkLevel.value}"),
                            label: 'toolbox.split.bookmarkLevel'.tr,
                            placeholder: 'toolbox.split.selectBookmarkLevel'.tr,
                            initialValue: [
                              controller.bookmarkLevel.value.toString()
                            ],
                            options: controller.bookmarkLevelList,
                            onChanged: (value) {
                              controller.bookmarkLevel.value =
                                  int.parse(value.single);
                            },
                          ),
                        ],
                        if (PathUtils.isDesktop)
                          ShadSelectCustom(
                            key: const ValueKey("output-position"),
                            label: 'toolbox.common.outputLocation'.tr,
                            placeholder:
                                'toolbox.common.selectOutputLocation'.tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.common.outputDirectory'.tr,
                            placeholder:
                                Text('toolbox.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.common.inputFile'.tr,
                          placeholder:
                              Text('toolbox.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['pdf'],
                          isRequired: true,
                          allowMultiple: true,
                          initialValue: controller.selectedFilePaths,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
