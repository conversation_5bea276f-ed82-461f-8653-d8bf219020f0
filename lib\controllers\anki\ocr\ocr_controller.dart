// controller.dart

import 'dart:io';
import 'dart:ui' as ui;
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:pro_image_editor/pro_image_editor.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pasteboard/pasteboard.dart';
import 'package:anki_guru/controllers/anki/ocr/components/ocr_image.dart';

class OCRController extends GetxController {
  // 图片卡片列表
  final imageCards = <OcrImage>[].obs;

  // 页面控制器和当前页索引
  final pageController = PageController();
  final currentPage = 0.obs;

  // 视图模式控制
  final isGridView = false.obs;
  final viewMode = "single".obs; // 添加viewMode属性，兼容之前的代码

  // 保留原有属性
  final roiModelList = <String>[].obs;
  final fieldList = <String>[].obs;
  final cardModel = "".obs;
  final noteList = <dynamic>[].obs;
  final modelFieldConfigs = <String, Map<String, Map<String, dynamic>>>{}.obs;
  final ocrProvider = "paddle-ocr".obs;
  final ocrProviderList = [
    {"label": "Paddle-OCR", "value": "paddle-ocr"},
    {"label": "OpenRouter", "value": "openrouter"},
    {"label": "SiliconFlow", "value": "siliconflow"},
    {"label": "anki.ocr.custom", "value": "custom"},
  ];
  final cardIds = ''.obs;
  final outputMode = "same".obs;
  final outputDir = ''.obs;
  final modelName = "".obs;
  final apiKey = "".obs;
  final baseUrl = "".obs;
  final systemPrompt = "".obs;
  final protocolType = "openai".obs;
  final mergeOutput = false.obs;
  final responseFormat = "json".obs;

  // 添加属性用于处理宫格视图
  int? _lastPageBeforeGrid;
  // 依赖控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final ankiConnectController = Get.find<AnkiConnectController>();

  // 添加变换矩阵属性，跟踪InteractiveViewer的变换状态
  final transformationController = TransformationController();

  // 图片文件选择
  Future<void> pickImageFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
        compressionQuality: 0,
      );

      if (result != null && result.files.isNotEmpty) {
        // 记录添加前的数量
        final oldCount = imageCards.length;

        // 添加所有选中的图片
        for (var file in result.files) {
          if (file.path != null) {
            final imageBytes = await File(file.path!).readAsBytes();
            imageCards.add(OcrImage(
              imageData: imageBytes,
            ));
          }
        }

        // 如果成功添加了图片
        if (imageCards.length > oldCount) {
          // 切换到新添加的第一张图片
          final newIndex = oldCount;
          currentPage.value = newIndex;

          // 使用延迟回调确保布局完成后再滚动和重置变换
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (pageController.hasClients) {
              pageController.jumpToPage(newIndex);
            }
          });

          // 如果当前是网格视图，切换到单页视图
          if (isGridView.value) {
            toggleViewMode();
          }
        }
      }
    } catch (e) {
      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.ocr.file_selection_failed'.tr,
          duration: const Duration(seconds: 2),
          snackPosition: SnackPosition.BOTTOM,
        ),
      );
    }
  }

  // 加载剪贴板图片
  Future<void> loadImageFromClipboard() async {
    try {
      // 在开始操作前显示加载状态
      final currentContext = Get.context;
      Uint8List? imageBytes;

      // 根据平台选择合适的剪贴板读取方式
      if (Platform.isAndroid) {
        try {
          const platform = MethodChannel('samples.flutter.dev/battery');
          final result =
              await platform.invokeMethod<dynamic>('getClipboardData');

          if (result != null && result['type'] == 'image') {
            // 直接解码base64，性能更好
            final base64String =
                result['content'].toString().replaceAll(RegExp(r'\s+'), '');
            imageBytes = base64Decode(base64String);
          }
        } catch (e) {
          logger.e("Android剪贴板读取失败: $e");
        }
      } else {
        // 其他平台直接使用Pasteboard读取
        imageBytes = await Pasteboard.image;
      }

      // 如果没有读取到图片，显示提示并退出
      if (imageBytes == null || imageBytes.isEmpty) {
        if (currentContext != null) {
          showToastNotification(currentContext, "anki.ocr.no_valid_image_in_clipboard".tr, "",
              type: "warning");
        }
        return;
      }

      // 添加图片到列表
      final newIndex = imageCards.length;
      imageCards.add(OcrImage(imageData: imageBytes));

      // 切换到新添加的图片
      currentPage.value = newIndex;

      // 如果当前是网格视图，切换到单页视图
      if (isGridView.value) {
        toggleViewMode();
      } else {
        // 确保在UI更新后重置变换矩阵并滚动到新图片
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // 重置变换矩阵 - 关键步骤确保图片居中显示

          if (pageController.hasClients) {
            // 使用jumpToPage而不是animateToPage，避免动画冲突
            pageController.jumpToPage(newIndex);
          }
        });
      }
    } catch (e) {
      logger.e("从剪贴板读取图片失败: $e");
      final currentContext = Get.context;
      if (currentContext != null) {
        showToastNotification(currentContext, "anki.ocr.clipboard_read_failed".tr, "", type: "error");
      }
    }
  }

  // 修改打开图片批注方法
  Future<void> openImageForAnnotation(int index) async {
    if (index < 0 || index >= imageCards.length) return;

    final imageObj = imageCards[index];
    final editedImageBytes = await Get.to(
      () => ProImageEditor.memory(
        imageObj.imageData,
        callbacks: ProImageEditorCallbacks(
          onImageEditingComplete: (bytes) async {
            Get.back(result: bytes);
          },
        ),
        configs: const ProImageEditorConfigs(
          paintEditor: PaintEditorConfigs(enabled: false),
          textEditor: TextEditorConfigs(enabled: false),
          filterEditor: FilterEditorConfigs(enabled: true),
          emojiEditor: EmojiEditorConfigs(enabled: false),
          tuneEditor: TuneEditorConfigs(enabled: true),
          blurEditor: BlurEditorConfigs(enabled: false),
        ),
      ),
    );

    // 如果编辑器返回了新的图片数据，则更新图片对象
    if (editedImageBytes != null && editedImageBytes is Uint8List) {
      imageCards[index].imageData = editedImageBytes;
      // 重置OCR处理状态
      imageCards[index].isProcessed = false;
      imageCards[index].textBlocks = [];
      imageCards[index].selectedIndices = [];
      // 强制刷新列表以显示更新后的图片
      imageCards.refresh();
    }
  }

  void nextPage() {
    if (currentPage.value < imageCards.length - 1) {
      final nextPageIndex = currentPage.value + 1;
      // 确保控制器已连接到视图
      if (pageController.hasClients) {
        pageController.animateToPage(
          nextPageIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        // 如果控制器尚未连接，直接更新值
        currentPage.value = nextPageIndex;
      }
      // 重置变换矩阵
    }
  }

  void previousPage() {
    if (currentPage.value > 0) {
      final prevPageIndex = currentPage.value - 1;
      // 确保控制器已连接到视图
      if (pageController.hasClients) {
        pageController.animateToPage(
          prevPageIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        // 如果控制器尚未连接，直接更新值
        currentPage.value = prevPageIndex;
      }
      // 重置变换矩阵
    }
  }

  void removeImage(int index) {
    if (index >= 0 && index < imageCards.length) {
      // 先获取当前列表长度
      final currentLength = imageCards.length;

      // 使用Future.microtask来确保所有的状态更新在同一个帧内完成
      Future.microtask(() {
        // 从列表中删除图片
        imageCards.removeAt(index);

        // 调整保存的页码索引
        if (_lastPageBeforeGrid != null) {
          if (_lastPageBeforeGrid! >= currentLength - 1) {
            _lastPageBeforeGrid =
                imageCards.isEmpty ? 0 : imageCards.length - 1;
          } else if (_lastPageBeforeGrid! > index) {
            _lastPageBeforeGrid = _lastPageBeforeGrid! - 1;
          }
        }

        // 调整当前页索引
        if (currentPage.value >= imageCards.length && imageCards.isNotEmpty) {
          currentPage.value = imageCards.length - 1;

          // 确保页面控制器也更新，但使用postFrameCallback确保在UI更新后执行
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (pageController.hasClients &&
                pageController.page?.round() != currentPage.value) {
              pageController.jumpToPage(currentPage.value);
            }
          });
        }
      });
    }
  }

  void removeAllImages() {
    // 使用Future.microtask来确保所有的状态更新在同一个帧内完成
    Future.microtask(() {
      // 清空列表
      imageCards.clear();
      currentPage.value = 0;
      _lastPageBeforeGrid = null;

      // 重置变换矩阵
    });
  }

  // 切换视图模式，优化状态管理
  void toggleViewMode() {
    if (!isGridView.value) {
      // 切换到宫格视图前保存当前页码
      _lastPageBeforeGrid = currentPage.value;
      viewMode.value = 'grid';
    } else {
      // 从宫格视图切换回普通视图时恢复之前的页码
      if (_lastPageBeforeGrid != null &&
          _lastPageBeforeGrid! < imageCards.length) {
        currentPage.value = _lastPageBeforeGrid!;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (pageController.hasClients) {
            pageController.jumpToPage(_lastPageBeforeGrid!);
          }
        });
      }
      viewMode.value = 'single';
    }
    isGridView.value = !isGridView.value;
  }

  // 从宫格视图切换到单页视图
  void switchToPageView(int index) {
    if (index < 0 || index >= imageCards.length) return;

    isGridView.value = false;
    viewMode.value = 'single';
    _lastPageBeforeGrid = null; // 清除保存的页码，因为是直接选择了新页面
    currentPage.value = index;

    // 重置变换矩阵

    // 延迟更新页面控制器，确保布局完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (pageController.hasClients) {
        pageController.jumpToPage(index);
      }
    });
  }

  // 选择图片从网格视图
  void selectImageFromGrid(int index) {
    switchToPageView(index);
  }

  // 获取当前图片，添加缓存优化
  OcrImage? get currentImage {
    final currentIndex = currentPage.value;
    return (currentIndex >= 0 && currentIndex < imageCards.length)
        ? imageCards[currentIndex]
        : null;
  }

  // 执行OCR识别并提交
  Future<void> submitImageOCR(BuildContext context) async {
    // 开始OCR前先重置变换状态

    if (imageCards.isEmpty) {
      showToastNotification(context, "anki.ocr.please_add_images_first".tr, "", type: "error");
      return;
    }

    progressController.reset(showOutputHint: false, numberButtons: 0);
    progressController.showProgressDialog(context);

    // 保存图片到临时文件并获取路径
    List<String> filePaths = [];
    List<String> errorMessages = [];

    try {
      for (var card in imageCards) {
        progressController.updateProgress(
      status: "running",
      message: "anki.ocr.recognizing".tr,
      current: 5,
      total: 100,
    );
        try {
          final tempDir = await getTemporaryDirectory();
          final tempFile = File(
            '${tempDir.path}/image_${DateTime.now().millisecondsSinceEpoch}_${imageCards.indexOf(card) + 1}.png',
          );

          // 确保使用合适的图像格式
          final image = await decodeImageFromList(card.imageData);
          final imgByteData =
              await image.toByteData(format: ui.ImageByteFormat.png);
          if (imgByteData == null) {
            logger.e("无法将图像转换为PNG格式");
            errorMessages.add("anki.ocr.cannot_convert_image_to_png".tr);
            continue;
          }

          // 写入PNG格式图像数据
          final pngBytes = imgByteData.buffer.asUint8List();
          await tempFile.writeAsBytes(pngBytes, flush: true); // 确保数据已写入磁盘

          // 保存临时文件路径到ImageObj
          card.imagePath = tempFile.path;
          filePaths.add(tempFile.path);
          logger.i("已保存图像到: ${tempFile.path} (${pngBytes.length} 字节)");
        } catch (cardError) {
          logger.e("处理单张图像时出错: $cardError");
          errorMessages.add("anki.ocr.error_processing_single_image".trParams({'error': cardError.toString()}));
        }
      }

      if (filePaths.isEmpty) {
        String errorMsg = "anki.ocr.cannot_save_any_valid_image_files".tr;
        if (errorMessages.isNotEmpty) {
          errorMsg += ":\n" + errorMessages.take(3).join("\n");
          if (errorMessages.length > 3) {
            errorMsg += "\n" + "anki.ocr.and_other_errors".trParams({'count': (errorMessages.length - 3).toString()});
          }
        }
        progressController.updateProgress(
      status: "error",
      message: errorMsg,
      current: 0.0,
      total: 100.0,
    );
        return;
      }
    } catch (e) {
      logger.e("保存图像文件时出错: $e");
      progressController.updateProgress(
      status: "error",
      message: "anki.ocr.error_saving_image_files".trParams({'error': e.toString()}),
      current: 0.0,
      total: 100.0,
    );
      return;
    }

    final data = {
      "file_paths": filePaths,
      "provider": ocrProvider.value,
      "model_name": modelName.value,
      "api_key": apiKey.value,
      "base_url": baseUrl.value,
      "system_prompt": systemPrompt.value,
      "merge_output": mergeOutput.value,
      "response_format": "json",
      "show_progress": true
    };

    final resp = await messageController.request(data, 'anki/ocr');

    // 从这开始保持原有的处理逻辑，但优化错误处理
    if (resp.status == "success") {
      try {
        final List<dynamic> results =
            (resp.data is String) ? jsonDecode(resp.data) : (resp.data as List);

        if (results.isEmpty) {
          progressController.updateProgress(
      status: "error",
      message: "anki.ocr.ocr_service_returned_empty_result".tr,
      current: 0.0,
      total: 100.0,
    );
          return;
        }

        // 解析结果并统计错误
        int sessionErrorCount = 0;
        int otherErrorCount = 0;
        String firstError = "";
        List<Map<String, dynamic>> parsedResults = [];

        // 一次性解析所有结果
        for (int i = 0; i < results.length; i++) {
          try {
            final Map<String, dynamic> resultJson = jsonDecode(results[i]);
            parsedResults.add(resultJson);

            // 统计错误
            if (resultJson.containsKey('error')) {
              String error = resultJson['error'];
              if (firstError.isEmpty) firstError = error;

              if (error.contains('Session not initialized')) {
                sessionErrorCount++;
              } else {
                otherErrorCount++;
              }
            }
          } catch (e) {
            logger.e("解析OCR结果失败: $e");
            otherErrorCount++;
          }
        }

        // 处理全局错误情况
        if (sessionErrorCount > 0 &&
            otherErrorCount == 0 &&
            sessionErrorCount == results.length) {
          progressController.updateProgress(
      status: "error",
      message: "anki.ocr.ocr_engine_initialization_failed".tr,
      current: 0.0,
      total: 100.0,
    );
          return;
        }

        // 如果全部出错但错误类型不是会话错误
        if ((sessionErrorCount + otherErrorCount) == results.length &&
            otherErrorCount > 0) {
          progressController.updateProgress(
      status: "error",
      message: "anki.ocr.ocr_recognition_failed".trParams({'error': firstError}),
      current: 0.0,
      total: 100.0,
    );
          return;
        }

        // 如果部分出错
        if ((sessionErrorCount + otherErrorCount) > 0 &&
            (sessionErrorCount + otherErrorCount) < results.length) {
          progressController.updateProgress(
              status: "warning", message: "anki.ocr.partial_ocr_recognition_failed".tr);
        }

        // 处理OCR结果
        int successCount = 0;

        // 根据是否合并输出进行处理
        if (!mergeOutput.value) {
          // 处理多张图片的OCR结果
          for (int i = 0;
              i < parsedResults.length && i < imageCards.length;
              i++) {
            final ocrJsonResult = parsedResults[i];

            // 跳过错误结果
            if (ocrJsonResult.containsKey('error')) continue;

            try {
              // 加载图片并更新OCR结果
              await _updateCardWithOcrResult(imageCards[i], ocrJsonResult);
              successCount++;
            } catch (e) {
              logger.e("处理OCR结果时出错: $e");
            }
          }
        } else if (parsedResults.isNotEmpty &&
            !parsedResults[0].containsKey('error')) {
          // 合并输出模式：使用第一个成功结果更新所有图片
          final ocrJsonResult = parsedResults[0];

          for (int i = 0; i < imageCards.length; i++) {
            try {
              await _updateCardWithOcrResult(imageCards[i], ocrJsonResult);
              successCount++;
            } catch (e) {
              logger.e("合并模式下处理图片失败: ${imageCards[i].imagePath}, 错误: $e");
            }
          }
        }

        if (successCount == 0) {
          progressController.updateProgress(
      status: "error",
      message: "anki.ocr.cannot_process_any_ocr_results".tr,
      current: 0.0,
      total: 100.0,
    );
          return;
        }
        // 根据处理结果更新进度
        if (successCount == 0) {
          progressController.updateProgress(
              status: "warning", message: "anki.ocr.failed_to_recognize_any_images".tr);
        } else if (successCount < imageCards.length) {
          progressController.updateProgress(
      status: "completed",
      message: "anki.ocr.ocr_recognition_partially_completed".trParams({'success': successCount.toString(), 'total': imageCards.length.toString()}),
      current: 100.0,
      total: 100.0,
    );
        } else {
          progressController.updateProgress(
      status: "completed",
      message: "anki.ocr.ocr_recognition_completed".tr,
      current: 100.0,
      total: 100.0,
    );
        }

        Get.back();
        // 更新UI显示，刷新图片列表
        imageCards.refresh();
        return;
      } catch (e) {
        logger.e("OCR处理错误: $e");
        progressController.updateProgress(
      status: "error",
      message: "anki.ocr.ocr_processing_failed".trParams({'error': e.toString()}),
      current: 0.0,
      total: 100.0,
    );
      }
    } else {
      progressController.updateProgress(
      status: "error",
      message: "toolbox.common.error_with_msg"
          .trParams({'error': resp.message}),
      current: 0.0,
      total: 100.0,
    );
    }
  }

  // 辅助方法：更新卡片OCR结果
  Future<void> _updateCardWithOcrResult(
      OcrImage card, Map<String, dynamic> ocrJsonResult) async {
    // 检查图片文件是否存在
    if (!File(card.imagePath).existsSync()) {
      throw Exception("anki.ocr.image_file_not_exist".trParams({'path': card.imagePath}));
    }

    // 加载图片数据
    final imageBytes = await File(card.imagePath).readAsBytes();
    if (imageBytes.isEmpty || imageBytes.length < 100) {
      throw Exception("anki.ocr.image_data_invalid".trParams({'path': card.imagePath}));
    }

    // 解码图片获取尺寸
    final codec = await ui.instantiateImageCodec(imageBytes);
    final frame = await codec.getNextFrame();

    // 更新卡片OCR结果 - 直接使用OcrImage的方法
    card.updateWithOcrResult(ocrJsonResult, frame.image);

    // 确保在更新OCR结果后重置变换状态
  }

  // 全选文本块
  void selectAllBlocks() {
    final currentImg = currentImage;
    if (currentImg == null || !currentImg.isProcessed) return;

    // 使用Future.microtask确保状态更新在当前帧完成后执行
    Future.microtask(() {
      currentImg.selectAllBlocks(); // 直接使用OcrImage的方法
      imageCards.refresh();
    });
  }

  // 清空选中的文本块
  void clearSelectedIndices() {
    final currentImg = currentImage;
    if (currentImg == null) return;

    // 使用Future.microtask确保状态更新在当前帧完成后执行
    Future.microtask(() {
      currentImg.clearSelection(); // 直接使用OcrImage的方法
      imageCards.refresh();
    });
  }

  // 复制选中的文本
  void copySelectedText() {
    final currentImg = currentImage;
    if (currentImg == null || !currentImg.isProcessed) return;

    final text = currentImg.getSelectedText(); // 使用OcrImage的方法
    Clipboard.setData(ClipboardData(text: text));

    Get.showSnackbar(
      GetSnackBar(
        message: 'anki.ocr.copied'.tr,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // 导出选中的文本或全部文本
  Future<void> exportSelectedText() async {
    final currentImg = currentImage;
    if (currentImg == null || !currentImg.isProcessed) return;

    final text = currentImg.getSelectedText(); // 使用OcrImage的方法

    try {
      // 创建默认文件名和初始目录
      final now = DateTime.now()
          .toIso8601String()
          .replaceAll(':', '-')
          .substring(0, 19);
      final defaultFileName = 'ocr_text_$now.txt';

      String initialDirectory = await PathUtils.downloadDir;
      // 显示保存文件对话框
      String? outputPath;
      if (Platform.isAndroid || Platform.isIOS) {
        // 移动平台不支持初始目录，只能指定文件名
        final result = await FilePicker.platform.saveFile(
          dialogTitle: 'anki.ocr.save_ocr_text'.tr,
          fileName: defaultFileName,
          type: FileType.custom,
          allowedExtensions: ['txt'],
        );
        outputPath = result;
      } else {
        // 桌面平台支持初始目录
        final result = await FilePicker.platform.saveFile(
          dialogTitle: 'anki.ocr.save_ocr_text'.tr,
          fileName: defaultFileName,
          initialDirectory: initialDirectory,
          type: FileType.custom,
          allowedExtensions: ['txt'],
        );
        outputPath = result;
      }

      // 如果用户取消了保存操作，直接返回
      if (outputPath == null) {
        return;
      }

      // 确保文件有.txt扩展名
      if (!outputPath.toLowerCase().endsWith('.txt')) {
        outputPath = '$outputPath.txt';
      }

      // 写入文件
      await File(outputPath).writeAsString(text);

      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.ocr.exported_to'.trParams({'path': outputPath}),
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      logger.e("导出文本失败: $e");
      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.ocr.export_failed'.trParams({'error': e.toString()}),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // 切换到下一张图片
  void nextImage() {
    if (currentPage.value < imageCards.length - 1) {
      currentPage.value++;

      // 确保控制器已连接到视图
      if (pageController.hasClients) {
        pageController.animateToPage(
          currentPage.value,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  // 切换到上一张图片
  void previousImage() {
    if (currentPage.value > 0) {
      currentPage.value--;

      // 确保控制器已连接到视图
      if (pageController.hasClients) {
        pageController.animateToPage(
          currentPage.value,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  @override
  void onInit() async {
    if (settingController.cardMode.value == "ankiconnect") {
      roiModelList.value = [];
      cardModel.value = ankiConnectController.modelList.isNotEmpty
          ? ankiConnectController.modelList[0]
          : "";
      updateFieldList(cardModel.value);
    }
    await initPaddleOCRModels();

    // 监听页面控制器的变化
    pageController.addListener(() {
      if (pageController.positions.isNotEmpty &&
          !pageController.position.isScrollingNotifier.value) {
        currentPage.value = pageController.page?.round() ?? 0;
      }
    });

    super.onInit();
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }

  Future<void> initPaddleOCRModels() async {
    try {
      // 获取临时目录
      final tempDir = await PathUtils.tempDir;
      final modelDir = PathUtils.join([tempDir, 'ocr_models']);
      logger.i('OCR模型目录: $modelDir');

      // 检查OCR模型目录
      final modelDirFile = Directory(modelDir);
      if (!modelDirFile.existsSync()) {
        logger.i('创建OCR模型目录: $modelDir');
        modelDirFile.createSync(recursive: true);
      }

      // 模型文件列表
      final modelFiles = [
        'ch_PP-OCRv5_mobile_det.onnx',
        'ch_ppocr_mobile_v2.0_cls_infer.onnx',
        'ch_PP-OCRv5_rec_mobile_infer.onnx'
      ];

      // 并行处理所有模型文件
      await Future.wait(modelFiles.map((fileName) async {
        final filePath = PathUtils.join([modelDir, fileName]);
        final file = File(filePath);

        // 仅当模型文件不存在或过小时才复制
        if (!file.existsSync() || await file.length() < 100 * 1024) {
          logger.i('复制OCR模型文件: $fileName');
          try {
            // 优化：直接使用rootBundle.load并一次性写入
            final ByteData data =
                await rootBundle.load('assets/models/$fileName');
            final bytes =
                data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);

            // 使用临时文件避免写入过程中崩溃导致文件损坏
            final tempFilePath = '$filePath.temp';
            await File(tempFilePath).writeAsBytes(bytes, flush: true);

            // 安全地替换文件
            if (file.existsSync()) {
              await file.delete();
            }
            await File(tempFilePath).rename(filePath);

            logger.i('OCR模型文件复制完成: $fileName (${bytes.length} 字节)');
          } catch (e) {
            logger.e('复制OCR模型文件失败: $fileName, 错误: $e');
            rethrow;
          }
        } else {
          logger.i('OCR模型文件已存在且有效: $fileName');
        }
      }));

      // 最终验证
      for (final fileName in modelFiles) {
        final filePath = PathUtils.join([modelDir, fileName]);
        final file = File(filePath);
        if (!file.existsSync() || await file.length() < 100 * 1024) {
          throw Exception('anki.ocr.ocr_model_file_invalid'.trParams({'fileName': fileName}));
        }
      }

      logger.i('OCR模型文件准备完成');
    } catch (e) {
      logger.e('初始化OCR模型失败: $e');
      rethrow;
    }
  }

  Future<void> updateFieldList(String modelName) async {
    // 保留原有逻辑
    if (settingController.cardMode.value == "ankiconnect") {
      try {
        fieldList.value =
            await AnkiConnectController().updateFieldList(modelName);
      } catch (e) {
        logger.e("updateFieldList error: $e");
      }
    }
  }

  // 添加识别单张图片的函数
  Future<OcrImage?> recognizeSingleImage(OcrImage image,
      {BuildContext? context}) async {
    if (image.imageData.isEmpty) {
      logger.e("图片数据为空");
      return null;
    }

    try {
      // 保存图片到临时文件
      final tempDir = await getTemporaryDirectory();
      final tempFile = File(
        '${tempDir.path}/image_${DateTime.now().millisecondsSinceEpoch}.png',
      );

      // 确保图片格式正确
      final decodedImage = await decodeImageFromList(image.imageData);
      final imgByteData =
          await decodedImage.toByteData(format: ui.ImageByteFormat.png);
      if (imgByteData == null) {
        logger.e("无法将图像转换为PNG格式");
        return null;
      }

      // 写入PNG格式图像
      final pngBytes = imgByteData.buffer.asUint8List();
      await tempFile.writeAsBytes(pngBytes, flush: true);

      // 记录图片路径
      image.imagePath = tempFile.path;
      logger.i("已保存单张图片到: ${tempFile.path} (${pngBytes.length} 字节)");

      // 准备OCR请求数据
      final data = {
        "file_paths": [tempFile.path],
        "provider": ocrProvider.value,
        "model_name": modelName.value,
        "api_key": apiKey.value,
        "base_url": baseUrl.value,
        "system_prompt": systemPrompt.value,
        "merge_output": false, // 单张图片无需合并
        "response_format": "json",
        "show_progress": false // 不显示进度对话框
      };

      // 发送OCR请求
      final resp = await messageController.request(data, 'anki/ocr');

      if (resp.status == "success") {
        final List<dynamic> results =
            (resp.data is String) ? jsonDecode(resp.data) : (resp.data as List);

        if (results.isEmpty) {
          logger.e("OCR服务返回空结果");
          return null;
        }

        // 解析结果
        try {
          final Map<String, dynamic> resultJson = jsonDecode(results[0]);

          if (resultJson.containsKey('error')) {
            logger.e("OCR识别错误: ${resultJson['error']}");
            return null;
          }

          // 创建一个副本来处理OCR结果，避免修改传入的原始图像
          OcrImage processedImage = OcrImage(imageData: image.imageData);
          processedImage.imagePath = image.imagePath;

          // 更新图片的OCR结果
          await _updateCardWithOcrResult(processedImage, resultJson);

          // 返回处理后的图像对象
          return processedImage;
        } catch (e) {
          logger.e("解析OCR结果失败: $e");
          return null;
        }
      } else {
        logger.e("OCR请求失败: ${resp.message}");
        return null;
      }
    } catch (e) {
      logger.e("识别单张图片时出错: $e");
      return null;
    }
  }

  // 添加识别图片路径的函数
  Future<OcrImage?> recognizeImageFromPath(String imagePath) async {
    try {
      // 读取图片文件
      final imageFile = File(imagePath);
      if (!imageFile.existsSync()) {
        logger.e("图片文件不存在: $imagePath");
        return null;
      }

      final imageBytes = await imageFile.readAsBytes();

      // 创建OcrImage
      final ocrImage = OcrImage(imageData: imageBytes);
      ocrImage.imagePath = imagePath;

      // 执行OCR识别并返回处理后的图像
      return await recognizeSingleImage(ocrImage);
    } catch (e) {
      logger.e("从路径识别图片失败: $e");
      return null;
    }
  }
}
