from ultralytics import YOLO

# Load the YOLO11 model
model = YOLO("/Users/<USER>/code/anki-guru/anki-guru2/third_party/question_split_ocr/runs/detect/train6/weights/best.pt")

# Export the model to ONNX format
model.export(format="onnx")  # creates 'yolo11n.onnx'

# Load the exported ONNX model
# onnx_model = YOLO("yolo11n.onnx")

# Run inference
# results = onnx_model("https://ultralytics.com/images/bus.jpg")