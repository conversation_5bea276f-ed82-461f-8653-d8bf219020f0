<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON><PERSON>-<PERSON>mplates -->
<div class="my-card">
    {{#Header}}
    <div class="header">{{Header}}</div>
    {{/Header}}
    <div class="image-container" id="imageContainer">
        <div class="main-image" id="mainImage">
            {{Front}}
        </div>
        <div id="masksContainer"></div>
        <div class="scratch-canvas-container" id="scratchCanvasContainer"></div>
    </div>
    {{#Source}}
    <div class="source">{{Source}}</div>
    {{/Source}}
</div>
<div class="my-card">
    <div class="image-container" id="imageContainerBack">
        <div class="main-image" id="mainImageBack">
            {{Back}}
        </div>
        <div id="masksContainerBack"></div>
        <div class="scratch-canvas-container" id="scratchCanvasContainer"></div>
    </div>
    <div>{{Notes}}</div>

</div>
<script>
    // 遮盖数据和设置
    var masks = [];
    var currentMaskIndex = 0;
    var mode = "{{Mode}}";
    var mainColor = "#FF5656";
    var secondaryColor = "#FFEBA2";
    var currentMaskRevealed = false;
    // 设置颜色变量
    document.documentElement.style.setProperty('--main-color', mainColor);
    document.documentElement.style.setProperty('--secondary-color', secondaryColor);

    // 解析遮盖数据
    try {
        masks = JSON.parse('{{Masks}}');
        console.log('解析后的遮盖数据:', masks);
    } catch (e) {
        console.error("遮盖数据解析错误:", e);
        masks = [];
    }

    // 创建遮盖元素
    function createMasks() {
        const container = document.getElementById('imageContainerBack');
        const masksContainer = document.getElementById('masksContainerBack');
        const imgContainer = document.getElementById('mainImageBack');

        if (!container || !masksContainer || !imgContainer) {
            console.error('找不到必要的DOM元素');
            return;
        }

        // 查找实际的img元素
        const img = imgContainer.querySelector('img');
        if (!img) {
            console.error('找不到图片元素');
            return;
        }

        // 清除现有遮盖
        masksContainer.innerHTML = '';

        // 如果图片已加载，直接创建遮盖，否则等待加载完成
        if (img.complete) {
            createMasksForImage(img);
        } else {
            img.onload = function () {
                createMasksForImage(img);
            };
        }
    }
    // 为已加载的图片创建遮盖
    function createMasksForImage(img) {
        const masksContainer = document.getElementById('masksContainerBack');

        if (!masksContainer) return;

        // Function to update mask positions based on current image dimensions
        function updateMaskPositions() {
            // Clear existing masks first
            masksContainer.innerHTML = '';

            // Get current image dimensions and position
            const imgRect = img.getBoundingClientRect();
            const containerRect = masksContainer.parentElement.getBoundingClientRect();

            // Calculate the offset of the image relative to its container
            const offsetLeft = imgRect.left - containerRect.left;
            const offsetTop = imgRect.top - containerRect.top;

            const imgWidth = img.offsetWidth;
            const imgHeight = img.offsetHeight;

            // Update containers to match image size and position
            masksContainer.style.width = imgWidth + 'px';
            masksContainer.style.height = imgHeight + 'px';
            masksContainer.style.left = offsetLeft + 'px';
            masksContainer.style.top = offsetTop + 'px';
            // Create each mask group
            masks.forEach((maskGroup, groupIndex) => {
                maskGroup.forEach((maskCoords, coordIndex) => {
                    const maskElement = document.createElement('div');
                    maskElement.className = 'mask';
                    maskElement.dataset.groupIndex = groupIndex;
                    maskElement.dataset.normalizedCoords = JSON.stringify(maskCoords);

                    // Calculate pixel values from normalized coordinates
                    const left = maskCoords[0] * imgWidth;
                    const top = maskCoords[1] * imgHeight;
                    const width = maskCoords[2] * imgWidth;
                    const height = maskCoords[3] * imgHeight;

                    maskElement.style.left = left + 'px';
                    maskElement.style.top = top + 'px';
                    maskElement.style.width = width + 'px';
                    maskElement.style.height = height + 'px';
                    maskElement.classList.add('main-mask');
                    maskElement.onclick = function () {
                        handleMaskClick(groupIndex);
                    };
                    masksContainer.appendChild(maskElement);

                });
            });
        }

        // Initial update
        updateMaskPositions();

        // Add window resize listener
        window.addEventListener('resize', function () {
            // Use debounce to avoid too many updates
            clearTimeout(window.resizeTimer);
            window.resizeTimer = setTimeout(function () {
                updateMaskPositions();
            }, 250);
        });

        // Add MutationObserver to watch for DOM changes that might affect image size
        const observer = new MutationObserver(function (mutations) {
            mutations.forEach(function (mutation) {
                if (mutation.type === 'attributes' &&
                    (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
                    updateMaskPositions();
                }
            });
        });

        // Start observing the image and its parent containers
        observer.observe(img, { attributes: true });
        observer.observe(img.parentElement, { attributes: true });
        observer.observe(masksContainer.parentElement, { attributes: true });

        // Also observe for resize events of the image
        const resizeObserver = new ResizeObserver(() => {
            updateMaskPositions();
        });
        resizeObserver.observe(img);
    }

    function handleMaskClick(groupIndex) {
        const clickedMasks = document.querySelectorAll(`.mask[data-group-index="${groupIndex}"]`);
        clickedMasks.forEach(mask => {
            mask.classList.toggle('revealed');
        });
    }

    // 切换遮盖组显示状态
    function toggleMaskGroup(groupIndex) {
        const maskElements = document.querySelectorAll('.mask');
        let anyToggled = false;

        maskElements.forEach(mask => {
            if (parseInt(mask.dataset.groupIndex) === groupIndex) {
                mask.classList.toggle('revealed');
                anyToggled = true;
            }
        });

        // 更新当前遮罩的状态
        if (groupIndex === currentMaskIndex) {
            // 检查当前遮罩组中是否所有遮罩都已揭示
            const currentMasks = Array.from(maskElements).filter(
                mask => parseInt(mask.dataset.groupIndex) === currentMaskIndex
            );
            currentMaskRevealed = currentMasks.every(mask => mask.classList.contains('revealed'));
        }

        return anyToggled;
    }
    // 切换所有遮盖
    function toggleAllMasks() {
        const maskElements = document.querySelectorAll('.mask');

        // 检查是否所有遮盖都已显示
        const allRevealed = Array.from(maskElements).every(mask =>
            mask.classList.contains('revealed'));

        // 根据当前状态切换所有遮盖
        maskElements.forEach(mask => {
            if (allRevealed) {
                mask.classList.remove('revealed');
            } else {
                mask.classList.add('revealed');
            }
        });
    }
    // 上一个遮盖
    function prevMask() {
        // 确定当前遮盖和前一个遮盖的索引
        const prevIndex = currentMaskIndex > 0 ? currentMaskIndex - 1 : 0;
        console.log(prevIndex);
        // 查找当前和前一个遮盖元素
        const currentMasks = document.querySelectorAll(`.mask[data-group-index="${currentMaskIndex}"]`);
        const prevMasks = document.querySelectorAll(`.mask[data-group-index="${prevIndex}"]`);

        // 确保当前遮盖是隐藏的（盖住）
        currentMasks.forEach(mask => {
            if (mask.classList.contains('revealed')) {
                mask.classList.remove('revealed');
            }
        });
        // 如果不是第一个，移到前一个
        if (currentMaskIndex > 0) {
            currentMaskIndex = prevIndex;
        }
    }

    // 下一个遮盖
    function nextMask() {
        // 确定当前遮盖和下一个遮盖的索引
        const nextIndex = currentMaskIndex < masks.length - 1 ? currentMaskIndex + 1 : currentMaskIndex;
        console.log(nextIndex);
        // 查找当前遮盖元素
        const currentMasks = document.querySelectorAll(`.mask[data-group-index="${currentMaskIndex}"]`);

        // 确保当前遮盖是可见的（揭开）
        currentMasks.forEach(mask => {
            if (!mask.classList.contains('revealed')) {
                mask.classList.add('revealed');
            }
        });
        // 如果不是最后一个，移到下一个
        if (currentMaskIndex < masks.length - 1) {
            currentMaskIndex = nextIndex;
        }
    }
    function updateToggleButtonIcon() {
        const toggleBtn = document.querySelector('.toggle-all');
        if (!toggleBtn) return;

        const maskElements = document.querySelectorAll('.mask');
        const allRevealed = Array.from(maskElements).every(mask =>
            mask.classList.contains('revealed'));

        // 根据当前状态更新SVG图标显示
        const eyeOpen = toggleBtn.querySelector('.eye-open');
        const eyeClosed = toggleBtn.querySelector('.eye-closed');

        if (allRevealed) {
            // 所有遮罩已显示，显示闭眼图标
            if (eyeOpen) eyeOpen.style.display = 'none';
            if (eyeClosed) eyeClosed.style.display = 'inline';
            toggleBtn.title = '隐藏所有遮盖';
        } else {
            // 有遮罩未显示，显示开眼图标
            if (eyeOpen) eyeOpen.style.display = 'inline';
            if (eyeClosed) eyeClosed.style.display = 'none';
            toggleBtn.title = '显示所有遮盖';
        }
    }
    // 设置导航按钮
    function setupNavigation() {
        if (masks.length === 0) return;
        const container = document.getElementById('imageContainerBack');

        // 如果没有找到容器或只有一个遮盖，不添加导航
        if (!container || masks.length <= 1) return;

        const navigation = document.createElement('div');
        navigation.className = 'navigation';

        // 上一个按钮 - 使用SVG箭头图标
        const prevBtn = document.createElement('a');
        prevBtn.className = 'nav-btn';
        prevBtn.innerHTML = `
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <line x1="19" y1="12" x2="5" y2="12"></line>
      <polyline points="12 19 5 12 12 5"></polyline>
    </svg>`;
        prevBtn.href = 'javascript:void(0);';
        prevBtn.title = '上一个';
        prevBtn.addEventListener('click', prevMask);

        // 下一个按钮 - 使用SVG箭头图标
        const nextBtn = document.createElement('a');
        nextBtn.className = 'nav-btn';
        nextBtn.innerHTML = `
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <line x1="5" y1="12" x2="19" y2="12"></line>
      <polyline points="12 5 19 12 12 19"></polyline>
    </svg>`;
        nextBtn.href = 'javascript:void(0);';
        nextBtn.title = '下一个';
        nextBtn.addEventListener('click', nextMask);

        // 切换全部按钮 - 使用SVG眼睛图标
        const toggleAllBtn = document.createElement('a');
        toggleAllBtn.className = 'nav-btn toggle-all';
        toggleAllBtn.innerHTML = `
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="eye-open">
      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
      <circle cx="12" cy="12" r="3"></circle>
    </svg>
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="eye-closed" style="display:none;">
      <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
      <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>`;
        toggleAllBtn.href = 'javascript:void(0);';
        toggleAllBtn.title = '显示/隐藏所有遮盖';
        toggleAllBtn.addEventListener('click', toggleAllMasks);

        // 添加按钮到导航栏
        navigation.appendChild(prevBtn);
        navigation.appendChild(toggleAllBtn);
        navigation.appendChild(nextBtn);

        // 将导航栏添加到容器后面
        container.parentNode.insertBefore(navigation, container.nextSibling);

        // 初始更新切换按钮图标状态
        updateToggleButtonIcon();
    }
    // 设置键盘快捷键
    function setupKeyboardShortcuts() {
        // 确保全局命名空间中没有重复定义
        window.currentCardKeyHandler = function (e) {
            if (e.key === 'j') {
                prevMask();
                e.preventDefault();
                e.stopPropagation();
                return false;
            } else if (e.key === 'k') {
                nextMask();
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        };
        document.onkeydown = window.currentCardKeyHandler;
    }

    createMasks();
    setupNavigation();
    setupKeyboardShortcuts();
</script>