# 使用我们之前的版本，该版本具有 normalization_mode
# 但没有 avg_punct_width，并且 get_character_widths
# 根据 normalization_mode 和 is_cjk/类别 处理宽度。

import easyocr
import cv2
import numpy as np
import matplotlib.pyplot as plt
import unicodedata

# --- Normalization Maps (Keep FULL_TO_HALF_MAP and HALF_TO_FULL_MAP as defined before) ---
FULL_TO_HALF_MAP = {ord(c): h for c, h in zip('，。；：？！（）“”‘’【】《》、￥…　０１２３４５６７８９ＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚ', ',.;:?!()""\'\'[]<>,\'$... 0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz')}
HALF_TO_FULL_MAP = {ord(h): f for f, h in zip('，。；：？！（）“”‘’【】《》、￥…　０１２３４５６７８９ＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚ', ',.;:?!()""\'\'[]<>,\'$... 0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz')}
# Ensure these maps are complete for your needs


def normalize_text_for_width(text, mode='half'):
    """Normalizes text based on mode."""
    if mode == 'half':
        return text.translate(FULL_TO_HALF_MAP)
    elif mode == 'full':
        return text.translate(HALF_TO_FULL_MAP)
    else:
        return text

# --- Helper Functions (is_cjk, interpolate - unchanged) ---
def is_cjk(character):
    cp = ord(character)
    # Basic CJK Range + Extensions + Compatibility + Hangul + Kana + Fullwidth
    if 0x4E00 <= cp <= 0x9FFF or \
       0x3400 <= cp <= 0x4DBF or \
       0xF900 <= cp <= 0xFAFF or \
       0xAC00 <= cp <= 0xD7AF or \
       0x3040 <= cp <= 0x309F or \
       0x30A0 <= cp <= 0x30FF or \
       0xFF00 <= cp <= 0xFFEF: # Fullwidth Forms check is important here
        return True
    return False

def interpolate(p1, p2, ratio):
    p1_arr = np.array(p1); p2_arr = np.array(p2)
    return p1_arr + (p2_arr - p1_arr) * ratio

# --- get_character_widths (Refined logic based on normalization) ---
def get_character_widths(normalized_text, cjk_ratio=1.8, space_width=0.8, half_punct_width=1.0):
    """
    Calculates widths based on normalized text properties.
    Full-width chars (incl. normalized punctuation/letters/digits) get cjk_ratio.
    Half-width punctuation gets half_punct_width.
    Half-width space gets space_width.
    Other half-width get 1.0.
    """
    widths = []
    total_width = 0
    if not normalized_text: return [], 0
    for char in normalized_text:
        width = 1.0 # Default half-width
        category = unicodedata.category(char)

        # Check CJK/Fullwidth first (covers CJK chars and anything normalized to fullwidth)
        if is_cjk(char):
            width = cjk_ratio
        elif category == 'Zs': # Standard space (U+0020) - likely result if normalized to half
            width = space_width
        elif category.startswith('P'): # Punctuation (likely half-width if normalized to half)
             width = half_punct_width
        # else: width remains 1.0 (for standard half-width letters/digits)

        widths.append(width)
        total_width += width

    return widths, max(total_width, 1e-6)


# --- Estimation function (using refined get_character_widths) ---
def estimate_keyword_bbox_at_index(bbox, text, keyword, start_index,
                                   normalization_mode='half',
                                   cjk_ratio=1.8, space_width=0.8, half_punct_width=1.0):
    """Estimates keyword bbox using refined width calculation."""
    try:
        # Step 1: Normalize text
        normalized_text = normalize_text_for_width(text, mode=normalization_mode)
        if len(normalized_text) != len(text):
            print(f"Warn: Len changed NormMode:{normalization_mode}"); return None

        # Step 2: Calculate widths based on normalized text and refined logic
        char_widths, total_effective_width = get_character_widths(
            normalized_text, cjk_ratio, space_width, half_punct_width
        )

        # Steps 3-6: Find indices, sum widths, calc ratios, interpolate (same)
        end_index = start_index + len(keyword)
        if end_index > len(text): return None
        pre_keyword_width = sum(char_widths[:start_index])
        keyword_width = sum(char_widths[start_index:end_index])
        start_ratio = max(0.0, min(1.0, pre_keyword_width / total_effective_width))
        end_ratio = max(0.0, min(1.0, (pre_keyword_width + keyword_width) / total_effective_width))

        tl, tr, br, bl = np.array(bbox, dtype=np.float32)
        kw_tl = interpolate(tl, tr, start_ratio); kw_tr = interpolate(tl, tr, end_ratio)
        kw_bl = interpolate(bl, br, start_ratio); kw_br = interpolate(bl, br, end_ratio)
        keyword_polygon = np.array([kw_tl, kw_tr, kw_br, kw_bl], dtype=np.int32)
        return keyword_polygon

    except Exception as e:
        print(f"Error estimating bbox kw:'{keyword}' idx:{start_index} txt:'{text}' mode:{normalization_mode}: {e}")
        return None

# --- Helper to get starting ROI ---
def get_starting_roi_coords(start_tl, start_bl, end_tr, end_br, # Estimated keyword corners (approx)
                             image_shape, target_char_count=3, avg_char_width_px=15):
    """
    Calculates ROI coordinates focused on the estimated starting boundary.
    Creates a narrow vertical strip.

    Args:
        start_tl (np.array): Estimated top-left corner of keyword.
        start_bl (np.array): Estimated bottom-left corner of keyword.
        end_tr (np.array): Estimated top-right corner (used for height hint).
        end_br (np.array): Estimated bottom-right corner (used for height hint).
        image_shape (tuple): Original image shape (height, width).
        target_char_count (int): How many characters wide the ROI should roughly be.
        avg_char_width_px (int): Estimated average pixel width of a character in the region.

    Returns:
        tuple: (x_min, y_min, x_max, y_max) ROI coordinates or None.
    """
    try:
        # Estimate center Y and height from the keyword corners
        center_y = (start_tl[1] + start_bl[1] + end_tr[1] + end_br[1]) / 4
        height = max(abs(start_bl[1] - start_tl[1]), abs(end_br[1] - end_tr[1]))
        if height < 5: height = 2 * avg_char_width_px # Min height fallback

        # Estimate center X of the starting boundary
        center_x = (start_tl[0] + start_bl[0]) / 2

        # Calculate ROI width based on target characters
        roi_width = target_char_count * avg_char_width_px

        # Define ROI box, centered vertically, starting slightly before estimated start_x
        x_min = max(0, int(center_x - roi_width * 0.7)) # Start a bit left
        x_max = min(image_shape[1], int(center_x + roi_width * 0.3)) # End a bit right
        y_min = max(0, int(center_y - height * 0.7)) # Slightly above center
        y_max = min(image_shape[0], int(center_y + height * 0.7)) # Slightly below center

        if x_min >= x_max or y_min >= y_max: return None
        return x_min, y_min, x_max, y_max

    except Exception as e:
        print(f"Error calculating starting ROI coords: {e}")
        return None

# --- Function for starting point calibration ---
def calibrate_start_point_with_local_ocr(image, reader, initial_polygon, keyword,
                                         image_shape, target_char_count=3, avg_char_width_px=15,
                                         min_confidence=0.2):
    """
    Attempts to calibrate the keyword's starting boundary (TL, BL) using local OCR.

    Args:
        image (np.ndarray): Original image.
        reader (easyocr.Reader): OCR reader.
        initial_polygon (np.array): Polygon from initial width estimation.
        keyword (str): Target keyword.
        image_shape (tuple): Original image shape.
        target_char_count (int): Width hint for starting ROI.
        avg_char_width_px (int): Pixel width hint for starting ROI.
        min_confidence (float): Min confidence for local OCR block containing start char.

    Returns:
        tuple: (calibrated_tl, calibrated_bl) as np.arrays in original coords, or (None, None).
    """
    if initial_polygon is None or len(keyword) == 0: return None, None

    # Get estimated corners from initial polygon
    p_tl, p_tr, p_br, p_bl = initial_polygon.astype(np.float32) # Use float for calculation

    # 1. Get starting ROI coordinates
    roi_coords = get_starting_roi_coords(p_tl, p_bl, p_tr, p_br, image_shape,
                                         target_char_count, avg_char_width_px)
    if roi_coords is None: return None, None
    x_min, y_min, x_max, y_max = roi_coords

    # 2. Extract ROI
    roi = image[y_min:y_max, x_min:x_max]
    if roi.size == 0: return None, None

    # 3. Local OCR on Starting ROI
    try:
        # We need fine-grained results, paragraph=False is essential
        local_results = reader.readtext(roi, detail=1, paragraph=False)
    except Exception as e:
        print(f"  -> Error during starting OCR: {e}"); return None, None

    if not local_results: return None, None

    # 4. Find block containing the start of the keyword
    first_char = keyword[0]
    # Maybe look for first 1 or 2 chars for robustness?
    start_prefix = keyword[:min(2, len(keyword))]

    best_match_box_roi = None
    min_x_start = float('inf') # Find the leftmost box containing the start

    for bbox_roi, text, conf in local_results:
        if conf >= min_confidence and (first_char in text or start_prefix in text):
             # Check if this box's left edge is the leftmost found so far
             current_min_x = min(p[0] for p in bbox_roi)
             if current_min_x < min_x_start:
                 # Basic check: does the text start roughly matching?
                 try:
                     text_start_idx = text.find(first_char)
                     if text_start_idx != -1 and keyword.startswith(text[text_start_idx:]):
                          min_x_start = current_min_x
                          best_match_box_roi = bbox_roi
                 except: pass # Ignore errors in string matching

    if best_match_box_roi is None:
        # print(f"  -> Start char '{first_char}' not found in starting ROI for '{keyword}'.")
        return None, None

    # 5. Use the left boundary of the found box as the calibrated start
    box_roi_np = np.array(best_match_box_roi, dtype=np.float32)
    calibrated_roi_tl = box_roi_np[0] # Top-left of the *found block* in ROI
    calibrated_roi_bl = box_roi_np[3] # Bottom-left of the *found block* in ROI

    # Refine: Instead of block corners, maybe estimate character start based on index? More complex.
    # Simpler: Assume the block's left edge is a good enough proxy for the first char's start.

    # Translate back to original image coordinates
    calibrated_tl = calibrated_roi_tl + np.array([x_min, y_min], dtype=np.float32)
    calibrated_bl = calibrated_roi_bl + np.array([x_min, y_min], dtype=np.float32)

    # print(f"  -> Start point calibrated for '{keyword}'.")
    return calibrated_tl, calibrated_bl


# --- Main Function (integrating calibration) ---
def highlight_keywords_in_image(image_path, keywords, languages=['ch_sim', 'en'], gpu=False,
                                enable_start_calibration=True, # <<--- New flag
                                # Calibration parameters
                                cal_roi_chars=3, cal_avg_char_px=15, cal_min_conf=0.2,
                                # Initial estimation parameters
                                normalization_mode='full', cjk_ratio=1.9,
                                space_width=0.8, half_punct_width=1.0):
    """
    Highlights keywords using starting point calibration via local OCR.

    Args:
        ...
        enable_start_calibration (bool): If True, attempts start point calibration.
        cal_roi_chars (int): Width hint (in chars) for calibration ROI.
        cal_avg_char_px (int): Pixel width hint for calibration ROI.
        cal_min_conf (float): Min confidence for local OCR block used in calibration.
        ...
    """
    print(f"Initializing EasyOCR with languages: {languages} (GPU: {gpu})")
    try: reader = easyocr.Reader(languages, gpu=gpu)
    except Exception as e: print(f"Error initializing EasyOCR: {e}"); return None, []

    print(f"Loading image: {image_path}")
    image = cv2.imread(image_path)
    if image is None: print(f"Error loading image: {image_path}"); return None, []
    img_h, img_w = image.shape[:2]

    output_image = image.copy()
    print(f"Performing Initial OCR... (Norm Mode: {normalization_mode}, CJK Ratio: {cjk_ratio})")
    results = reader.readtext(image, detail=1, paragraph=False)
    print(f"Initial OCR finished. Found {len(results)} text blocks.")

    found_keywords_info = []
    ocr_results_map = {i: (bbox, text, prob) for i, (bbox, text, prob) in enumerate(results)}

    print(f"Searching keywords & Calibrating Starts (Calibration {'Enabled' if enable_start_calibration else 'Disabled'})...")
    for block_index, (bbox, text, prob) in ocr_results_map.items():
        processed_indices = set()
        for keyword in keywords:
            start_search_index = 0
            while True:
                found_index = text.find(keyword, start_search_index)
                if found_index == -1: break

                keyword_range = set(range(found_index, found_index + len(keyword)))
                if not keyword_range.isdisjoint(processed_indices):
                    start_search_index = found_index + 1; continue

                # --- Step 1: Initial Full Estimation (We need ratios and end points from this) ---
                initial_polygon = estimate_keyword_bbox_at_index(
                    bbox, text, keyword, found_index,
                    normalization_mode=normalization_mode, cjk_ratio=cjk_ratio,
                    space_width=space_width, half_punct_width=half_punct_width
                )

                if initial_polygon is None: # If initial estimation fails, skip
                     print(f" -> Initial estimation failed for '{keyword}' idx {found_index}.")
                     start_search_index = found_index + 1
                     continue

                p_tl, p_tr, p_br, p_bl = initial_polygon.astype(np.float32)
                final_polygon_points = [p_tl, p_tr, p_br, p_bl] # Default to initial

                # --- Step 2: Optional Start Point Calibration ---
                calibrated = False
                if enable_start_calibration:
                    # print(f"Attempting start calibration for '{keyword}'...")
                    calibrated_tl, calibrated_bl = calibrate_start_point_with_local_ocr(
                        image, reader, initial_polygon, keyword, (img_h, img_w),
                        cal_roi_chars, cal_avg_char_px, cal_min_conf
                    )

                    if calibrated_tl is not None and calibrated_bl is not None:
                        # --- Step 3: Calculate End Point based on Calibrated Start ---
                        try:
                            # Need width ratio from original calculation
                            norm_text = normalize_text_for_width(text, normalization_mode)
                            char_widths, total_eff_w = get_character_widths(norm_text, cjk_ratio, space_width, half_punct_width)
                            kw_width = sum(char_widths[found_index : found_index + len(keyword)])
                            kw_width_ratio = kw_width / total_eff_w
                            kw_width_ratio = max(0.0, min(1.0, kw_width_ratio)) # Clamp ratio

                            # Original direction vectors (use float versions)
                            vec_top = p_tr - p_tl
                            vec_bottom = p_br - p_bl

                            # Calculate end points based on calibrated start and original vectors scaled by width ratio
                            calibrated_tr = calibrated_tl + vec_top * kw_width_ratio
                            calibrated_br = calibrated_bl + vec_bottom * kw_width_ratio

                            final_polygon_points = [calibrated_tl, calibrated_tr, calibrated_br, calibrated_bl]
                            calibrated = True
                        except Exception as e:
                            print(f"  -> Error calculating endpoint after calibration: {e}")
                            # Fallback to initial estimate if endpoint calculation fails
                            final_polygon_points = [p_tl, p_tr, p_br, p_bl]
                            calibrated = False # Mark as not successfully calibrated


                # --- Step 4: Store and Draw ---
                final_polygon = np.array(final_polygon_points, dtype=np.int32)
                processed_indices.update(keyword_range)
                found_keywords_info.append({
                    "keyword": keyword, "text_block": text, "confidence": prob,
                    "block_bbox": bbox,
                    "final_keyword_bbox": final_polygon.tolist(),
                    "calibrated_start": calibrated,
                    "found_index": found_index, "block_index": block_index
                })
                cv2.polylines(output_image, [final_polygon], isClosed=True, color=(0, 0, 255), thickness=2)

                start_search_index = found_index + 1

    if not found_keywords_info: print("No specified keywords found.")
    return output_image, found_keywords_info


# --- 使用示例 ---
from pathlib import Path
image_file = '/Users/<USER>/Downloads/Snipaste_2025-04-29_20-52-16.png'  # <--- 修改为你的图片路径
search_keywords = ['语言模型', '使用', '多轮对话', '事物', '文本', '图片', '调用'] # <--- 修改为你需要查找的关键词
output_image_file = str(Path(image_file).parent / 'output_char_boxes.png')

# --- Configuration ---
cfg_enable_start_calibration = True # <<--- Enable/Disable Calibration
# Calibration Params
cfg_cal_roi_chars = 4           # How many chars wide should the start ROI be? (Try 2-5)
cfg_cal_avg_char_px = 20        # Estimated avg char width in pixels (Adjust based on font size)
cfg_cal_min_conf = 0.15         # Min confidence for local OCR block (Try 0.1-0.3)

# Initial Estimation Parameters (Still important!)
cfg_normalization_mode = 'full'
cfg_cjk_ratio = 1.9
cfg_space_width = 0.8
cfg_half_punct_width = 1.0

print(f"Running with Start Calibration: {cfg_enable_start_calibration}, CalROI Chars: {cfg_cal_roi_chars}, CalAvgPx: {cfg_cal_avg_char_px}")
print(f"Initial Estimation Params - NormMode: {cfg_normalization_mode}, CJKRatio: {cfg_cjk_ratio}")

highlighted_image, found_data = highlight_keywords_in_image(
    image_path=image_file,
    keywords=search_keywords,
    languages=['ch_sim', 'en'],
    gpu=False,
    enable_start_calibration=cfg_enable_start_calibration,
    cal_roi_chars=cfg_cal_roi_chars,
    cal_avg_char_px=cfg_cal_avg_char_px,
    cal_min_conf=cfg_cal_min_conf,
    normalization_mode=cfg_normalization_mode,
    cjk_ratio=cfg_cjk_ratio,
    space_width=cfg_space_width,
    half_punct_width=cfg_half_punct_width
)

# --- Post-processing and Display ---
if highlighted_image is not None:
    print("\n--- Found Keywords Summary ---")
    calibrated_count = 0
    if found_data:
        found_data.sort(key=lambda x: (x['block_index'], x['found_index']))
        for item in found_data:
            status = "Calibrated" if item['calibrated_start'] else "InitialEst"
            if item['calibrated_start']: calibrated_count += 1
            print(f"Blk {item['block_index']}, Idx {item['found_index']}: Kw '{item['keyword']}' ({status}), Txt: '{item['text_block']}', Final BBox: {item['final_keyword_bbox']}")
        print(f"\nTotal keywords found: {len(found_data)}, Successfully calibrated starts: {calibrated_count}")
    else: print("No keywords were found.")
    print("-----------------------------")

    plt.figure(figsize=(15, 12))
    plt.imshow(cv2.cvtColor(highlighted_image, cv2.COLOR_BGR2RGB))
    title = f'Keywords Highlighted (Start Calibration {"Enabled" if cfg_enable_start_calibration else "Disabled"})'
    plt.title(title)
    plt.axis('off')
    plt.show()

    try:
        fname = output_image_file.replace('.png', f'_startcal{str(cfg_enable_start_calibration)[0]}.png')
        cv2.imwrite(fname, highlighted_image)
        print(f"Highlighted image saved to: {fname}")
    except Exception as e: print(f"Error saving image: {e}")
else: print("Failed to process the image.")