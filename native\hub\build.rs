// use std::env;

fn main() {
    println!("cargo:rerun-if-changed=build.rs");
    // ios
    // println!("cargo:rustc-link-lib=static=pdfium");
    // println!("cargo:rustc-link-search=native=/Users/<USER>/code/anki-guru/native/hub/assets/ios");
    // println!("cargo:rustc-link-search=framework={}", "/Users/<USER>/code/anki-guru/native/paddle-ocr-rs/libs/onnxruntime.xcframework");
    // println!("cargo:rustc-link-lib=dylib=c++");
    // println!("cargo:rustc-link-lib=framework=CoreGraphics");
}
