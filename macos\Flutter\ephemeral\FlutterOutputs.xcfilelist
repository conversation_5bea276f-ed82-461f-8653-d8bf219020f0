/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/App
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/Info.plist
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/AssetManifest.bin
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/AssetManifest.json
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/FontManifest.json
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/NOTICES.Z
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/NativeAssetsManifest.json
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/anki/__anki-persistence.js
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/fonts/SourceHanSansSC-Normal.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/i18n/en.json
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/i18n/ja.json
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/i18n/zh-CN.json
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/i18n/zh-TW.json
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/bili.ico
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/logo.ico
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/logo.png
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/pdd.ico
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/qq_channel.jpg
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/qq_group.jpg
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/splash.png
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/tb.png
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/up_header.png
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/wechat_group.jpg
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/jsonrepair.min.js
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/mammoth.browser.min.js
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/markmap.index.mjs
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/models/ch_PP-OCRv5_mobile_det.onnx
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/models/ch_PP-OCRv5_rec_mobile_infer.onnx
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/models/ch_ppocr_mobile_v2.0_cls_infer.onnx
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/parse_html.js
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/fonts/MaterialIcons-Regular.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/isolate_snapshot_data
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/kernel_blob.bin
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/flex_color_picker/assets/opacity.png
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/flutter_inappwebview_web/assets/web/web_support.js
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/flutter_js/assets/js/fetch.js
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/iconsax_flutter/fonts/FlutterIconsax.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/lucide_icons_flutter/assets/build_font/LucideVariable-w100.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/lucide_icons_flutter/assets/build_font/LucideVariable-w200.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/lucide_icons_flutter/assets/build_font/LucideVariable-w300.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/lucide_icons_flutter/assets/build_font/LucideVariable-w400.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/lucide_icons_flutter/assets/build_font/LucideVariable-w500.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/lucide_icons_flutter/assets/build_font/LucideVariable-w600.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/lucide_icons_flutter/assets/lucide.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/media_kit/assets/web/hls1.4.10.js
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/pro_image_editor/assets/fonts/ProImageEditorIcons.ttf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/pro_image_editor/lib/shared/shaders/pixelate.frag
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/pro_image_editor/lib/web/web_worker.dart.js
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/pro_image_editor/lib/web/web_worker.dart.js.map
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/quill_native_bridge_linux/assets/xclip
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/Geist-Black.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/Geist-Bold.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/Geist-Light.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/Geist-Medium.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/Geist-Regular.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/Geist-SemiBold.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/Geist-Thin.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/Geist-UltraBlack.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/Geist-UltraLight.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/GeistMono-Black.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/GeistMono-Bold.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/GeistMono-Light.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/GeistMono-Medium.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/GeistMono-Regular.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/GeistMono-SemiBold.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/GeistMono-Thin.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/GeistMono-UltraBlack.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/shadcn_ui/fonts/GeistMono-UltraLight.otf
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/wakelock_plus/assets/no_sleep.js
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/shaders/ink_sparkle.frag
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/vm_snapshot_data
/Users/<USER>/code/anki-guru/build/macos/Build/Products/Debug/FlutterMacOS.framework/Versions/A/FlutterMacOS
