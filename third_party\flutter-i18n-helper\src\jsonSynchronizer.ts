import * as vscode from 'vscode';
import { getConfiguration, getI18nDirUri, readJsonFile } from './utils';

export async function syncJsonKeys() {
    const config = getConfiguration();
    const sourceLang = config.get<string>('sourceLanguage');
    const i18nDirUri = await getI18nDirUri();

    if (!sourceLang || !i18nDirUri) {
        vscode.window.showErrorMessage('Please configure sourceLanguage and i18nDir in settings.');
        return;
    }

    const sourceFileUri = vscode.Uri.joinPath(i18nDirUri, `${sourceLang}.json`);
    const sourceData = await readJsonFile(sourceFileUri);

    if (!sourceData) {
        vscode.window.showErrorMessage(`Source language file not found or is invalid: ${sourceFileUri.fsPath}`);
        return;
    }
    
    const sourceKeys = Object.keys(sourceData);

    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "Synchronizing i18n keys...",
        cancellable: false
    }, async (progress) => {
        const entries = await vscode.workspace.fs.readDirectory(i18nDirUri);
        for (const [fileName, fileType] of entries) {
            if (fileType === vscode.FileType.File && fileName.endsWith('.json') && fileName !== `${sourceLang}.json`) {
                progress.report({ message: `Processing ${fileName}` });
                const targetFileUri = vscode.Uri.joinPath(i18nDirUri, fileName);
                const targetData = await readJsonFile(targetFileUri) || {};
                
                const newTargetData: Record<string, string> = {};
                
                for (const key of sourceKeys) {
                    if (targetData.hasOwnProperty(key)) {
                        newTargetData[key] = targetData[key];
                    } else {
                        newTargetData[key] = '';
                    }
                }
                
                const newContent = JSON.stringify(newTargetData, null, 2);
                await vscode.workspace.fs.writeFile(targetFileUri, Buffer.from(newContent, 'utf8'));
            }
        }
    });

    vscode.window.showInformationMessage('i18n keys synchronized successfully!');
}