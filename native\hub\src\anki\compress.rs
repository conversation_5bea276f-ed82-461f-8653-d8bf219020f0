#![allow(unused)]

use anyhow::{anyhow, Result};
use rinf::debug_print;
use serde::{Deserialize, Serialize};
use std::io::BufWriter;
use std::path::{Path, PathBuf};
use thiserror::Error;

/// Output mode for compression
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum OutputMode {
    /// Save compressed files to the same directory as input, appending "_compressed" to filename
    Same,
    /// Replace the original file with the compressed version
    Overwrite,
    /// Save compressed files to custom directory, appending "_compressed" to filename
    Custom,
}

impl Default for OutputMode {
    fn default() -> Self {
        OutputMode::Same
    }
}

/// Image compression configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompressionConfig {
    /// Quality setting (1-100, higher is better quality)
    pub quality: u8,
    /// Output format (jpeg, png, webp)
    pub format: String,
    /// Whether to keep metadata
    pub keep_metadata: bool,
    /// Output directory (used when output_mode is Custom)
    pub output_dir: Option<String>,
    /// Output mode determining where and how to save compressed files
    pub output_mode: OutputMode,
}

impl Default for CompressionConfig {
    fn default() -> Self {
        Self {
            quality: 80,
            format: "jpeg".to_string(),
            keep_metadata: false,
            output_dir: None,
            output_mode: OutputMode::Same,
        }
    }
}

/// Compression result information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompressionResult {
    /// Input file path
    pub input_path: String,
    /// Output file path
    pub output_path: String,
    /// Original file size in bytes
    pub original_size: u64,
    /// Compressed file size in bytes
    pub compressed_size: u64,
    /// Compression ratio (0.0 to 1.0)
    pub compression_ratio: f64,
    /// Whether compression was successful
    pub success: bool,
    /// Error message if compression failed
    pub error_message: Option<String>,
}

/// Compression errors
#[derive(Error, Debug)]
pub enum CompressionError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Image processing error: {0}")]
    ImageError(String),
    #[error("Unsupported format: {0}")]
    UnsupportedFormat(String),
    #[error("Invalid quality value: {0} (must be 1-100)")]
    InvalidQuality(u8),
    #[error("File not found: {0}")]
    FileNotFound(String),
    #[error("Permission denied: {0}")]
    PermissionDenied(String),
    #[error("Other error: {0}")]
    Other(String),
}

/// Compress a single image file
pub fn compress_image(input_path: &str, config: &CompressionConfig) -> Result<CompressionResult> {
    debug_print!("Starting image compression for: {}", input_path);
    
    // Validate input
    let input_path_buf = PathBuf::from(input_path);
    if !input_path_buf.exists() {
        return Err(CompressionError::FileNotFound(input_path.to_string()).into());
    }

    // Validate quality
    if config.quality == 0 || config.quality > 100 {
        return Err(CompressionError::InvalidQuality(config.quality).into());
    }

    // Get original file size
    let original_size = std::fs::metadata(&input_path_buf)?.len();

    // Determine output path
    let output_path = determine_output_path(&input_path_buf, config)?;

    // Handle existing output file for non-overwrite modes
    if output_path.exists() && config.output_mode != OutputMode::Overwrite {
        debug_print!("Output file exists, attempting to delete: {}", output_path.display());

        // Attempt to delete the existing file
        match std::fs::remove_file(&output_path) {
            Ok(_) => {
                debug_print!("Successfully deleted existing output file: {}", output_path.display());
            }
            Err(e) => {
                return Err(CompressionError::Other(format!(
                    "Output file exists and could not be removed: {}. Error: {}. Please check file permissions or ensure the file is not in use.",
                    output_path.display(),
                    e
                )).into());
            }
        }
    }

    // Perform compression based on format
    let compression_result = match config.format.to_lowercase().as_str() {
        "jpeg" | "jpg" => compress_to_jpeg(&input_path_buf, &output_path, config),
        "png" => compress_to_png(&input_path_buf, &output_path, config),
        "webp" => compress_to_webp(&input_path_buf, &output_path, config),
        _ => Err(CompressionError::UnsupportedFormat(config.format.clone()).into()),
    };

    match compression_result {
        Ok(_) => {
            // Get compressed file size
            let compressed_size = std::fs::metadata(&output_path)?.len();
            let compression_ratio = if original_size > 0 {
                compressed_size as f64 / original_size as f64
            } else {
                1.0
            };

            debug_print!(
                "Compression successful: {} -> {} ({}% of original)",
                input_path,
                output_path.display(),
                (compression_ratio * 100.0) as u32
            );

            Ok(CompressionResult {
                input_path: input_path.to_string(),
                output_path: output_path.to_string_lossy().to_string(),
                original_size,
                compressed_size,
                compression_ratio,
                success: true,
                error_message: None,
            })
        }
        Err(e) => {
            debug_print!("Compression failed for {}: {}", input_path, e);
            Ok(CompressionResult {
                input_path: input_path.to_string(),
                output_path: output_path.to_string_lossy().to_string(),
                original_size,
                compressed_size: 0,
                compression_ratio: 0.0,
                success: false,
                error_message: Some(e.to_string()),
            })
        }
    }
}

/// Compress multiple image files with coordinated progress reporting
/// Returns all results, including failed compressions, instead of stopping on first error
pub fn compress_images(
    input_paths: &[String],
    config: &CompressionConfig,
    progress_callback: Option<impl Fn(f64, f64, String) + Send + Sync>,
) -> Result<Vec<CompressionResult>> {
    compress_images_with_range(input_paths, config, progress_callback, 0.0, 100.0)
}

/// Compress multiple image files with progress range coordination
pub fn compress_images_with_range(
    input_paths: &[String],
    config: &CompressionConfig,
    progress_callback: Option<impl Fn(f64, f64, String) + Send + Sync>,
    progress_start: f64,
    progress_end: f64,
) -> Result<Vec<CompressionResult>> {
    let mut results = Vec::new();
    let total_files = input_paths.len();

    if total_files == 0 {
        if let Some(ref callback) = progress_callback {
            callback(progress_end, 100.0, "No files to compress".to_string());
        }
        return Ok(results);
    }

    let progress_range = progress_end - progress_start;

    for (index, input_path) in input_paths.iter().enumerate() {
        // Calculate current progress within the specified range
        let file_progress = progress_start + (progress_range * index as f64 / total_files as f64);

        if let Some(ref callback) = progress_callback {
            callback(
                file_progress,
                100.0, // Always use 100 as total for consistency with frontend
                format!("Compressing: {} ({}/{})",
                    std::path::Path::new(input_path)
                        .file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or(input_path),
                    index + 1,
                    total_files
                ),
            );
        }

        // Handle individual file compression errors gracefully
        match compress_image(input_path, config) {
            Ok(result) => {
                results.push(result);
            }
            Err(e) => {
                // Create a failed result instead of stopping the entire process
                results.push(CompressionResult {
                    input_path: input_path.clone(),
                    output_path: input_path.clone(),
                    original_size: 0,
                    compressed_size: 0,
                    compression_ratio: 0.0,
                    success: false,
                    error_message: Some(e.to_string()),
                });

                // Continue processing other files
                if let Some(ref callback) = progress_callback {
                    callback(
                        file_progress,
                        100.0,
                        format!("Failed to compress: {} - {}",
                            std::path::Path::new(input_path)
                                .file_name()
                                .and_then(|n| n.to_str())
                                .unwrap_or(input_path),
                            e
                        ),
                    );
                }
            }
        }
    }

    // Send final progress update
    if let Some(ref callback) = progress_callback {
        callback(
            progress_end,
            100.0,
            format!("Compression completed: {}/{} files processed",
                results.iter().filter(|r| r.success).count(),
                total_files
            )
        );
    }

    Ok(results)
}

/// Determine the output path based on configuration
fn determine_output_path(input_path: &Path, config: &CompressionConfig) -> Result<PathBuf> {
    let file_stem = input_path
        .file_stem()
        .ok_or_else(|| CompressionError::Other("Invalid file name".to_string()))?;

    let extension = match config.format.to_lowercase().as_str() {
        "jpeg" | "jpg" => "jpg",
        "png" => "png",
        "webp" => "webp",
        _ => return Err(CompressionError::UnsupportedFormat(config.format.clone()).into()),
    };

    let (output_dir, output_filename) = match config.output_mode {
        OutputMode::Same => {
            // Save to same directory as input, append "_compressed" to filename
            let output_dir = input_path.parent().unwrap_or(Path::new(".")).to_path_buf();
            let output_filename = format!("{}_compressed.{}", file_stem.to_string_lossy(), extension);
            (output_dir, output_filename)
        },
        OutputMode::Overwrite => {
            // Replace original file (same directory, same filename)
            let output_dir = input_path.parent().unwrap_or(Path::new(".")).to_path_buf();
            let output_filename = format!("{}.{}", file_stem.to_string_lossy(), extension);
            (output_dir, output_filename)
        },
        OutputMode::Custom => {
            // Save to custom directory, append "_compressed" to filename
            let output_dir = if let Some(ref dir) = config.output_dir {
                PathBuf::from(dir)
            } else {
                return Err(CompressionError::Other("Custom output mode requires output_dir to be specified".to_string()).into());
            };
            let output_filename = format!("{}_compressed.{}", file_stem.to_string_lossy(), extension);
            (output_dir, output_filename)
        },
    };

    // Create output directory if it doesn't exist
    if !output_dir.exists() {
        std::fs::create_dir_all(&output_dir)?;
    }

    Ok(output_dir.join(output_filename))
}

/// Compress image to JPEG format using mozjpeg
fn compress_to_jpeg(input_path: &Path, output_path: &Path, config: &CompressionConfig) -> Result<()> {
    debug_print!("Compressing to JPEG with quality {}", config.quality);

    // Use panic catching as required by mozjpeg
    let result = std::panic::catch_unwind(|| -> std::io::Result<Vec<u8>> {
        // Load image using the image crate
        let img = image::open(input_path)
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidData, e))?;

        // Convert to RGB8 format for mozjpeg
        let rgb_img = img.to_rgb8();
        let (width, height) = rgb_img.dimensions();

        // Create mozjpeg encoder
        let mut comp = mozjpeg::Compress::new(mozjpeg::ColorSpace::JCS_RGB);
        comp.set_size(width as usize, height as usize);
        comp.set_quality(config.quality as f32);

        // Start compression with Vec as output
        let mut comp = comp.start_compress(Vec::new())?;

        // Write image data
        comp.write_scanlines(rgb_img.as_raw())?;

        // Finish compression and get the output
        let compressed_data = comp.finish()?;

        Ok(compressed_data)
    });

    match result {
        Ok(Ok(compressed_data)) => {
            std::fs::write(output_path, compressed_data)?;
            Ok(())
        }
        Ok(Err(e)) => Err(CompressionError::ImageError(e.to_string()).into()),
        Err(_) => Err(CompressionError::ImageError("JPEG compression panicked".to_string()).into()),
    }
}

/// Compress image to PNG format using image crate
fn compress_to_png(input_path: &Path, output_path: &Path, _config: &CompressionConfig) -> Result<()> {
    debug_print!("Compressing to PNG");

    // Load image using the image crate
    let img = image::open(input_path)
        .map_err(|e| CompressionError::ImageError(e.to_string()))?;

    // Create output file
    let output_file = std::fs::File::create(output_path)?;
    let mut writer = std::io::BufWriter::new(output_file);

    // Encode as PNG
    img.write_to(&mut writer, image::ImageFormat::Png)
        .map_err(|e| CompressionError::ImageError(e.to_string()))?;

    Ok(())
}

/// Compress image to WebP format using image crate
fn compress_to_webp(input_path: &Path, output_path: &Path, config: &CompressionConfig) -> Result<()> {
    debug_print!("Compressing to WebP with quality {}", config.quality);

    // Load image using the image crate
    let img = image::open(input_path)
        .map_err(|e| CompressionError::ImageError(e.to_string()))?;

    // Create output file
    let output_file = std::fs::File::create(output_path)?;
    let mut writer = std::io::BufWriter::new(output_file);

    // Encode as WebP
    img.write_to(&mut writer, image::ImageFormat::WebP)
        .map_err(|e| CompressionError::ImageError(e.to_string()))?;

    Ok(())
}

/// Get supported image formats
pub fn get_supported_formats() -> Vec<String> {
    vec![
        "jpeg".to_string(),
        "jpg".to_string(),
        "png".to_string(),
        "webp".to_string(),
    ]
}

/// Check if a file format is supported
pub fn is_format_supported(format: &str) -> bool {
    get_supported_formats().contains(&format.to_lowercase())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use std::path::PathBuf;
    use tempfile::TempDir;

    fn create_test_image(temp_dir: &TempDir, name: &str) -> PathBuf {
        let image_path = temp_dir.path().join(name);

        // Create a simple 10x10 RGB image
        let img = image::RgbImage::new(10, 10);
        let dynamic_img = image::DynamicImage::ImageRgb8(img);
        dynamic_img.save(&image_path).unwrap();

        image_path
    }

    #[test]
    fn test_file_deletion_on_same_mode() {
        let temp_dir = TempDir::new().unwrap();
        let input_path = create_test_image(&temp_dir, "test.jpg");

        let config = CompressionConfig {
            quality: 80,
            format: "jpeg".to_string(),
            keep_metadata: false,
            output_dir: None,
            output_mode: OutputMode::Same,
        };

        // Create the expected output file to simulate existing file
        let output_path = determine_output_path(&input_path, &config).unwrap();
        fs::write(&output_path, "existing content").unwrap();
        assert!(output_path.exists());

        // Compress the image - should delete existing file and create new one
        let result = compress_image(input_path.to_str().unwrap(), &config);

        // Should succeed (existing file was deleted)
        assert!(result.is_ok());
        assert!(output_path.exists()); // New compressed file should exist
    }

    #[test]
    fn test_overwrite_mode_ignores_existing_file() {
        let temp_dir = TempDir::new().unwrap();
        let input_path = create_test_image(&temp_dir, "test.jpg");

        let config = CompressionConfig {
            quality: 80,
            format: "jpeg".to_string(),
            keep_metadata: false,
            output_dir: None,
            output_mode: OutputMode::Overwrite,
        };

        // Create existing content at the same path as input (overwrite mode)
        fs::write(&input_path, "existing content").unwrap();

        // Compress the image - should work without trying to delete
        let result = compress_image(input_path.to_str().unwrap(), &config);

        // Should succeed (overwrite mode doesn't check for existing files)
        assert!(result.is_ok());
    }
}
