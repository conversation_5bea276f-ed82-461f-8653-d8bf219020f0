import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/cut.dart';

class PDFCutPage extends StatefulWidget {
  const PDFCutPage({super.key});

  @override
  State<PDFCutPage> createState() => _PDFCutPageState();
}

class _PDFCutPageState extends State<PDFCutPage> {
  final controller = Get.put(PDFCutPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.cut.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.cut.description'.tr, style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        const SizedBox(height: 4),
                        ShadRadioGroupCustom(
                          label: 'toolbox.cut.cutType'.tr,
                          initialValue: controller.cutMode.value,
                          items: controller.cutModeList,
                          onChanged: (value) {
                            controller.cutMode.value = value;
                          },
                        ),
                        if (controller.cutMode.value == 'grid') ...[
                          ShadInputWithValidate(
                              key: const ValueKey("num-rows"),
                              label: 'toolbox.cut.grid.numRows'.tr,
                              placeholder:
                                  'toolbox.cut.grid.numRowsPlaceholder'.tr,
                              initialValue: controller.numRows.value.toString(),
                              onChanged: (value) {
                                controller.numRows.value =
                                    int.tryParse(value) ??
                                        controller.numRows.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.cut.grid.numRowsRequired'.tr;
                                }
                                final reg = RegExp(r'^\d+$');
                                if (!reg.hasMatch(value)) {
                                  return "请输入整数";
                                }
                                final size = int.parse(value);
                                if (size <= 0) {
                                  return "必须大于0";
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              key: const ValueKey("num-cols"),
                              label: 'toolbox.cut.grid.numCols'.tr,
                              placeholder:
                                  'toolbox.cut.grid.numColsPlaceholder'.tr,
                              initialValue: controller.numCols.value.toString(),
                              onChanged: (value) {
                                controller.numCols.value =
                                    int.tryParse(value) ??
                                        controller.numCols.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.cut.grid.numColsRequired'.tr;
                                }
                                final reg = RegExp(r'^\d+$');
                                if (!reg.hasMatch(value)) {
                                 return 'anki.placeholder.mustBeInteger'.tr;
                                }
                                final size = int.parse(value);
                                if (size <= 0) {
                                  return 'anki.placeholder.mustBeGreaterThan0'.tr;
                                }
                                return "";
                              }),
                        ],
                        if (controller.cutMode.value == 'page') ...[
                          ShadSelectCustom(
                            label: 'toolbox.cut.page.pageSize'.tr,
                            placeholder: 'toolbox.cut.page.selectPageSize'.tr,
                            initialValue: [controller.pageSize.value],
                            isMultiple: false,
                            options: paperSizeList,
                            onChanged: (value) {
                              logger.i(value);
                              controller.pageSize.value = value.single;
                            },
                          ),
                          ShadSelectCustom(
                            label: 'toolbox.cut.page.orientation'.tr,
                            placeholder:
                                'toolbox.cut.page.selectOrientation'.tr,
                            initialValue: [controller.orientation.value],
                            options: controller.orientationList,
                            onChanged: (value) {
                              controller.orientation.value = value.single;
                            },
                          ),
                          ShadInputWithValidate(
                              label: 'toolbox.cut.page.margin.top'.tr,
                              placeholder:
                                  'toolbox.cut.page.margin.topPlaceholder'.tr,
                              initialValue: controller.top.value.toString(),
                              onChanged: (value) {
                                controller.top.value = double.tryParse(value) ??
                                    controller.top.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.cut.page.margin.topPlaceholder'
                                      .tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return 'toolbox.cut.page.margin.invalid'.tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              label: 'toolbox.cut.page.margin.bottom'.tr,
                              placeholder:
                                  'toolbox.cut.page.margin.bottomPlaceholder'
                                      .tr,
                              initialValue: controller.bottom.value.toString(),
                              onChanged: (value) {
                                controller.bottom.value =
                                    double.tryParse(value) ??
                                        controller.bottom.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.cut.page.margin.bottomPlaceholder'
                                      .tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return 'toolbox.cut.page.margin.invalid'.tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              label: 'toolbox.cut.page.margin.left'.tr,
                              placeholder:
                                  'toolbox.cut.page.margin.leftPlaceholder'.tr,
                              initialValue: controller.left.value.toString(),
                              onChanged: (value) {
                                controller.left.value =
                                    double.tryParse(value) ??
                                        controller.left.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.cut.page.margin.leftPlaceholder'
                                      .tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return 'toolbox.cut.page.margin.invalid'.tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              label: 'toolbox.cut.page.margin.right'.tr,
                              placeholder:
                                  'toolbox.cut.page.margin.rightPlaceholder'.tr,
                              initialValue: controller.right.value.toString(),
                              onChanged: (value) {
                                controller.right.value =
                                    double.tryParse(value) ??
                                        controller.right.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.cut.page.margin.rightPlaceholder'
                                      .tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return 'toolbox.cut.page.margin.invalid'.tr;
                                }
                                return "";
                              }),
                        ],
                        if (controller.cutMode.value == 'custom') ...[
                          ShadInputWithValidate(
                              key: const ValueKey("h_breakpoints"),
                              label: 'toolbox.cut.custom.hBreakpoints'.tr,
                              placeholder:
                                  'toolbox.cut.custom.hBreakpointsPlaceholder'
                                      .tr,
                              initialValue:
                                  controller.hBreakpoints.value.toString(),
                              onChanged: (value) {
                                controller.hBreakpoints.value = value;
                              },
                              onValidate: (value) async {
                                final reg = RegExp(
                                    r'^(0(\.\d+)?|1(\.0+)?)(,(0(\.\d+)?|1(\.0+)?))*$');
                                if (!reg.hasMatch(value)) {
                                  return 'toolbox.cut.custom.hBreakpointsInvalid'
                                      .tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              key: const ValueKey("v_breakpoints"),
                              label: 'toolbox.cut.custom.vBreakpoints'.tr,
                              placeholder:
                                  'toolbox.cut.custom.vBreakpointsPlaceholder'
                                      .tr,
                              initialValue:
                                  controller.vBreakpoints.value.toString(),
                              onChanged: (value) {
                                controller.vBreakpoints.value = value;
                              },
                              onValidate: (value) async {
                                final reg = RegExp(
                                    r'^(0(\.\d+)?|1(\.0+)?)(,(0(\.\d+)?|1(\.0+)?))*$');
                                if (!reg.hasMatch(value)) {
                                  return 'toolbox.cut.custom.vBreakpointsInvalid'
                                      .tr;
                                }
                                return "";
                              }),
                        ],
                        ShadInputWithValidate(
                            key: const ValueKey("split-range"),
                            label: 'toolbox.common.pageRange'.tr,
                            placeholder:
                                'toolbox.common.pageRangePlaceholder'.tr,
                            initialValue: controller.pageRange.value,
                            onChanged: (value) {
                              controller.pageRange.value = value;
                            },
                            onValidate: (value) async {
                              if (validatePageRange(value)) {
                                return "";
                              }
                              return 'toolbox.common.enterPageRange'.tr;
                            }),
                        if (PathUtils.isDesktop)
                          ShadSelectCustom(
                            key: const ValueKey("output-position"),
                            label: 'toolbox.common.outputLocation'.tr,
                            placeholder:
                                'toolbox.common.selectOutputLocation'.tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.common.outputDirectory'.tr,
                            placeholder:
                                Text('toolbox.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.common.inputFile'.tr,
                          placeholder:
                              Text('toolbox.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['pdf'],
                          isRequired: true,
                          allowMultiple: true,
                          initialValue: controller.selectedFilePaths,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
