use super::connect::{gen_id, get_model_info_from_anki_connect};
use genanki_rs::{Deck, Error, Field, Model, Note, Package, Template};
use rinf::debug_print;
use serde::Deserialize;
use std::collections::HashMap;
use std::default::Default;
use std::fs;
use std::io;
use std::path::Path;
use std::path::PathBuf;

#[derive(Deserialize, Default, Debug)]
pub struct AnkiNote {
    #[serde(rename = "deckName", default)]
    pub deck_name: String,
    #[serde(rename = "modelName", default)]
    pub model_name: String,
    #[serde(rename = "fields", default)]
    pub fields: Vec<String>,
    #[serde(rename = "tags", default)]
    pub tags: Option<Vec<String>>,
    #[serde(rename = "guid", default)]
    pub guid: Option<String>,
}

pub fn get_template_content(model_name: &str) -> io::Result<(String, String, String)> {
    match model_name {
        "Kevin Text QA Card v2" => Ok((
            include_str!("../../../../third_party/templates/Kevin Text QA Card v2/front.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Text QA Card v2/back.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Text QA Card v2/style.css").to_string(),
        )),
        "Kevin Text Cloze v3" => Ok((
            include_str!("../../../../third_party/templates/Kevin Text Cloze v3/front.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Text Cloze v3/back.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Text Cloze v3/style.css").to_string(),
        )),
        "Kevin Choice Card v2" => Ok((
            include_str!("../../../../third_party/templates/Kevin Choice Card v2/front.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Choice Card v2/back.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Choice Card v2/style.css").to_string(),
        )),
        "Kevin Image Cloze v5" => Ok((
            include_str!("../../../../third_party/templates/Kevin Image Cloze v5/front.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Image Cloze v5/back.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Image Cloze v5/style.css").to_string(),
        )),
        "Kevin Image QA Card v2" => Ok((
            include_str!("../../../../third_party/templates/Kevin Image QA Card v2/front.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Image QA Card v2/back.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Image QA Card v2/style.css").to_string(),
        )),
        "Kevin Mindmap Card v3" => Ok((
            include_str!("../../../../third_party/templates/Kevin Mindmap Card v3/front.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Mindmap Card v3/back.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Mindmap Card v3/style.css").to_string(),
        )),
        "Kevin Reader Card v2" => Ok((
            include_str!("../../../../third_party/templates/Kevin Reader Card v2/front.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Reader Card v2/back.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Reader Card v2/style.css").to_string(),
        )),
        "Kevin Vocab Card v2" => Ok((
            include_str!("../../../../third_party/templates/Kevin Vocab Card v2/front.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Vocab Card v2/back.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Vocab Card v2/style.css").to_string(),
        )),
        "Kevin Media Card" => Ok((
            include_str!("../../../../third_party/templates/Kevin Media Card/front.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Media Card/back.html").to_string(),
            include_str!("../../../../third_party/templates/Kevin Media Card/style.css").to_string(),
        )),
        _ => Err(io::Error::new(
            io::ErrorKind::NotFound,
            format!("Template not found for model: {}", model_name),
        )),
    }
}

pub fn get_kevin_text_qa_card_model() -> Result<Model, Error> {
    let (front, back, css) = get_template_content("Kevin Text QA Card v2")?;
    let my_model = Model::new(
        1737251809,
        "Kevin Text QA Card v2",
        vec![
            Field::new("Front"),
            Field::new("Back"),
            Field::new("Hint"),
            Field::new("Notes"),
            Field::new("Source"),
        ],
        vec![Template::new("Card 1").qfmt(&front).afmt(&back)],
    )
    .css(&css);
    Ok(my_model)
}

pub fn get_kevin_image_cloze_model() -> Result<Model, Error> {
    let (front, back, css) = get_template_content("Kevin Image Cloze v5")?;
    let my_model = Model::new(
        1743588010,
        "Kevin Image Cloze v5",
        vec![
            Field::new("ID"),
            Field::new("Header"),
            Field::new("Image"),
            Field::new("Text"),
            Field::new("Masks"),
            Field::new("Source"),
            Field::new("Notes"),
            Field::new("Mode"),
            Field::new("Index"),
            Field::new("Colors"),
            Field::new("Reversed"),
        ],
        vec![Template::new("Card 1").qfmt(&front).afmt(&back)],
    )
    .css(&css);
    Ok(my_model)
}

pub fn get_kevin_mindmap_card_model() -> Result<Model, Error> {
    let (front, back, css) = get_template_content("Kevin Mindmap Card v3")?;
    let my_model = Model::new(
        1737251812,
        "Kevin Mindmap Card v3",
        vec![
            Field::new("ID"),
            Field::new("MapID"),
            Field::new("Path"),
            Field::new("Front"),
            Field::new("Back"),
            Field::new("Source"),
            Field::new("Notes"),
        ],
        vec![Template::new("Card 1").qfmt(&front).afmt(&back)],
    )
    .css(&css);
    Ok(my_model)
}

pub fn get_kevin_text_cloze_model() -> Result<Model, Error> {
    let (front, back, css) = get_template_content("Kevin Text Cloze v3")?;
    let my_model = Model::new(
        1737251813,
        "Kevin Text Cloze v3",
        vec![
            Field::new("ID"),
            Field::new("Front"),
            Field::new("Back"),
            Field::new("Mode"),
            Field::new("Index"),
        ],
        vec![Template::new("Card 1").qfmt(&front).afmt(&back)],
    )
    .css(&css);
    Ok(my_model)
}

pub fn get_kevin_choice_card_model() -> Result<Model, Error> {
    let (front, back, css) = get_template_content("Kevin Choice Card v2")?;
    let my_model = Model::new(
        1737251814,
        "Kevin Choice Card v2",
        vec![
            Field::new("Question"),
            Field::new("Options"),
            Field::new("Answers"),
            Field::new("Remarks"),
            Field::new("Notes"),
            Field::new("Source"),
            Field::new("Extra"),
        ],
        vec![Template::new("Card 1").qfmt(&front).afmt(&back)],
    )
    .css(&css);
    Ok(my_model)
}

pub fn get_kevin_reader_card_model() -> Result<Model, Error> {
    let (front, back, css) = get_template_content("Kevin Reader Card v2")?;
    let my_model = Model::new(
        1737251815,
        "Kevin Reader Card v2",
        vec![
            Field::new("原文"),
            Field::new("出处"),
            Field::new("备注"),
            Field::new("标题"),
            Field::new("笔记"),
            Field::new("反转"),
        ],
        vec![Template::new("Card 1").qfmt(&front).afmt(&back)],
    )
    .css(&css);
    Ok(my_model)
}

pub fn get_kevin_word_card_model() -> Result<Model, Error> {
    let (front, back, css) = get_template_content("Kevin Vocab Card v2")?;
    let my_model = Model::new(
        1737251817,
        "Kevin Vocab Card v2",
        vec![
            Field::new("Word"),
            Field::new("Data"),
            Field::new("Note"),
        ],
        vec![Template::new("Card 1").qfmt(&front).afmt(&back)],
    )
    .css(&css);
    Ok(my_model)
}

pub fn get_kevin_image_qa_card_model() -> Result<Model, Error> {
    let (front, back, css) = get_template_content("Kevin Image QA Card v2")?;
    let my_model = Model::new(
        1737251818,
        "Kevin Image QA Card v2",
        vec![
            Field::new("ID"),
            Field::new("Header"),
            Field::new("Front"),
            Field::new("Back"),
            Field::new("Source"),
            Field::new("Masks"),
            Field::new("Notes"),
            Field::new("FrontText"),
            Field::new("BackText"),
            Field::new("Mode"),
        ],
        vec![Template::new("Card 1").qfmt(&front).afmt(&back)],
    )
    .css(&css);
    Ok(my_model)
}
pub fn get_kevin_media_card_model() -> Result<Model, Error> {
    let (front, back, css) = get_template_content("Kevin Media Card")?;
    let my_model = Model::new(
        1737251819,
        "Kevin Media Card",
        vec![
            Field::new("ID"),
            Field::new("Video"),
            Field::new("Audio"),
            Field::new("Subtitle"),
            Field::new("Meaning"),
            Field::new("Notes"),
        ],
        vec![Template::new("Card 1").qfmt(&front).afmt(&back)],
    )
    .css(&css);
    Ok(my_model)
}

pub fn get_builtin_model(model_name: &str) -> Result<Model, Error> {
    // Use built-in models when address is not provided
    let result = match model_name {
        "Kevin Image Cloze v5" => get_kevin_image_cloze_model(),
        "Kevin Text QA Card v2" => get_kevin_text_qa_card_model(),
        "Kevin Mindmap Card v3" => get_kevin_mindmap_card_model(),
        "Kevin Text Cloze v3" => get_kevin_text_cloze_model(),
        "Kevin Choice Card v2" => get_kevin_choice_card_model(),
        "Kevin Reader Card v2" => get_kevin_reader_card_model(),
        "Kevin Vocab Card v2" => get_kevin_word_card_model(),
        "Kevin Image QA Card v2" => get_kevin_image_qa_card_model(),
        "Kevin Media Card" => get_kevin_media_card_model(),
        _ => {
            return Err(Error::Io(std::io::Error::new(
                std::io::ErrorKind::InvalidInput,
                format!("Invalid model name: {}", model_name),
            )))
        }
    };
    result
}

pub async fn get_model(model_name: &str, address: Option<&str>) -> Result<Model, std::io::Error> {
    if let Some(addr) = address.filter(|a| !a.is_empty()) {
        // Try to get model info from AnkiConnect
        match get_model_info_from_anki_connect(addr, model_name).await {
            Ok(model_info) => {
                let template = Template::new("Card 1")
                    .qfmt(&model_info.front)
                    .afmt(&model_info.back);

                let fields: Vec<Field> = model_info
                    .fields
                    .iter()
                    .map(|s| Field::new(s.as_str()))
                    .collect();

                Ok(
                    Model::new(model_info.id, model_name, fields, vec![template])
                        .css(&model_info.css),
                )
            }
            Err(_) => match get_builtin_model(model_name) {
                Ok(model) => Ok(model),
                Err(e) => {
                    debug_print!("get_builtin_model error: {}", e);
                    Err(std::io::Error::new(
                        std::io::ErrorKind::InvalidInput,
                        "请检查： 1.Anki是否打开，2.AnkiConnect插件是否安装，3.AnkiConnect插件是否启用，4.AnkiConnect插件是否配置正确",
                    ))
                }
            },
        }
    } else {
        get_builtin_model(model_name)
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
    }
}

pub async fn get_image_cloze_card_assets(temp_dir: &str) -> Result<Vec<String>, std::io::Error> {
    let mut media_files = Vec::new();
    // 补充内置媒体文件
    let next_svg_path = PathBuf::from(temp_dir).join("__next.svg");
    fs::write(
        &next_svg_path,
        include_bytes!("../../../../third_party/templates/assets/__next.svg"),
    )?;
    media_files.push(next_svg_path.to_string_lossy().to_string());
    let prev_svg_path = PathBuf::from(temp_dir).join("__prev.svg");
    fs::write(
        &prev_svg_path,
        include_bytes!("../../../../third_party/templates/assets/__prev.svg"),
    )?;
    media_files.push(prev_svg_path.to_string_lossy().to_string());

    Ok(media_files)
}

pub async fn get_mindmap_card_assets(temp_dir: &str) -> Result<Vec<String>, std::io::Error> {
    let mut media_files = Vec::new();
    // 补充内置媒体文件
    let d3_js_path = PathBuf::from(temp_dir).join("__d3.js");
    fs::write(&d3_js_path, include_str!("../../../../third_party/templates/assets/__d3.js"))?;
    media_files.push(d3_js_path.to_string_lossy().to_string());

    let markmap_lib_js_path = PathBuf::from(temp_dir).join("__markmap_lib.js");
    fs::write(
        &markmap_lib_js_path,
        include_str!("../../../../third_party/templates/assets/__markmap_lib.js"),
    )?;
    media_files.push(markmap_lib_js_path.to_string_lossy().to_string());

    let markmap_view_js_path = PathBuf::from(temp_dir).join("__markmap_view.js");
    fs::write(
        &markmap_view_js_path,
        include_str!("../../../../third_party/templates/assets/__markmap_view.js"),
    )?;
    media_files.push(markmap_view_js_path.to_string_lossy().to_string());

    Ok(media_files)
}

pub async fn get_choice_card_assets(temp_dir: &str) -> Result<Vec<String>, std::io::Error> {
    let mut media_files = Vec::new();
    // 补充内置媒体文件
    let warning_png_path = PathBuf::from(temp_dir).join("__warning.png");
    fs::write(
        &warning_png_path,
        include_bytes!("../../../../third_party/templates/assets/__warning.png"),
    )?;
    media_files.push(warning_png_path.to_string_lossy().to_string());

    let correct_svg_path = PathBuf::from(temp_dir).join("__correct.svg");
    fs::write(
        &correct_svg_path,
        include_bytes!("../../../../third_party/templates/assets/__correct.svg"),
    )?;
    media_files.push(correct_svg_path.to_string_lossy().to_string());

    let error_svg_path = PathBuf::from(temp_dir).join("__error.svg");
    fs::write(
        &error_svg_path,
        include_bytes!("../../../../third_party/templates/assets/__error.svg"),
    )?;
    media_files.push(error_svg_path.to_string_lossy().to_string());

    let bookmark_svg_path = PathBuf::from(temp_dir).join("__bookmark.svg");
    fs::write(
        &bookmark_svg_path,
        include_bytes!("../../../../third_party/templates/assets/__bookmark.svg"),
    )?;
    media_files.push(bookmark_svg_path.to_string_lossy().to_string());

    let gear_svg_path = PathBuf::from(temp_dir).join("__gear.svg");
    fs::write(
        &gear_svg_path,
        include_bytes!("../../../../third_party/templates/assets/__gear.svg"),
    )?;
    media_files.push(gear_svg_path.to_string_lossy().to_string());

    let correct_audio_path = PathBuf::from(temp_dir).join("__correct.mp3");
    fs::write(
        &correct_audio_path,
        include_bytes!("../../../../third_party/templates/assets/__correct.mp3"),
    )?;
    media_files.push(correct_audio_path.to_string_lossy().to_string());

    let wrong_audio_path = PathBuf::from(temp_dir).join("__wrong.mp3");
    fs::write(
        &wrong_audio_path,
        include_bytes!("../../../../third_party/templates/assets/__wrong.mp3"),
    )?;
    media_files.push(wrong_audio_path.to_string_lossy().to_string());

    Ok(media_files)
}

pub async fn gen_apkg(
    notes: Vec<AnkiNote>,
    media_list: Option<Vec<String>>,
    output_path: &str,
    address: Option<&str>,
    remove_media: bool,
    internal_media_types: Option<Vec<String>>,
) -> Result<String, std::io::Error> {
    // Validate and fix output path
    let validated_output_path = if output_path.is_empty() {
        return Err(std::io::Error::new(
            std::io::ErrorKind::InvalidInput,
            "Output path cannot be empty",
        ));
    } else {
        let path = Path::new(output_path);
        if path.is_dir() {
            // If output_path is a directory, generate a filename
            let timestamp = chrono::Utc::now().format("%Y%m%d%H%M%S%3f").to_string();
            path.join(format!("output_{}.apkg", timestamp))
                .to_string_lossy()
                .to_string()
        } else if !output_path.ends_with(".apkg") {
            // If it doesn't end with .apkg, add the extension
            format!("{}.apkg", output_path)
        } else {
            output_path.to_string()
        }
    };

    let temp_dir = crate::anki::utils::get_temp_dir()
        .await
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;
    let mut model_map: HashMap<String, Model> = HashMap::new();
    let mut deck_map: HashMap<String, Vec<&AnkiNote>> = HashMap::new();
    // Group notes by deck and prepare models
    for note in &notes {
        // Prepare model if not already cached
        if !model_map.contains_key(&note.model_name) {
            let model = get_model(&note.model_name, address).await?;
            model_map.insert(note.model_name.clone(), model);
        }

        // Group notes by deck
        deck_map
            .entry(note.deck_name.clone())
            .or_default()
            .push(note);
    }
    // Create decks and add notes
    let mut decks = Vec::new();
    for (deck_name, deck_notes) in deck_map {
        let mut deck = Deck::new(gen_id(), &deck_name, "");

        for note_data in deck_notes {
            if note_data.fields.len() == 0 {
                continue;
            }
            // Get the cached model
            let model = model_map.get(&note_data.model_name).ok_or_else(|| {
                std::io::Error::new(
                    std::io::ErrorKind::NotFound,
                    format!("Model not found: {}", note_data.model_name),
                )
            })?;
            // Create note with the model
            let note = Note::new_with_options(
                model.clone(),
                note_data.fields.iter().map(|s| s.as_str()).collect(),
                None,
                note_data
                    .tags
                    .as_ref()
                    .map(|tags| tags.iter().map(|s| s.as_str()).collect()),
                note_data.guid.as_deref().filter(|s| !s.is_empty()),
            )
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;

            deck.add_note(note);
        }

        decks.push(deck);
    }
    dbg!("decks gen!");
    // Handle media files
    let mut media_files: Vec<String> = media_list.unwrap_or_default();
    // 补充内置媒体文件
    if let Some(internal_media_types) = internal_media_types {
        for media_type in internal_media_types {
            match media_type.as_str() {
                "mindmap_card" => {
                    let mindmap_assets = get_mindmap_card_assets(&temp_dir).await?;
                    media_files.extend(mindmap_assets);
                }
                "image_cloze_card" => {
                    let image_cloze_assets = get_image_cloze_card_assets(&temp_dir).await?;
                    media_files.extend(image_cloze_assets);
                }
                "choice_card" => {
                    let choice_card_assets = get_choice_card_assets(&temp_dir).await?;
                    media_files.extend(choice_card_assets);
                }
                _ => {}
            }
        }
    }
    let anki_persistence_js_path = PathBuf::from(temp_dir.clone()).join("__anki-persistence.js");
    let pico_css_path = PathBuf::from(temp_dir.clone()).join("__pico.blue.min.css");
    fs::write(
        &anki_persistence_js_path,
        include_bytes!("../../../../third_party/templates/assets/__anki-persistence.js"),
    )?;
    fs::write(
        &pico_css_path,
        include_bytes!("../../../../third_party/templates/assets/__pico.blue.min.css"),
    )?;
    media_files.push(pico_css_path.to_string_lossy().to_string());
    media_files.push(anki_persistence_js_path.to_string_lossy().to_string());
    // 对media_files进行去重
    media_files.sort();
    media_files.dedup();

    // 验证所有媒体文件路径都是有效文件而不是目录
    let mut valid_media_files = Vec::new();
    for media_file in media_files {
        let path = Path::new(&media_file);
        if path.exists() {
            if path.is_file() {
                valid_media_files.push(media_file);
            } else if path.is_dir() {
                debug_print!("Skipping directory in media_files: {}", media_file);
            }
        } else {
            debug_print!("Media file does not exist: {}", media_file);
        }
    }

    dbg!("valid_media_files: {:?}", &valid_media_files);
    let media_files_refs: Vec<&str> = valid_media_files.iter().map(|s| s.as_str()).collect();
    // Create and write package
    let mut package = Package::new(decks, media_files_refs)
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;
    if let Some(parent) = Path::new(&validated_output_path).parent() {
        std::fs::create_dir_all(parent)?;
    }
    package
        .write_to_file2(Some(temp_dir), &validated_output_path)
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;
    // Clean up media files after package is created
    if remove_media {
        for media_file in valid_media_files {
            if let Err(e) = std::fs::remove_file(&media_file) {
                debug_print!("Failed to remove media file {}: {}", media_file, e);
            }
        }
    }

    Ok(validated_output_path)
}
