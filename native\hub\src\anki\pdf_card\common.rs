#![allow(unused)]

use crate::anki::pdf_utils::{delete_annotations, export_pdf_to_images, extract_annotations};
use crate::anki::types::{Annotation, AnnotationType, PdfError};
use image::GenericImageView;
use lopdf::{Dictionary, Document, Object, ObjectId, Outline};
use pdfium_render::prelude::*;
use regex::Regex;
use rinf::debug_print;
use serde_json::{json, Value};
use std::collections::{HashMap, HashSet};
use std::error::Error;
use std::path::{Path, PathBuf};
use thiserror::Error;
use ulid::Ulid;

// 修改 PdfiumError 的定义
#[derive(Error, Debug)]
pub enum PdfiumError {
    #[error("IO Error")]
    IoError,
    #[error("Image Error")]
    ImageError,
    #[error("Page Error: {0}")]
    PageError(String),
    #[error("Pdfium Error: {0}")]
    Pdfium(#[from] pdfium_render::prelude::PdfiumError),
}

/// 内部结构体,用于存储挖空匹配信息
struct ClozeMatch {
    full_match: String,
    group_num: Option<String>,
    content: String,
    start: usize,
    is_standard: bool,
}

/// 将文本中的挖空标记转换为 Anki 挖空格式 [[c1::xx]]
///
/// [text] 原始文本
/// [cloze_grammar_list] 挖空语法列表,如 ["[[xx]]", "{{xx}}"]
/// [ignore_group] 是否忽略原有分组
///   - true: 对所有挖空重新编号
///   - false: 标准格式保持原编号,自定义格式统一使用c1
///
/// 返回转换后的文本
pub fn convert_cloze(text: String, cloze_grammar_list: &[String], ignore_group: bool) -> String {
    if text.is_empty() || cloze_grammar_list.is_empty() {
        return text;
    }

    // 收集所有匹配项
    let mut all_matches: Vec<ClozeMatch> = Vec::new();

    for grammar in cloze_grammar_list {
        // 处理 [[c1::xx]] 格式
        if grammar == "[[c1::xx]]" {
            let regex = Regex::new(r"\[\[c(\d+)::(.*?)\]\]").unwrap();
            for cap in regex.captures_iter(&text) {
                all_matches.push(ClozeMatch {
                    full_match: cap[0].to_string(),
                    group_num: Some(cap[1].to_string()),
                    content: cap[2].to_string(),
                    start: cap.get(0).unwrap().start(),
                    is_standard: true,
                });
            }
            continue;
        }

        // 处理 {{c1::xx}} 格式
        if grammar == "{{c1::xx}}" {
            let regex = Regex::new(r"\{\{c(\d+)::(.*?)\}\}").unwrap();
            for cap in regex.captures_iter(&text) {
                all_matches.push(ClozeMatch {
                    full_match: cap[0].to_string(),
                    group_num: Some(cap[1].to_string()),
                    content: cap[2].to_string(),
                    start: cap.get(0).unwrap().start(),
                    is_standard: true,
                });
            }
            continue;
        }

        // 处理自定义挖空语法
        let escaped = regex::escape(&grammar);
        let pattern = escaped.replace("xx", "(.*?)");
        let regex = Regex::new(&pattern).unwrap();
        for cap in regex.captures_iter(&text) {
            all_matches.push(ClozeMatch {
                full_match: cap[0].to_string(),
                group_num: None,
                content: cap[1].to_string(),
                start: cap.get(0).unwrap().start(),
                is_standard: false,
            });
        }
    }

    if all_matches.is_empty() {
        return text;
    }

    // 按位置排序后，从后往前替换，避免干扰
    all_matches.sort_by_key(|m| m.start);
    let mut result = text.clone();

    // 从后往前替换，这样不会影响前面未替换部分的位置
    let mut index = all_matches.len(); // 从最大数开始往下计数

    for m in all_matches.iter().rev() {
        let replacement = if m.is_standard {
            if ignore_group {
                format!("[[c{}::{}]]", index, m.content)
            } else {
                format!("[[c{}::{}]]", m.group_num.as_ref().unwrap(), m.content)
            }
        } else {
            if ignore_group {
                format!("[[c{}::{}]]", index, m.content)
            } else {
                format!("[[c1::{}]]", m.content)
            }
        };

        if ignore_group {
            index -= 1; // 递减计数器
        }

        // 使用 start 和 end 位置进行精确替换
        let end = m.start + m.full_match.len();
        result.replace_range(m.start..end, &replacement);
    }

    result
}

/// 计算两个矩形的重叠面积比例
pub fn calculate_overlap_ratio(rect1: &[f64], rect2: &[f64]) -> f64 {
    // 快速检查：如果矩形完全不相交，直接返回 0
    if rect1[0] >= rect2[2] || rect2[0] >= rect1[2] || rect1[1] >= rect2[3] || rect2[1] >= rect1[3]
    {
        return 0.0;
    }

    let x_left = rect1[0].max(rect2[0]);
    let y_bottom = rect1[1].max(rect2[1]);
    let x_right = rect1[2].min(rect2[2]);
    let y_top = rect1[3].min(rect2[3]);

    let intersection_area = (x_right - x_left) * (y_top - y_bottom);
    let rect2_area = (rect2[2] - rect2[0]) * (rect2[3] - rect2[1]);

    intersection_area / rect2_area
}

pub fn group_page_annotations(
    mut annotations: Vec<Annotation>,
) -> (Vec<Vec<Annotation>>, Vec<String>, Vec<String>) {
    let mut groups = Vec::new();
    // 收集需要删除的注释ID
    let mut annotations_to_delete = Vec::new();
    let mut cloze_colors = Vec::new();

    // 移除 popup 注释
    let annotations: Vec<Annotation> = annotations
        .into_iter()
        .filter(|a| a.annot_type != AnnotationType::Popup)
        .collect();
    let mut used = vec![false; annotations.len()];

    // 按从上到下，从左到右排序
    let mut sorted_annotations: Vec<(usize, &Annotation)> = annotations
        .iter()
        .enumerate()
        .filter(|(_, a)| a.annot_type == AnnotationType::Square)
        .collect();

    sorted_annotations.sort_by(|(_, a), (_, b)| {
        b.rect[3]
            .partial_cmp(&a.rect[3])
            .unwrap_or(std::cmp::Ordering::Equal)
            .then(
                a.rect[0]
                    .partial_cmp(&b.rect[0])
                    .unwrap_or(std::cmp::Ordering::Equal),
            )
    });

    // 处理每个 Square
    for (current_idx, current_square) in sorted_annotations {
        if used[current_idx] {
            continue;
        }

        let mut group = Vec::new();
        let mut main_square = annotations[current_idx].clone();
        used[current_idx] = true;

        // 检查边界线穿插
        for (other_idx, other_annot) in annotations.iter().enumerate() {
            if used[other_idx] || other_annot.annot_type != AnnotationType::Square {
                continue;
            }

            let is_left_intersect = current_square.rect[0] > other_annot.rect[0]
                && current_square.rect[0] < other_annot.rect[2]
                && current_square.rect[3] >= other_annot.rect[3]
                && current_square.rect[1] <= other_annot.rect[1];

            let is_right_intersect = current_square.rect[2] > other_annot.rect[0]
                && current_square.rect[2] < other_annot.rect[2]
                && current_square.rect[3] >= other_annot.rect[3]
                && current_square.rect[1] <= other_annot.rect[1];

            if is_left_intersect || is_right_intersect {
                used[other_idx] = true;

                // 更新 comment
                let mut comment = main_square.contents.take().unwrap_or_default();
                if is_left_intersect && !comment.starts_with('^') {
                    comment.insert(0, '^');
                }
                if is_right_intersect {
                    if comment.starts_with('^') && !comment.starts_with("^^") {
                        comment.insert(0, '^');
                    } else if !comment.starts_with('^') {
                        comment.insert_str(0, "^^");
                    }
                }
                main_square.contents = Some(comment);
                // if let Some(id) = &other_annot.id {
                //     annotations_to_delete.push(id.clone());
                // }
                let rect = &other_annot.rect;
                let generated_id = format!(
                    "{}-[{:.3}, {:.3}, {:.3}, {:.3}]",
                    other_annot.page_num, rect[0], rect[1], rect[2], rect[3]
                );
                annotations_to_delete.push(generated_id);

                if let Some(color) = main_square.color {
                    cloze_colors.push(format!("{:?}", color));
                }
            }
        }

        group.push(main_square);

        // 检查其他标注是否被包含
        for (idx, annotation) in annotations.iter().enumerate() {
            if !used[idx] {
                if calculate_overlap_ratio(&current_square.rect, &annotation.rect) >= 0.7 {
                    group.push(annotation.clone());
                    used[idx] = true;
                }
            }
        }

        groups.push(group);
    }

    (groups, annotations_to_delete, cloze_colors)
}

/// 清理PDF中的注释并导出相关页面为图片
///
/// # 参数
/// * `doc_path` - PDF文档路径
/// * `groupped_annotations` - 分组后的注释列表
/// * `img_format` - 导出图片的格式 ("jpg" 或 "png")
///
/// # 返回值
/// 返回一个 HashMap，其中：
/// - key: 页码 (u32)
/// - value: 该页对应的图片绝对路径 (String)
pub async fn clean_page_annotations(
    doc_path: &Path,
    groupped_annotations: Vec<Vec<Annotation>>,
    extra_annotations_to_delete: Vec<String>,
    img_format: &str,
    is_full_page_mode: bool,
) -> Result<HashMap<u32, String>, PdfError> {
    // 收集需要处理的页码
    let mut pages: HashSet<u32> = HashSet::new();

    // 收集需要删除的注释ID
    let mut annotations_to_delete = Vec::new();
    annotations_to_delete.extend(extra_annotations_to_delete);
    // 遍历分组后的注释，收集页码和需要删除的注释
    for group in &groupped_annotations {
        if let Some(first_annotation) = group.first() {
            pages.insert(first_annotation.page_num);
            // if let Some(id) = &first_annotation.id {
            //     annotations_to_delete.push(id.clone());
            // } else {
            // 如果没有id，以页码拼接rect坐标作为id
            let rect = &first_annotation.rect;
            let generated_id = format!(
                "{}-[{:.3}, {:.3}, {:.3}, {:.3}]",
                first_annotation.page_num, rect[0], rect[1], rect[2], rect[3]
            );
            annotations_to_delete.push(generated_id);
            // }
        }
    }
    // 如果没有需要处理的页面，直接返回空结果
    if pages.is_empty() {
        return Ok(HashMap::new());
    }
    // 构建页码范围字符串
    let page_range = pages
        .into_iter()
        .map(|p| p.to_string())
        .collect::<Vec<_>>()
        .join(",");
    // 创建临时目录用于存储导出的图片
    let uid = Ulid::new().to_string();
    // 获取临时目录路径
    let temp_dir = crate::anki::utils::get_temp_dir()
        .await
        .map_err(|e| PdfError::Other(e.to_string()))?;
    let img_temp_dir = Path::new(&temp_dir).join(&uid);
    std::fs::create_dir_all(&img_temp_dir)?;
    dbg!(&img_temp_dir);
    let mut pdf_temp_path = doc_path.to_path_buf();
    // 删除指定的注释
    if !is_full_page_mode {
        match crate::anki::utils::delete_pdf_annotations(
            doc_path.to_str().unwrap(),
            &page_range,
            &annotations_to_delete,
        )
        .await
        {
            Ok(result) => {
                pdf_temp_path = PathBuf::from(result);
            }
            Err(e) => {
                return Err(PdfError::Other(e.to_string()));
            }
        };
    }
    // dbg!("img_temp_dir: {}", &img_temp_dir);
    dbg!("pdf_temp_path: {}", &pdf_temp_path);
    // 导出图片
    let image_map = export_pdf_to_images(
        pdf_temp_path
            .to_str()
            .ok_or_else(|| PdfError::Other("Invalid path".to_string()))?,
        &page_range,
        img_temp_dir.as_path().to_str().unwrap(),
        300,
        false, // 不使用灰度
        None,  // 使用默认的 libpath
        img_format,
        |current, total, message| {
            // 简单的进度回调
            println!("Progress: {}/{} - {}", current, total, message);
        },
    )
    .await?;
    // 删除临时文件
    if !is_full_page_mode {
        std::fs::remove_file(pdf_temp_path)?;
    }
    Ok(image_map)
}

/// 获取PDF所有页码对应的子牌组路径映射
pub async fn get_page_subdeck_map(pdf_path: &Path) -> Result<HashMap<u32, String>, PdfError> {
    #[cfg(test)]
    {
        let mut page_to_subdeck = HashMap::new();

        // 获取PDF文件名作为顶级标题
        let pdf_title = pdf_path
            .file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("PDF文档")
            .to_string();

        println!("PDF标题: {}", pdf_title);

        // 方法1：使用pdfium_render读取书签
        let pdfium_page_paths = try_get_bookmarks_with_pdfium(pdf_path)?;
        println!("Pdfium找到{}个书签", pdfium_page_paths.len());

        // 方法2：尝试使用lopdf方法（即使pdfium已找到书签）
        println!("尝试使用lopdf方法查找书签...");
        let lopdf_page_paths = match try_get_bookmarks_with_lopdf(pdf_path) {
            Ok(paths) => {
                println!("Lopdf找到{}个书签", paths.len());
                paths
            }
            Err(e) => {
                println!("Lopdf查找书签时出错: {}，将使用pdfium结果", e);
                Vec::new()
            }
        };

        // 选择找到更多书签的方法结果
        let mut page_paths =
            if !lopdf_page_paths.is_empty() && lopdf_page_paths.len() > pdfium_page_paths.len() {
                println!("使用lopdf结果 ({} 个书签)", lopdf_page_paths.len());
                lopdf_page_paths
            } else {
                println!("使用pdfium结果 ({} 个书签)", pdfium_page_paths.len());
                pdfium_page_paths
            };

        // 如果没有找到任何书签，返回空映射
        if page_paths.is_empty() {
            println!("未能从PDF中提取出有效书签");
            return Ok(page_to_subdeck);
        }

        println!("总计解析到 {} 个书签路径", page_paths.len());

        // 加载PDF获取总页数
        let pdfium =
            crate::anki::pdf_utils::get_pdfium(None).map_err(|e| PdfError::Other(e.to_string()))?;
        let document = pdfium
            .load_pdf_from_file(pdf_path, None)
            .map_err(|e| PdfError::Other(e.to_string()))?;
        let total_pages = document.pages().len() as u32;

        println!("PDF总页数: {}", total_pages);

        // 按页码排序
        page_paths.sort_by_key(|(page, _)| *page);

        // 创建一个临时的书签数组，添加书签页码和路径
        let mut bookmark_pages: Vec<(u32, String)> = Vec::new();

        // 处理第一个书签之前的页码
        if !page_paths.is_empty() && page_paths[0].0 > 1 {
            // 如果第一个书签不在第1页，为第1页创建默认书签
            bookmark_pages.push((1, page_paths[0].1.clone()));
        }

        // 添加所有书签
        for (page, path) in &page_paths {
            bookmark_pages.push((*page, path.clone()));
        }

        // 填充页码映射
        for page in 1..=total_pages {
            // 找到最近的前一个书签
            let mut closest_bookmark_idx = None;
            for (idx, (bookmark_page, _)) in bookmark_pages.iter().enumerate() {
                if *bookmark_page <= page {
                    closest_bookmark_idx = Some(idx);
                } else {
                    break; // 已经超过当前页码
                }
            }

            if let Some(idx) = closest_bookmark_idx {
                page_to_subdeck.insert(page, bookmark_pages[idx].1.clone());
            }
        }

        // 打印一些示例结果
        let sample_count = std::cmp::min(5, page_to_subdeck.len());
        println!("生成的页码-子牌组路径映射示例:");
        for (i, (page, path)) in page_to_subdeck.iter().take(sample_count).enumerate() {
            println!("  示例 {}: 第{}页 -> \"{}\"", i + 1, page, path);
        }

        Ok(page_to_subdeck)
    }
    #[cfg(not(test))]
    {
        // 发送请求到 Dart 端获取临时目录路径
        let response = crate::anki::utils::send_dart_request(
            json!({
                "filePath": pdf_path.to_string_lossy().to_string(),
            }),
            "get_page_subdeck_map".to_string(),
        )
        .await
        .map_err(|e| PdfError::Other(e.to_string()))?;
        debug_print!("response: {:?}", response);
        let status = response.status;
        let data = response.data;
        if status == "success" {
            // 将 JSON 字符串转换为 HashMap<String, String>
            let map: HashMap<String, String> = serde_json::from_str(&data)
                .map_err(|e| PdfError::Other(format!("Failed to parse JSON response: {}", e)))?;

            // 将 HashMap<String, String> 转换为 HashMap<u32, String>
            let mut result = HashMap::new();
            for (key, value) in map {
                if let Ok(page_num) = key.parse::<u32>() {
                    result.insert(page_num, value);
                }
            }

            Ok(result)
        } else {
            Err(PdfError::Other(
                "Failed to get page subdeck map from dart".to_string(),
            ))
        }
    }
}

/// 使用pdfium_render尝试获取书签
fn try_get_bookmarks_with_pdfium(pdf_path: &Path) -> Result<Vec<(u32, String)>, PdfError> {
    let mut page_paths: Vec<(u32, String)> = Vec::new();

    // 使用pdfium_render加载PDF文档
    let pdfium =
        crate::anki::pdf_utils::get_pdfium(None).map_err(|e| PdfError::Other(e.to_string()))?;

    let document = pdfium
        .load_pdf_from_file(pdf_path, None)
        .map_err(|e| PdfError::Other(e.to_string()))?;

    // 获取书签（大纲）
    let bookmarks = document.bookmarks();

    // 获取根书签
    let root_bookmark = bookmarks.root();

    // 如果没有根书签（即没有书签），直接返回空结果
    if root_bookmark.is_none() {
        println!("PDF没有书签");
        return Ok(page_paths);
    }

    // 从根书签开始直接收集所有叶子节点的书签信息
    if let Some(root) = root_bookmark {
        // 直接访问根书签的所有子书签
        println!("开始处理根书签的子书签");

        // 检查根书签是否有标题
        let root_title = root.title().unwrap_or_default();
        let root_has_title = !root_title.is_empty();

        // 检查根书签是否有目标页面
        let root_has_destination = if let Some(dest) = root.destination() {
            dest.page_index().is_ok()
        } else {
            false
        };

        // 如果根书签有标题和目标页面，将其作为顶级书签
        if root_has_title && root_has_destination {
            if let Some(dest) = root.destination() {
                if let Ok(index) = dest.page_index() {
                    let page_num = index as u32 + 1; // 页码从1开始
                    println!("添加根书签: 第{}页 -> \"{}\"", page_num, root_title);
                    page_paths.push((page_num, root_title.clone()));
                }
            }
        }

        // 获取子书签数量
        let child_count = root.iter_direct_children().count();
        println!("根书签有 {} 个直接子书签", child_count);

        // 递归处理每个直接子书签
        for (idx, child) in root.iter_direct_children().enumerate() {
            let title = child.title().unwrap_or_default();
            println!("处理第 {}/{} 个直接子书签: {}", idx + 1, child_count, title);
            process_bookmark(&child, Vec::new(), &mut page_paths);
        }
    }

    // 递归处理每个书签及其子书签
    fn process_bookmark(
        bookmark: &PdfBookmark,
        mut current_path: Vec<String>,
        page_paths: &mut Vec<(u32, String)>,
    ) {
        // 获取书签标题
        let title = bookmark.title().unwrap_or_default();
        if title.is_empty() {
            // 跳过没有标题的书签
            return;
        }

        // 将当前书签标题添加到路径
        current_path.push(title);

        // 检查是否有目标页码
        if let Some(dest) = bookmark.destination() {
            if let Ok(index) = dest.page_index() {
                let page_num = index as u32 + 1; // 页码从1开始
                let full_path = current_path.join("::");
                println!("添加书签路径: 第{}页 -> \"{}\"", page_num, full_path);
                page_paths.push((page_num, full_path));
            }
        }

        // 处理子书签
        for child in bookmark.iter_direct_children() {
            process_bookmark(&child, current_path.clone(), page_paths);
        }
    }

    Ok(page_paths)
}

/// 使用lopdf库尝试获取书签
fn try_get_bookmarks_with_lopdf(pdf_path: &Path) -> Result<Vec<(u32, String)>, PdfError> {
    let mut page_paths: Vec<(u32, String)> = Vec::new();

    // 使用lopdf加载PDF文档
    let doc = Document::load(pdf_path)?;

    // 获取目录结构
    let toc = doc.get_toc().map_err(|e| PdfError::Other(e.to_string()))?;

    println!("Lopdf发现 {} 个书签项", toc.toc.len());

    // 构建书签路径
    for item in toc.toc {
        let title = item.title;
        let page = item.page as u32;
        let level = item.level as usize;

        // 只处理标题不为空的书签
        if !title.is_empty() {
            println!(
                "Lopdf找到书签: 第{}页 -> \"{}\" (层级: {})",
                page, title, level
            );
            page_paths.push((page, title));
        }
    }

    Ok(page_paths)
}

/// 获取指定页码的子牌组路径
pub fn get_subdeck_by_bookmark(
    page_map: &HashMap<u32, String>,
    parent_deck: String,
    is_create_subdeck: bool,
    max_level: u32,
    page_num: u32,
) -> Result<String, PdfError> {
    let deck_name = if is_create_subdeck {
        let full_name = page_map.get(&page_num).cloned().unwrap_or("".to_string());
        if full_name.is_empty() {
            parent_deck.clone()
        } else {
            // 根据subdeck_max_level截取子牌组层级
            let subdeck_parts: Vec<&str> = full_name.split("::").collect();
            let truncated_subdeck = if max_level > 0 {
                subdeck_parts
                    .into_iter()
                    .take(max_level as usize)
                    .collect::<Vec<&str>>()
                    .join("::")
            } else {
                full_name
            };
            format!("{}::{}", parent_deck, truncated_subdeck)
        }
    } else {
        parent_deck.clone()
    };
    Ok(deck_name)
}

/// 根据百分比坐标裁剪图片
///
/// # 参数
/// * `image_path` - 源图片路径字符串
/// * `rect` - 裁剪区域的百分比坐标 [x1, y1, x2, y2]，其中：
///   - x1: 左边界（0.0 ~ 1.0）
///   - y1: 上边界（0.0 ~ 1.0）
///   - x2: 右边界（0.0 ~ 1.0）
///   - y2: 下边界（0.0 ~ 1.0）
/// * `output_file` - 裁剪后图片的保存路径
//
pub fn crop_image(
    image_path: String,
    rect: [f64; 4],
    output_file: &Path,
) -> Result<(), std::io::Error> {
    // 打开图片
    let mut image = image::open(&image_path)
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;

    // 获取图片尺寸
    let (width, height) = image.dimensions();

    // 将百分比坐标转换为像素坐标，并确保是非负数
    let x = (rect[0] * width as f64).max(0.0) as u32;
    let y = (rect[1] * height as f64).max(0.0) as u32;
    let crop_width = ((rect[2] - rect[0]) * width as f64).max(0.0) as u32;
    let crop_height = ((rect[3] - rect[1]) * height as f64).max(0.0) as u32;

    // 确保裁剪区域有效
    if crop_width == 0 || crop_height == 0 {
        return Err(std::io::Error::new(
            std::io::ErrorKind::InvalidInput,
            "crop region invalid",
        ));
    }

    // 确保裁剪区域不超出图片边界
    let crop_width = crop_width.min(width - x);
    let crop_height = crop_height.min(height - y);

    // 执行裁剪
    let cropped = image::imageops::crop(&mut image, x, y, crop_width, crop_height);

    // 保存裁剪后的图片
    cropped
        .to_image()
        .save(output_file)
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;

    Ok(())
}

/// 根据百分比坐标裁剪图片
///
/// # 参数
/// * `image_path` - 源图片路径字符串
/// * `rect` - 裁剪区域的百分比坐标 [x1, y1, x2, y2]，其中：
///   - x1: 左边界（0.0 ~ 1.0）
///   - y1: 上边界（0.0 ~ 1.0）
///   - x2: 右边界（0.0 ~ 1.0）
///   - y2: 下边界（0.0 ~ 1.0）
/// * `output_file` - 裁剪后图片的保存路径
//
pub fn crop_pdf_page(
    document: &PdfDocument,
    page_num: u16,
    image_path: &str,
    clip_rect: [f64; 4],
    output_dir: &str,
) -> Result<PathBuf, std::io::Error> {
    let mut image = image::open(&image_path)
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;
    // 获取图片尺寸
    let (img_w, img_h) = image.dimensions();
    let page = document.pages().get(page_num).unwrap();
    let width = page.width().value as f64;
    let height = page.height().value as f64;

    let aspect_ratio = width / height;
    let target_width = img_w as f64;
    let target_height = target_width / aspect_ratio;
    let id = Ulid::new().to_string();
    // let image_path = PathBuf::from(output_dir).join(format!("{}.png", id));
    let render_config = PdfRenderConfig::new()
        .set_target_width(target_width as i32)
        .set_maximum_height(target_height as i32);

    // 计算裁剪坐标并限制在页面范围内
    let (mut clip_left, mut clip_top) = page
        .points_to_pixels(
            PdfPoints::new(clip_rect[0] as f32),
            PdfPoints::new(clip_rect[1] as f32),
            &render_config,
        )
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;
    let (mut clip_right, mut clip_bottom) = page
        .points_to_pixels(
            PdfPoints::new(clip_rect[2] as f32),
            PdfPoints::new(clip_rect[3] as f32),
            &render_config,
        )
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;

    // 限制坐标范围
    let target_width_i32 = target_width as i32;
    let target_height_i32 = target_height as i32;
    clip_left = clip_left.max(0).min(target_width_i32);
    clip_top = clip_top.max(0).min(target_height_i32);
    clip_right = clip_right.max(0).min(target_width_i32);
    clip_bottom = clip_bottom.max(0).min(target_height_i32);

    let crop_width = (clip_right - clip_left).abs() as u32;
    let crop_height = (clip_bottom - clip_top).abs() as u32;
    let output_path = PathBuf::from(output_dir).join(format!("{}.png", id));
    let cropped = image::imageops::crop(
        &mut image,
        clip_left as u32,
        clip_top as u32 - crop_height,
        crop_width,
        crop_height,
    );
    cropped
        .to_image()
        .save(&output_path)
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;
    Ok(output_path)
}

/// 处理卡片图片和遮罩
///
/// # 参数
/// * `card` - 卡片的注释组
/// * `mask_types` - 需要处理的遮罩类型
/// * `document` - PDF文档对象
/// * `image_map` - 页面图片映射
///
/// # 返回值
/// 返回 (最终图片路径, 遮罩坐标数组)
pub async fn process_card_image_and_masks(
    card: &[Vec<Annotation>],
    mask_types: &[String],
    doc_path: &str,
    temp_dir: &str,
    image_map: &std::collections::HashMap<u32, String>,
) -> Result<(PathBuf, Vec<Vec<[f64; 4]>>), PdfError> {
    let pdfium =
        crate::anki::pdf_utils::get_pdfium(None).map_err(|e| PdfError::Other(e.to_string()))?;
    let document = pdfium
        .load_pdf_from_file(doc_path, None)
        .map_err(|e| PdfError::Other(e.to_string()))?;
    let card_id = ulid::Ulid::new().to_string();
    let mut image_paths: Vec<PathBuf> = Vec::new();
    let mut raw_masks: Vec<([f64; 4], Vec<[f64; 4]>)> = Vec::new();
    // 处理每个组的图片和遮罩
    for (idx, group) in card.iter().enumerate() {
        let page_num = group[0].page_num;
        let rect = group[0].rect;
        // 裁剪图片
        let cropped_image_path = crop_pdf_page(
            &document,
            (page_num - 1) as u16,
            image_map.get(&page_num).unwrap(),
            rect,
            temp_dir,
        )
        .map_err(|e| PdfError::Other(e.to_string()))?;
        image_paths.push(cropped_image_path);

        // 收集遮罩
        let group_masks: Vec<[f64; 4]> = group[1..]
            .iter()
            .filter_map(|annot| {
                let annot_type_str = format!("{:?}", annot.annot_type);
                if mask_types
                    .iter()
                    .any(|mask_type| mask_type.eq_ignore_ascii_case(&annot_type_str))
                {
                    Some(annot.rect)
                } else {
                    None
                }
            })
            .collect();
        raw_masks.push((rect, group_masks));
    }

    // 处理图片信息
    let mut total_height = 0;
    let mut heights = Vec::new();
    let mut widths = Vec::new();
    let mut max_width = 0;
    for path in &image_paths {
        let img = image::open(path).map_err(|e| PdfError::Other(e.to_string()))?;
        total_height += img.height();
        max_width = max_width.max(img.width());
        heights.push(img.height());
        widths.push(img.width());
    }
    // 计算遮罩坐标
    let mut content_groups: std::collections::HashMap<String, Vec<[f64; 4]>> =
        std::collections::HashMap::new();
    let mut y_offset = 0.0;
    let mut empty_group_counter = 0;

    for (idx, (base_rect, _)) in raw_masks.iter().enumerate() {
        let current_height = heights[idx] as f64;
        let current_width = widths[idx] as f64;
        let height_ratio = current_height / total_height as f64;
        let width_ratio = current_width / max_width as f64;

        for annot in &card[idx][1..] {
            let annot_type_str = format!("{:?}", annot.annot_type);
            if !mask_types
                .iter()
                .any(|mask_type| mask_type.eq_ignore_ascii_case(&annot_type_str))
            {
                continue;
            }
            let rects = if let Some(quad_points) = &annot.quad_points {
                if quad_points.len() > 8 {
                    quad_points
                        .chunks(8)
                        .filter(|chunk| chunk.len() == 8)
                        .map(|chunk| {
                            let x_coords: Vec<f64> = chunk.iter().step_by(2).copied().collect();
                            let y_coords: Vec<f64> =
                                chunk.iter().skip(1).step_by(2).copied().collect();

                            [
                                x_coords
                                    .iter()
                                    .min_by(|a, b| a.partial_cmp(b).unwrap())
                                    .copied()
                                    .unwrap(),
                                y_coords
                                    .iter()
                                    .min_by(|a, b| a.partial_cmp(b).unwrap())
                                    .copied()
                                    .unwrap(),
                                x_coords
                                    .iter()
                                    .max_by(|a, b| a.partial_cmp(b).unwrap())
                                    .copied()
                                    .unwrap(),
                                y_coords
                                    .iter()
                                    .max_by(|a, b| a.partial_cmp(b).unwrap())
                                    .copied()
                                    .unwrap(),
                            ]
                        })
                        .collect()
                } else {
                    vec![annot.rect]
                }
            } else {
                vec![annot.rect]
            };

            let normalized_rects: Vec<[f64; 4]> = rects
                .iter()
                .map(|&rect| {
                    let rel_coords = [
                        (rect[0] - base_rect[0]) / (base_rect[2] - base_rect[0]),
                        (base_rect[3] - rect[3]) / (base_rect[3] - base_rect[1]),
                        (rect[2] - rect[0]) / (base_rect[2] - base_rect[0]),
                        (rect[3] - rect[1]) / (base_rect[3] - base_rect[1]),
                    ];

                    let adjusted_coords = [
                        rel_coords[0] * width_ratio,
                        rel_coords[1] * height_ratio + y_offset,
                        rel_coords[2] * width_ratio,
                        rel_coords[3] * height_ratio,
                    ];

                    [
                        format!("{:.4}", adjusted_coords[0])
                            .parse::<f64>()
                            .unwrap_or(0.0)
                            .clamp(0.0, 1.0),
                        format!("{:.4}", adjusted_coords[1])
                            .parse::<f64>()
                            .unwrap_or(0.0)
                            .clamp(0.0, 1.0),
                        format!("{:.4}", adjusted_coords[2])
                            .parse::<f64>()
                            .unwrap_or(1.0)
                            .clamp(0.0, 1.0),
                        format!("{:.4}", adjusted_coords[3])
                            .parse::<f64>()
                            .unwrap_or(0.0)
                            .clamp(0.0, 1.0),
                    ]
                })
                .collect();

            if !normalized_rects.is_empty() {
                let group_key = match &annot.contents {
                    Some(content) => {
                        if content.is_empty() {
                            empty_group_counter += 1;
                            format!("__empty_group_{}", empty_group_counter)
                        } else {
                            content.clone()
                        }
                    }
                    None => {
                        empty_group_counter += 1;
                        format!("__empty_group_{}", empty_group_counter)
                    }
                };
                content_groups
                    .entry(group_key)
                    .or_default()
                    .extend(normalized_rects);
            }
        }
        y_offset += height_ratio;
    }
    let mut final_masks: Vec<Vec<[f64; 4]>> = content_groups.into_values().collect();

    let final_image_path = image_paths[0].clone();
    // 如果有多个图片，进行拼接
    if image_paths.len() > 1 {
        let mut images = Vec::new();
        let mut total_height = 0;
        let mut max_width = 0;

        for path in &image_paths {
            let img = image::open(path).map_err(|e| PdfError::Other(e.to_string()))?;
            total_height += img.height();
            max_width = max_width.max(img.width());
            images.push(img);
        }
        let mut combined = image::RgbImage::new(max_width, total_height);
        // 使用 fill 方法一次性将整个图片填充为白色
        combined.fill(255);

        let mut y_offset = 0;

        for img in images {
            let rgb_img = img.into_rgb8();
            image::imageops::overlay(&mut combined, &rgb_img, 0, y_offset as i64);
            y_offset += i64::from(rgb_img.height());
        }
        combined
            .save(&final_image_path)
            .map_err(|e| PdfError::Other(e.to_string()))?;
    }
    // 使用更简单稳定的排序方法
    const TOLERANCE: f64 = 5.0;
    final_masks.sort_by_key(|mask_group| {
        if mask_group.is_empty() {
            // 处理空数组情况
            return (i32::MAX, i32::MAX, 0);
        }

        // 使用第一个遮罩的y坐标作为主要排序键
        let y = (mask_group[0][1] * 1000.0 / TOLERANCE).round() as i32;
        // 使用第一个遮罩的x坐标作为次要排序键
        let x = (mask_group[0][0] * 1000.0 / TOLERANCE).round() as i32;
        // 使用遮罩数量作为第三排序键
        let count = mask_group.len();

        // 返回整数类型的排序键组合
        (y, x, count)
    });

    Ok((final_image_path, final_masks))
}
